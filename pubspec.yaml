name: coinbag
description: "Coinbag"
version: 1.8.0+83
publish_to: none

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.3.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.2
  # rxdart https://pub.flutter-io.cn/packages/rxdart
  rxdart: ^0.28.0
  #GetX状态管理框架 https://pub.flutter-io.cn/packages/get
  get: ^4.7.2
  #GetX提供的KeyValue存储 https://pub-web.flutter-io.cn/packages/get_storage
  get_storage: ^2.1.1
  # 网络请求库 https://github.com/flutterchina/dio
  dio: ^5.8.0+1
  # 网络请求库 https://pub-web.flutter-io.cn/packages/retrofit
  retrofit: ^4.6.0
  # dio缓存库 https://pub.dev/packages/dio_cache_interceptor
  dio_cache_interceptor: ^4.0.3
  # 加载SVG图片 https://pub.flutter-io.cn/packages/flutter_svg
  flutter_svg: ^2.1.0
  # lottie动画 https://pub-web.flutter-io.cn/packages/lottie
  lottie: ^3.3.1
  # WebView
  flutter_inappwebview: ^6.1.5
  #读取相册
  #https://pub:dev/packages/image_picker
  image_picker: ^1.1.2
  # 打开网页、发起电话、发送电子邮件以及与其他应用程序交互，如打开地图或播放器应，或为URL配置通用和深度链接https://github.com/flutter/plugins/tree/master/packages/url_launcher
  url_launcher: ^6.3.2
  #上拉刷新、下拉加载
  #https://pub.dev/packages/pull_to_refresh
  pull_to_refresh: ^2.0.0
  #权限申请框架 https://pub.flutter-io.cn/packages/permission_handler
  permission_handler: ^12.0.1
  #网络响应日志打印框架 https://pub-web.flutter-io.cn/packages/pretty_dio_logger
  pretty_dio_logger: ^1.4.0
  #普通日志打印框架  https://pub-web.flutter-io.cn/packages/logger
  logger: ^2.6.1
  #屏幕适配 https://pub-web.flutter-io.cn/packages/flutter_screenutil
  flutter_screenutil: ^5.9.3
  #提供了日志、method channel信息、路由信息、网络抓包、帧率、设备与内存信息查看、控件信息查看、颜色拾取、启动耗时、查看源码、查看widget的build链以及对齐标尺的功能.
  #dokit: ^0.8.0-nullsafety.0
  #Kefram性能优化组件  https://github.com/LianjiaTech/keframe/blob/master/README-ZH.md
  keframe: ^3.0.0
  #图片缓存加载框架
  extended_image: ^10.0.1
  #photo_view 图片预览功能插件 https://pub-web.flutter-io.cn/packages/photo_view
  photo_view: ^0.15.0
  #Swiper https://pub-web.flutter-io.cn/packages/card_swiper
  card_swiper: ^3.0.1
  #Widget是否可见判断 https://pub-web.flutter-io.cn/packages/visibility_detector
  visibility_detector: ^0.4.0+2
  #虚线绘制 https://pub-web.flutter-io.cn/packages/path_drawing
  path_drawing: ^1.0.1
  #ORM数据库 https://pub.dev/packages/drift
  drift: ^2.28.0
  #提供 sqlite 动态库，如果要加密数据库，请使用 sqlcipher_flutter_libs
  #sqlcipher_flutter_libs: ^0.6.0
  #sqlite3: ^2.3.0
  sqlite3_flutter_libs: ^0.5.37
  # 用于寻找合适的位置来存放数据库
  path: ^1.9.1
  #文件路径管理工具类
  #https://pub.dev/packages/path_provider
  path_provider: ^2.1.5
  #二维码 https://pub.dev/packages/qr_flutter
  qr_flutter: ^4.1.0
  #Toast https://pub.dev/packages/shimmer_animation/
  oktoast: ^3.4.0
  #骨架屏闪 https://pub.dev/packages/shimmer_animation/
  shimmer_animation: ^2.2.2
  #抖动动画组件  https://pub.dev/packages/shake_animation_widget/
  shake_animation_widget: ^3.0.4
  # https://pub.dev/packages/http_client_helper
  http_client_helper: ^3.0.0
  # 应用中获取版本号和基本信息 https://pub.dev/packages/package_info_plus/install
  package_info_plus: ^8.3.0
  # 折叠组件https://pub.dev/packages/expandable/
  expandable: ^5.0.1
  #时区 https://pub.dev/packages/flutter_timezone
  flutter_timezone: ^4.1.1
  # 分栏switch https://pub.dev/packages/toggle_switch
  toggle_switch: ^2.3.0
  # Switch  https://pub.dev/packages/toggle_switch
  flutter_switch: ^0.3.2
  # 系统跳转 https://pub.dev/packages/app_settings
  app_settings: ^6.1.1
  android_intent_plus: ^5.3.0
  # NFC https://pub.dev/packages/flutter_nfc_kit
  flutter_nfc_kit: ^3.6.0
  #设备类型 https://pub.dev/packages/device_info_plus
  device_info_plus: ^11.5.0
  #播放声音 https://pub.dev/packages/audioplayers
  audioplayers: ^6.5.0
  # Loading https://pub.dev/packages/flutter_easyloading
  flutter_easyloading: ^3.0.5
  #扫码 https://pub.dev/packages/google_mlkit_barcode_scanning
  fl_mlkit_scanning: ^5.2.2
  # https://pub.dev/packages/carousel_slider_plus
  carousel_slider_plus: ^7.1.1
  # https://pub.dev/packages/enough_convert
  enough_convert: ^1.6.0
  # https://pub.dev/packages/js
  js: "^0.7.2"
  # https://pub.dev/packages/flutter_html/versions/3.0.0-beta.2/install
  flutter_html: ^3.0.0
  # https://pub.dev/packages/screen_brightness
  screen_brightness: ^2.1.4
  #  https://pub.dev/packages/timezone/install
  timezone: ^0.10.1
  # https://pub.dev/packages/popup_menu
  popup_menu: ^2.1.0
  #悬浮窗引导 https://pub.dev/packages/showcaseview/install
  showcaseview: ^4.0.1
  # Tab Switch https://pub.dev/packages/animated_toggle_switch
  animated_toggle_switch: ^0.8.5
  #分享 https://pub.dev/packages/share_plus
  share_plus: ^11.0.0
  #URL验证 https://pub.dev/packages/regexed_validator/install
  regexed_validator: ^2.0.0+1
  #指示器 https://pub.dev/packages/smooth_page_indicator/install
  smooth_page_indicator: ^1.2.1
  #将密钥存储在 .env 文件中 https://pub.dev/packages/flutter_dotenv/install
  flutter_dotenv: ^5.2.1
  # 网络状态 https://pub.dev/packages/connectivity_plus/install
  connectivity_plus: ^6.1.4
  # 版本号 https://pub.dev/packages/pub_semver/install
  pub_semver: ^2.2.0
  # base58 https://pub.dev/packages/bs58
  bs58: ^1.0.2
  # 提供国际化和本地化功能，包括消息翻译、复数和性别、金额千分位 日期/数字格式和解析 DateFormat、NumberFormat和BidiFormatter类。 https://pub.dev/packages/intl
  intl: ^0.20.2
  # 加密库https://pub.dev/packages/encrypt
  encrypt: ^5.0.3
  # string转16进制字符 https://pub.dev/packages/hex
  hex: ^0.2.0
  # https://pub.dev/packages/crypto
  crypto: ^3.0.6
  #数据json解析（官方框架）https://pub-web:flutter-io:cn/packages/json_annotation:
  json_annotation: ^4.9.0
  #常用工具类库 common_utils   https://github.com/Sky24n/common_utils
  common_utils: ^2.1.0
  #大数字精度处理 https://pub.dev/packages/decimal
  decimal: ^3.2.1
  big_decimal: ^0.5.0
  # Android内升级
  flutter_app_update: ^3.2.2
  # Bugly
  flutter_bugly: ^1.1.0
  # 钱包核心库
  wallet_core:
    path: ../CoinBagWalletCore
    # git:
    #   url: codecommit://dev@CoinBagWalletCore
    #   ref: 1.5.9
  #生物识别 指纹或面部 https://pub.dev/packages/local_auth
  local_auth: ^2.3.0
  local_auth_platform_interface: ^1.0.10
  # 安全存储 Android Keystore / ios Keychain 保护https://pub.dev/packages/flutter_secure_storage/install
  flutter_secure_storage: ^10.0.0-beta.4
  # Dart 官方提供的代码格式化工具 https://pub.dev/packages/dart_style
  dart_style: ^3.1.1
dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  #自动生成多语言代码
  auto_locale_generator: ^0.0.4
  #drift自动生成代码工具
  drift_dev: ^2.28.0
  flutter_lints: ^6.0.0
  #自动生成retrofit代码
  retrofit_generator: ^10.0.0
  build_runner: ^2.4.15
  #自动生成Json model
  json_serializable: ^6.10.0
  analyzer: ^7.4.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/TargaryenCoin.json
    - assets/lottie/
    - assets/sounds/scan.mp3
    - assets/dapp/
    - assets/env/
    - assets/test/
  fonts:
    - family: PingFang SC
      fonts:
        - asset: assets/fonts/PingFangSC-Regular.ttf
          weight: 400
        - asset: assets/fonts/PingFangSC-Medium.ttf
          weight: 500
        - asset: assets/fonts/PingFangSC-SemiBold.ttf
          weight: 600
    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope-Regular.ttf
          weight: 400
        - asset: assets/fonts/Manrope-Medium.ttf
          weight: 500
        - asset: assets/fonts/Manrope-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Manrope-Bold.ttf
          weight: 700
