#!/bin/bash
###
 # @author: wds
 # @description: 
 # @LastEditTime: 2024-09-04 17:42:02
### 

echo "iOS 打包AppStore渠道包"

# ultra.json 路径
ASSET_PATH="assets/test/ultra.json"
BACKUP_PATH="assets/test/ultra.json.bak"

# Stop on errors
cd ..
set -e 

# Save current directory
current_dir=$(pwd)

# Clean the Flutter project
echo "Cleaning Flutter project..."
flutter clean

# Get all the dependencies
echo "Getting dependencies..."
flutter pub get

# Navigate to the iOS directory
echo "Navigating to the iOS directory..."

if [ -d "ios" ]; then
  cd ios
  
  # Install CocoaPods dependencies
  echo "Installing CocoaPods dependencies..."
  pod install

  # Go back to the original directory
  cd "$current_dir"
  echo "CocoaPods setup completed successfully."
  
  # Build the iOS app
  echo "Building the iOS app..."
  flutter build ipa --release --target=lib/main.dart
  
  echo "iOS app build completed successfully."

  # 上传
  echo "Upload ipa..."
  xcrun altool --upload-app --type ios -f build/ios/ipa/*.ipa --api<PERSON><PERSON> MT6KPUSMTG --apiIssuer 532b44ed-f8a2-4438-a16f-a7a5dae3adda
  
  echo "Upload ipa successfully."
  
else
  echo "Error: 'ios' directory does not exist."
  exit 1
fi

# Optional: Open the iOS project in Xcode (uncomment if needed)
# echo "Opening iOS project in Xcode..."
# open ios/Runner.xcworkspace

# 备份并移除 ultra.json
if [ -f "$ASSET_PATH" ]; then
  mv "$ASSET_PATH" "$BACKUP_PATH"
  echo "ultra.json temporarily removed for release build."
fi

# 恢复 ultra.json
if [ -f "$BACKUP_PATH" ]; then
  mv "$BACKUP_PATH" "$ASSET_PATH"
  echo "ultra.json restored after release build."
fi

echo "All tasks completed successfully."
