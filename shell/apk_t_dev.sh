#!/bin/bash
###
 # @author: wds
 # @description: 
 # @LastEditTime: 2024-12-03 14:53:57
### 
###
 # @author: wds
 # @description: 
 # @LastEditTime: 2024-08-26 11:01:03
### 

echo "开始打包Android 手动广播调试apk，包含测试币，开发模式，不是线上apk"

# ultra.json 路径
ASSET_PATH="assets/test/ultra.json"
BACKUP_PATH="assets/test/ultra.json.bak"

# 切换到上一级目录
cd ..

# 设置脚本遇到错误时停止执行
set -e

# 保存当前目录
current_dir=$(pwd)

# 清理 Flutter 项目
echo "正在清理 Flutter 项目..."
flutter clean

# 备份并移除 ultra.json
if [ -f "$ASSET_PATH" ]; then
  mv "$ASSET_PATH" "$BACKUP_PATH"
  echo "ultra.json temporarily removed for release build."
fi

# 打包 APK
echo "开始构建apk..."
flutter build apk --release --target=lib/main.testnet_dev.dart

# 恢复 ultra.json
if [ -f "$BACKUP_PATH" ]; then
  mv "$BACKUP_PATH" "$ASSET_PATH"
  echo "ultra.json restored after release build."
fi

# 打包完成
echo "APK 打包完成，文件位于: $current_dir/build/app/outputs/apk/release/"

# 打开 APK 输出文件夹
open "./build/app/outputs/apk/release/"
