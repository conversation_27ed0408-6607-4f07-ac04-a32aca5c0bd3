{"contractName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abi": [{"constant": true, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "tokenOwner", "type": "address"}, {"indexed": true, "name": "spender", "type": "address"}, {"indexed": false, "name": "tokens", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "to", "type": "address"}, {"indexed": false, "name": "tokens", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "tokenOwner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "receiver", "type": "address"}, {"name": "numTokens", "type": "uint256"}], "name": "transfer", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "delegate", "type": "address"}, {"name": "numTokens", "type": "uint256"}], "name": "approve", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "owner", "type": "address"}, {"name": "delegate", "type": "address"}], "name": "allowance", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "owner", "type": "address"}, {"name": "buyer", "type": "address"}, {"name": "numTokens", "type": "uint256"}], "name": "transferFrom", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.5.8+commit.23d335f2\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"constant\":true,\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"name\":\"delegate\",\"type\":\"address\"},{\"name\":\"numTokens\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"name\":\"owner\",\"type\":\"address\"},{\"name\":\"buyer\",\"type\":\"address\"},{\"name\":\"numTokens\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"name\":\"\",\"type\":\"uint8\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"name\":\"tokenOwner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"name\":\"\",\"type\":\"string\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"name\":\"receiver\",\"type\":\"address\"},{\"name\":\"numTokens\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"name\":\"owner\",\"type\":\"address\"},{\"name\":\"delegate\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"name\":\"tokenOwner\",\"type\":\"address\"},{\"indexed\":true,\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"name\":\"tokens\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"name\":\"tokens\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"/C/@Projects/ether-wallet-contract/contracts/TrargaryenCoin.sol\":\"TargaryenCoin\"},\"evmVersion\":\"petersburg\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"/C/@Projects/ether-wallet-contract/contracts/TrargaryenCoin.sol\":{\"keccak256\":\"0x70f9049d4d61921814ef52d67c4443ed7297bfc1b1c77ff6424ef83a53e07316\",\"urls\":[\"bzzr://358d33e5be2e122ac847f72085507e914aca3bf97d2e32407ae8c0b0122e4f68\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "sourceMap": "140:2094:1:-;;;631:119;8:9:-1;5:2;;;30:1;27;20:12;5:2;631:119:1;677:22;662:12;:37;;;;732:12;;709:8;:20;718:10;709:20;;;;;;;;;;;;;;;:35;;;;140:2094;;;;;;", "deployedSourceMap": "140:2094:1:-;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;140:2094:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;169:45;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;23:1:-1;8:100;33:3;30:1;27:10;8:100;;;99:1;94:3;90:11;84:18;80:1;75:3;71:11;64:39;52:2;49:1;45:10;40:15;;8:100;;;12:14;169:45:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1348:208;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1348:208:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;756:86;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1695:537;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1695:537:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;263:35;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;848:110;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;848:110:1;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;220:37;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;23:1:-1;8:100;33:3;30:1;27:10;8:100;;;99:1;94:3;90:11;84:18;80:1;75:3;71:11;64:39;52:2;49:1;45:10;40:15;;8:100;;;12:14;220:37:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;964:378;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;964:378:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;1562:127;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1562:127:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;169:45;;;;;;;;;;;;;;;;;;;:::o;1348:208::-;1415:4;1463:9;1431:7;:19;1439:10;1431:19;;;;;;;;;;;;;;;:29;1451:8;1431:29;;;;;;;;;;;;;;;:41;;;;1508:8;1487:41;;1496:10;1487:41;;;1518:9;1487:41;;;;;;;;;;;;;;;;;;1545:4;1538:11;;1348:208;;;;:::o;756:86::-;800:7;823:12;;816:19;;756:86;:::o;1695:537::-;1779:4;1816:8;:15;1825:5;1816:15;;;;;;;;;;;;;;;;1803:9;:28;;1795:71;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1897:7;:14;1905:5;1897:14;;;;;;;;;;;;;;;:26;1912:10;1897:26;;;;;;;;;;;;;;;;1884:9;:39;;1876:83;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1988:30;2008:9;1988:8;:15;1997:5;1988:15;;;;;;;;;;;;;;;;:19;;:30;;;;:::i;:::-;1970:8;:15;1979:5;1970:15;;;;;;;;;;;;;;;:48;;;;2057:41;2088:9;2057:7;:14;2065:5;2057:14;;;;;;;;;;;;;;;:26;2072:10;2057:26;;;;;;;;;;;;;;;;:30;;:41;;;;:::i;:::-;2028:7;:14;2036:5;2028:14;;;;;;;;;;;;;;;:26;2043:10;2028:26;;;;;;;;;;;;;;;:70;;;;2126:30;2146:9;2126:8;:15;2135:5;2126:15;;;;;;;;;;;;;;;;:19;;:30;;;;:::i;:::-;2108:8;:15;2117:5;2108:15;;;;;;;;;;;;;;;:48;;;;2187:5;2171:33;;2180:5;2171:33;;;2194:9;2171:33;;;;;;;;;;;;;;;;;;2221:4;2214:11;;1695:537;;;;;:::o;263:35::-;296:2;263:35;:::o;848:110::-;908:4;931:8;:20;940:10;931:20;;;;;;;;;;;;;;;;924:27;;848:110;;;:::o;220:37::-;;;;;;;;;;;;;;;;;;;:::o;964:378::-;1032:4;1069:8;:20;1078:10;1069:20;;;;;;;;;;;;;;;;1056:9;:33;;1048:77;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1159:35;1184:9;1159:8;:20;1168:10;1159:20;;;;;;;;;;;;;;;;:24;;:35;;;;:::i;:::-;1136:8;:20;1145:10;1136:20;;;;;;;;;;;;;;;:58;;;;1225:33;1248:9;1225:8;:18;1234:8;1225:18;;;;;;;;;;;;;;;;:22;;:33;;;;:::i;:::-;1204:8;:18;1213:8;1204:18;;;;;;;;;;;;;;;:54;;;;1294:8;1273:41;;1282:10;1273:41;;;1304:9;1273:41;;;;;;;;;;;;;;;;;;1331:4;1324:11;;964:378;;;;:::o;1562:127::-;1635:4;1658:7;:14;1666:5;1658:14;;;;;;;;;;;;;;;:24;1673:8;1658:24;;;;;;;;;;;;;;;;1651:31;;1562:127;;;;:::o;2259:116::-;2317:7;2346:1;2341;:6;;2334:14;;;;2367:1;2363;:5;2356:12;;2259:116;;;;:::o;2381:137::-;2439:7;2456:9;2472:1;2468;:5;2456:17;;2493:1;2488;:6;;2481:14;;;;2510:1;2503:8;;;2381:137;;;;:::o", "source": "/*\n * This code has not been reviewed.\n * Do not use or deploy this code before reviewing it personally first.\n */\npragma solidity ^0.5.0;\n\ncontract TargaryenCoin {\n    string public constant name = \"<PERSON><PERSON><PERSON><PERSON><PERSON>oin\";\n    string public constant symbol = \"TGE\";\n    uint8 public constant decimals = 18;\n\n    event Approval(address indexed tokenOwner, address indexed spender, uint tokens);\n    event Transfer(address indexed from, address indexed to, uint tokens);\n\n    mapping(address => uint256) balances;\n    mapping(address => mapping (address => uint256)) allowed;\n\n    uint256 totalSupply_;\n\n    using SafeMath for uint256;\n\n   constructor() public {\n        totalSupply_ = 100000000 * (10 ** 18);\n        balances[msg.sender] = totalSupply_;\n   }\n\n    function totalSupply() public view returns (uint256) {\n\t    return totalSupply_;\n    }\n\n    function balanceOf(address tokenOwner) public view returns (uint) {\n        return balances[tokenOwner];\n    }\n\n    function transfer(address receiver, uint numTokens) public returns (bool) {\n        require(numTokens <= balances[msg.sender], \"Sender there's no enough funds.\");\n\n        balances[msg.sender] = balances[msg.sender].sub(numTokens);\n        balances[receiver] = balances[receiver].add(numTokens);\n        emit Transfer(msg.sender, receiver, numTokens);\n        return true;\n    }\n\n    function approve(address delegate, uint numTokens) public returns (bool) {\n        allowed[msg.sender][delegate] = numTokens;\n        emit Approval(msg.sender, delegate, numTokens);\n        return true;\n    }\n\n    function allowance(address owner, address delegate) public view returns (uint) {\n        return allowed[owner][delegate];\n    }\n\n    function transferFrom(address owner, address buyer, uint numTokens) public returns (bool) {\n        require(numTokens <= balances[owner], \"Owner there's no enough funds.\");\n        require(numTokens <= allowed[owner][msg.sender], \"Sender there's no enough funds.\");\n\n        balances[owner] = balances[owner].sub(numTokens);\n        allowed[owner][msg.sender] = allowed[owner][msg.sender].sub(numTokens);\n        balances[buyer] = balances[buyer].add(numTokens);\n        emit Transfer(owner, buyer, numTokens);\n        return true;\n    }\n}\n\nlibrary SafeMath {\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\n      assert(b <= a);\n      return a - b;\n    }\n\n    function add(uint256 a, uint256 b) internal pure returns (uint256) {\n      uint256 c = a + b;\n      assert(c >= a);\n      return c;\n    }\n}", "sourcePath": "C:/@Projects/ether-wallet-contract/contracts/TrargaryenCoin.sol", "ast": {"absolutePath": "/C/@Projects/ether-wallet-contract/contracts/TrargaryenCoin.sol", "exportedSymbols": {"SafeMath": [361], "TargaryenCoin": [316]}, "id": 362, "nodeType": "SourceUnit", "nodes": [{"id": 58, "literals": ["solidity", "^", "0.5", ".0"], "nodeType": "PragmaDirective", "src": "115:23:1"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 316, "linearizedBaseContracts": [316], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 61, "name": "name", "nodeType": "VariableDeclaration", "scope": 316, "src": "169:45:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory", "typeString": "string"}, "typeName": {"id": 59, "name": "string", "nodeType": "ElementaryTypeName", "src": "169:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "54617267617279656e436f696e", "id": 60, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "199:15:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_72cbe7939b8ec539b710f4713fa2305641613679cf4882c2f9a0fe6a3edf220f", "typeString": "literal_string \"Targaryen<PERSON>oin\""}, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "visibility": "public"}, {"constant": true, "id": 64, "name": "symbol", "nodeType": "VariableDeclaration", "scope": 316, "src": "220:37:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory", "typeString": "string"}, "typeName": {"id": 62, "name": "string", "nodeType": "ElementaryTypeName", "src": "220:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "544745", "id": 63, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "252:5:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_24d16e9f8cacdd71860f78d57849207c1f943bbfe1e16c888fc16b746f3b2945", "typeString": "literal_string \"TGE\""}, "value": "TGE"}, "visibility": "public"}, {"constant": true, "id": 67, "name": "decimals", "nodeType": "VariableDeclaration", "scope": 316, "src": "263:35:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 65, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "263:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "value": {"argumentTypes": null, "hexValue": "3138", "id": 66, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "296:2:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "visibility": "public"}, {"anonymous": false, "documentation": null, "id": 75, "name": "Approval", "nodeType": "EventDefinition", "parameters": {"id": 74, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 69, "indexed": true, "name": "tokenOwner", "nodeType": "VariableDeclaration", "scope": 75, "src": "320:26:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 68, "name": "address", "nodeType": "ElementaryTypeName", "src": "320:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 71, "indexed": true, "name": "spender", "nodeType": "VariableDeclaration", "scope": 75, "src": "348:23:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 70, "name": "address", "nodeType": "ElementaryTypeName", "src": "348:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 73, "indexed": false, "name": "tokens", "nodeType": "VariableDeclaration", "scope": 75, "src": "373:11:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 72, "name": "uint", "nodeType": "ElementaryTypeName", "src": "373:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "319:66:1"}, "src": "305:81:1"}, {"anonymous": false, "documentation": null, "id": 83, "name": "Transfer", "nodeType": "EventDefinition", "parameters": {"id": 82, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 77, "indexed": true, "name": "from", "nodeType": "VariableDeclaration", "scope": 83, "src": "406:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 76, "name": "address", "nodeType": "ElementaryTypeName", "src": "406:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 79, "indexed": true, "name": "to", "nodeType": "VariableDeclaration", "scope": 83, "src": "428:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 78, "name": "address", "nodeType": "ElementaryTypeName", "src": "428:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 81, "indexed": false, "name": "tokens", "nodeType": "VariableDeclaration", "scope": 83, "src": "448:11:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 80, "name": "uint", "nodeType": "ElementaryTypeName", "src": "448:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "405:55:1"}, "src": "391:70:1"}, {"constant": false, "id": 87, "name": "balances", "nodeType": "VariableDeclaration", "scope": 316, "src": "467:36:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 86, "keyType": {"id": 84, "name": "address", "nodeType": "ElementaryTypeName", "src": "475:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "467:27:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 85, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "486:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 93, "name": "allowed", "nodeType": "VariableDeclaration", "scope": 316, "src": "509:56:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 92, "keyType": {"id": 88, "name": "address", "nodeType": "ElementaryTypeName", "src": "517:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "509:48:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueType": {"id": 91, "keyType": {"id": 89, "name": "address", "nodeType": "ElementaryTypeName", "src": "537:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "528:28:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 90, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "548:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 95, "name": "totalSupply_", "nodeType": "VariableDeclaration", "scope": 316, "src": "572:20:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 94, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "572:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"id": 98, "libraryName": {"contractScope": null, "id": 96, "name": "SafeMath", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 361, "src": "605:8:1", "typeDescriptions": {"typeIdentifier": "t_contract$_SafeMath_$361", "typeString": "library SafeMath"}}, "nodeType": "UsingForDirective", "src": "599:27:1", "typeName": {"id": 97, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "618:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, {"body": {"id": 117, "nodeType": "Block", "src": "652:98:1", "statements": [{"expression": {"argumentTypes": null, "id": 108, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 101, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "662:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_rational_100000000000000000000000000_by_1", "typeString": "int_const 100000000000000000000000000"}, "id": 107, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "hexValue": "313030303030303030", "id": 102, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "677:9:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_100000000_by_1", "typeString": "int_const 100000000"}, "value": "100000000"}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"argumentTypes": null, "components": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "id": 105, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "hexValue": "3130", "id": 103, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "690:2:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "nodeType": "BinaryOperation", "operator": "**", "rightExpression": {"argumentTypes": null, "hexValue": "3138", "id": 104, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "696:2:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "src": "690:8:1", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}], "id": 106, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "689:10:1", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}, "src": "677:22:1", "typeDescriptions": {"typeIdentifier": "t_rational_100000000000000000000000000_by_1", "typeString": "int_const 100000000000000000000000000"}}, "src": "662:37:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 109, "nodeType": "ExpressionStatement", "src": "662:37:1"}, {"expression": {"argumentTypes": null, "id": 115, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 110, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "709:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 113, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 111, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "718:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 112, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "718:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "709:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 114, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "732:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "709:35:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 116, "nodeType": "ExpressionStatement", "src": "709:35:1"}]}, "documentation": null, "id": 118, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nodeType": "FunctionDefinition", "parameters": {"id": 99, "nodeType": "ParameterList", "parameters": [], "src": "642:2:1"}, "returnParameters": {"id": 100, "nodeType": "ParameterList", "parameters": [], "src": "652:0:1"}, "scope": 316, "src": "631:119:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 125, "nodeType": "Block", "src": "809:33:1", "statements": [{"expression": {"argumentTypes": null, "id": 123, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "823:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 122, "id": 124, "nodeType": "Return", "src": "816:19:1"}]}, "documentation": null, "id": 126, "implemented": true, "kind": "function", "modifiers": [], "name": "totalSupply", "nodeType": "FunctionDefinition", "parameters": {"id": 119, "nodeType": "ParameterList", "parameters": [], "src": "776:2:1"}, "returnParameters": {"id": 122, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 121, "name": "", "nodeType": "VariableDeclaration", "scope": 126, "src": "800:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 120, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "800:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "799:9:1"}, "scope": 316, "src": "756:86:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 137, "nodeType": "Block", "src": "914:44:1", "statements": [{"expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 133, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "931:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 135, "indexExpression": {"argumentTypes": null, "id": 134, "name": "tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 128, "src": "940:10:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "931:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 132, "id": 136, "nodeType": "Return", "src": "924:27:1"}]}, "documentation": null, "id": 138, "implemented": true, "kind": "function", "modifiers": [], "name": "balanceOf", "nodeType": "FunctionDefinition", "parameters": {"id": 129, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 128, "name": "tokenOwner", "nodeType": "VariableDeclaration", "scope": 138, "src": "867:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 127, "name": "address", "nodeType": "ElementaryTypeName", "src": "867:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "866:20:1"}, "returnParameters": {"id": 132, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 131, "name": "", "nodeType": "VariableDeclaration", "scope": 138, "src": "908:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 130, "name": "uint", "nodeType": "ElementaryTypeName", "src": "908:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "907:6:1"}, "scope": 316, "src": "848:110:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 190, "nodeType": "Block", "src": "1038:304:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 148, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1056:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 149, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1069:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 152, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 150, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1078:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 151, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1078:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1069:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1056:33:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "53656e6465722074686572652773206e6f20656e6f7567682066756e64732e", "id": 154, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1091:33:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}, "value": "Sender there's no enough funds."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}], "id": 147, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [379, 380], "referencedDeclaration": 380, "src": "1048:7:1", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 155, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1048:77:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 156, "nodeType": "ExpressionStatement", "src": "1048:77:1"}, {"expression": {"argumentTypes": null, "id": 168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 157, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1136:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 160, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 158, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1145:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 159, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1145:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1136:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 166, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1184:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 161, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1159:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 164, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 162, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1168:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 163, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1168:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1159:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 165, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 336, "src": "1159:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 167, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1159:35:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1136:58:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 169, "nodeType": "ExpressionStatement", "src": "1136:58:1"}, {"expression": {"argumentTypes": null, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 170, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1204:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 172, "indexExpression": {"argumentTypes": null, "id": 171, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1213:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1204:18:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 177, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1248:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 173, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1225:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 175, "indexExpression": {"argumentTypes": null, "id": 174, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1234:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1225:18:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 176, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "1225:22:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 178, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1225:33:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1204:54:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 180, "nodeType": "ExpressionStatement", "src": "1204:54:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 182, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1282:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 183, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1282:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 184, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1294:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 185, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1304:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 181, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 83, "src": "1273:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1273:41:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 187, "nodeType": "EmitStatement", "src": "1268:46:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 188, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1331:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 146, "id": 189, "nodeType": "Return", "src": "1324:11:1"}]}, "documentation": null, "id": 191, "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "nodeType": "FunctionDefinition", "parameters": {"id": 143, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 140, "name": "receiver", "nodeType": "VariableDeclaration", "scope": 191, "src": "982:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 139, "name": "address", "nodeType": "ElementaryTypeName", "src": "982:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 142, "name": "numTokens", "nodeType": "VariableDeclaration", "scope": 191, "src": "1000:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 141, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1000:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "981:34:1"}, "returnParameters": {"id": 146, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 145, "name": "", "nodeType": "VariableDeclaration", "scope": 191, "src": "1032:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 144, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1032:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1031:6:1"}, "scope": 316, "src": "964:378:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 218, "nodeType": "Block", "src": "1421:135:1", "statements": [{"expression": {"argumentTypes": null, "id": 207, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 200, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1431:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 204, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 201, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1439:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 202, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1439:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1431:19:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 205, "indexExpression": {"argumentTypes": null, "id": 203, "name": "delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1451:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1431:29:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 206, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 195, "src": "1463:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1431:41:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 208, "nodeType": "ExpressionStatement", "src": "1431:41:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 210, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1496:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 211, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1496:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 212, "name": "delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1508:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 213, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 195, "src": "1518:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 209, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 75, "src": "1487:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 214, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1487:41:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 215, "nodeType": "EmitStatement", "src": "1482:46:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 216, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1545:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 199, "id": 217, "nodeType": "Return", "src": "1538:11:1"}]}, "documentation": null, "id": 219, "implemented": true, "kind": "function", "modifiers": [], "name": "approve", "nodeType": "FunctionDefinition", "parameters": {"id": 196, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 193, "name": "delegate", "nodeType": "VariableDeclaration", "scope": 219, "src": "1365:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 192, "name": "address", "nodeType": "ElementaryTypeName", "src": "1365:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 195, "name": "numTokens", "nodeType": "VariableDeclaration", "scope": 219, "src": "1383:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 194, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1383:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1364:34:1"}, "returnParameters": {"id": 199, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 198, "name": "", "nodeType": "VariableDeclaration", "scope": 219, "src": "1415:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 197, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1415:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1414:6:1"}, "scope": 316, "src": "1348:208:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 234, "nodeType": "Block", "src": "1641:48:1", "statements": [{"expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 228, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1658:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 230, "indexExpression": {"argumentTypes": null, "id": 229, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 221, "src": "1666:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1658:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 232, "indexExpression": {"argumentTypes": null, "id": 231, "name": "delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 223, "src": "1673:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1658:24:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 227, "id": 233, "nodeType": "Return", "src": "1651:31:1"}]}, "documentation": null, "id": 235, "implemented": true, "kind": "function", "modifiers": [], "name": "allowance", "nodeType": "FunctionDefinition", "parameters": {"id": 224, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 221, "name": "owner", "nodeType": "VariableDeclaration", "scope": 235, "src": "1581:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 220, "name": "address", "nodeType": "ElementaryTypeName", "src": "1581:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 223, "name": "delegate", "nodeType": "VariableDeclaration", "scope": 235, "src": "1596:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 222, "name": "address", "nodeType": "ElementaryTypeName", "src": "1596:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "1580:33:1"}, "returnParameters": {"id": 227, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 226, "name": "", "nodeType": "VariableDeclaration", "scope": 235, "src": "1635:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 225, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1635:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1634:6:1"}, "scope": 316, "src": "1562:127:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 314, "nodeType": "Block", "src": "1785:447:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 251, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 247, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "1803:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 248, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1816:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 250, "indexExpression": {"argumentTypes": null, "id": 249, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1825:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1816:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1803:28:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "4f776e65722074686572652773206e6f20656e6f7567682066756e64732e", "id": 252, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1833:32:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_20f92e31651865479c230e80930318b133341c20b1254aa6619a11e9e361b902", "typeString": "literal_string \"Owner there's no enough funds.\""}, "value": "Owner there's no enough funds."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_20f92e31651865479c230e80930318b133341c20b1254aa6619a11e9e361b902", "typeString": "literal_string \"Owner there's no enough funds.\""}], "id": 246, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [379, 380], "referencedDeclaration": 380, "src": "1795:7:1", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 253, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1795:71:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 254, "nodeType": "ExpressionStatement", "src": "1795:71:1"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 263, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 256, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "1884:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 257, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1897:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 259, "indexExpression": {"argumentTypes": null, "id": 258, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1905:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1897:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 262, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 260, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1912:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 261, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1912:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1897:26:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1884:39:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "53656e6465722074686572652773206e6f20656e6f7567682066756e64732e", "id": 264, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1925:33:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}, "value": "Sender there's no enough funds."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}], "id": 255, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [379, 380], "referencedDeclaration": 380, "src": "1876:7:1", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 265, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1876:83:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 266, "nodeType": "ExpressionStatement", "src": "1876:83:1"}, {"expression": {"argumentTypes": null, "id": 276, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 267, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1970:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 269, "indexExpression": {"argumentTypes": null, "id": 268, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1979:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1970:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 274, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2008:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 270, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1988:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 272, "indexExpression": {"argumentTypes": null, "id": 271, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1997:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1988:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 273, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 336, "src": "1988:19:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 275, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1988:30:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1970:48:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 277, "nodeType": "ExpressionStatement", "src": "1970:48:1"}, {"expression": {"argumentTypes": null, "id": 293, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 278, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "2028:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 282, "indexExpression": {"argumentTypes": null, "id": 279, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2036:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2028:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 283, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 280, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "2043:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 281, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2043:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2028:26:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 291, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2088:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 284, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "2057:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 286, "indexExpression": {"argumentTypes": null, "id": 285, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2065:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2057:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 289, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 287, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "2072:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 288, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2072:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2057:26:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 290, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 336, "src": "2057:30:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 292, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2057:41:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2028:70:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 294, "nodeType": "ExpressionStatement", "src": "2028:70:1"}, {"expression": {"argumentTypes": null, "id": 304, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 295, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "2108:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 297, "indexExpression": {"argumentTypes": null, "id": 296, "name": "buyer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 239, "src": "2117:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2108:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 302, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2146:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 298, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "2126:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 300, "indexExpression": {"argumentTypes": null, "id": 299, "name": "buyer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 239, "src": "2135:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2126:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 301, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "2126:19:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 303, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2126:30:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2108:48:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 305, "nodeType": "ExpressionStatement", "src": "2108:48:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 307, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2180:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 308, "name": "buyer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 239, "src": "2187:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 309, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2194:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 306, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 83, "src": "2171:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 310, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2171:33:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 311, "nodeType": "EmitStatement", "src": "2166:38:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 312, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2221:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 245, "id": 313, "nodeType": "Return", "src": "2214:11:1"}]}, "documentation": null, "id": 315, "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "nodeType": "FunctionDefinition", "parameters": {"id": 242, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 237, "name": "owner", "nodeType": "VariableDeclaration", "scope": 315, "src": "1717:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 236, "name": "address", "nodeType": "ElementaryTypeName", "src": "1717:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 239, "name": "buyer", "nodeType": "VariableDeclaration", "scope": 315, "src": "1732:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 238, "name": "address", "nodeType": "ElementaryTypeName", "src": "1732:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 241, "name": "numTokens", "nodeType": "VariableDeclaration", "scope": 315, "src": "1747:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 240, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1747:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1716:46:1"}, "returnParameters": {"id": 245, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 244, "name": "", "nodeType": "VariableDeclaration", "scope": 315, "src": "1779:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 243, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1779:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1778:6:1"}, "scope": 316, "src": "1695:537:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}], "scope": 362, "src": "140:2094:1"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "library", "documentation": null, "fullyImplemented": true, "id": 361, "linearizedBaseContracts": [361], "name": "SafeMath", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 335, "nodeType": "Block", "src": "2326:49:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 328, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 326, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 320, "src": "2341:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "id": 327, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 318, "src": "2346:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2341:6:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 325, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 365, "src": "2334:6:1", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 329, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2334:14:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 330, "nodeType": "ExpressionStatement", "src": "2334:14:1"}, {"expression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 333, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 331, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 318, "src": "2363:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"argumentTypes": null, "id": 332, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 320, "src": "2367:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2363:5:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 324, "id": 334, "nodeType": "Return", "src": "2356:12:1"}]}, "documentation": null, "id": 336, "implemented": true, "kind": "function", "modifiers": [], "name": "sub", "nodeType": "FunctionDefinition", "parameters": {"id": 321, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 318, "name": "a", "nodeType": "VariableDeclaration", "scope": 336, "src": "2272:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 317, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2272:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 320, "name": "b", "nodeType": "VariableDeclaration", "scope": 336, "src": "2283:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 319, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2283:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2271:22:1"}, "returnParameters": {"id": 324, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 323, "name": "", "nodeType": "VariableDeclaration", "scope": 336, "src": "2317:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 322, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2317:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2316:9:1"}, "scope": 361, "src": "2259:116:1", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, {"body": {"id": 359, "nodeType": "Block", "src": "2448:70:1", "statements": [{"assignments": [346], "declarations": [{"constant": false, "id": 346, "name": "c", "nodeType": "VariableDeclaration", "scope": 359, "src": "2456:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 345, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2456:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "id": 350, "initialValue": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 349, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 347, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 338, "src": "2468:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"argumentTypes": null, "id": 348, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 340, "src": "2472:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2468:5:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2456:17:1"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 354, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 352, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 346, "src": "2488:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 353, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 338, "src": "2493:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2488:6:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 351, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 365, "src": "2481:6:1", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 355, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2481:14:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 356, "nodeType": "ExpressionStatement", "src": "2481:14:1"}, {"expression": {"argumentTypes": null, "id": 357, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 346, "src": "2510:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 344, "id": 358, "nodeType": "Return", "src": "2503:8:1"}]}, "documentation": null, "id": 360, "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nodeType": "FunctionDefinition", "parameters": {"id": 341, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 338, "name": "a", "nodeType": "VariableDeclaration", "scope": 360, "src": "2394:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 337, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2394:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 340, "name": "b", "nodeType": "VariableDeclaration", "scope": 360, "src": "2405:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 339, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2405:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2393:22:1"}, "returnParameters": {"id": 344, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 343, "name": "", "nodeType": "VariableDeclaration", "scope": 360, "src": "2439:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 342, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2439:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2438:9:1"}, "scope": 361, "src": "2381:137:1", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}], "scope": 362, "src": "2236:284:1"}], "src": "115:2405:1"}, "legacyAST": {"absolutePath": "/C/@Projects/ether-wallet-contract/contracts/TrargaryenCoin.sol", "exportedSymbols": {"SafeMath": [361], "TargaryenCoin": [316]}, "id": 362, "nodeType": "SourceUnit", "nodes": [{"id": 58, "literals": ["solidity", "^", "0.5", ".0"], "nodeType": "PragmaDirective", "src": "115:23:1"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 316, "linearizedBaseContracts": [316], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 61, "name": "name", "nodeType": "VariableDeclaration", "scope": 316, "src": "169:45:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory", "typeString": "string"}, "typeName": {"id": 59, "name": "string", "nodeType": "ElementaryTypeName", "src": "169:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "54617267617279656e436f696e", "id": 60, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "199:15:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_72cbe7939b8ec539b710f4713fa2305641613679cf4882c2f9a0fe6a3edf220f", "typeString": "literal_string \"Targaryen<PERSON>oin\""}, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "visibility": "public"}, {"constant": true, "id": 64, "name": "symbol", "nodeType": "VariableDeclaration", "scope": 316, "src": "220:37:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory", "typeString": "string"}, "typeName": {"id": 62, "name": "string", "nodeType": "ElementaryTypeName", "src": "220:6:1", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "544745", "id": 63, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "252:5:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_24d16e9f8cacdd71860f78d57849207c1f943bbfe1e16c888fc16b746f3b2945", "typeString": "literal_string \"TGE\""}, "value": "TGE"}, "visibility": "public"}, {"constant": true, "id": 67, "name": "decimals", "nodeType": "VariableDeclaration", "scope": 316, "src": "263:35:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 65, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "263:5:1", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "value": {"argumentTypes": null, "hexValue": "3138", "id": 66, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "296:2:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "visibility": "public"}, {"anonymous": false, "documentation": null, "id": 75, "name": "Approval", "nodeType": "EventDefinition", "parameters": {"id": 74, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 69, "indexed": true, "name": "tokenOwner", "nodeType": "VariableDeclaration", "scope": 75, "src": "320:26:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 68, "name": "address", "nodeType": "ElementaryTypeName", "src": "320:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 71, "indexed": true, "name": "spender", "nodeType": "VariableDeclaration", "scope": 75, "src": "348:23:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 70, "name": "address", "nodeType": "ElementaryTypeName", "src": "348:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 73, "indexed": false, "name": "tokens", "nodeType": "VariableDeclaration", "scope": 75, "src": "373:11:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 72, "name": "uint", "nodeType": "ElementaryTypeName", "src": "373:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "319:66:1"}, "src": "305:81:1"}, {"anonymous": false, "documentation": null, "id": 83, "name": "Transfer", "nodeType": "EventDefinition", "parameters": {"id": 82, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 77, "indexed": true, "name": "from", "nodeType": "VariableDeclaration", "scope": 83, "src": "406:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 76, "name": "address", "nodeType": "ElementaryTypeName", "src": "406:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 79, "indexed": true, "name": "to", "nodeType": "VariableDeclaration", "scope": 83, "src": "428:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 78, "name": "address", "nodeType": "ElementaryTypeName", "src": "428:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 81, "indexed": false, "name": "tokens", "nodeType": "VariableDeclaration", "scope": 83, "src": "448:11:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 80, "name": "uint", "nodeType": "ElementaryTypeName", "src": "448:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "405:55:1"}, "src": "391:70:1"}, {"constant": false, "id": 87, "name": "balances", "nodeType": "VariableDeclaration", "scope": 316, "src": "467:36:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 86, "keyType": {"id": 84, "name": "address", "nodeType": "ElementaryTypeName", "src": "475:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "467:27:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 85, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "486:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 93, "name": "allowed", "nodeType": "VariableDeclaration", "scope": 316, "src": "509:56:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 92, "keyType": {"id": 88, "name": "address", "nodeType": "ElementaryTypeName", "src": "517:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "509:48:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueType": {"id": 91, "keyType": {"id": 89, "name": "address", "nodeType": "ElementaryTypeName", "src": "537:7:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "528:28:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 90, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "548:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 95, "name": "totalSupply_", "nodeType": "VariableDeclaration", "scope": 316, "src": "572:20:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 94, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "572:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"id": 98, "libraryName": {"contractScope": null, "id": 96, "name": "SafeMath", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 361, "src": "605:8:1", "typeDescriptions": {"typeIdentifier": "t_contract$_SafeMath_$361", "typeString": "library SafeMath"}}, "nodeType": "UsingForDirective", "src": "599:27:1", "typeName": {"id": 97, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "618:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, {"body": {"id": 117, "nodeType": "Block", "src": "652:98:1", "statements": [{"expression": {"argumentTypes": null, "id": 108, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 101, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "662:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_rational_100000000000000000000000000_by_1", "typeString": "int_const 100000000000000000000000000"}, "id": 107, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "hexValue": "313030303030303030", "id": 102, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "677:9:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_100000000_by_1", "typeString": "int_const 100000000"}, "value": "100000000"}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"argumentTypes": null, "components": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "id": 105, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "hexValue": "3130", "id": 103, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "690:2:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "nodeType": "BinaryOperation", "operator": "**", "rightExpression": {"argumentTypes": null, "hexValue": "3138", "id": 104, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "696:2:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "src": "690:8:1", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}], "id": 106, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "689:10:1", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}, "src": "677:22:1", "typeDescriptions": {"typeIdentifier": "t_rational_100000000000000000000000000_by_1", "typeString": "int_const 100000000000000000000000000"}}, "src": "662:37:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 109, "nodeType": "ExpressionStatement", "src": "662:37:1"}, {"expression": {"argumentTypes": null, "id": 115, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 110, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "709:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 113, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 111, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "718:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 112, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "718:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "709:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 114, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "732:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "709:35:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 116, "nodeType": "ExpressionStatement", "src": "709:35:1"}]}, "documentation": null, "id": 118, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nodeType": "FunctionDefinition", "parameters": {"id": 99, "nodeType": "ParameterList", "parameters": [], "src": "642:2:1"}, "returnParameters": {"id": 100, "nodeType": "ParameterList", "parameters": [], "src": "652:0:1"}, "scope": 316, "src": "631:119:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 125, "nodeType": "Block", "src": "809:33:1", "statements": [{"expression": {"argumentTypes": null, "id": 123, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 95, "src": "823:12:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 122, "id": 124, "nodeType": "Return", "src": "816:19:1"}]}, "documentation": null, "id": 126, "implemented": true, "kind": "function", "modifiers": [], "name": "totalSupply", "nodeType": "FunctionDefinition", "parameters": {"id": 119, "nodeType": "ParameterList", "parameters": [], "src": "776:2:1"}, "returnParameters": {"id": 122, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 121, "name": "", "nodeType": "VariableDeclaration", "scope": 126, "src": "800:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 120, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "800:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "799:9:1"}, "scope": 316, "src": "756:86:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 137, "nodeType": "Block", "src": "914:44:1", "statements": [{"expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 133, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "931:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 135, "indexExpression": {"argumentTypes": null, "id": 134, "name": "tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 128, "src": "940:10:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "931:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 132, "id": 136, "nodeType": "Return", "src": "924:27:1"}]}, "documentation": null, "id": 138, "implemented": true, "kind": "function", "modifiers": [], "name": "balanceOf", "nodeType": "FunctionDefinition", "parameters": {"id": 129, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 128, "name": "tokenOwner", "nodeType": "VariableDeclaration", "scope": 138, "src": "867:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 127, "name": "address", "nodeType": "ElementaryTypeName", "src": "867:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "866:20:1"}, "returnParameters": {"id": 132, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 131, "name": "", "nodeType": "VariableDeclaration", "scope": 138, "src": "908:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 130, "name": "uint", "nodeType": "ElementaryTypeName", "src": "908:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "907:6:1"}, "scope": 316, "src": "848:110:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 190, "nodeType": "Block", "src": "1038:304:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 153, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 148, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1056:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 149, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1069:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 152, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 150, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1078:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 151, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1078:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1069:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1056:33:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "53656e6465722074686572652773206e6f20656e6f7567682066756e64732e", "id": 154, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1091:33:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}, "value": "Sender there's no enough funds."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}], "id": 147, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [379, 380], "referencedDeclaration": 380, "src": "1048:7:1", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 155, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1048:77:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 156, "nodeType": "ExpressionStatement", "src": "1048:77:1"}, {"expression": {"argumentTypes": null, "id": 168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 157, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1136:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 160, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 158, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1145:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 159, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1145:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1136:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 166, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1184:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 161, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1159:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 164, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 162, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1168:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 163, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1168:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1159:20:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 165, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 336, "src": "1159:24:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 167, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1159:35:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1136:58:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 169, "nodeType": "ExpressionStatement", "src": "1136:58:1"}, {"expression": {"argumentTypes": null, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 170, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1204:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 172, "indexExpression": {"argumentTypes": null, "id": 171, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1213:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1204:18:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 177, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1248:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 173, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1225:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 175, "indexExpression": {"argumentTypes": null, "id": 174, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1234:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1225:18:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 176, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "1225:22:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 178, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1225:33:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1204:54:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 180, "nodeType": "ExpressionStatement", "src": "1204:54:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 182, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1282:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 183, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1282:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 184, "name": "receiver", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1294:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 185, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 142, "src": "1304:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 181, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 83, "src": "1273:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1273:41:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 187, "nodeType": "EmitStatement", "src": "1268:46:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 188, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1331:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 146, "id": 189, "nodeType": "Return", "src": "1324:11:1"}]}, "documentation": null, "id": 191, "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "nodeType": "FunctionDefinition", "parameters": {"id": 143, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 140, "name": "receiver", "nodeType": "VariableDeclaration", "scope": 191, "src": "982:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 139, "name": "address", "nodeType": "ElementaryTypeName", "src": "982:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 142, "name": "numTokens", "nodeType": "VariableDeclaration", "scope": 191, "src": "1000:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 141, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1000:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "981:34:1"}, "returnParameters": {"id": 146, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 145, "name": "", "nodeType": "VariableDeclaration", "scope": 191, "src": "1032:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 144, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1032:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1031:6:1"}, "scope": 316, "src": "964:378:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 218, "nodeType": "Block", "src": "1421:135:1", "statements": [{"expression": {"argumentTypes": null, "id": 207, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 200, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1431:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 204, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 201, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1439:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 202, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1439:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1431:19:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 205, "indexExpression": {"argumentTypes": null, "id": 203, "name": "delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1451:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1431:29:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 206, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 195, "src": "1463:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1431:41:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 208, "nodeType": "ExpressionStatement", "src": "1431:41:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 210, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1496:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 211, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1496:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 212, "name": "delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1508:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 213, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 195, "src": "1518:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 209, "name": "Approval", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 75, "src": "1487:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 214, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1487:41:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 215, "nodeType": "EmitStatement", "src": "1482:46:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 216, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1545:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 199, "id": 217, "nodeType": "Return", "src": "1538:11:1"}]}, "documentation": null, "id": 219, "implemented": true, "kind": "function", "modifiers": [], "name": "approve", "nodeType": "FunctionDefinition", "parameters": {"id": 196, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 193, "name": "delegate", "nodeType": "VariableDeclaration", "scope": 219, "src": "1365:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 192, "name": "address", "nodeType": "ElementaryTypeName", "src": "1365:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 195, "name": "numTokens", "nodeType": "VariableDeclaration", "scope": 219, "src": "1383:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 194, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1383:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1364:34:1"}, "returnParameters": {"id": 199, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 198, "name": "", "nodeType": "VariableDeclaration", "scope": 219, "src": "1415:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 197, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1415:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1414:6:1"}, "scope": 316, "src": "1348:208:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 234, "nodeType": "Block", "src": "1641:48:1", "statements": [{"expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 228, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1658:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 230, "indexExpression": {"argumentTypes": null, "id": 229, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 221, "src": "1666:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1658:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 232, "indexExpression": {"argumentTypes": null, "id": 231, "name": "delegate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 223, "src": "1673:8:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1658:24:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 227, "id": 233, "nodeType": "Return", "src": "1651:31:1"}]}, "documentation": null, "id": 235, "implemented": true, "kind": "function", "modifiers": [], "name": "allowance", "nodeType": "FunctionDefinition", "parameters": {"id": 224, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 221, "name": "owner", "nodeType": "VariableDeclaration", "scope": 235, "src": "1581:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 220, "name": "address", "nodeType": "ElementaryTypeName", "src": "1581:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 223, "name": "delegate", "nodeType": "VariableDeclaration", "scope": 235, "src": "1596:16:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 222, "name": "address", "nodeType": "ElementaryTypeName", "src": "1596:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "1580:33:1"}, "returnParameters": {"id": 227, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 226, "name": "", "nodeType": "VariableDeclaration", "scope": 235, "src": "1635:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 225, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1635:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1634:6:1"}, "scope": 316, "src": "1562:127:1", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 314, "nodeType": "Block", "src": "1785:447:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 251, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 247, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "1803:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 248, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1816:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 250, "indexExpression": {"argumentTypes": null, "id": 249, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1825:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1816:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1803:28:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "4f776e65722074686572652773206e6f20656e6f7567682066756e64732e", "id": 252, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1833:32:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_20f92e31651865479c230e80930318b133341c20b1254aa6619a11e9e361b902", "typeString": "literal_string \"Owner there's no enough funds.\""}, "value": "Owner there's no enough funds."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_20f92e31651865479c230e80930318b133341c20b1254aa6619a11e9e361b902", "typeString": "literal_string \"Owner there's no enough funds.\""}], "id": 246, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [379, 380], "referencedDeclaration": 380, "src": "1795:7:1", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 253, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1795:71:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 254, "nodeType": "ExpressionStatement", "src": "1795:71:1"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 263, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 256, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "1884:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 257, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1897:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 259, "indexExpression": {"argumentTypes": null, "id": 258, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1905:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1897:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 262, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 260, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "1912:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 261, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1912:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1897:26:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1884:39:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "53656e6465722074686572652773206e6f20656e6f7567682066756e64732e", "id": 264, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1925:33:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}, "value": "Sender there's no enough funds."}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_5b03effa773c07f574a5e590a960bd96b926c7d1b2f31d5bf9ab9b65966e3c1b", "typeString": "literal_string \"Sender there's no enough funds.\""}], "id": 255, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [379, 380], "referencedDeclaration": 380, "src": "1876:7:1", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 265, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1876:83:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 266, "nodeType": "ExpressionStatement", "src": "1876:83:1"}, {"expression": {"argumentTypes": null, "id": 276, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 267, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1970:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 269, "indexExpression": {"argumentTypes": null, "id": 268, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1979:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1970:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 274, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2008:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 270, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "1988:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 272, "indexExpression": {"argumentTypes": null, "id": 271, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1997:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1988:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 273, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 336, "src": "1988:19:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 275, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1988:30:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1970:48:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 277, "nodeType": "ExpressionStatement", "src": "1970:48:1"}, {"expression": {"argumentTypes": null, "id": 293, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 278, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "2028:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 282, "indexExpression": {"argumentTypes": null, "id": 279, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2036:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2028:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 283, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 280, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "2043:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 281, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2043:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2028:26:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 291, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2088:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 284, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "2057:7:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 286, "indexExpression": {"argumentTypes": null, "id": 285, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2065:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2057:14:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 289, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 287, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 376, "src": "2072:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 288, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2072:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2057:26:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 290, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 336, "src": "2057:30:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 292, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2057:41:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2028:70:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 294, "nodeType": "ExpressionStatement", "src": "2028:70:1"}, {"expression": {"argumentTypes": null, "id": 304, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 295, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "2108:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 297, "indexExpression": {"argumentTypes": null, "id": 296, "name": "buyer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 239, "src": "2117:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2108:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 302, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2146:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 298, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 87, "src": "2126:8:1", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 300, "indexExpression": {"argumentTypes": null, "id": 299, "name": "buyer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 239, "src": "2135:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2126:15:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 301, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 360, "src": "2126:19:1", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 303, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2126:30:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2108:48:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 305, "nodeType": "ExpressionStatement", "src": "2108:48:1"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 307, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2180:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 308, "name": "buyer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 239, "src": "2187:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 309, "name": "numTokens", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 241, "src": "2194:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 306, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 83, "src": "2171:8:1", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 310, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2171:33:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 311, "nodeType": "EmitStatement", "src": "2166:38:1"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 312, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2221:4:1", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 245, "id": 313, "nodeType": "Return", "src": "2214:11:1"}]}, "documentation": null, "id": 315, "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "nodeType": "FunctionDefinition", "parameters": {"id": 242, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 237, "name": "owner", "nodeType": "VariableDeclaration", "scope": 315, "src": "1717:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 236, "name": "address", "nodeType": "ElementaryTypeName", "src": "1717:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 239, "name": "buyer", "nodeType": "VariableDeclaration", "scope": 315, "src": "1732:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 238, "name": "address", "nodeType": "ElementaryTypeName", "src": "1732:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 241, "name": "numTokens", "nodeType": "VariableDeclaration", "scope": 315, "src": "1747:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 240, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1747:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1716:46:1"}, "returnParameters": {"id": 245, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 244, "name": "", "nodeType": "VariableDeclaration", "scope": 315, "src": "1779:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 243, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1779:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "1778:6:1"}, "scope": 316, "src": "1695:537:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}], "scope": 362, "src": "140:2094:1"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "library", "documentation": null, "fullyImplemented": true, "id": 361, "linearizedBaseContracts": [361], "name": "SafeMath", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 335, "nodeType": "Block", "src": "2326:49:1", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 328, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 326, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 320, "src": "2341:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "id": 327, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 318, "src": "2346:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2341:6:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 325, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 365, "src": "2334:6:1", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 329, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2334:14:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 330, "nodeType": "ExpressionStatement", "src": "2334:14:1"}, {"expression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 333, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 331, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 318, "src": "2363:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"argumentTypes": null, "id": 332, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 320, "src": "2367:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2363:5:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 324, "id": 334, "nodeType": "Return", "src": "2356:12:1"}]}, "documentation": null, "id": 336, "implemented": true, "kind": "function", "modifiers": [], "name": "sub", "nodeType": "FunctionDefinition", "parameters": {"id": 321, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 318, "name": "a", "nodeType": "VariableDeclaration", "scope": 336, "src": "2272:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 317, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2272:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 320, "name": "b", "nodeType": "VariableDeclaration", "scope": 336, "src": "2283:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 319, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2283:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2271:22:1"}, "returnParameters": {"id": 324, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 323, "name": "", "nodeType": "VariableDeclaration", "scope": 336, "src": "2317:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 322, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2317:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2316:9:1"}, "scope": 361, "src": "2259:116:1", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, {"body": {"id": 359, "nodeType": "Block", "src": "2448:70:1", "statements": [{"assignments": [346], "declarations": [{"constant": false, "id": 346, "name": "c", "nodeType": "VariableDeclaration", "scope": 359, "src": "2456:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 345, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2456:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "id": 350, "initialValue": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 349, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 347, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 338, "src": "2468:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"argumentTypes": null, "id": 348, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 340, "src": "2472:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2468:5:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "2456:17:1"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 354, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 352, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 346, "src": "2488:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 353, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 338, "src": "2493:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2488:6:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 351, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 365, "src": "2481:6:1", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 355, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2481:14:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 356, "nodeType": "ExpressionStatement", "src": "2481:14:1"}, {"expression": {"argumentTypes": null, "id": 357, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 346, "src": "2510:1:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 344, "id": 358, "nodeType": "Return", "src": "2503:8:1"}]}, "documentation": null, "id": 360, "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nodeType": "FunctionDefinition", "parameters": {"id": 341, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 338, "name": "a", "nodeType": "VariableDeclaration", "scope": 360, "src": "2394:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 337, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2394:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 340, "name": "b", "nodeType": "VariableDeclaration", "scope": 360, "src": "2405:9:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 339, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2405:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2393:22:1"}, "returnParameters": {"id": 344, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 343, "name": "", "nodeType": "VariableDeclaration", "scope": 360, "src": "2439:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 342, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2439:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2438:9:1"}, "scope": 361, "src": "2381:137:1", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}], "scope": 362, "src": "2236:284:1"}], "src": "115:2405:1"}, "compiler": {"name": "solc", "version": "0.5.8+commit.23d335f2.Emscripten.clang"}, "networks": {"3": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0xd7c7fa7673386c66fb5cb18731d79d6806dc7736abd206e6696373e11f48077e"}, "5777": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0xa1f7853e0c13194f213a0b03ee79387d45454d558ee8c074d7bb5aa308c78f39"}}, "schemaVersion": "3.0.11", "updatedAt": "2019-08-06T09:06:22.286Z", "devdoc": {"methods": {}}, "userdoc": {"methods": {}}}