﻿Name,Name,zh(中文),en(英语),zh_hk(香港),ja_jp(日语)
appName,appName,Coinbag,Coinbag,Coinbag,Coinbag
settingLanguageDefault,settingLanguageDefault,跟随系统,Follow System,跟隨系統,フォローシステム
stringConfirm,stringConfirm,确认,Confirm,確認,確認
stringCancel,stringCancel,取消,Cancel,取消,キャンセル
stringDiscover,stringDiscover,发现,Discover,發現,発見
stringAvatar,stringAvatar,头像,Avatar,头像,アバター
stringUid,stringUid,stringNfcInputOnePsw,UID,UID,UID
stringPhone,stringPhone,电话,Phone,電話,電話
stringEmail,stringEmail,邮箱,Email,郵箱,メール
stringLoginpassword,stringLoginpassword,登录密码,Login Password,登錄密碼,ログインパスワード
stringAccount,stringAccount,账号,Account,账号,アカウント
stringNickname,stringNickname,昵称,Nickname,昵稱,ニックネーム
stringHelp,stringHelp,帮助中心,Help Center,幫助中心,ヘルプセンター
stringcommonproblem,stringcommonproblem,常见问题,Frequently Asked Questions (FAQs),常見問題,よくある質問
profileLogin,profileLogin,登录账号,Login Account,登錄帳號,ログインアカウント
profileTool,profileTool,钱包工具,Wallet Tools,錢包工具,ウォレットツール
profileHelp,profileHelp,帮助中心,Help Center ,幫助中心,ヘルプセンター
profileAddressBook,profileAddressBook,地址簿,Address Book,地址簿,アドレス帳
profileMall,profileMall,概览,Overview,概覽,概要
profileLanguage,profileLanguage,多语言,Multilingual,多語言,多言語
profileQR,profileQR,二维码容量,QR Code Capacity,二維碼容量,QRコード容量
profileAbout,profileAbout,关于,About,關於,このアプリについて
profileCurrency,profileCurrency,货币单位,Currency Unit,貨幣單位,通貨単位
qrStandard,qrStandard,标准,Standard,標準,標準
qrSmall,qrSmall,较小,Smaller,較小,小さい
qrLarge,qrLarge,较大,Larger,較大,大きい
qrSuperLarge,qrSuperLarge,超大,Extra Large,超大,超大
qrBest,qrBest,推荐,Recommended,推薦,おすすめ
clientNetError,clientNetError,网络异常，请检网络连接状态,"Network error, please check your network connection",網絡異常，請檢查網絡連接狀態,ネットワーク異常、ネットワーク接続状態を確認してください
timeOutError,timeOutError,请求超时!请稍后再试,Request timed out! Please try again later,請求超時！請稍後再試,リクエストタイムアウト！後でもう一度お試しください
serverNetError,serverNetError,服务器连接出现问题\n请稍后重试或联系客服处理,There is a problem with the server connection\nPlease try again later or contact customer support,伺服器連接出現問題\n請稍後重試或聯絡客服處理,サーバー接続に問題が発生しました\n後でもう一度お試しいただくか、カスタマーサポートにお問い合わせください
netConnectError,netConnectError,网络未连接，请检查后重试,"Network not connected, please check and try again",網絡未連接，請檢查後重試,ネットワークが接続されていません。確認後、再試行してください
cancelConnectError,cancelConnectError,请求取消,Request canceled,請求取消,リクエストがキャンセルされました
qrTip,qrTip,二维码容量越小扫码识别越快，但会导致二维码的页数增多,"The smaller the QR code capacity, the faster the scan recognition, but it will increase the number of pages in the QR code",二維碼容量越小掃描識別越快，但會導致二維碼的頁數增多,QRコードの容量が小さいほど、スキャンの認識が速くなりますが、QRコードのページ数が増加します
emptyData,emptyData,暂无数据,No data available,暫無數據,データがありません
dataError,dataError,加载失败,Loading failed,加載失敗,読み込みに失敗しました
reload,reload,重新加载,Reload,重新加載,再読み込み
exitApplication,exitApplication,再按一次退出应用,Press again to exit the app,再按一次退出應用,もう一度押すとアプリが終了します
releaseText,releaseText,释放立即刷新,Refresh immediately,釋放立即刷新,即座にリフレッシュ
refreshingText,refreshingText,正在刷新...,Refreshing...,正在刷新...,リフレッシュ中...
completeText,completeText,刷新完成,Refresh complete,刷新完成,リフレッシュ完了
idleText,idleText,下拉可以刷新,Pull down to refresh,下拉可以刷新,下に引っ張るとリフレッシュできます
loadingText,loadingText,正在加载中...,Loading...,正在加載中...,読み込み中...
pullUpToLoad,pullUpToLoad,上拉加载更多,Pull up to load more,上拉加載更多,上に引っ張るともっと読み込みます
canLoadingText,canLoadingText,松手加载更多,Release to load more,鬆手加載更多,手を放すともっと読み込みます
noDataText,noDataText,没有更多数据了,No more data,沒有更多數據了,これ以上のデータはありません
aboutKS,aboutKS,关于,About,關於,このアプリについて
userAgreement,userAgreement,用户协议,User Agreement,用戶協議,ユーザー契約
wallet,wallet,钱包,Wallet,錢包,ウォレット
send,send,发送,Send,發送,送信
receive,receive,接收,Receive,接收,受信
scan,scan,扫一扫,Scan,掃描,スキャン
activity,activity,交易记录,Activity,交易記錄,取引履歴
currencySearch,currencySearch,币种搜索,Currency Search,幣種搜索,通貨検索
allWallet,allWallet,所有钱包,All Wallets,所有錢包,すべてのウォレット
totalAsset,totalAsset,资产总值,Total Assets,資產總值,総資産
loginString,loginString,登录,Login,登錄,ログイン
loginPhone,loginPhone,手机号,Phone Number,手機號碼,携帯番号
loginPhoneHintText,loginPhoneHintText,输入手机号,Enter Phone Number,輸入手機號碼,携帯番号を入力してください
loginEmail,loginEmail,邮箱,Email,郵箱,メール
loginEmailHintText,loginEmailHintText,输入邮箱,Enter Email,輸入郵箱,メールを入力してください
loginPassword,loginPassword,密码,Password,密碼,パスワード
loginPasswordHintText,loginPasswordHintText,输入密码,Enter Password,輸入密碼,パスワードを入力してください
loginForgetPassword,loginForgetPassword,忘记密码？,Forgot Password?,忘記密碼？,パスワードを忘れた？
loginRegisterNew,loginRegisterNew,注册新账号,Register New Account,註冊新帳號,新しいアカウントを登録
loginOkPhone,loginOkPhone,请输入正确的手机号,Please enter a valid phone number,請輸入正確的手機號碼,正しい携帯番号を入力してください
loginOkEmail,loginOkEmail,请输入正确的邮箱,Please enter a valid email,請輸入正確的郵箱,正しいメールアドレスを入力してください
loginPswTip,loginPswTip,请设置6-20个数字和字母组成的密码,Please set a password with 6-20 characters consisting of numbers and letters,請設置6-20個數字和字母組成的密碼,6〜20文字の数字とアルファベットを含むパスワードを設定してください
loginSuccess,loginSuccess,登录成功,Login Successful,登錄成功,ログイン成功
emptyWallethint,emptyWallethint,保护资产安全，收发快捷方便,"Protect asset security, fast and convenient transactions",保護資產安全，收發快捷方便,資産の安全を保護し、送受信が迅速で便利
stringGuideTitle,stringGuideTitle,不止超越 更重安全,"More than just surpassing, more emphasis on safety",不止超越，更重安全,超越するだけでなく、より重要なのは安全
stringGuideSubTitle,stringGuideSubTitle,冷热分离 私钥永不触网,"Cold and hot separation, private keys never touch the network",冷熱分離，私鑰永不觸網,コールドとホットの分離、プライベートキーはネットワークに触れない
stringBindNow,stringBindNow,现在连接,Connect Now,現在連接,今すぐ接続
stringBindBuyU1,stringBindBuyU1,选购 U1,Choose U1,選購 U1,U1を選択
stringGuideimmediatelyExperience,stringGuideimmediatelyExperience,立即体验,Experience Now,立即體驗,今すぐ体験
stringAllWallet,stringAllWallet,所有钱包,All Wallets,所有錢包,すべてのウォレット
stringDefaultWallet,stringDefaultWallet,使用中,In Use,使用中,使用中
stringBalance,stringBalance,余额,Balance,餘額,残高
stringRegister,stringRegister,注册,Register,註冊,登録
stringCode,stringCode,验证码,Verification Code,驗證碼,認証コード
stringInputCode,stringInputCode,输入验证码,Enter Verification Code,輸入驗證碼,認証コードを入力してください
stringGetCode,stringGetCode,获取验证码,Get Verification Code,獲取驗證碼,認証コードを取得
stringInvitationCode,stringInvitationCode,邀请码（选填）,Invitation Code (optional),邀請碼（選填）,招待コード（任意）
stringInputInvitationCode,stringInputInvitationCode,输入邀请码,Enter Invitation Code,輸入邀請碼,招待コードを入力してください
stringCollection,stringCollection,收款,Receive,收款,受け取り
stringReceiveAddress,stringReceiveAddress,收款地址,Receiving Address,收款地址,受取先住所
stringCopyAddress,stringCopyAddress,复制地址,Copy Address,複製地址,アドレスをコピー
stringSaveImage,stringSaveImage,保存图片,Save Image,保存圖片,画像を保存
stringAgreement1,stringAgreement1,我已阅读并同意,I have read and agree to the,我已閱讀並同意,私は読んで同意します
stringAgreement2,stringAgreement2,《用户协议》,User Agreement,《用戶協議》,ユーザー契約
stringHasAccount,stringHasAccount,已有账号，,"Already have an account,",已有帳號，,すでにアカウントをお持ちの方、
stringGotoLogin,stringGotoLogin,立即登录,Log In Now,立即登錄,今すぐログイン
stringCopySuccess,stringCopySuccess,复制成功,Copy Successful,複製成功,コピー成功
stringAll,stringAll,全部,All,全部,全て
stringTransferSend,stringTransferSend,转帐,Transfer,轉帳,転送
stringSendAddress,stringSendAddress,发送地址,Send Address,發送地址,送信先住所
stringReciveAddress,stringReciveAddress,接收地址,Receive Address,接收地址,受信先住所
stringComplte,stringComplte,完成,Complete,完成,完了
stringTxFail,stringTxFail,已失败,Failed,已失敗,失敗しました
stringReadAgreement,stringReadAgreement,请阅读并同意《用户服务协议》,Please read and agree to the User Service Agreement,請閱讀並同意《用戶服務協議》,ユーザーサービス契約を読み、同意してください
stringOKCode,stringOKCode,请输入正确的验证码,Please enter the correct verification code,請輸入正確的驗證碼,正しい認証コードを入力してください
stringPswTip1,stringPswTip1,6-20个字符,6-20 characters,6-20個字符,6〜20文字
stringPswTip2,stringPswTip2,密码不可以为空,Password cannot be empty,密碼不能為空,パスワードを空にすることはできません
stringPswTip3,stringPswTip3,必须同时包含字母及数字，不可以包含特殊字符,"Must include both letters and numbers, and cannot contain special characters",必須同時包含字母及數字，不能包含特殊字符,英字と数字を含む必要があり、特殊文字を含めることはできません
stringPswTip4,stringPswTip4,与首次输入密码相同,Same as the first password entry,與首次輸入密碼相同,初回のパスワード入力と同じ
stringPswTip5,stringPswTip5,确认密码不可以为空,Confirm password cannot be empty,確認密碼不能為空,確認パスワードを空にすることはできません
stringConfirmPswHit,stringConfirmPswHit,再次输入密码,Re-enter Password,再次輸入密碼,パスワードを再入力してください
stringSetPassword,stringSetPassword,设置密码,Set Password,設置密碼,パスワードを設定
stringRegisterSuccess,stringRegisterSuccess,注册成功,Registration Successful,註冊成功,登録成功
forgetTitle,forgetTitle,忘记密码,Forgot Password,忘記密碼,パスワードを忘れた
resetPasswordSuccess,resetPasswordSuccess,重置成功,Reset Successful,重置成功,リセット成功
sendAddress,sendAddress,发送地址,Send Address,發送地址,送信先住所
sendCoin,sendCoin,发送币种,Sending Currency,發送幣種,送信通貨
receiveAddress,receiveAddress,接收地址,Receive Address,接收地址,受信先住所
sendNumber,sendNumber,数量,Quantity,數量,数量
sendAvailable,sendAvailable,可用,Available,可用,利用可能
sendButton,sendButton,转账,Transfer,轉帳,転送
sendMaxText,sendMaxText,最大,Maximum,最大,最大
stringChange,stringChange,修改,Modify,修改,修正
stringChangePassword,stringChangePassword,修改密码,Change Password,修改密碼,パスワード変更
oldPassword,oldPassword,旧密码,Old Password,舊密碼,現在のパスワード
oldHintPassword,oldHintPassword,输入旧密码,Enter Old Password,輸入舊密碼,現在のパスワードを入力してください
newPassword,newPassword,新密码,New Password,新密碼,新しいパスワード
newHintPassword,newHintPassword,输入新密码,Enter New Password,輸入新密碼,新しいパスワードを入力してください
okNewPassword,okNewPassword,确认新密码,Confirm New Password,確認新密碼,新しいパスワードを確認
okNewHintPassword,okNewHintPassword,再次输入新密码,Re-enter New Password,再次輸入新密碼,新しいパスワードを再入力してください
passwordChangeSuccess,passwordChangeSuccess,修改成功,Change Successful,修改成功,変更成功
inputOkToAddress,inputOkToAddress,请输入正确的接收地址,Please enter the correct receive address,請輸入正確的接收地址,正しい受信先住所を入力してください
balanceNot,balanceNot,余额不足,Insufficient Balance,餘額不足,残高不足
broadcastSending,broadcastSending,发送中，请稍等…,"Sending, please wait…",發送中，請稍候…,送信中、少々お待ちください…
broadcastSuccess,broadcastSuccess,交易广播成功,Send Successful,交易廣播成功,送信成功
broadcastError,broadcastError,交易广播失败,Send Failed,交易廣播失敗,送信失敗
broadcastReset,broadcastReset,重试,Retry,重試,再試行
stringBack,stringBack,返回,Return,返回,戻る
stringSendOneself,stringSendOneself,转给自己,Transfer to Yourself,轉給自己,自分に転送
stringAddress,stringAddress,地址,Address,地址,アドレス
storageAccessNotEnabled,storageAccessNotEnabled,未开启存储权限,Storage Permission Not Granted,未開啟存儲權限,ストレージ権限が未設定
cameraAccessNotEnabled,cameraAccessNotEnabled,相机权限未开启,Camera Permission Not Granted,相機權限未開啟,カメラ権限が未設定
contactPerson,contactPerson,联系人,Contacts,聯絡人,連絡先
insertaddress,insertaddress,输入地址,Enter Address,輸入地址,アドレスを入力してください
addressTag,addressTag,联系人名称,Contact Name,聯絡人名稱,連絡先名
addAddress,addAddress,添加联系人,Add Contacts,添加聯絡人,連絡先を追加
usernameEditor,usernameEditor,修改昵称,Change Nickname,修改昵稱,ニックネームを変更
accountback,accountback,退出账号,Log Out,退出帳號,アカウントからログアウト
bindEmail,bindEmail,绑定邮箱,Bind Email,綁定郵箱,メールをバインド
stringonlineservice,stringonlineservice,联系客服,Contact Customer Service,聯繫客服,カスタマーサービスに連絡
stringuserfeedback,stringuserfeedback,反馈意见,Feedback,反饋意見,フィードバック
stringhelpqq,stringhelpqq,官方QQ群,Official QQ Group,官方QQ群,公式QQグループ
stringhelpwechat,stringhelpwechat,官方客服微信,Official Customer Service WeChat,官方客服微信,公式カスタマーサービスWeChat
stringhelptel,stringhelptel,客服电话,Customer Service Phone Number,客服電話,カスタマーサービス電話番号
feedbackname,feedbackname,您的称呼,Your Name,您的稱呼,お名前
feedbackcontext,feedbackcontext,"您要反馈的内容,不超过300字",Feedback Content (up to 300 characters),您要反饋的內容，不超過300字,フィードバック内容（300文字以内）
feedbackmode,feedbackmode,您的联系方式(QQ、微信号、电话号码等),"Your Contact Information (QQ, WeChat ID, Phone Number, etc.)",您的聯繫方式（QQ、微信號、電話號碼等）,ご連絡先（QQ、WeChat ID、電話番号など）
submitfeedback,submitfeedback,提交反馈,Submit Feedback,提交反饋,フィードバックを送信
feenotegasprice,feenotegasprice,当前最佳矿工费,Current Best Miner Fee,當前最佳礦工費,現在の最良マイナー手数料
makeqr,makeqr,生成二维码,Generate QR Code,生成二維碼,QRコードを生成
textcontent,textcontent,输入文本内容,Enter Text Content,輸入文本內容,テキスト内容を入力
contentText,contentText,内容,Content,內容,内容
timesync,timesync,同步时间,Sync Time,同步時間,同期時間
afterRefresh,afterRefresh,@seconds 秒后刷新,Refresh in @seconds seconds,@秒後刷新,@秒後に更新
bindHardwareWallet,bindHardwareWallet,连接硬件钱包,Connect Hardware Wallet,連接硬件錢包,ハードウェアウォレットに接続
verifysn,verifysn,防伪验证,Anti-counterfeit Verification,防偽驗證,偽造防止認証
p4verifysucess,p4verifysucess,验证成功,Verification Successful,驗證成功,認証成功
p4verifyfail,p4verifyfail,验证失败,Verification Failed,驗證失敗,認証失敗
p4verifyRemind2,p4verifyRemind2,设备信息验证通过，请使用设备扫码，以进行签名。,"Device information verification passed, please use the device to scan for signing.",設備信息驗證通過，請使用設備掃描，以進行簽名。,デバイス情報の認証が成功しました。署名のためにデバイスでスキャンしてください。
p4verifyRemind,p4verifyRemind,扫码验证签名,Scan to Verify Signature,掃描驗證簽名,スキャンして署名を認証
verifysnSucceedHint,verifysnSucceedHint,此钱包设备为官方出售的正品，感谢您的支持。,"This wallet device is an official genuine product, thank you for your support.",此錢包設備為官方出售的正品，感謝您的支持。,このウォレットデバイスは公式に販売された正規品です。ご支援ありがとうございます。
verifySnTime,verifySnTime,首次验证时间,First Verification Time,首次驗證時間,初回認証時間
errwalletFake,errwalletFake,"此钱包设备非官方正品,请您谨慎使用。","This wallet device is not an official genuine product, please use it with caution.",此錢包設備非官方正品，請您謹慎使用。,このウォレットデバイスは公式の正規品ではありません。ご注意ください。
stringNotices,stringNotices, 提示：,Notice:,提示:,ご案内:
textTimesyncInfo,textTimesyncInfo," Ultra  \n1.打开硬件钱包，进入首页。\n2.点击\\""扫一扫\\""按钮，扫描以上二维码。\n\n Pro 3+\n1.打开硬件钱包，进入首页。\n2.点击\\""扫一扫\\""按钮，扫描以上二维码。\n\n Pro 3\n1.打开硬件钱包，进入首页。\n2.点击\\""菜单\\"" ->\\""设置\\""->\\""时间\\""。\n3.点击""扫码同步""按钮，扫描以上二维码。。","Ultra 1. Open the hardware wallet, go to the home page. 2. Click ""Scan"" button, scan the QR code above. Pro 3+ 1. Open the hardware wallet, go to the home page. 2. Click ""Scan"" button, scan the QR code above. Pro 3 1. Open the hardware wallet, go to the home page. 2. Click ""Menu"" -> ""Settings"" -> ""Time"". 3. Click ""Scan to Sync"" button, scan the QR code above. ",Ultra 1. 打開硬件錢包，進入首頁。 2. 點擊「掃描」按鈕，掃描以上二維碼。 Pro 3+ 1. 打開硬件錢包，進入首頁。 2. 點擊「掃描」按鈕，掃描以上二維碼。 Pro 3 1. 打開硬件錢包，進入首頁。 2. 點擊「菜單」 ->「設置」 ->「時間」。 3. 點擊「掃碼同步」按鈕，掃描以上二維碼。 ,Ultra 1. ハードウェアウォレットを開き、ホームページにアクセスします。 2. 「スキャン」ボタンをクリックし、上のQRコードをスキャンします。 Pro 3+ 1. ハードウェアウォレットを開き、ホームページにアクセスします。 2. 「スキャン」ボタンをクリックし、上のQRコードをスキャンします。 Pro 3 1. ハードウェアウォレットを開き、ホームページにアクセスします。 2. 「メニュー」 -> 「設定」 -> 「時間」をクリックします。 3. 「スキャンして同期」ボタンをクリックし、上のQRコードをスキャンします。 
stringOfficialEmail,stringOfficialEmail,官方邮箱,Official Email,官方郵箱,公式メールアドレス
stringTextDial,stringTextDial,拨打,Dial,撥打,電話をかける
textFeedbackDialogMessage,textFeedbackDialogMessage,提交成功，谢谢您的反馈！,"Submission successful, thank you for your feedback!",提交成功，謝謝您的反饋！,提出が成功しました。ご意見ありがとうございます！
textRemind,textRemind,请完善信息,Please complete the information,請完善信息,情報を完備してください
textUnbounded,textUnbounded,未绑定,Not Bound,未綁定,未設定
textBindPhone,textBindPhone,绑定手机,Bind Phone,綁定手機,携帯電話を設定する
texSubmit,texSubmit,提交,Submit,提交,提出
textbindSuccess,textbindSuccess,绑定成功,Binding Successful,綁定成功,設定成功
manageCoin,manageCoin,币种管理,Currency Management,幣種管理,通貨管理
supportToken,supportToken,已添加币种,Added Currencies,已添加幣種,追加済みの通貨
nosupportToken,nosupportToken,待添加币种,Currencies to Add,待添加幣種,追加予定の通貨
tokenSearch,tokenSearch,代币简称、名称、合约地址,"Token Abbreviation, Name, Contract Address",代幣簡稱、名稱、合約地址,トークンの略称、名称、契約アドレス
notifiAllRead,notifiAllRead,全部已读,All Read,全部已讀,全て既読
notifiTxTitle,notifiTxTitle,转账通知,Transfer Notification,轉帳通知,転送通知
notifiSysTitle,notifiSysTitle,系统通知,System Notification,系統通知,システム通知
textChooseChain,textChooseChain,请选择主链,Please Select Main Chain,請選擇主鏈,メインチェーンを選択してください
textCorrectAddress,textCorrectAddress,请输入正确的地址,Please Enter the Correct Address,請輸入正確的地址,正しいアドレスを入力してください
textChain,textChain,主链,Main Chain,主鏈,メインチェーン
submitToken,submitToken,提交Token,Submit Token,提交Token,トークンを提出する
tokenSymbol,tokenSymbol,币种名称,Symbol,幣種名稱,シンボル
tokenContracts,tokenContracts,合约地址,ID/Contract Address,合約地址,ID/契約アドレス
enterContractsInfo,enterContractsInfo,输入合约地址,Enter Contract Address,輸入合約地址,契約アドレスを入力してください
addContractRemind1,addContractRemind1,1.您只需要向我们提供token的合约地址，审核通过后即可正常使用，可联系客服获知审核进度；\n2.如果需要显示您的token logo，请将以下指定信息发送至********************，或者联系客服。 \n,"1. You only need to provide the token\'s contract address, and it will be usable after the review is approved. 2. If you need to display your token logo, please send the following specified <NAME_EMAIL>. ",1. 您只需要向我們提供token的合約地址，審核通過後即可正常使用； 2. 如果需要顯示您的token logo，請將以下指定信息發送至********************。 ,1. トークンの契約アドレスを提供するだけで、審査が通過すれば正常に使用できます。 2. トークンのロゴを表示する必要がある場合は、指定された情報を********************まで送信してください。 
stringConfirmPsw,stringConfirmPsw,确认密码,Confirm Password,確認密碼,確認パスワード
addContractRemind2,addContractRemind2,① 合约地址； \n② logo.png (500像素x500像素)； \n③ 公司、个人或官网等相关信息； \n④ 其他情况说明。 \n,"① Contract Address; ② logo.png (500 pixels x 500 pixels); ③ Company, individual, or official website related information; ④ Other situation description. ",① 合約地址； ② logo.png (500像素x500像素)； ③ 公司、個人或官網等相關信息； ④ 其他情況說明。 ,① 契約アドレス； ② logo.png (500ピクセルx500ピクセル)； ③ 会社、個人または公式ウェブサイトなどの関連情報； ④ その他の状況説明。 
importTokenSymbol,importTokenSymbol,输入币种名称,Enter Token symbol,輸入幣種名稱,トークンシンボルを入力
addressBookEditContacts,addressBookEditContacts,编辑联系人,Edit contacts,編輯聯絡人,連絡先を編集
addressBookDelContacts,addressBookDelContacts,删除联系人,Delete contacts,刪除聯絡人,連絡先を削除
addressBookDelContactsContent,addressBookDelContactsContent,确认删除@contacts联系人？,Confirm deletion of @contacts contacts?,確認刪除@contacts聯絡人？,@contactsの連絡先を削除してもよろしいですか？
addressBookDelContactsConfirm,addressBookDelContactsConfirm,确认删除,Confirm deletion,確認刪除,削除を確認
addressAlreadyExists,addressAlreadyExists,该地址已存在!,This address already exists!,該地址已存在！,このアドレスは既に存在します！
unsearchToken,unsearchToken,未找到指定的Token?,Specified Token not found?,未找到指定的Token？,指定されたトークンが見つかりませんでしたか？
tokenLabel,tokenLabel,类型,Type,類型,タイプ
authFingerprintTip,authFingerprintTip,请按压屏内指纹感应区验证指纹,Please press the on-screen fingerprint sensor to verify your fingerprint,請按壓屏幕內的指紋感應區驗證指紋,画面内の指紋センサーを押して指紋を認証してください
authContnetTextFace,authContnetTextFace,如需使用面容ID解锁，请到系统设置开启权限,"To use Face ID unlock, please enable the permission in system settings",如需使用面容ID解鎖，請到系統設置開啟權限,顔認証でロック解除を行うには、システム設定で権限を有効にしてください
authContnetTextFingerprint,authContnetTextFingerprint,如需使用指纹解锁，请到系统设置开启权限,"To use fingerprint unlock, please enable the permission in system settings",如需使用指紋解鎖，請到系統設置開啟權限,指紋認証でロック解除を行うには、システム設定で権限を有効にしてください
authContnetTextSaveFingerprint,authContnetTextSaveFingerprint,您还没有录入指纹，请在设置界面录入指纹,You haven\'t registered a fingerprint yet. Please register your fingerprint in the settings,您還未錄入指紋，請在設置介面錄入指紋,指紋が登録されていません。設定画面で指紋を登録してください
gotoSettingTitle,gotoSettingTitle,去设置,Go to settings,去設置,設定へ
authFaceUnlock,authFaceUnlock,面容解锁,Face unlock,面容解鎖,顔認証ロック解除
authFingerprintUnlock,authFingerprintUnlock,指纹解锁,Fingerprint unlock,指紋解鎖,指紋認証ロック解除
authFingerprintTap,authFingerprintTap,按压指纹识别器,Press the fingerprint sensor,按壓指紋識別器,指紋センサーを押してください
authFaceTitle,authFaceTitle,面容ID,Face ID,面容ID,顔認証ID
authFingerprintTitle,authFingerprintTitle,指纹验证,Fingerprint verification,指紋驗證,指紋認証
stringTips,stringTips,提示,Tips,提示,ヒント
nfcOpen,nfcOpen,已开启,Opened,已開啟,オーペンしました
nfcClose,nfcClose,已关闭,Closed,已關閉,クロースしました
addHardwareTitle,addHardwareTitle,添加硬件钱包,Add hardware wallet,添加硬件錢包,ハードウェアウォレットを追加
bindWalletTitle,bindWalletTitle,连接钱包,Connect wallet,連接錢包,ウォレットを接続
scanBindTitle,scanBindTitle,扫码连接,Connect via QR code,掃碼連接,QRコードで接続
pageCount,pageCount,暂缺第@page页,@page page temporarily missing,暫缺第@page頁,@pageページが一時的に欠けています
noinstallQQ,noinstallQQ,未安装QQ或安装的版本不支持,QQ is not installed or the installed version is not supported,未安裝QQ或安裝的版本不支持,QQがインストールされていないか、インストールされたバージョンが対応していません
noinstallWechat,noinstallWechat,未安装微信,WeChat is not installed,未安裝微信,WeChatがインストールされていません
scanResult,scanResult,扫描结果,Scan Result,掃描結果,スキャン結果
copy,copy,复制 ,Copy,複製,コピー
zxingError,zxingError,二维码扫描错误，请重新扫描,"QR code scanning error, please scan again",二維碼掃描錯誤，請重新掃描,QRコードのスキャンエラー、再スキャンしてください
pleaseScanPage,pleaseScanPage,请扫第@scanPage页,Please scan @scanPage page,請掃第@scanPage頁,@scanPageページをスキャンしてください
scanNotMatchWallet,scanNotMatchWallet,扫码信息与当前选择钱包不符,The scan information does not match the currently selected wallet,掃碼信息與當前選擇的錢包不符,スキャン情報が現在選択されているウォレットと一致しません
scanError,scanError,扫码二维码错误，请重试,"QR code scanning error, please try again",掃碼二維碼錯誤，請重試,QRコードのスキャンエラー、再試行してください
bindSucessInfo,bindSucessInfo,您已成功连接硬件钱包@wallet，快去使用吧,"You have successfully connected the hardware wallet @wallet, go ahead and use it",您已成功連接硬件錢包@wallet，快去使用吧,ハードウェアウォレット@walletに正常に接続されました、さっそく使用しましょう
bindSucess,bindSucess,绑定成功!,Binding successful!,綁定成功！,バインド成功！
openWallet,openWallet,打开钱包,Open wallet,打開錢包,ウォレットを開く
chainManager,chainManager,管理,Manage,管理,管理
chainSegWitP2sh,chainSegWitP2sh,兼容,Nested,兼容,互換性
chainSegWit,chainSegWit,隔离,SegWit,隔離,隔離
chainLegacy,chainLegacy,普通,Legacy,普通,標準
chainSegWitP2shItem,chainSegWitP2shItem,隔离见证(兼容),SegWit(p2sh),隔離見證（兼容）,隔離証明（互換性あり）
chainLegacyItem,chainLegacyItem,普通地址,Legacy,普通地址,標準アドレス
chainSegWitItem,chainSegWitItem,隔离见证(原生),SegWit,隔離見證（原生）,隔離証明（ネイティブ）
chainSelectWallet,chainSelectWallet,选择钱包,Select wallet,選擇錢包,ウォレットを選択
chainWalletManager,chainWalletManager,钱包管理,Wallet management,錢包管理,ウォレット管理
chainAddAddress,chainAddAddress,添加地址,Add address,添加地址,アドレスを追加
chainAddressDetail,chainAddressDetail,地址详情,Address details,地址詳情,アドレス詳細
chainEditRemark,chainEditRemark,编辑备注名,Edit memo name,編輯備註名,メモ名を編集
chainAddressDetailTip1,chainAddressDetailTip1,仅支持接收,Only support receiving,僅支持接收,受信のみサポート
chainAddressDetailTip2,chainAddressDetailTip2,网络资产,Network assets,網絡資產,ネットワーク資産
chainAddType,chainAddType,类型,Type,類型,タイプ
chainAddRemark,chainAddRemark,备注名,Memo name,備註名,メモ名
chainComplete,chainComplete,完成,Complete,完成,完了
chainAllChains,chainAllChains,全部网络,All Chains,全部網絡,すべてのネットワーク
chainTotalAssets,chainTotalAssets,资产总值,Total Asset Value,資產總值,資産総額
allWallets,allWallets,所有钱包,All wallets,所有錢包,すべてのウォレット
walletInfo,walletInfo,钱包资料,Wallet information,錢包資料,ウォレット情報
walletName,walletName,钱包名称,Wallet name,錢包名稱,ウォレット名
bindingTime,bindingTime,绑定时间,Binding Time,綁定時間,バインド時間
deviceModel,deviceModel,设备型号,Device Model,設備型號,デバイスモデル
deviceId,deviceId,设备ID,Device ID,設備ID,デバイスID
seVersion,seVersion,安全芯片版本,Security Chip Version,安全芯片版本,セキュリティチップバージョン
walletAppVersion,walletAppVersion,应用版本,Application Version,應用版本,アプリバージョン
unbind,unbind,断开连接,Disconnect,斷開連接,接続解除
using,using,使用中,In use,使用中,使用中
bindFailed,bindFailed,连接失败!,Connection failed!,連接失敗！,接続に失敗しました！
rebind,rebind,再次连接,Reconnect,再次連接,再接続
accountDelete,accountDelete,注销账号,Log out account,註銷賬號,アカウントをログアウト
accountDeleteTip,accountDeleteTip,是否注销当前账号？,Do you want to log out of the current account?,是否註銷當前賬號？,現在のアカウントをログアウトしますか？
safeSetting,safeSetting,安全设置,Security Settings,安全設置,セキュリティ設定
deleteError,deleteError,注销失败,Logout failed,註銷失敗,ログアウトに失敗しました
deleteSuccess,deleteSuccess,注销成功,Logout successful,註銷成功,ログアウトに成功しました
loading,loading,加载中...,Loading...,加載中...,読み込み中...
binding,binding,正在连接...,Connecting...,正在連接...,接続中...
sameMnemonicWallet,sameMnemonicWallet,检测到已导入相同钱包，是否覆盖？,Detected the import of the same wallet. Do you want to overwrite?,檢測到已導入相同錢包，是否覆蓋？,同じニーモニックフレーズのウォレットがインポートされていることを検出しました。上書きしますか？
sureDeleteWallet,sureDeleteWallet,确定要断开连接吗,Are you sure you want to disconnect?,確定要斷開連接嗎？,接続を解除してもよろしいですか？
textAddressCount,textAddressCount,(@indexAdd个地址),(@indexAdd addresses),(@indexAdd 個地址),(@indexAdd個のアドレス)
textAccountCount,textAccountCount,(@indexAcc个账户) ,(@indexAcc accounts),(@indexAcc 個帳戶),(@indexAcc個のアカウント)
baseSettings,baseSettings,偏好设置,Preferences,偏好設定,設定
myTitle,myTitle,我的,Profile,我的,マイページ
feeSettingTitle,feeSettingTitle,矿工费设置,Miner fee settings,礦工費設定,マイニング手数料設定
feeTitle,feeTitle,矿工费,Miner fee,礦工費,マイニング手数料
gearSelect,gearSelect,档位选择,Tier selection,檔位選擇,レベル選択
customizeTitle,customizeTitle,自定义,Customize,自訂,カスタム
fastTitle,fastTitle,快速,Fast,快速,高速
normalTitle,normalTitle,标准,Standard,標準,標準
slowTitle,slowTitle,缓慢,Slow,緩慢,低速
estimateTxTime,estimateTxTime,预计交易时间 @minute,Estimated transaction time @minute,預計交易時間 @minute,予想取引時間 @minute
minerFeeTitle,minerFeeTitle,矿工费率,Miner fee rate,礦工費率,マイニング手数料率
minuteTitle,minuteTitle,分钟,Minutes,分鐘,分
hourTitle,hourTitle,小时,Hours,小時,時間
secondTitle,secondTitle,秒,Seconds,秒,秒
p4seedRemind,p4seedRemind,助记词增强模式下，私钥由助记词和密码共同生成，密码不可更改。需要同时拥有助记词和密码才能恢复钱包，两者缺一不可。\n\n 普通模式下，私钥仅由助记词生成，密码可更改。只要拥有助记词就可以恢复钱包。\n\n请务必保管好助记词并牢记密码。,"In mnemonic enhanced mode, the private key is generated by both the mnemonic and the password, and the password cannot be changed. You need both the mnemonic and the password to restore the wallet; one without the other is insufficient.\n\n In regular mode, the private key is generated only by the mnemonic, and the password can be changed. You can restore the wallet as long as you have the mnemonic.\n\n Be sure to keep the mnemonic safe and remember the password.",助記詞增強模式下，私鑰由助記詞和密碼共同生成，密碼不可更改。需要同時擁有助記詞和密碼才能恢復錢包，兩者缺一不可。\n\n 普通模式下，私鑰僅由助記詞生成，密碼可更改。只要擁有助記詞就可以恢復錢包。\n\n 請務必妥善保管助記詞並記住密碼。,ニーモニック強化モードでは、秘密鍵はニーモニックとパスワードの両方で生成され、パスワードは変更できません。ウォレットを復元するには、ニーモニックとパスワードの両方が必要で、どちらか一方が欠けると復元できません。\n\n 通常モードでは、秘密鍵はニーモニックのみで生成され、パスワードは変更可能です。ニーモニックさえあればウォレットを復元できます。\n\n ニーモニックを必ず安全に保管し、パスワードを忘れないようにしてください。
p4seedIspharase,p4seedIspharase,助记词增强模式,Mnemonic enhanced mode,助記詞增強模式,ニーモニック強化モード
p4seedIspharaseYes,p4seedIspharaseYes,是,Yes,是,はい
p4seedIspharaseNo,p4seedIspharaseNo,否,No,否,いいえ
stringBind,stringBind,绑定,Bind,綁定,バインド
textPro1Binding,textPro1Binding,1.打开硬件钱包，选定某币种并打开“接收”页面；\n\n 2.进入“我的地址”列表，选中需要绑定的地址；\n\n 3.跳转到“余额”页面，点击“更新余额”，显示二维码；,"1. Open the hardware wallet, select a currency, and go to the ""Receive"" page.\n\n 2. Go to the ""My Addresses"" list and select the address you want to bind.\n\n 3. Go to the ""Balance"" page, click ""Update Balance"" to display the QR code.",1.打開硬件錢包，選擇某幣種並打開“接收”頁面。\n\n 2. 進入“我的地址”列表，選中需要綁定的地址。\n\n 3. 跳轉到“餘額”頁面，點擊“更新餘額”，顯示二維碼。,1. ハードウェアウォレットを開き、対象の通貨を選んで「受信」ページを開きます。\n\n 2. 「マイアドレス」リストに入り、バインドするアドレスを選択します。\n\n 3. 「残高」ページに移動し、「残高を更新」をクリックしてQRコードを表示します。
scanConnect,scanConnect,扫码连接,Scan to connect,掃碼連接,QRコードで接続
customFeeConfirm,customFeeConfirm,矿工费设置未确认，确认要离开此页？,Miner fee settings not confirmed. Are you sure you want to leave this page?,礦工費設定未確認，確認要離開此頁？,マイニング手数料の設定が確認されていません。このページを離れますか？
customFeeSuccess,customFeeSuccess,自定义矿工费已生效,Custom miner fee has been applied,自訂礦工費已生效,カスタムマイニング手数料が有効になりました
feeSlowTips,feeSlowTips,矿工费率过低，将会影响交易确认时间,The miner fee rate is too low and may affect transaction confirmation time,礦工費率過低，將會影響交易確認時間,マイニング手数料率が低すぎると、取引確認時間に影響を与える可能性があります
feeFastTips,feeFastTips,矿工费率过高，将会造成矿工费浪费,The miner fee rate is too high and may result in wasted miner fees,礦工費率過高，將會造成礦工費浪費,マイニング手数料率が高すぎると、マイニング手数料の無駄が生じる可能性があります
feeOkTips,feeOkTips,请输入有效的矿工费率,Please enter a valid miner fee rate,請輸入有效的礦工費率,有効なマイニング手数料率を入力してください
inputToAddressTip,inputToAddressTip,请输入收款地址,Please enter the recipient address,請輸入收款地址,受取アドレスを入力してください
inputOkToAddressTip,inputOkToAddressTip,请输入正确的地址,Please enter the correct address,請輸入正確的地址,正しいアドレスを入力してください
inputAmountTip,inputAmountTip,请输入支付金额,Please enter the payment amount,請輸入支付金額,支払い金額を入力してください
amounNumberError,amounNumberError,请输入正确的金额,Please enter the correct amount,請輸入正確的金額,正しい金額を入力してください
transferToSelfTip,transferToSelfTip,无法给自己转账,Cannot transfer to yourself,無法給自己轉賬,自分自身に送金することはできません
insufficientBalanceFee,insufficientBalanceFee,余额不足以抵扣矿工费,Insufficient balance to cover miner fees,餘額不足以抵扣礦工費,残高がマイニング手数料をカバーするのに不足しています
insufficientBalance,insufficientBalance,余额不足,Insufficient balance,餘額不足,残高不足
insufficientMainFee,insufficientMainFee,矿工费不足,Insufficient miner fees,礦工費不足,マイニング手数料不足
coinTransferTitle,coinTransferTitle,@symbol 转账,@symbol Transfer,@symbol 轉賬,@symbol 送金
coinTsTypeTitle,coinTsTypeTitle,交易类型,Transaction type,交易類型,取引タイプ
qrTitleTransfer,qrTitleTransfer,签名交易,Sign transaction,簽名交易,取引に署名
qrAutoPage,qrAutoPage,自动翻页,Auto page flip,自動翻頁,自動ページ送り
qrManualPage,qrManualPage,手动翻页,Manual page flip,手動翻頁,手動ページ送り
scanQrSendTitle,scanQrSendTitle,扫码发送,Scan to send,掃碼發送,QRコードで送信
qrSettingQrsizeTitle1,qrSettingQrsizeTitle1,使用硬件钱包扫码\n识别困难？,Use hardware wallet to scan QR code\nDifficulty recognizing?,使用硬件錢包掃碼\n識別困難？,ハードウェアウォレットでQRコードをスキャン\n認識が難しいですか？
qrSettingQrsizeTitle2,qrSettingQrsizeTitle2,设置二维码容量 >,Set QR code capacity >,設置二維碼容量 >,QRコードの容量を設定 >
sendAmountSmall,sendAmountSmall,转账金额太小，不能转账,Transfer amount is too small to proceed,轉賬金額太小，不能轉賬,送金金額が小さすぎるため、送金できません
completionProgress,completionProgress,当前进度,Current progress,當前進度,現在の進行状況
ethGasPriceSlowTip,ethGasPriceSlowTip,您设置的最高基本费用低于当前网络基本费用@fee，交易成功需等待较长时间,"The maximum base fee you set is lower than the current network base fee @fee, and the transaction will take longer to be successful",您設定的最高基本費用低於當前網絡基本費用@fee，交易成功需等待較長時間,設定した最高基本手数料が現在のネットワーク基本手数料@feeより低いため、取引が成功するまでに長時間かかる可能性があります
ethGasPriceLargeTip,ethGasPriceLargeTip,Gas price偏高，可能浪费矿工费,Gas price is too high and may waste miner fees,Gas price偏高，可能浪費礦工費,Gas priceが高すぎると、マイニング手数料が無駄になる可能性があります
ethGasLimitTip,ethGasLimitTip,Gas上限必须大于@limit小于15.00M,Gas limit must be greater than @limit and less than 15.00M,Gas上限必須大於@limit且小於15.00M,Gas上限は@limitより大きく、15.00Mより小さい必要があります
filGasEmtyTips,filGasEmtyTips,请输入有效的Gas,Please enter a valid Gas,請輸入有效的Gas,有効なGasを入力してください
filGasSlowTips,filGasSlowTips,Gas过低可能导致Out of Gas交易失败,Gas too low may result in Out of Gas transaction failure,Gas過低可能導致Out of Gas交易失敗,Gasが低すぎると、Out of Gasで取引が失敗する可能性があります
ethGasPriceMinTip,ethGasPriceMinTip,Gas价格不得低于@gas,Gas price must not be lower than @gas,Gas價格不得低於@gas,Gas価格は@gasより低くしてはいけません
remarkMemo,remarkMemo,备注(Memo),Memo,備註(Memo),メモ（Memo）
remark,remark,备注,Remark,備註,備考
remarkNoText,remarkNoText,暂无备注,No remarks available,暫無備註,現在メモはありません
remarkHitText,remarkHitText,选填,Optional,選填,任意
remarkTag,remarkTag,备注(Tag),Tag,備註(Tag),タグ（Tag）
xrpRemarkTips,xrpRemarkTips,XRP主链规定，地址余额中只有大于@value XRP的部分才可进行转账。,"According to XRP main chain regulations, only the portion of the address balance greater than @value XRP can be transferred.",XRP主鏈規定，地址餘額中只有大於@value XRP的部分才可進行轉賬。,XRP主チェーンの規定により、アドレスの残高が@value XRPを超える部分のみが送金可能です。
xrpTsTips,xrpTsTips,余额不足！XRP主链规定，地址余额中只有大于@value XRP的部分才可进行转账。,"Insufficient balance! According to XRP main chain regulations, only the portion of the address balance greater than @value XRP can be transferred.",餘額不足！XRP主鏈規定，地址餘額中只有大於@value XRP的部分才可進行轉賬。,残高不足！XRP主チェーンの規定により、アドレスの残高が@value XRPを超える部分のみが送金可能です。
xrpNoActivityTips,xrpNoActivityTips,账户未激活，请往该地址一次性转入不低于@valueXRP以激活账户,Account not activated. Please transfer at least @value XRP to this address to activate the account,帳戶未激活，請往該地址一次性轉入不少於@value XRP以激活帳戶,アカウントがアクティブ化されていません。アカウントをアクティブにするには、最低@value XRPを一度にこのアドレスに送金してください
maxFeeValidTip,maxFeeValidTip,请输入有效的 maxFee,Please enter a valid maxFee,請輸入有效的 maxFee,有効なmaxFeeを入力してください
maxFeeNoLessMaxPriorityFeeTip,maxFeeNoLessMaxPriorityFeeTip,maxFee 不能小于 maxPriorityFee,maxFee cannot be less than maxPriorityFee,maxFee 不能小於 maxPriorityFee,maxFeeはmaxPriorityFeeより小さくてはいけません
maxPriorityFeeTip,maxPriorityFeeTip,设置的 maxPriorityFee @inputMax 可能无法发挥全部效用，maxPriorityFee @rawMax 、currentBaseFee @baseFee 之和大于设置的 maxFee @maxFee，调高 maxFee 以发挥 maxPriorityFee 全部效用,"The set maxPriorityFee @inputMax may not be fully effective. Since maxPriorityFee @rawMax plus currentBaseFee @baseFee is greater than the set maxFee @maxFee, increase maxFee to fully utilize maxPriorityFee.",設定的 maxPriorityFee @inputMax 可能無法發揮全部效用，因為 maxPriorityFee @rawMax 加上 currentBaseFee @baseFee 的總和大於設定的 maxFee @maxFee，請提高 maxFee 以發揮 maxPriorityFee 的全部效用,設定されたmaxPriorityFee @inputMaxは、maxPriorityFee @rawMaxとcurrentBaseFee @baseFeeの合計が設定されたmaxFee @maxFeeより大きいため、maxFeeを引き上げてmaxPriorityFeeの効果を最大限に引き出してください
maxPriorityFeeHeightTip,maxPriorityFeeHeightTip,maxPriorityFee 偏高，可能浪费矿工费,The maxPriorityFee is too high and may result in wasted miner fees,maxPriorityFee 偏高，可能浪費礦工費,maxPriorityFeeが高すぎると、マイニング手数料が無駄になる可能性があります
maxFeeHeightTip,maxFeeHeightTip,设置的 maxFee 偏大，超过预估 @rawFee Gwei 的 4 倍以上，只提高 maxFee 可能无法让交易更快被打包,"The set maxFee is too large, exceeding more than 4 times the estimated @rawFee Gwei. Simply increasing maxFee may not make the transaction faster to be packaged",設定的 maxFee 偏大，超過預估 @rawFee Gwei 的 4 倍以上，只提高 maxFee 可能無法使交易更快被打包,設定されたmaxFeeが大きすぎると、予想@rawFee Gweiの4倍以上になっており、maxFeeを上げるだけでは取引がより速くパッケージ化されない可能性があります
tronActivatedTip,tronActivatedTip,该地址未激活，将消耗 1 TRX 激活该地址,The address is not activated; it will consume 1 TRX to activate the address,該地址尚未激活，將消耗 1 TRX 激活該地址,このアドレスはアクティブ化されておらず、1 TRXを消費してアクティブ化する必要があります
tronNeedSource,tronNeedSource,交易所需资源,Resources required for the transaction,交易所需資源,取引に必要なリソース
valueBandwidth,valueBandwidth,@bandwidth 带宽,@bandwidth bandwidth,@bandwidth 帶寬,@bandwidth 帯域幅
valueEnergy,valueEnergy,@energy 能量,@energy energy,@energy 能量,@energy エネルギー
activedAccount,activedAccount,激活账户,Activate account,激活帳戶,アカウントをアクティブ化
deductionValueBandwidth,deductionValueBandwidth,抵扣 @bandwidth 带宽,Deduct @bandwidth bandwidth,扣除 @bandwidth 帶寬,@bandwidth 帯域幅を支払う
deductionValueEnergy,deductionValueEnergy,抵扣 @energy 能量,Deduct @energy energy,扣除 @energy 能量,@energy エネルギーを支払う
tronPopTip,tronPopTip,账户可用 TRX 不足以抵扣矿工费，交易可能失败,"The available TRX in the account is insufficient to cover the miner fee, and the transaction may fail",帳戶可用 TRX 不足以抵扣礦工費，交易可能失敗,アカウントの利用可能なTRXがマイニング手数料をカバーするのに不足しており、取引が失敗する可能性があります
tronRemarkTip,tronRemarkTip,添加转账备注，将额外消耗1TRX的手续费，不超过200个字符,"Adding a transfer note will incur an additional 1 TRX fee, up to 200 characters",添加轉帳備註將額外消耗 1 TRX 的手續費，不超過 200 個字符,送金メモを追加すると、1 TRXの追加手数料がかかります。文字数は200文字以内です
tronResourceDetail,tronResourceDetail,交易所需资源 = 带宽消耗 + 能量消耗\n\n带宽消耗:\n接收账户交易只能扣除免费带宽或质押带宽，若二者都不满足该笔交易消耗，则需要全部用 TRX 支付，无法部分扣除\n\n能量消耗:\n交易支持扣除全部能量后，剩余部分由 TRX 支付\n\n交易所需资源按用户支付全部资源进行估算，实际消耗资源以链上数据为准,"Resources required for the transaction = bandwidth consumption + energy consumption\n\nBandwidth consumption:\nTransactions to the receiving account can only deduct free bandwidth or staked bandwidth. If neither satisfies the transaction consumption, TRX must be paid in full, and partial deduction is not possible\n\nEnergy consumption:\nAfter all energy is deducted, the remaining part is paid with TRX\n\nResources required for the transaction are estimated based on the total resources paid by the user, and actual resource consumption is based on on-chain data",交易所需資源 = 帶寬消耗 + 能量消耗\n\n 帶寬消耗：\n接收帳戶交易只能扣除免費帶寬或質押帶寬，若二者都不滿足該筆交易消耗，則需要全部用 TRX 支付，無法部分扣除\n\n 能量消耗：\n交易支持扣除全部能量後，剩餘部分由 TRX 支付\n\n 交易所需資源按用戶支付全部資源進行估算，實際消耗資源以鏈上數據為準,取引に必要なリソース = 帯域幅消費 + エネルギー消費\n\n帯域幅消費:\n受信アカウントの取引は、無料帯域幅またはステーキング帯域幅でのみ差し引かれます。どちらも取引消費を満たさない場合は、すべてTRXで支払う必要があります\n\nエネルギー消費:\n取引は、すべてのエネルギーが差し引かれた後、残りはTRXで支払われます\n\n取引に必要なリソースはユーザーが支払ったすべてのリソースに基づいて推定され、実際のリソース消費はチェーン上のデータを基準とします
tronFeeDetail,tronFeeDetail,TRON网络费用：\n\n用户在TRON网络上的费用包括因消耗带宽或能量不足而需要抵扣的TRX费用，以及特定交易手续费（例如，激活账户需支付1 TRX）。\n\n所有费用均由链自行收取，实际消耗的数量以链上数据为准。,"TRON Network Fees:\n\nUsers incur fees on the TRON network, which include TRX fees deducted due to insufficient bandwidth or energy consumption, as well as specific transaction fees (for example, activating an account requires 1 TRX). \n\nAll fees are collected by the chain itself, and the actual consumed amounts are based on on-chain data.",TRON網絡費用：\n\n用戶在TRON網絡上的費用包括因消耗帶寬或能量不足而需要抵扣的TRX費用，以及特定交易手續費（例如，激活帳戶需支付1 TRX）。\n\n所有費用均由鏈自行收取，實際消耗的數量以鏈上數據為準。,TRONネットワーク料金：\n\nユーザーはTRONネットワーク上で、帯域幅やエネルギー不足によるTRX料金の控除、および特定の取引手数料（例えば、アカウントの有効化には1 TRXが必要）を含む料金が発生します。\n\nすべての料金はチェーン自体によって徴収され、実際の消費量はオンチェーンデータに基づきます。
tronRiskTip,tronRiskTip,继续交易会消耗账户剩余 TRX/资源，且存在交易失败的风险，请确认是否继续,Continuing with the transaction will consume the remaining TRX/resources in the account and there is a risk of transaction failure. Please confirm whether to continue,繼續交易將消耗帳戶剩餘 TRX/資源，且存在交易失敗的風險，請確認是否繼續,取引を続行すると、アカウントの残りのTRX/リソースが消費され、取引が失敗するリスクがあります。続行するかどうかを確認してください
basicEnergy,basicEnergy,正常消耗,Normal consumption,正常消耗,通常の消費
extraEnergy,extraEnergy,额外消耗,Additional consumption,額外消耗,追加の消費
tronLearnMore,tronLearnMore,交易调用热门合约会额外消耗能量，了解详情,Transactions calling popular contracts will consume additional energy. Learn more,交易調用熱門合約會額外消耗能量，了解詳情,人気のある契約を呼び出す取引は、追加でエネルギーを消費します。詳細を確認してください
fromAccount,fromAccount,发送账户,Sending account,發送帳戶,送信アカウント
toAccount,toAccount,接收账户,Receiving account,接收帳戶,受信アカウント
addressLabelEmpty,addressLabelEmpty,请输入备注名,Please enter the remark name,請輸入備註名,メモ名を入力してください
feeRang,feeRang,预估范围,Estimated range,預估範圍,推定範囲
bindWallet,bindWallet,绑定钱包,Bind wallet,綁定錢包,ウォレットをバインド
scanQrBind,scanQrBind,扫码绑定,Scan QR code to bind,掃描 QR 碼綁定,QRコードでバインド
verifySuccessTitle,verifySuccessTitle,防伪验证成功！,Anti-counterfeiting verification successful!,防偽驗證成功！,防偽検証成功！
verifyFailTitle,verifyFailTitle,防伪验证失败！,Anti-counterfeiting verification failed!,防偽驗證失敗！,防偽検証失敗！
verifySuccessSubTitle,verifySuccessSubTitle,此钱包设备为官方出售的正品，感谢您的支持,"This wallet device is an official genuine product, thank you for your support",此錢包設備為官方出售的正品，感謝您的支持,このウォレットデバイスは公式販売品であり、ご支援ありがとうございます
verifyFailSubTitle,verifyFailSubTitle,此钱包设备非官方出售的正品，请您谨慎使用,"This wallet device is not an official genuine product, please use it with caution",此錢包設備非官方出售的正品，請您謹慎使用,このウォレットデバイスは公式販売品ではありません。慎重に使用してください
goBackHome,goBackHome,返回首页,Return to homepage,返回首頁,ホームに戻る
stringResource,stringResource,资源,Resources,資源,リソース
stringDelegateResource,stringDelegateResource,代理资源,Proxy resources,代理資源,代理リソース
stringVote,stringVote,节点投票,Node voting,節點投票,ノード投票
stringStake2,stringStake2,质押资产2.0,Staked assets 2.0,質押資產2.0,ステーキング資産2.0
stringUnstake2,stringUnstake2,解锁资产2.0,Unlocked assets 2.0,解鎖資產2.0,アンロック資産2.0
stringStake,stringStake,质押资产,Staked assets,質押資產,ステーキング資産
stringUnstake,stringUnstake,解锁资产,Unlocked assets,解鎖資產,アンロック資産
stringReclaim,stringReclaim,回收资源,Recycle resources,回收資源,リソースの回収
stringReclaimAddress,stringReclaimAddress,回收地址,Recycle address,回收地址,回収アドレス
stringConfirming,stringConfirming,确认中,Confirming,確認中,確認中
stringPacking,stringPacking,打包中,Packaging,打包中,パッケージング中
stringSendFail,stringSendFail,已失败,Failed,已失敗,失敗しました
stringTxTime,stringTxTime,交易时间,Transaction time,交易時間,取引時間
stringTxId,stringTxId,交易ID,Transaction ID,交易 ID,取引ID
stringTxBlock,stringTxBlock,区块,Block,區塊,ブロック
stringTxSource,stringTxSource,消耗资源,Consume resources,消耗資源,消費リソース
stringTxEnergy,stringTxEnergy,能量,Energy,能量,エネルギー
stringTxBandwidth,stringTxBandwidth,带宽,Bandwidth,帶寬,帯域幅
stringTxWithdraw,stringTxWithdraw,提取 TRX,Withdraw TRX,提取 TRX,TRXを引き出す
stringBurnedEnergy,stringBurnedEnergy,燃烧抵扣能量,Burn to deduct energy,燃燒抵扣能量,エネルギーを燃焼して控除
stringBurnedBandwidth,stringBurnedBandwidth,燃烧抵扣带宽,Burn to deduct bandwidth,燃燒抵扣帶寬,帯域幅を燃焼して控除
stringBlockchain,stringBlockchain,在区块链浏览器中查看,View in blockchain explorer,在區塊鏈瀏覽器中查看,ブロックチェーンブラウザで表示
stringSearchEmpty,stringSearchEmpty,未找到指定的 Token？,Token not found?,未找到指定的 Token？,指定されたTokenが見つかりません
stringSubmitToken,stringSubmitToken,提交 Token,Submit Token,提交 Token,Tokenを提出する
stringInputOkContract,stringInputOkContract,请输入正确的合约地址,Please enter the correct contract address,請輸入正確的合約地址,正しい契約アドレスを入力してください
stringAddTokenSuccess,stringAddTokenSuccess,提交成功，请等待审核!,"Submission successful, please wait for review!",提交成功，請等待審核！,提出が成功しました。審査をお待ちください！
stringBuyWallet,stringBuyWallet,购买硬件钱包,Purchase hardware wallet,購買硬件錢包,ハードウェアウォレットを購入
stringGuideTitle1,stringGuideTitle1,多链资产钱包,Multi-chain asset wallet,多鏈資產錢包,マルチチェーン資産ウォレット
stringGuideSubTitle1,stringGuideSubTitle1,专为硬件钱包定制，在保证资产安全的前提下，提供更方便友好的Web3交互体验。,"Custom-designed for hardware wallets, providing a more convenient and user-friendly Web3 interaction experience while ensuring asset security.",專為硬件錢包定制，在保證資產安全的前提下，提供更方便友好的 Web3 交互體驗。,ハードウェアウォレット専用に設計され、資産の安全を確保しつつ、より便利でユーザーフレンドリーなWeb3インタラクション体験を提供します。
stringGuideTitle2,stringGuideTitle2,欢迎使用Coinbag钱包,Welcome to Coinbag Wallet,歡迎使用 Coinbag 錢包,Coinbagウォレットへようこそ
stringGuideSubTitle2,stringGuideSubTitle2,即刻享受更好的资产转账、余额概览、质押收益等功能，请绑定硬件钱包后使用。,"Enjoy better asset transfers, balance overview, staking rewards, and more. Please bind your hardware wallet to use these features.",即刻享受更好的資產轉帳、餘額概覽、質押收益等功能，請綁定硬件錢包後使用。,より良い資産転送、残高の概覧、ステーキングリターンなどの機能を今すぐ体験してください。ハードウェアウォレットを接続して使用してください。
stringEmptyTitle,stringEmptyTitle,连接钱包，管理资产,"Connect wallet, manage assets",連接錢包，管理資產,ウォレットに接続して資産を管理
stringEmptyTitle2,stringEmptyTitle2,多链支持，全面覆盖,"Multi-chain support, comprehensive coverage",多鏈支持，全面覆蓋,マルチチェーンサポート、全面カバー
stringEmptySubTitle2,stringEmptySubTitle2,管理全球优质币种，支持主流NFT,"Manage top global coins, support major NFTs",管理全球優質幣種，支持主流 NFT,世界中の優れたコインを管理し、主要なNFTをサポート
stringAllNetTitle,stringAllNetTitle,查看全部网络,View all networks,查看全部網絡,全ネットワークを表示
stringWeb3Title,stringWeb3Title,Web3新体验,New Web3 experience,Web3 新體驗,Web3の新しい体験
stringWeb3SubTitle,stringWeb3SubTitle,专为硬件钱包定制,Custom-designed for hardware wallets,專為硬件錢包定制,ハードウェアウォレット専用に設計
stringWeb3Title1,stringWeb3Title1,轻松上手,Easy to get started,輕鬆上手,簡単に始める
stringWeb3SubTitle1,stringWeb3SubTitle1,更方便友好的Web3交互体验,More convenient and user-friendly Web3 interaction experience,更方便友好的 Web3 交互體驗,より便利でユーザーフレンドリーなWeb3インタラクション体験
stringWeb3Title2,stringWeb3Title2,安全透明,Secure and transparent,安全透明,安全で透明
stringWeb3SubTitle2,stringWeb3SubTitle2,三层防护，离线储存 ，永不触网，内置安全芯片守护大额资产安全,"Three layers of protection, offline storage, never online, with built-in security chip to safeguard large asset security",三層防護，離線儲存，永不觸網，內置安全芯片守護大額資產安全,三層保護、オフライン保存、ネットに触れることなく、大額の資産の安全を守る内蔵セキュリティチップ
stringWeb3Title3,stringWeb3Title3,多链聚合,Multi-chain aggregation,多鏈聚合,マルチチェーン統合
stringWeb3SubTitle3,stringWeb3SubTitle3,一个钱包完成区块链储存和管理,Complete blockchain storage and management with one wallet,一個錢包完成區塊鏈儲存和管理,一つのウォレットでブロックチェーンの保存と管理を完了
stringSupportChains,stringSupportChains,现在已支持的@count个网络,Now supporting @count networks,現在已支持的 @count 個網絡,現在サポートされている @count 個のネットワーク
stringCreateNewWallet,stringCreateNewWallet,创建新钱包,Create new wallet,創建新錢包,新しいウォレットを作成
stringScanQrBind,stringScanQrBind,二维码连接,QR code connection,二維碼連接,QRコードで接続
stringScanQrBindDes,stringScanQrBindDes,扫一扫，连接硬件钱包，轻松管理数字资产,Scan to connect hardware wallet and easily manage digital assets,掃描一下，連接硬件錢包，輕鬆管理數位資產,スキャンしてハードウェアウォレットを接続し、デジタル資産を簡単に管理
stringNFCBind,stringNFCBind,NFC连接,NFC connection,NFC 連接,NFC接続
stringNFCBindDes,stringNFCBindDes,一触即连Touch卡，极速连接硬件钱包，安全高效。,"Touch card for instant connection, rapid connection to hardware wallet, secure and efficient.",一觸即連 Touch 卡，极速連接硬件錢包，安全高效。,タッチカードで即座に接続、ハードウェアウォレットと迅速に接続、安全かつ効率的。
stringTouchPsw,stringTouchPsw,请输入密码,Please enter password,請輸入密碼,パスワードを入力してください
stringTouchPhone,stringTouchPhone,将卡片靠近手机,Hold the card near the phone,將卡片靠近手機,カードをスマートフォンに近づけてください
stringTouchNONfc,stringTouchNONfc,当前设备不支持NFC功能,Current device does not support NFC functionality,當前設備不支持 NFC 功能,現在のデバイスはNFC機能をサポートしていません
stringTouchReadError,stringTouchReadError,读取错误，请重试,"Reading error, please try again",讀取錯誤，請重試,読み取りエラー、再試行してください
stringTouchReading,stringTouchReading,读取中，请勿移走卡片,"Reading in progress, do not remove the card",讀取中，請勿移走卡片,読み取り中、カードを移動しないでください
stringTouchNfcSuccess,stringTouchNfcSuccess,操作成功,Operation successful,操作成功,操作成功
stringNfcPswMax,stringNfcPswMax,密码错误次数已达上限，钱包已重置,"Password error attempts have reached the limit, the wallet has been reset",密碼錯誤次數已達上限，錢包已重置,パスワードのエラー回数が上限に達しました。ウォレットがリセットされました
stringNfcInputOnePsw,stringNfcInputOnePsw,还可尝试 1 次密码，再次错误后将重置钱包,"You can try the password one more time. After another error, the wallet will be reset",還可嘗試 1 次密碼，再次錯誤後將重置錢包,パスワードをもう 1 回試すことができます。再度エラーが発生するとウォレットがリセットされます
stringNfcPswErrorNum,stringNfcPswErrorNum,密码不正确，再错误 @count 次后将重置钱包,Incorrect password. The wallet will be reset after another @count errors,密碼不正確，再錯誤 @count 次後將重置錢包,パスワードが正しくありません。さらに @count 回エラーが発生するとウォレットがリセットされます
stringNfcPswError,stringNfcPswError,密码不正确，请重试,"Incorrect password, please try again",密碼不正確，請重試,パスワードが正しくありません。再試行してください
stringNfcScanError,stringNfcScanError,扫描失败，请重试,"Scan failed, please retry",掃描失敗，請重試,スキャンに失敗しました。再試行してください
stringCardHaveWallet,stringCardHaveWallet,此卡片已存在钱包,This card already exists in the wallet,此卡片已存在錢包,このカードは既にウォレットに存在します
stringCardWalletError,stringCardWalletError,钱包不符,Wallet mismatch,錢包不符,ウォレットが一致しません
stringCardNumberError,stringCardNumberError,卡号错误,Card number error,卡號錯誤,カード番号が間違っています
stringNfcUnknownError,stringNfcUnknownError,未知错误,Unknown error,未知錯誤,不明なエラー
stringTouchNoOfficial,stringTouchNoOfficial,此卡片非Coinbag官方出品,This card is not an official Coinbag product,此卡片非 Coinbag 官方出品,このカードはCoinbagの公式製品ではありません
stringTouchSpotError,stringTouchSpotError,无法识别，请读取正确的卡片,"Cannot recognize, please read the correct card",無法識別，請讀取正確的卡片,認識できません。正しいカードを読み取ってください
stringToucReadCard,stringToucReadCard,读取卡片,Reading card,讀取卡片,カードを読み取る
stringTouchReadTips,stringTouchReadTips,读取卡片需要一定时间，请保持卡片与手机接触。,"Reading the card takes some time, please keep the card in contact with the phone.",讀取卡片需要一定時間，請保持卡片與手機接觸。,カードの読み取りには時間がかかる場合があります。カードとスマートフォンを接触させたままにしてください。
stringNewWallet,stringNewWallet,新钱包,New wallet,新錢包,新しいウォレット
stringCreateTitle,stringCreateTitle,创建,Create,創建,作成
stringCreateDesTitle,stringCreateDesTitle,未备份助记词，请选择“创建钱包”,"Mnemonic not backed up, please choose ""Create Wallet""",未備份助記詞，請選擇“創建錢包”,バックアップされた助記詞がありません。ウォレットを「作成」してください
stringImportTitle,stringImportTitle,导入,Import,導入,インポート
stringImportDesTitle,stringImportDesTitle,已备份助记词，请选择“导入钱包”,"Mnemonic backed up, please choose ""Import Wallet""",已備份助記詞，請選擇“導入錢包”,助記詞がバックアップされています。「インポートウォレット」を選択してください
stringCreateWalletTitle,stringCreateWalletTitle,创建钱包,Create Wallet,創建錢包,ウォレットを作成
stringImportWalletTitle,stringImportWalletTitle,导入钱包,Import Wallet,導入錢包,ウォレットをインポート
stringAgreementTip,stringAgreementTip,请阅读并同意《用户协议》,"Please read and agree to the ""User Agreement""",請閱讀並同意《用戶協議》,「ユーザー契約」をお読みの上、同意してください
stringTouchNumber,stringTouchNumber,请使用尾号为@number的卡片,Please use the card with the last four digits @number,請使用尾號為 @number 的卡片,尾番号が @number のカードを使用してください
stringCreating,stringCreating,正在创建...,Creating...,正在創建...,作成中...
stringImporting,stringImporting,正在导入...,Importing...,正在導入...,インポート中...
stringCreateSuccess,stringCreateSuccess,创建成功,Creation successful,創建成功,作成成功
stringImportSuccess,stringImportSuccess,导入成功,Import successful,導入成功,インポート成功
stringInputMnemonic,stringInputMnemonic,请输入正确的助记词,Please enter the correct mnemonic,請輸入正確的助記詞,正しい助記詞を入力してください
stringVerifyFail,stringVerifyFail,验证失败,Verification failed,驗證失敗,検証に失敗しました
stringImportMnemonic,stringImportMnemonic,导入助记词,Import mnemonic,導入助記詞,助記詞をインポート
stringBackupMnemonic,stringBackupMnemonic,备份助记词,Backup mnemonic,備份助記詞,助記詞をバックアップ
stringBackupMnemonicDes,stringBackupMnemonicDes,助记词可用于恢复钱包，掌控钱包资产，请务必按顺序抄写到纸上并妥善保管！,Mnemonics can be used to restore the wallet and control wallet assets. Please be sure to write them down in order on paper and keep them safe!,助記詞可用於恢復錢包，掌控錢包資產，請務必按順序抄寫到紙上並妥善保管！,助記詞はウォレットの復元に使用できます。ウォレットの資産を管理するために、必ず順番に書き写し、適切に保管してください！
stringVerifyMnemonic,stringVerifyMnemonic,验证助记词,Verify mnemonic,驗證助記詞,助記詞を検証
stringOrderInputMnemonic,stringOrderInputMnemonic,按顺序输入助记词,Enter mnemonic in order,按順序輸入助記詞,助記詞を順番に入力してください
stringTouchPasswordTip,stringTouchPasswordTip,6-32个字符，可包含a-z，A-Z和0-9。密码遗失不可找回，请牢记,"6-32 characters, can include a-z, A-Z, and 0-9. Passwords cannot be recovered if lost, please remember",6-32 個字符，可包含 a-z，A-Z 和 0-9。密碼遺失不可找回，請記牢,6-32文字で、a-z、A-Z、0-9を含むことができます。パスワードを紛失すると復元できませんので、必ず記憶してください
stringTouchInputPsw,stringTouchInputPsw,输入密码,Enter password,輸入密碼,パスワードを入力してください
stringTouchInputPswTwo,stringTouchInputPswTwo,请确认密码,Confirm password,請確認密碼,パスワードを確認してください
stringTouchImInputCorrectPsw,stringTouchImInputCorrectPsw,请输入正确的密码,Please enter the correct password,請輸入正確的密碼,正しいパスワードを入力してください
stringTouchTowPswFail,stringTouchTowPswFail,两次密码不一致,Passwords do not match,兩次密碼不一致,パスワードが一致しません
stringTouchSetWalletName,stringTouchSetWalletName,设置钱包名称,Set wallet name,設置錢包名稱,ウォレット名を設定
stringTouchInputWalletNameHint,stringTouchInputWalletNameHint,输入钱包名称,Enter wallet name,輸入錢包名稱,ウォレット名を入力
stringTouchWalletNameToast,stringTouchWalletNameToast,请输入钱包名称,Please enter wallet name,請輸入錢包名稱,ウォレット名を入力してください
stringTouchNextTitle,stringTouchNextTitle,下一步,Next,下一步,次へ
stringTouchSafeTitle,stringTouchSafeTitle,安全提示,Security Notice,安全提示,セキュリティ通知
stringTouchPopTip1,stringTouchPopTip1,为保护助记词安全，请确保周围没有他人及监控设备,"To protect the security of your mnemonic phrase, please ensure that no one and no monitoring devices are around.",為保護助記詞安全，請確保周圍沒有他人及監控設備。,ニーモニックフレーズの安全を守るため、周囲に他の人や監視装置がないことを確認してください。
stringTouchPopTip2,stringTouchPopTip2,重要提醒：\n·请勿泄密\n·切勿在任何网页中输入\n·切勿通过网络工具传输\n·切勿使用手机拍照\n·不要存储电子版\n·请勿泄密,Important Reminder:\n·Do not disclose\n·Do not enter on any webpage\n·Do not transmit via network tools\n·Do not use phone to take photos\n·Do not store in electronic form\n·Do not disclose,重要提醒：\n·請勿泄露\n·切勿在任何網頁中輸入\n·切勿通過網絡工具傳輸\n·切勿使用手機拍照\n·不要存儲電子版\n·請勿泄露,重要な注意事項: \n·漏洩しないでください\n·ウェブページに入力しないでください\n·ネットワークツールを通じて送信しないでください\n·スマートフォンで撮影しないでください\n·電子版で保存しないでください\n·漏洩しないでください
stringTouchPopOkTitle,stringTouchPopOkTitle,我知道了,I got it.,我知道了,了解しました
stringTouchEnglishTitle,stringTouchEnglishTitle,英文,English,英文,英語
stringTouchChineseTitle,stringTouchChineseTitle,中文,Chinese,中文,中国語
stringTouchNumberTitle,stringTouchNumberTitle,数字,Numbers,數字,数字
stringWalletIdTitle,stringWalletIdTitle,钱包ID,Wallet ID,錢包ID,ウォレットID
stringResetCardTitle,stringResetCardTitle,重置卡片,Reset Card,重置卡片,カードのリセット
stringChangePswTitle,stringChangePswTitle,修改密码,Change Password,修改密碼,パスワードの変更
stringChangeWalletNameTitle,stringChangeWalletNameTitle,修改钱包名称,Change Wallet Name,修改錢包名稱,ウォレット名の変更
stringCardEmpty,stringCardEmpty,此卡片为空卡,This card is empty,此卡片為空卡,このカードは空のカードです
stringBackupComplete,stringBackupComplete,备份完成,Backup Complete,備份完成,バックアップ完了
stringVerifybackupTitle,stringVerifybackupTitle,验证备份,Verify Backup,驗證備份,バックアップの確認
stringWalletNameNoEmpty,stringWalletNameNoEmpty,钱包名称不能为空,Wallet name cannot be empty,錢包名稱不能為空,ウォレット名は空であってはいけません
stringWalletNameMax12,stringWalletNameMax12,最多12个字符,Up to 12 characters,最多12個字符,最大12文字
stringSetWalletName,stringSetWalletName,设置钱包名称,Set Wallet Name,設置錢包名稱,ウォレット名の設定
stringNewNameSameOldName,stringNewNameSameOldName,新名称不能与原名称相同,New name cannot be the same as the old name,新名稱不能與原名稱相同,新しい名前は以前の名前と同じにできません
stringNameChangeSuccess,stringNameChangeSuccess,修改成功,Change Successful,修改成功,変更成功
stringInputOldPswTip,stringInputOldPswTip,请输入旧密码,Please enter the old password,請輸入舊密碼,古いパスワードを入力してください
stringInputNewPswTip,stringInputNewPswTip,请输入新密码,Please enter the new password,請輸入新密碼,新しいパスワードを入力してください
stringInputNewPswTwoTip,stringInputNewPswTwoTip,请确认新密码,Please confirm the new password,請確認新密碼,新しいパスワードを確認してください
stringTouchVerifyPswTitle,stringTouchVerifyPswTitle,验证密码,Verify Password,驗證密碼,パスワードの確認
stringOldNewSameTip,stringOldNewSameTip,新密码不能与旧密码相同,New password cannot be the same as the old password,新密碼不能與舊密碼相同,新しいパスワードは古いパスワードと同じにできません
stringTouchResetSuccess,stringTouchResetSuccess,重置成功,Reset Successful,重置成功,リセット成功
stringTouchSignErrorTip,stringTouchSignErrorTip,签名数据错误,Signature Data Error,簽名數據錯誤,署名データエラー
stringTronRawDataHexError,stringTronRawDataHexError,Tron交易验签失败,Tron Transaction Verification Failed,Tron交易驗簽失敗,Tron取引の検証に失敗しました
stringEmptyWalletTitle,stringEmptyWalletTitle,Web3入口，一个就够,One Web3 Entry is Enough,Web3入口，一個就夠,Web3の入り口は一つで十分です
stringEmptyWalletSubTitle,stringEmptyWalletSubTitle,钱包 · 交易 · NFT · DeFI · DApp,Wallet · Transactions · NFT · DeFi · DApp,錢包 · 交易 · NFT · DeFi · DApp,ウォレット · 取引 · NFT · DeFi · DApp
stringTabWallet,stringTabWallet,钱包,Wallet,錢包,ウォレット
stringTabDiscover,stringTabDiscover,发现,Discover,發現,発見
stringTabProfile,stringTabProfile,我,Profile,我,私
stringBackupTip,stringBackupTip,请及时备份助记词，以避免手机、钱包设备丢失或损坏造成资产损失。,Please back up your mnemonic phrase promptly to avoid asset loss due to phone or wallet device loss or damage.,請及時備份助記詞，以避免手機、錢包設備丟失或損壞造成資產損失。,スマートフォンやウォレットデバイスの紛失や損傷による資産の損失を避けるために、ニーモニックフレーズを速やかにバックアップしてください。
stringBackupButtonTitle,stringBackupButtonTitle,立即备份,Back Up Now,立即備份,今すぐバックアップ
stringJumpTitle,stringJumpTitle,跳过,Skip,跳過,スキップ
stringNfcReading,stringNfcReading,已准备好扫描,Ready to Scan,已準備好掃描,スキャンの準備ができました
stringSignLoading,stringSignLoading,签名中，请等待,"Signing, please wait",簽名中，請等待,署名中、しばらくお待ちください
stringBingdingTitle,stringBingdingTitle,立即绑定,Bind Now,立即綁定,今すぐバインド
stringCreateDesString,stringCreateDesString,您已成功创建钱包，快去使用吧！,"You have successfully created a wallet, go ahead and use it!",您已成功創建錢包，快去使用吧！,ウォレットの作成が成功しました。すぐに使用してください！
stringImportDesString,stringImportDesString,您已成功导入钱包，快去使用吧！,"You have successfully imported a wallet, go ahead and use it!",您已成功導入錢包，快去使用吧！,ウォレットのインポートが成功しました。すぐに使用してください！
stringCreateLoading,stringCreateLoading,创建中，请稍等,"Creating, please wait",創建中，請稍等,作成中、しばらくお待ちください
stringImportLoading,stringImportLoading,导入中，请稍等,"Importing, please wait",導入中，請稍等,インポート中、しばらくお待ちください
stringNfcCloseAndroidTip,stringNfcCloseAndroidTip,NFC已关闭，请在“设置”中打开。,"NFC is off, please turn it on in ""Settings.""",NFC已關閉，請在「設置」中打開。,NFCがオフです。設定でオンにしてください。
stringNfcCloseIOSTip,stringNfcCloseIOSTip,NFC已关闭，请在“设置 -> 通用”中打开。,"NFC is off, please turn it on in ""Settings -> General.""",NFC已關閉，請在「設置 -> 通用」中打開。,NFCがオフです。設定 -> 一般でオンにしてください。
stringTxError01,stringTxError01,请更新余额后重新发送,Please update balance and resend,請更新餘額後重新發送,残高を更新して再送信してください
stringTxError02,stringTxError02,交易已广播，请勿重复广播,"Transaction has been broadcasted, do not broadcast again",交易已廣播，請勿重複廣播,取引がブロードキャストされました。再度ブロードキャストしないでください
stringTxError03,stringTxError03,支付金额过低，请调整后重新发送！,"Payment amount is too low, please adjust and resend!",支付金額過低，請調整後重新發送！,支払い金額が低すぎます。調整して再送信してください！
stringTxError04,stringTxError04,请填写备注后重新发送,Please fill in the remark and resend,請填寫備註後重新發送,備考を記入して再送信してください
stringTxError05,stringTxError05,交易已失效，请重新发送,"Transaction has expired, please resend",交易已失效，請重新發送,取引が失効しました。再送信してください
stringTxError06,stringTxError06,发送失败，请检查资源是否充足,"Send failed, please check if resources are sufficient",發送失敗，請檢查資源是否充足,送信失敗。リソースが十分か確認してください
stringTxError07,stringTxError07,交易未成熟，请达到成熟期后发送<,Transaction is not matured yet. Please wait until it matures before sending.,交易未成熟，請達到成熟期後發送,取引が未成熟です。成熟期に達してから送信してください。
stringTxError08,stringTxError08,UTXO数量过大，请调整金额查询发送,UTXO count is too high. Adjust the amount and resend.,UTXO數量過大，請調整金額查詢發送,UTXOの数が多すぎます。金額を調整して再送信してください。
stringTxError09,stringTxError09,转账金额超出地址限额。,The transfer amount exceeds the address limit.,轉帳金額超出地址限額。,送金額がアドレスの制限を超えています。
stringTxError10,stringTxError10,请更新余额后重新发送,Please update the balance and resend.,請更新餘額後重新發送,残高を更新してから再送信してください。
stringTxError11,stringTxError11,矿工费太高，请调整矿工费重新发送！,Transaction fee is too high. Adjust the fee and resend!,礦工費太高，請調整礦工費重新發送！,手数料が高すぎます。手数料を調整して再送信してください！
stringTxError12,stringTxError12,支付金额过低，请调整后重新发送！,Payment amount is too low. Adjust and resend!,支付金額過低，請調整後重新發送！,支払金額が低すぎます。調整してから再送信してください！
stringTxError13,stringTxError13,发送失败，交易包含未成熟的币，挖矿奖励的币必须等成熟后才能发送，成熟期为100个确认,Send failed. The transaction contains immature coins. Mining reward coins must mature before they can be sent. The maturity period is 100 confirmations.,發送失敗，交易包含未成熟的幣，挖礦獎勵的幣必須等成熟後才能發送，成熟期為100個確認,送信失敗、取引には未成熟のコインが含まれています。マイニング報酬のコインは成熟するまで送信できません。成熟期間は100確認です。
stringTxError14,stringTxError14,矿工费过低，请调整矿工费重新发送。,Transaction fee is too low. Adjust the fee and resend.,礦工費過低，請調整礦工費重新發送。,手数料が低すぎます。手数料を調整して再送信してください。
stringTxError15,stringTxError15,发送失败，请检查系统时间是否正确。,Send failed. Please check if the system time is correct.,發送失敗，請檢查系統時間是否正確。,送信失敗、システム時間が正しいか確認してください。
stringTxError16,stringTxError16,发送失败，请确认金额是否正确。,Send failed. Please confirm if the amount is correct.,發送失敗，請確認金額是否正確。,送信失敗、金額が正しいか確認してください。
stringTxError17,stringTxError17,发送失败，瑞波币官方限制：账户余额不能低于20 XRP！,Send failed. Ripple official restriction: Account balance cannot be less than 20 XRP!,發送失敗，瑞波幣官方限制：帳戶餘額不能低於20 XRP！,送信失敗、リップルの公式制限：アカウント残高は20 XRPを下回ることはできません！
stringTxError18,stringTxError18,该账户尚未激活！,This account is not yet activated!,該帳戶尚未激活！,このアカウントはまだアクティベートされていません！
stringTxError19,stringTxError19,矿工费过低，请重新设置。,Transaction fee is too low. Please reset.,礦工費過低，請重新設置。,手数料が低すぎます。再設定してください。
stringTxError20,stringTxError20,请填写备注后重新发送,Please fill in the memo and resend.,請填寫備註後重新發送,メモを入力してから再送信してください。
stringTxError21,stringTxError21,该笔交易GAS过低，可能交易失败，若长时间未确认请调整GAS重新发送。,"The GAS for this transaction is too low, which may cause the transaction to fail. If it remains unconfirmed for a long time, please adjust the GAS and resend.",該筆交易GAS過低，可能交易失敗，若長時間未確認請調整GAS重新發送。,この取引のGASが低すぎると取引が失敗する可能性があります。長時間確認されない場合はGASを調整して再送信してください。
stringTxError22,stringTxError22,请等待上一笔交易完成，或提高10%以上的矿工费再进行转账,Please wait for the previous transaction to complete or increase the transaction fee by more than 10% before proceeding with the transfer.,請等待上一筆交易完成，或提高10%以上的礦工費再進行轉帳,前の取引が完了するまで待つか、手数料を10%以上上げてから送金してください。
stringTxError23,stringTxError23,GasLimit过低，请调整后重新发送,GasLimit is too low. Please adjust and resend.,GasLimit過低，請調整後重新發送,GasLimitが低すぎます。調整してから再送信してください。
stringSetting,stringSetting,设置,Settings,設置,設定
stringP2SHDes,stringP2SHDes,"兼容 \""Legacy\"" 和 \""Segwit\"" 地址类型,交易费中等,地址以\""3\""开头","Compatible with ""Legacy"" and ""Segwit"" address types, medium transaction fee, address starts with ""3"".","兼容 ""Legacy"" 和 ""Segwit"" 地址類型，交易費中等，地址以 ""3"" 開頭","""Legacy"" と ""Segwit"" アドレスタイプに対応、取引手数料は中程度、アドレスは ""3"" で始まります。"
stringP2TRDes,stringP2TRDes,"Taproot:更高的隐私性和效率,以\""bc1p\""开头","Taproot: Higher privacy and efficiency, starts with ""bc1p"".","Taproot: 更高的隱私性和效率，以 ""bc1p"" 開頭","Taproot: より高いプライバシーと効率性、""bc1p"" で始まります。"
stringP2WPKHDes,stringP2WPKHDes,"当前主流的地址类型,交易费较低,地址以\""bc1\""开头","Current mainstream address type, lower transaction fee, address starts with ""bc1"".","當前主流的地址類型，交易費較低，地址以 ""bc1"" 開頭","現在の主流アドレスタイプ、取引手数料は低め、アドレスは ""bc1"" で始まります。"
stringP2PKHDes,stringP2PKHDes,"最初的比特币地址格式,交易费较高,地址以\"" 1\""开头","Original Bitcoin address format, higher transaction fee, address starts with ""1"".","最初的比特幣地址格式，交易費較高，地址以 ""1"" 開頭","最初のビットコインアドレス形式、取引手数料は高め、アドレスは ""1"" で始まります。"
stringSignMessage,stringSignMessage,签名消息,Message Signature,簽名消息,メッセージ署名
stringMessgaeSign,stringMessgaeSign,消息签名,Sign Message,消息簽名,メッセージの署名
stringSignMethodTitle,stringSignMethodTitle,签名方法,Signature Method,簽名方法,署名方法
stringMessageContentTitle,stringMessageContentTitle,消息内容,Message Content,消息內容,メッセージ内容
stringSignTextHint,stringSignTextHint,请输入或粘贴需要签名的文本信息,Please enter or paste the text information that needs to be signed.,請輸入或粘貼需要簽名的文本信息,署名が必要なテキスト情報を入力または貼り付けてください。
stringSignTextTip,stringSignTextTip,请输入消息内容,Please enter the message content.,請輸入消息內容,メッセージ内容を入力してください。
stringUnconfirmedUtxo,stringUnconfirmedUtxo,包含未确认的UTXO资产,Contains unconfirmed UTXO assets.,包含未確認的UTXO資產,未確認のUTXO資産を含んでいます。
stringAddressPublicTitle,stringAddressPublicTitle,地址公钥,Address Public Key,地址公鑰,アドレスの公開鍵
stringUnavailableBalance,stringUnavailableBalance,不可用余额,Unavailable Balance,不可用餘額,使用できない残高
stringTotalBalance,stringTotalBalance,总资产:,Total Assets:,總資產：,総資産：
stringNotSupportedTaproot,stringNotSupportedTaproot,当前钱包不支持Taproot地址,The current wallet does not support Taproot addresses.,當前錢包不支持Taproot地址,現在のウォレットはTaprootアドレスをサポートしていません。
stringDownloadFailed,stringDownloadFailed,下载失败,Download failed,下載失敗,ダウンロードに失敗しました
stringUpgradeNow,stringUpgradeNow,立即更新,Upgrade Now,立即更新,今すぐアップグレード
stringTalkLater,stringTalkLater,稍后再说,Talk later,稍後再說,後で話しましょう
stringBackButton,stringBackButton,返回按钮,Back Button,返回按钮,戻るボタン (もどるボタン)
stringNewVersion,stringNewVersion,发现新版本,A new version is available!,發現新版本！,新しいバージョンがあります！
stringCoinbagISavailable,stringCoinbagISavailable,Coinbag有新版本了！,A new version of Coinbag is available,Coinbag有新版本了！,"Coinbagの新しいバージョンがあります！
"
stringCheckForUpdates,stringCheckForUpdates,检查更新,Check for updates,檢查更新, アップデートを確認
stringHaveLatestVersion,stringHaveLatestVersion,已经是最新版本了,You already have the latest version.,已經是最新版本了。,すでに最新バージョンです。
stringP2SHDLTCes,stringP2SHDLTCes,"当前主流的地址类型,地址以\""M\""开头","The current mainstream address type, the address starts with ""M""","目前主流的位址類型,位址以\""M\""開頭",現在主流のアドレスタイプ。アドレスは「M」で始まります
stringP2WPKHLTCDes,stringP2WPKHLTCDes,"隔离见证（SegWit）技术的地址格式，提供了更高的效率和更低的交易费用,地址以\""ltc1\""开头","The address format of Segregated Witness (SegWit) technology provides higher efficiency and lower transaction fees, the address starts with ""ltc1""","隔離見證（SegWit）技術的地址格式，提供了更高的效率和更低的交易費用,地址以\""ltc1\""開頭",Segregated Witness (SegWit) テクノロジーのアドレス形式により、効率が向上し、トランザクション手数料が低くなります。アドレスは「ltc1」で始まります。
stringP2PKHLTCDes,stringP2PKHLTCDes,"最初的莱特币地址格式,地址以\""L\""开头","The original Litecoin address format, the address starts with ""L""","最初的萊特幣地址格式,地址以\""L\""開頭",オリジナルのライトコインアドレス形式、アドレスは「L」で始まります
stringBroadcast,stringBroadcast,广播,Broadcast,廣播,放送
stringBroadcastTitle,stringBroadcastTitle,点击下面按钮广播,Click the button below to broadcast.,點擊下面的按鈕廣播,下のボタンをクリックして放送します
stringNodeTimeOut,stringNodeTimeOut,节点请求超时，请稍后重试,"Node request timed out, please try again later","節點請求超時,請稍後重試",ノード要求がタイムアウトしました。後でもう一度試してください。
stringNotAddedToAddressBook,stringNotAddedToAddressBook,"未添加到地址簿,","Not added to address book,",未添加到地址簿，,アドレス帳に追加されていません、
stringAddNow,stringAddNow,去添加,add now,去添加,追加してください
stringAddressInTheAddressBook,stringAddressInTheAddressBook,地址簿中的地址,Address in the address book,地址簿中的地址,アドレス帳の中のアドレス
stringPaste,stringPaste,粘贴,Paste,粘貼,貼り付け
stringSupportsReceivingNetwork,stringSupportsReceivingNetwork,仅支持接收@receiveInfo网络资产,Only supports receiving @receiveInfonetwork asset,僅支持接收@receiveInfo網絡資產,@receiveInfoネットワーク資産の受信のみをサポートしています
stringEosEmptyTitle,stringEosEmptyTitle,无账户名,No account name,無帳戶名,アカウント名がありません
 stringEosStake, stringEosStake,抵押资源 ,Stake,代理資源,リソースを委任する
stringEosEmptyDes,stringEosEmptyDes,由于EOS生态原因，暂不支持注册。,"Due to EOS ecosystem reasons, registration is not currently supported.",由於EOS生態原因，暫不支援註冊。,EOS の環境上の理由により、登録は当面サポートされません。
stringEosBuyRam,stringEosBuyRam,购买内存,Buy RAM,購買內存,RAMを購入する
stringEosSellRam,stringEosSellRam,卖出内存,Sell RAM,賣出內存, RAMを売却する
stringEosRefoud,stringEosRefoud,赎回资源,Refund,贖回資源,リソースを回収する
stringEosNewAccount,stringEosNewAccount,注册账户,New account,註冊賬戶, アカウントを登録する
stringEosBidname,stringEosBidname,竞拍账户,Bidname,競拍賬戶,オークションアカウント
stringEosReceiveTheRefunded,stringEosReceiveTheRefunded,赎回领取,Receive the refunded, 贖回領取,引き換え請求
stringExportAccountTitle,stringExportAccountTitle,导出账户,Export account,匯出帳戶,エクスポートアカウント
stringEosPublicKeyTitle,stringEosPublicKeyTitle,EOS 公钥,EOS public key,EOS 公鑰,EOS公開鍵
stringEosAccountTitle,stringEosAccountTitle,EOS 账户,EOS account,EOS 帳戶,EOSアカウント
stringScanCompleteTitle,stringScanCompleteTitle,扫码完成,Scan code completed,掃碼完成,コードのスキャンが完了しました
stringNORemarkTitle,stringNORemarkTitle,没有标签,No tags,沒有標籤,ラベルなし
stringCheckDetailTitle,stringCheckDetailTitle,查看详情,View details,看詳情,詳細を確認する
stringShowQRTitle,stringShowQRTitle,显示二维码,Show QR code,顯示二維碼,QRコードを表示
stringCopyAccmountTitle,stringCopyAccmountTitle,复制账户,Copy account,複製帳戶,アカウントをコピーする
stringEditRemarkTitle,stringEditRemarkTitle,编辑标签,Edit tags,編輯標籤,タグを編集する
stringEthNotSupportedTips,stringEthNotSupportedTips,以太坊已升级到 EIP155，旧版硬件钱包不再支持。请及时更换为新钱包以确保资产安全。,"Ethereum has upgraded to EIP155, and old hardware wallets are no longer supported. Please replace them with new wallets to ensure the safety of your assets.",以太坊已升級至 EIP155，舊版硬體錢包不再支援。請及時更換為新錢包以確保資產安全。,イーサリアムはEIP155にアップグレードされ、古いハードウェアウォレットはサポートされなくなりました。資産の安全を確保するために、新しいウォレットに交換してください。
stringNotSupportedChain,stringNotSupportedChain,当前硬件钱包已不再支持这条链,The current hardware wallet is no longer supported for this chain.,当前硬件钱包已不再支持这条链。,現在のハードウェアウォレットは、このチェーンをサポートしていません。
stringZecNotSupportedTips,stringZecNotSupportedTips,旧版硬件钱包不再支持ZEC链，请及时更换为新钱包以确保资产安全。,The old hardware wallet no longer supports the ZEC chain. Please replace it with a new wallet in time to ensure the safety of your assets.,舊版硬體錢包不再支援ZEC鏈，請及時更換為新錢包以確保資產安全。,古いハードウェア ウォレットは ZEC チェーンをサポートしなくなりました。資産の安全を確保するために、時間内に新しいウォレットに変更してください。
stringCPULease,stringCPULease,CPU租赁,CPU Lease,CPU 租賃,CPUリース
stringEosTxInvalid,stringEosTxInvalid,交易已失效，请重新发送,"The transaction has expired, please resend",交易已失效，請重新發送,トランザクションの有効期限が切れました。再送信してください
stringInsufficientResources,stringInsufficientResources,发送失败，请检查资源是否充足,"The sending failed, please check whether the resources are sufficient",發送失敗，請檢查資源是否足夠,送信に失敗しました。リソースが十分であるかどうかを確認してください
stringEosAccountDetialTitle,stringEosAccountDetialTitle,EOS账户详情,EOS account details,EOS帳戶詳情,EOSアカウントの詳細
stringCreateDateTitle,stringCreateDateTitle,创建时间：@date,Creation time: @date,建立時間：@date,作成時刻: @date
stringAccountRemarkTitle,stringAccountRemarkTitle,账户标签：@tag,Account tag: @tag,帳戶標籤：@tag,アカウントタグ：@tag
stringThresholdTitle,stringThresholdTitle,阈值：@value,Threshold: @value,閾值：@value,しきい値: @value
stringWeightTitle,stringWeightTitle,权重：@value,Weight: @value,權重：@value,重み: @value
stringVIPbenefits,stringVIPbenefits,VIP权益,VIP Benefits,VIP權益,VIP特典
stringVIPTip1,stringVIPTip1,查看权益引导,View Benefits Guide,查看權益指引,特典ガイドを見る
stringVIPTip2,stringVIPTip2,"欢迎尊贵的VIP用户,您的尊享权益支持在\""我的\""页面查看,请跟随指引操作","Welcome, esteemed VIP users! Your exclusive benefits can be viewed on the ""My"" page. Please follow the instructions to proceed.",歡迎尊貴的VIP用戶，您的尊享權益可在「我的」頁面查看，請跟隨指引操作。,尊敬なるVIPユーザー様、あなたの特典は「マイページ」でご確認いただけます。ガイドに従って操作してください。
stringVIPTip3,stringVIPTip3,"在""我的""页面点击""查看VIP权益""即可","Simply click ""View VIP Benefits"" on the ""My"" page.",在「我的」頁面點擊「查看VIP權益」即可。,「マイページ」で「VIP特典を見る」をクリックするだけです。
stringNotSupportEIP1559,stringNotSupportEIP1559,当前钱包不支持EIP1559交易,The current wallet does not support EIP1559 transactions.,當前錢包不支持EIP1559交易。,現在のウォレットはEIP1559取引をサポートしていません。
stringResourceManager,stringResourceManager,资源管理,Resource management,資源管理,リソース管理
stringNetDetail,stringNetDetail,带宽详情,Bandwidth details,頻寬詳情,帯域幅の詳細
stringNetManager,stringNetManager,带宽管理,Bandwidth management,頻寬管理,帯域幅管理
stringStakeGet,stringStakeGet,质押获得,Pledge to obtain,質押獲得,誓約により取得
stringOtherDelegateOwner,stringOtherDelegateOwner,他人代理给自己,Others delegate to you,他人代理給自己,他人は自分のために行動する
stringDelegateToOther,stringDelegateToOther,代理给他人,Delegate to others,代理給他人,他人のために行動する
stringFreeGet,stringFreeGet,免费赠送,Free gift,免費贈送,無料ギフト
stringDelegated,stringDelegated,已代理,Already delegated,已代理,すでに代表されている
stringCanDelegate,stringCanDelegate,可代理,Can delegate,可代理,エージェント可能
stringDelegate,stringDelegate,代理,Delegate,代理商,演技
stringReclaimTitle,stringReclaimTitle,回收,Reclaim,回收,リサイクル
stringEnergy,stringEnergy,能量,Energy,能量,エネルギー
stringBandwidth,stringBandwidth,带宽,Bandwidth,頻寬,帯域幅
stringGetBandwidth,stringGetBandwidth,获得带宽,Get bandwidth,獲得頻寬,ゲイン帯域幅
stringEnergyDetail,stringEnergyDetail,能量详情,Energy details,能量詳情,エネルギーの詳細
stringEnergyManager,stringEnergyManager,能量管理,Energy management,能量管理,エネルギー管理
stringGetEnergy,stringGetEnergy,获得能量,Get energy,獲得能量,エネルギーを得る
stringTronNoSupportedTip,stringTronNoSupportedTip,受Tron网络升级Stake 2.0影响，当前钱包不支持此功能,"Affected by the Tron network upgrade Stake 2.0, the current wallet does not support this function",受Tron網路升級Stake 2.0影響，目前錢包不支援此功能,Tron ネットワークのアップグレード Stake 2.0 の影響により、現在のウォレットはこの機能をサポートしていません
stringResourceToAddress,stringResourceToAddress,资源接收地址,Resource receiving address,資源接收地址,リソース受信アドレス
stringResourceToAddressTip,stringResourceToAddressTip,请输入资源接收地址,Please enter the resource receiving address,請輸入資源接收位址,リソース受信アドレスを入力してください
stringDelegateAmount,stringDelegateAmount,代理数量,Amount of proxies,代理數量,エージェントの数
stringDelegateAmountTip,stringDelegateAmountTip,请输入代理数量,Please enter the number of proxies,請輸入代理數量,エージェントの数を入力してください
stringCanDelegateEnergy,stringCanDelegateEnergy,可代理：@value 能量,Can be proxied: @value energy,可代理：@value 能量,エージェント可能: @value エネルギー
stringCanDelegateBandwidth,stringCanDelegateBandwidth,可代理：@value 带宽,Can be proxied: @value bandwidth,可代理：@value 頻寬,プロキシ可能: @value 帯域幅
stringUseStakeTrx,stringUseStakeTrx,* 预计占用您质押的 @value TRX,* Estimated to occupy your staked @value TRX,* 預計佔用您質押的 @value TRX,* ステークされた @value TRX を占有することが予想されます
stringDelegateTip,stringDelegateTip,• 代理后 TRX 仍在质押账户里，仅将资源使用权代理给他人,"• After proxying, TRX is still in the staked account, and only the right to use resources is delegated to others",• 代理後 TRX 仍在質押帳戶裡，僅將資源使用權代理給他人,• 委任後も、TRX はまだ質権アカウント内にあり、リソース使用権のみが他の人に委任されます。
stringOk,stringOk,确定,Ok,確定,もちろん
stringUseStakeAmount,stringUseStakeAmount,占用质押数量,Occupied pledge amount,佔用質押數量,占有誓約数量
stringOtherGetResource,stringOtherGetResource,* 预计对方获得 @value @resource,* Expected other party to receive @value @resource,* 預計對方獲得 @value @resource,* 相手が @value @resource を取得することを期待します
stringResourceMaxTip,stringResourceMaxTip,超过当前最大使用数量,Exceeds the current maximum usage amount,超過目前最大使用數量,現在の最大使用量を超えています
stringInputHintTrx,stringInputHintTrx,请输入TRX数量,Please enter the TRX amount,請輸入TRX數量,TRX数量を入力してください
stringResourceOwnAddressTips,stringResourceOwnAddressTips,资源接收地址不能为自己地址,The resource receiving address cannot be your own address,資源接收地址不能為自己地址,リソース受信アドレスを自分のアドレスにすることはできません
stringDelegateUseTrx,stringDelegateUseTrx,可占用：@value TRX,Available: @value TRX,可佔用：@value TRX,利用可能: @value TRX
share,share,分享,Share,分享,シェア
penInBrowser,penInBrowser,浏览器打开,Open in browser,瀏覽器打開,ブラウザを開く
copyLink,copyLink,复制链接,Copy link,複製鏈接,リンクをコピー
favorites,favorites,收藏,Favorites,收藏,お気に入り
switchWallet,switchWallet,切换钱包,Switch wallet,切換錢包,ウォレットを切り替える
successfullyAddedToFavorites,successfullyAddedToFavorites,收藏成功,Successfully added to favorites,收藏成功,お気に入りに追加しました
unfavorited,unfavorited,已取消收藏,Unfavorited,已取消收藏,お気に入りを解除しました
recent,recent,最近,Recent,最近,最近
viewAll,viewAll,查看全部,View all,查看全部,すべて表示
recentlyUsed,recentlyUsed,最近使用,Recently used,最近使用,最近使用
refresh,refresh,刷新,Refresh,刷新,更新
messages,messages,消息,Messages,消息,メッセージ
paymentDetails,paymentDetails,交易详情,Payment details,支付詳情,支払いの詳細
stringTronErrorMessage1,stringTronErrorMessage1,未到解冻时间，请解冻后操作,"The unfreezing time has not yet arrived, please operate after unfreezing",未到解凍時間，請解凍後操作,まだ解凍時期ではありませんので、解凍してから操作してください。
stringTronErrorMessage2,stringTronErrorMessage2,请勿转到自己的地址,Please do not transfer to your own address,請勿轉到自己的地址,自分のアドレスには転送しないでください
stringTronErrorMessage3,stringTronErrorMessage3,无可解冻资产,No unfreezing assets,無可解凍資產,凍結を解除する資産はありません
stringDelegatePopAmount,stringDelegatePopAmount,占用质押数量,Occupied pledge amount,佔用質押數量,占有誓約数量
stringDeleagteEnergy,stringDeleagteEnergy,代理能量,Delegate energy,代理能量,代理店のエネルギー
stringDelegateBandwidth,stringDelegateBandwidth,代理带宽,Delegate bandwidth,代理頻寬,プロキシ帯域幅
stringResouecePopTips,stringResouecePopTips,矿工费：包含燃烧抵扣资源矿工费和特定交易矿工费\n\n矿工费按用户支付全部资源进行估算，实际支付数量以链上数据为准,"Miner fee: includes the mining fee for burning deducted resources and the mining fee for specific transactions\n\nThe mining fee is estimated based on the total resources paid by the user, and the actual payment amount is subject to the on-chain data",礦工費：包含燃燒抵扣資源礦工費和特定交易礦工費\n\n礦工費按用戶支付全部資源進行估算，實際支付數量以鏈上數據為準,マイニング料金: 差し引かれたリソースを燃やすためのマイニング料金と、特定のトランザクションのマイニング料金が含まれます\n\nマイニング料金は、ユーザーが支払ったすべてのリソースに基づいて見積もられ、実際の支払い額はチェーン上のデータの影響を受けます。
stringSearchDapp,stringSearchDapp,搜索Dapp或输入网址,Search Dapp or enter URL,搜尋Dapp或輸入網址,Dappを検索するか、URLを入力してください
stringSearchResult,stringSearchResult,搜索结果,Search results,搜尋結果,検索結果
stringSearchHistory,stringSearchHistory,搜索历史,Search history,搜尋歷史,検索履歴
stringUnableToFind,stringUnableToFind,搜索不到该应用,Unable to find the application,找不到該應用程式,アプリケーションが見つかりません
recommend,recommend,推荐,Recommendations,推薦,おすすめ
stringLockingResource,stringLockingResource,锁定中资源,Locked Resources,锁定中资源,ロック中のリソース
stringCanReclaimTitle,stringCanReclaimTitle,可回收,Recyclable,可回收资源,リサイクル可能
stringDateTitle,stringDateTitle,时间,Time,时间,時間
stringReclaimAmount1,stringReclaimAmount1,回收数量,Recycling Quantity,回收數量,リサイクル数量
stringReclaimAmount2,stringReclaimAmount2,解除占用 TRX 数量,Unstaking TRX Quantity,解除占用 TRX 数量,TRXのステーキング解除数量
stringCanReclaimValue1,stringCanReclaimValue1,可回收：@value @resource,Recyclable: @value @resource,可回收：@value @resource,回収可能：@value @resource
stringCanReclaimValue2,stringCanReclaimValue2,可解除：@value TRX,Can be unstaked: @value TRX,可解除：@value TRX,持ち上げることができます: @value TRX
stringInputReclaimHint1,stringInputReclaimHint1,请输入回收数量,Please enter the recycling quantity,请输入回收数量,リサイクル数量を入力してください
stringInputReclaimHint2,stringInputReclaimHint2,请输入解除占用TRX数量,Please enter the TRX unstaking quantity,请输入解除占用TRX数量,TRXのステーキング解除数量を入力してください
stringInputReclaimTip1,stringInputReclaimTip1,* 预计解除占用您质押的 @value TRX,* Estimated TRX unstaked from your staking: @value,* 预计解除占用您质押的 @value TRX,* 推定ステーキング解除 TRX: @value
stringInputReclaimTip2,stringInputReclaimTip2,* 预计从对方地址回收 @value @resource,* @value @resource will be reclaimed from the delegate,* 预计从对方地址回收 @value @resource,* 相手先アドレスからの推定回収: @value @resource
stringReclaimEnergy,stringReclaimEnergy,回收能量,Recycling Energy,回收能量,リサイクルエネルギー
stringReclaimBandwidth,stringReclaimBandwidth,回收带宽,Recycling Bandwidth,回收带宽,バン_Widthのリサイクル
stringResourcePopTip1,stringResourcePopTip1,注：硬件钱包回收显示为解锁,Note: Hardware wallet recovery is displayed as unlocking,注：硬件钱包回收显示为解锁,ノート：ハードウェアウォレット回復はアンロックとして表示されます
stringResourcePopTip2,stringResourcePopTip2,注：硬件钱包代理显示为质押,Note: Hardware wallet proxy is displayed as staking,注：硬件钱包代理显示为质押,ノート：ハードウェアウォレットプロキシはステーキングとして表示されます
stringStakeTitle,stringStakeTitle,质押,Staking,質押,誓約
stringTotalAmount,stringTotalAmount,总资产,Total assets,總資產,総資産
stringTotalStakeAmount,stringTotalStakeAmount,总质押,Total stake,總質押,誓約総額
stringUnlocking,stringUnlocking,解锁中,Unlocking,解鎖中,ロックを解除する
stringCanExtract,stringCanExtract,可提取,Withdrawable,可提取,抽出可能
stringUnderstandingStaking,stringUnderstandingStaking,了解质押2.0,Learn about Staking 2.0,了解質押2.0,ステーキング 2.0 を理解する
stringStakeBandwidthTips,stringStakeBandwidthTips,• 带宽需要质押TRX获得,• Bandwidth needs to be staked with TRX,• 頻寬需要質押TRX獲得,• 帯域幅はTRXを約束することで取得する必要があります
stringStakeEnergyTips,stringStakeEnergyTips,• 能量需要质押TRX获得,• Energy needs to be staked with TRX,• 能量需要質押TRX獲得,• エネルギーはTRXをステーキングして取得する必要があります
stringUnstakeTitle,stringUnstakeTitle,解锁,Unlocked,解鎖,ロックを解除する
stringFavorited,stringFavorited,已收藏,Favorited,已收藏,収集済み (しゅうしゅうずみ)
stringEdit,stringEdit,编辑,Edit,编辑,収集済み (しゅうしゅうずみ)
stringWithraw,stringWithraw,提取,Withdraw,提取,引き出し
stringGetAmount,stringGetAmount,获得数量,Received amount,獲得數量,数量を取得する
stringStakeGetResource,stringStakeGetResource,可获取：@value @resource,Available: @value @resource,可取得：@value @resource,利用可能: @value @resource
stringStakeHint1,stringStakeHint1,输入获取资源数量,Enter the amount of resources to be received,輸入獲取資源數量,取得するリソースの量を入力します
stringStakeHint2,stringStakeHint2,输入质押数量,Enter the amount to be pledged,輸入質押數量,誓約額を入力してください
stringStakeAmount,stringStakeAmount,质押数量,Amount to be pledged,質押數量,誓約数量
stringStakeTip1,stringStakeTip1,* 预计需质押 @trx TRX，同时获得 @vote 投票权,* Expected to pledge @trx TRX and get @vote voting rights,* 預計需質押 @trx TRX，同時獲得 @vote 投票權,※@trx TRXのステーキングと@voteの議決権の取得を同時に行う予定です
stringStakeTip2,stringStakeTip2,* 预计获得 @value @resource，同时获得 @vote 投票权,* Expected to get @value @resource and get @vote voting rights,* 預計獲得 @value @resource，同時獲得 @vote 投票權,* @value @resource および @vote 議決権を取得する予定
stringStakeTip3,stringStakeTip3,• 仅支持给自己质押，质押获得的资源可随时代理给他人,"• Only supports staking for yourself, and the resources obtained by staking can be delegated to others at any time",• 僅支持給自己質押，質押所獲得的資源可隨時代理給他人,• 自分自身への誓約のみをサポートし、誓約を通じて得られたリソースはいつでも他の人に委任できます。
stringStakeTip4,stringStakeTip4,• 解锁后需等待14天时间方可提取,"• After unlocking, you need to wait 14 days before you can withdraw",• 解鎖後需等待14天時間方可提取,• ロックを解除してから撤回するには、14 日間待つ必要があります。
stringNoSearchResultsFound,stringNoSearchResultsFound,未找到搜索内容,No search results found,未找到搜尋內容,検索結果が見つかりませんでした
stringTrxCanUse,stringTrxCanUse,可用：@value TRX,Available: @value TRX,可用：@value TRX,利用可能: @value TRX
stringStakeEnergy,stringStakeEnergy,质押能量,Staking energy,質押能量,エネルギーを賭ける
stringStakeBandwidth,stringStakeBandwidth,质押带宽,Staking bandwidth,質押頻寬,保証帯域幅
stringUnstake1,stringUnstake1,解锁资产1.0,Unlock Assets 1.0,解鎖資產1.0,アセットのロックを解除 1.0
stringCanUnstakeTitle,stringCanUnstakeTitle,可解锁：@value TRX,Unlockable: @value TRX,可解鎖：@value TRX,ロック解除可能: @value TRX
stringUnstakeTip,stringUnstakeTip,• 解除TRX需要14天的等待期，解除质押的资产在14天后可取出,"• It takes 14 days to unlock TRX, and the unlocked assets can be withdrawn after 14 days",• 解除TRX需要14天的等待期，解除質押的資產在14天後可取出,• TRX を解除するには 14 日間の待機期間が必要で、質権から解除された資産は 14 日後に引き出すことができます。
stringClearAll,stringClearAll,清除全部,Clear All,清除全部,すべて削除
stringUnstakeTrx2,stringUnstakeTrx2,解锁 TRX 2.0,Unlock TRX 2.0,解鎖 TRX 2.0,TRX 2.0のロックを解除する
stringUnstakeHint,stringUnstakeHint,输入解锁数量,Enter unlock quantity,輸入解鎖數量,ロック解除数量を入力してください
stringUnstakeEnergy,stringUnstakeEnergy,解锁能量,Unlock energy,解鎖能量,エネルギーのロックを解除する
stringUnstakeBandwidth,stringUnstakeBandwidth,解锁带宽,Unlock bandwidth,解鎖頻寬,帯域幅のロックを解除する
stringUnstakeEnergy2,stringUnstakeEnergy2,解锁能量2.0,Unlock energy 2.0,解鎖能量2.0,エネルギー 2.0 のロックを解除する
stringUnstakeBandwidth2,stringUnstakeBandwidth2,解锁带宽2.0,Unlock bandwidth 2.0,解鎖頻寬2.0,帯域幅 2.0 のロックを解除する
stringDayToWithdraw,stringDayToWithdraw,@day 天后可提取,"@day days later, you can withdraw",@day 天後可提取,@day 曜日を抽出できます
stringUnlockingTrx,stringUnlockingTrx,解锁中 TRX,Unlocking TRX,解鎖中 TRX,TRXのロックを解除する
stringWithdrawTrx,stringWithdrawTrx,提取 TRX,Withdraw TRX,提取 TRX,TRXを引き出す
stringTronNoSupportedStake2,stringTronNoSupportedStake2,受Tron网络升级Stake 2.0影响，当前硬件钱包不支持质押资产2.0,"Affected by the Tron network upgrade Stake 2.0, the current hardware wallet does not support staking assets 2.0",受Tron網路升級Stake 2.0影響，目前硬體錢包不支援質押資產2.0,Tron ネットワークのアップグレード Stake 2.0 の影響により、現在のハードウェア ウォレットは Stake Asset 2.0 をサポートしていません
stringTronNoSupportedUnstake2,stringTronNoSupportedUnstake2,受Tron网络升级Stake 2.0影响，当前硬件钱包不支持解锁资产2.0,"Affected by the Tron network upgrade Stake 2.0, the current hardware wallet does not support unlocking assets 2.0",受Tron網路升級Stake 2.0影響，目前硬體錢包不支援解鎖資產2.0,Tron ネットワークのアップグレード Stake 2.0 の影響により、現在のハードウェア ウォレットはアセット 2.0 のロック解除をサポートしていません
stringStake1DetialTitle,stringStake1DetialTitle,质押详情,Stake details,質押詳情,誓約内容
stringStakeToOwnTitle,stringStakeToOwnTitle,为自己质押,Stake for yourself,為自己質押,自分自身に誓約する
stringStakeToOtherTitle,stringStakeToOtherTitle,为他人质押,Stake for others,為他人質押,他人への誓い
stingCannotAccessThisWebsite,stingCannotAccessThisWebsite,无法访问此网站,Cannot access this website,無法訪問此網站,このウェブサイトにアクセスできません
stingCannotAccessThisWebsiteVPN,stingCannotAccessThisWebsiteVPN,访问网站出错，请尝试重新加载或使用VPN,"There was an error accessing the website, please try reloading or using a VPN",訪問網站出錯，請嘗試重新加載或使用VPN,ウェブサイトへのアクセス中にエラーが発生しました。再読み込みするか、VPNを使用してください
stringRedirecting,stringRedirecting,正在跳转三方网站,Redirecting to a third-party website,正在跳轉第三方網站,第三者のウェブサイトにリダイレクトしています
stringPledgedTitle,stringPledgedTitle,已抵押,Pledged,已抵押,抵当に入れられた
stringRedemptionTitle,stringRedemptionTitle,赎回中,Redemption,贖回中,償還中
stringEosResourceTip1,stringEosResourceTip1,• 内存资源需要使用EOS购买,• Memory resources need to be purchased with EOS,• 記憶體資源需要使用EOS購買,• メモリ リソースは EOS を使用して購入する必要があります
stringEosResourceTip2,stringEosResourceTip2,• 带宽资源需要抵押EOS获取,• Bandwidth resources need to be pledged with EOS,• 頻寬資源需要抵押EOS獲取,• 帯域幅リソースは EOS を抵当にして取得する必要があります
stringAvailableBalance,stringAvailableBalance,可用余额,Available balance,可用餘額,利用可能残高
stringOnlySupportsTaproot,stringOnlySupportsTaproot,"该Dapp仅支持Taproot或隔离见证原生地址格式,请在管理里添加地址",This Dapp only supports Taproot or Segregated Witness native address formats. Please add an address in the management section.,該Dapp僅支持Taproot或隔離見證原生地址格式，請在管理裡添加地址。,このDappはTaprootまたは隔離証人のネイティブアドレス形式のみをサポートしています。管理セクションでアドレスを追加してください
stringRequestAccess,stringRequestAccess,申请访问你的钱包地址，确认将钱包地址公开给此网站吗？,Request access to your wallet address. Do you confirm to publicly disclose your wallet address to this website?,申請訪問你的錢包地址，確認將錢包地址公開給此網站嗎？,あなたのウォレットアドレスへのアクセスをリクエストします。このサイトにウォレットアドレスを公開することを確認しますか？
stringDeny,stringDeny,拒绝,Deny,拒絕,拒否
stringRequestAuthorization,stringRequestAuthorization,申请授权,Request Authorization,申請授權,承認をリクエスト
stringCurrentNetworkNotSupported,stringCurrentNetworkNotSupported,暂不支持当前网络,Current network not supported,暫不支持當前網絡,現在のネットワークはサポートされていません
stringContractCall,stringContractCall,合约调用,Contract Call,合約調用,契約呼び出し
stringAssetSecurityWarning1,stringAssetSecurityWarning1,为保障资产安全，授权前请先确认了解相关风险和后果,"To ensure asset security, please confirm that you understand the relevant risks and consequences before authorizing.",為保障資產安全，授權前請先確認了解相關風險和後果,資産の安全を確保するために、承認する前に関連するリスクと結果を理解していることを確認してください。
stringAssetSecurityWarning2,stringAssetSecurityWarning2,我已了解授权的常用诈骗手段,I understand the common scams related to authorization:,我已了解授權的常用詐騙手段：,私は承認に関する一般的な詐欺手段を理解しています：
stringAssetSecurityWarning3,stringAssetSecurityWarning3,以场外交易为由，要求扫码验资并引导授权。,Requesting QR code verification under the pretext of OTC trading and guiding authorization.,以場外交易為由，要求掃碼驗資並引導授權。,OTC取引を理由にQRコードでの資金確認を要求し、承認を促す。
stringAssetSecurityWarning4,stringAssetSecurityWarning4,以空投假币为由，要求兑换并引导授权。,Asking for a token exchange under the pretext of airdropping fake coins and guiding authorization.,以空投假幣為由，要求兌換並引導授權。,エアドロップされた偽コインを理由に交換を要求し、承認を促す。
stringAssetSecurityWarning5,stringAssetSecurityWarning5,以存币生息为由，要求兑换并引导授权,Requesting an exchange under the pretext of earning interest on deposits and guiding authorization.,以存幣生息為由，要求兌換並引導授權。,預金利息を理由に交換を要求し、承認を促す。
stringAssetSecurityWarning6,stringAssetSecurityWarning6,"进入不明Dapp网址,并被诱导授权",Entering unknown Dapp URLs and being induced to authorize.,進入不明Dapp網址，並被誘導授權。,不明なDappのURLに入り、承認を促される。
stringColdWalletAuthorizationWarning,stringColdWalletAuthorizationWarning,我已了解：一旦我的冷钱包地址授权给其他人，对方无需经过我的同意，即可转走我的钱包中已授权地址上的资产。,"I understand that once my cold wallet address is authorized to others, the other party can transfer assets from the authorized address without my consent.",我已了解：一旦我的冷錢包地址授權給其他人，對方無需經過我的同意，即可轉走我錢包中已授權地址上的資產。,私は理解しています：一度私のコールドウォレットアドレスが他の人に承認されると、相手は私の同意なしに承認されたアドレスから資産を移動できます。
stringWarning,stringWarning,警告,Warning,警告,警告
stringAuthorizeSmartContract,stringAuthorizeSmartContract,授权智能合约,Authorize Smart Contract,授權智能合約,スマートコントラクトを承認
stringAuthorizedAddress,stringAuthorizedAddress,授权地址,Authorized Address,授權地址,承認されたアドレス
stringSignTransaction,stringSignTransaction,签名交易,Sign Transaction,簽名交易,取引に署名
stringTransactionType,stringTransactionType,交易类型,Transaction Type,交易類型,取引タイプ
stringData,stringData,数据,Data,數據,データ
stringNftAmount,stringNftAmount,NFT数量,NFT quantity,NFT數量,NFT数量
stringNotCurrentlySupporte,stringNotCurrentlySupporte,暂不支持此链,This link is not currently supported.,暫不支持此鏈,このリンクは現在サポートされていません。
stringHoldValue,stringHoldValue,我持有的 @value,I hold @value,我持有的 @value,私が保持している @value
stringValuation,stringValuation,估值,Valuation,估價,評価
stringNftOverview,stringNftOverview,概览,Overview,概览,概要
stringNftActivity,stringNftActivity,交易动态,Activity,交易动态,取引のダイナミクス
stringContractAddress,stringContractAddress,合约地址,Contract Address,合約地址,契約住所
stringTokenStandards,stringTokenStandards,代币标准,Token Standard,代幣標準,トークン規格
stringNftNetwork,stringNftNetwork,网络,Chain,網路,ネットワーク
stringNftTransfer,stringNftTransfer,转移,Transfer,轉移,移行
stringNftFrom,stringNftFrom,来自 @value,from @value,來自 @value,@value から
stringNftTo,stringNftTo,去向 @value,to @value,去向 @value,宛先 @value
stringNftSale,stringNftSale,成交,Sale,成交,取引をする
stringNftMint,stringNftMint,铸造,Mint,鑄造,鋳造
stringNftBurn,stringNftBurn,销毁,Burn,銷毀,破壊する
stringNftSaleBuy,stringNftSaleBuy,购买,Buy,購買,買う
stringNftSaleSell,stringNftSaleSell,出售,Sell,出售,売る
stringNftReceiveAddress,stringNftReceiveAddress,接收地址 @value,From @value,發送地址 @value,送信アドレス @value
stringNftSendAddress,stringNftSendAddress,发送地址 @value,To @value,接收地址 @value,受信アドレス @value
stringNftContractAddress,stringNftContractAddress,合约地址 @value,Contract @value,合約地址 @value,コントラクトアドレス @value
stringQrMaxNumberTip,stringQrMaxNumberTip,二维码页面过多，当前钱包暂不支持,"There are too many QR code pages, and the current wallet does not support it.",二維碼頁面太多，當前錢包暫不支,QR コード ページが多すぎるため、現在のウォレットはそれらをサポートしていません。
stringNftSuccess,stringNftSuccess,成功,Success,成功,成功
stringTargetAddress,stringTargetAddress,目标地址,Target,目標地址,宛先アドレス
stringSendNft,stringSendNft,发送NFT,Send NFT,發送NFT,NFTを送信する
stringNftSend,stringNftSend,NFT 发送,NFT Send,NFT 發送,NFT発行
stringNftReceive,stringNftReceive,NFT 接收,NFT Receive,NFT 接收,NFT受付
stringQRScanError,stringQRScanError,二维码信息错误，请重新扫描,"Data error, please rescan",二維碼資訊錯誤，請重新掃描,QRコード情報は正しくありません。もう一度スキャンしてください
stringBindAddressNow,stringBindAddressNow,立即绑定,Bind Now,立即綁定,今すぐバインド
stringNotBoundYet,stringNotBoundYet,暂未绑定,Not Bound Yet,暫未綁定,まだバインドされていません
stringBindAddressInfo,stringBindAddressInfo,绑定主链才能添加地址,You must bind the main chain to add an address,綁定主鏈才能添加地址,アドレスを追加するには、メインチェーンをバインドする必
stringTokenAddTitle,stringTokenAddTitle,添加 ERC20,Add ERC20,新增 ERC20,ERC20を追加
stringTokenAddTip1,stringTokenAddTip1,使用硬件钱包扫码以添加Token,Use your hardware to scan to add token,使用硬體錢包掃碼以添加Token,ハードウェアウォレットを使用してQRコードをスキャンし、トークンを追加します
stringTokenPro1Tip1,stringTokenPro1Tip1,Pro 1添加步骤:,Pro 1:,Pro 1新增步驟:,Pro 1 では次の手順が追加されました。
stringTokenPro1Tip2,stringTokenPro1Tip2,1.打开硬件钱包，进入“所有资产”页面；\n2.点击右上角图标，然后点击“添加币种”；\n3.点击“添加新资产”，扫描以上二维码。,"1. Open your hardware wallet, go to \""All Assets\"" page;\n2. Click the icon in the upper right corner, then click \""Add Coins\"";\n3. Click \""Add new asset\"" button to scan the QR code above.;",1.打開硬體錢包，進入“所有資產”頁；\n2.點選右上角圖標，然後點選“新增幣種”；\n3.點選“新增資產”，掃描以上二維碼。,1. ハードウェア ウォレットを開いて、「すべての資産」ページに入ります; \n2. 右上隅のアイコンをクリックし、「通貨の追加」をクリックします。\n3. 「新しいアセットの追加」をクリックし、上記の QR コードをスキャンします。
stringTokenPro2Tip1,stringTokenPro2Tip1,Pro 2 / Pro 2+添加步骤:,Pro 2 / Pro 2+:,Pro 2 / Pro 2+新增步驟:,Pro 2/Pro 2+ で追加された手順:
stringTokenPro2Tip2,stringTokenPro2Tip2,1.打开硬件钱包，进入“我的资产”页面;\n2.点击右上角图标，进入“币种管理”页面;\n3.点击右上角加号图标，进入“添加TOKEN”页面;\n4.点击“扫码添加TOKEN”按钮，扫描以上二维码。,"1. Open your hardware wallet, go to \""My Assets\"" page;\n2. Click the icon in the upper right corner to enter the \""Coin Management\"" page;\n3. Click the plus icon in the upper right corner to enter the \""Add Token\"" page;\n4. Click \""SCAN TO ADD TOKEN\"" button to scan the QR code above.",1.打開硬體錢包，進入“我的資產”頁;\n2.點選右上角圖標，進入“幣種管理”頁面;\n3.點選右上角加號圖標，進入“新增TOKEN”頁面;\n4.點選“掃碼加入TOKEN”按鈕，掃描以上二維碼。,1. ハードウェア ウォレットを開いて、「マイ アセット」ページに入ります。\n2. 右上隅のアイコンをクリックして「通貨管理」ページに入ります。\n3. 右上隅のプラス アイコンをクリックして、「トークンの追加」ページに入ります。\n4. 「コードをスキャンしてトークンを追加」ボタンをクリックし、上記の QR コードをスキャンします。
stringTokenPro3Tip1,stringTokenPro3Tip1,Pro 3添加步骤:,Pro 3:,Pro 3新增步驟:,Pro 3 では次の手順が追加されました。
stringTokenPro3Tip2,stringTokenPro3Tip2,1.打开硬件钱包，进入“我的钱包”页面;\n2.点击右上角加号图标，进入“币种管理”页面;\n3.点击右上角加号图标，进入“添加TOKEN”页面;\n4.点击“扫码添加Token”按钮，扫描以上二维码。,"1. Open your hardware wallet, go to \""My Wallet\"" page;\n2. Click the plus icon in the upper right corner to enter the \""Coin Management\"" page;\n3. Click the plus icon in the upper right corner to enter the \""Add Token\"" page;\n4. Click \""SCAN TO ADD TOKEN\"" button to scan the QR code above.",1.打開硬體錢包，進入“我的錢包”頁;\n2.點選右上角加號圖標，進入“幣種管理”頁面;\n3.點選右上角加號圖標，進入“新增TOKEN”頁面;\n4.點選“掃碼新增Token”按鈕，掃描以上二維碼。,1. ハードウェア ウォレットを開いて、「マイ ウォレット」ページに入ります。\n2. 右上隅のプラス アイコンをクリックして、「通貨管理」ページに入ります。\n3. 右上隅のプラス アイコンをクリックして、「トークンの追加」ページに入ります。\n4. 「コードをスキャンしてトークンを追加」ボタンをクリックし、上記の QR コードをスキャンします。
stringSyncAmount,stringSyncAmount,同步余额,Sync balance,同步餘額,同期バランス
stringSyncAmountValue,stringSyncAmountValue,@value 同步余额,@value Sync balance,@value 同步餘額,@value 同期バランス
stringCurrentSyncAmount,stringCurrentSyncAmount,本次同步数额,Amount of this sync,本次同步金額,この同期の量
stringSyncAmountTip1,stringSyncAmountTip1,请用硬件钱包扫码,Please scan the code with a hardware wallet,請用硬體錢包掃碼,ハードウェアウォレットを使用してコードをスキャンしてください
stringSyncAmountTip2,stringSyncAmountTip2,每次发送交易前请在冷钱包设置合适的矿工费率，当前最佳 Gas Price 为 @value  Gwei。,Please set a suitable miner fee rate in the cold wallet before sending each transaction. The current best Gas Price is @value Gwei.,每次發送交易前請在冷錢包設定適當的礦工費率，目前最佳 Gas Price 為 @value Gwei。,各トランザクションを送信する前に、コールド ウォレットに適切なマイナー レートを設定してください。現在の最高のガス価格は @value Gwei です。
stringSyncAmountTip3,stringSyncAmountTip3,每次发送交易前请在冷钱包设置合适的矿工费率，当前最佳矿工费率为 @value BTC/KB 。,Please set a suitable miner fee rate in the cold wallet before sending each transaction. The current best miner fee rate is @value BTC/KB.,每次發送交易前請在冷錢包設定適當的礦工費率，目前最佳礦工費率為 @value BTC/KB 。,各トランザクションを送信する前に、コールド ウォレットに適切なマイナー レートを設定してください。現在の最良のマイナー レートは @value BTC/KB です。
stringP1info,stringP1info,1.打开硬件钱包，选定某币种并打开“接收”页面；”\n2.跳转到“余额”页面，点击“更新余额”，显示二维码；,"1.Open the hardware wallet, select the cryptocurrency, and go to the ""Receive"" page;\n 2. Navigate to the ""Balance"" page and click ""Update Balance"" to display the QR code;",打開硬件錢包，選定某種貨幣並打開「接收」頁面；\n 2. 跳轉到「餘額」頁面，點擊「更新餘額」，顯示二維碼；,ハードウェアウォレットを開き、暗号通貨を選択して「受信」ページに移動します;\n 2. 「残高」ページに移動し、「残高を更新」をクリックしてQRコードを表示します;
stringP2info,stringP2info,1.打开硬件钱包\n2.点击“功能”->“导出地址”\n3.选中需要绑定的地址，点击“导出”按钮，显示二维码\n4.点击下方“扫码绑定”按钮，扫描冷端二维码,"1. Open the hardware wallet;\n2. Click “Function” -> “Export Address”;\n3. Select the address to bind, click the “Export” button to display the QR code;\n4. Click the “Scan to Bind” button below to scan the cold-end QR code.",1. 打開硬件錢包\n2. 點擊“功能”->“導出地址”\n3. 選中需要綁定的地址，點擊“導出”按鈕，顯示二維碼\n4. 點擊下方“掃碼綁定”按鈕，掃描冷端二維碼,1. ハードウェアウォレットを開く；\n2. 「機能」をクリックし、「アドレスをエクスポート」を選択；\n3. バインドするアドレスを選択し、「エクスポート」ボタンをクリックしてQRコードを表示；\n4. 下の「QRコードでバインド」ボタンをクリックしてコールドエンドQRコードをスキャンします。。
stringP2Plusinfo,stringP2Plusinfo,1.打开硬件钱包\n2.点击“功能”->“监控账户”\n3.选中需要绑定的币种，点击“导出”按钮，显示二维码\n4.点击下方“扫码绑定”按钮，扫描冷端二维码,"1. Open the hardware wallet.\n 2. Click “Functions” -> “Monitor Account.”\n 3. Select the cryptocurrency to bind, click the “Export” button to display the QR code.\n 4. Click the “Scan to Bind” button below and scan the cold-end QR code.",1. 打開硬件錢包。\n 2. 點擊“功能”->“監控賬戶”。\n 3. 選中需要綁定的幣種，點擊“導出”按鈕，顯示二維碼。\n 4. 點擊下方“掃碼綁定”按鈕，掃描冷端二維碼。,1. ハードウェアウォレットを開く。\n 2. 「機能」をクリックし、「アカウントを監視」を選択します。\n 3. バインドする通貨を選択し、「エクスポート」ボタンをクリックしてQRコードを表示します。\n 4. 下の「QRコードでバインド」ボタンをクリックし、コールドエンドのQRコードをスキャンします。
stringP3info,stringP3info,"1.打开硬件钱包；\n2.打开系统菜单;然后选择“监控账户”\n3.选中需要监控的币种,点击“传至APP”按钮,可使用二维码\n4.点击下方“扫码绑定”按钮,根据提示进行绑定。","1. Open the hardware wallet;\n2. Open the system menu; then select ""Monitor Account"";\n3. Select the cryptocurrency to monitor, click the ""Send to APP"" button, and use the QR code;\n4. Click the ""Scan Code to Bind"" button below and follow the prompts to bind.",1. 打開硬件錢包；\n2. 打開系統菜單；然後選擇「監控帳戶」；\n3. 選中需要監控的幣種，點擊「傳至APP」按鈕，可使用二維碼；\n4. 點擊下方「掃碼綁定」按鈕，根據提示進行綁定。,1. ハードウェアウォレットを開く；\n2. システムメニューを開き、「監視アカウント」を選択する；\n3. 監視する暗号通貨を選択し、「APPに送る」ボタンをクリック、QRコードを使用する；\n4. 下の「スキャンしてバインド」ボタンをクリックし、指示に従ってバインドする。
stringNotBoundInfo,stringNotBoundInfo,该硬件钱包未绑定，请先绑定,This hardware wallet is not bound. Please bind it first.,該硬件錢包未綁定，請先綁定。,このハードウェアウォレットは未バインドです。まずバインドしてください。
stirngSyncBroadInfo,stirngSyncBroadInfo,同步广播结果, Sync and broadcast the results,同步廣播結果,結果を同期して送信します
stringConfirmTransaction,stringConfirmTransaction,确认交易,Confirm Transaction,確認交易,取引を確認する
stringNoSupportChain,stringNoSupportChain,由于生态原因，不再支持此链,"Due to ecological reasons, this chain is no longer supported", 由於生態原因，不再支持此鏈。,環境上の理由により、このチェーンはサポートされなくなりました。
stringNoMonitorAddress,stringNoMonitorAddress,未查询到地址，请先监控该地址,No address found. Please monitor the address first.,未查詢到地址，請先監控該地址,アドレスが見つかりません。まずそのアドレスを監視してください。
stringSwitchAddress,stringSwitchAddress,切换地址格式,Switch address format,切換地址格式,アドレス形式を切り替える
stringNotBoundInfoAddres,stringNotBoundInfoAddres,该硬件钱包的地址未绑定，请先绑定,The address of the hardware wallet is not bound. Please bind it first.,該硬件錢包的地址未綁定，請先綁定。,そのハードウェアウォレットのアドレスはバインドされていません。先にバインドしてください
stirngSelectHardwareWallet,stirngSelectHardwareWallet,选择硬件钱包,Select Hardware Wallet,選擇硬件錢包,ハードウェアウォレットを選択
stirngSwitchHardwareWallet,stirngSwitchHardwareWallet,当前选中硬件钱包和扫码中的硬件钱包不符，请切换, The currently selected hardware wallet does not match the hardware wallet scanned. Please switch.,當前選中的硬件錢包與掃描中的硬件錢包不符，請切換,現在選択されているハードウェアウォレットは、スキャンされたハードウェアウォレットと一致しません。切り替えてください。
stirngBroadcastNoData,stirngBroadcastNoData,广播数据缺失，请重试,Broadcast data is missing. Please try again.,廣播數據缺失，請重試。, ブロードキャストデータが欠落しています。再試行してください。
stringNoSupportedFeature,stringNoSupportedFeature,生态原因，不再支持此功能,"Due to ecological reasons, this feature is no longer supported.",基於生態原因，此功能不再支持,生態的な理由により、この機能はサポートされなくなりました。
stringNetworkDiagnostics,stringNetworkDiagnostics,网络诊断,Network Diagnostics,網路診斷,ネットワーク診断
stringNetworkStatus,stringNetworkStatus,请检查你的网络设置,Please check your network settings,請檢查你的網路設置,あなたのネットワーク設定を確認してください
stringXrpNotSupportedTips,stringXrpNotSupportedTips,旧版硬件钱包不再支持XRP，请及时升级新钱包以确保资产安全，有问题联系客服。,"Old hardware wallets no longer support XRP. Please upgrade to a new wallet in a timely manner to ensure your asset security. If you have any questions, please contact customer service.",舊版硬體錢包不再支持XRP，請及時升級新錢包以確保資產安全，如有問題請聯繫客服。,旧型ハードウェアウォレットはXRPのサポートを終了しました。資産の安全を確保するために、早めに新しいウォレットにアップグレードしてください。ご不明な点がございましたら、カスタマーサービスにお問い合わせください。
stringSynAddressTitle,stringSynAddressTitle,同步地址,Syn Address,同步位址,同期アドレスの手順
stringSynAddressTip,stringSynAddressTip,若需要添加新地址需在硬件钱包中操作，之后同步地址到热端，操作步骤如下：\n1.打开硬件钱包，选中Solana链，进入收款二维码页面；\n2.点击右上角图标，进入地址管理页面；\n3.选中需同步的地址，进入同步地址页面；\n4.点击下方同步地址按钮，扫描硬件钱包二维码。,"If you need to add a new address, you need to operate in the hardware wallet, and then synchronize the address to the hot end. The steps are as follows:\n1. Open the hardware wallet, select the Solana chain, and enter the payment QR code page;\n2. Click the icon in the upper right corner to enter the address management page;\n3. Select the address to be synchronized and enter the synchronization address page;\n4. Click the synchronize address button below to scan the hardware wallet QR code.",若需要新增位址需在硬體錢包中操作，之後同步位址到熱端，操作步驟如下：\n1.開啟硬體錢包，選取Solana鏈，進入收款二維碼頁面；\n2.點選右上角圖標，進入位址管理頁面；\n3.選取需同步的位址，進入同步位址頁面；\n4.點選下方同步位址按鈕，掃描硬體錢包二維碼。,"もし新しいアドレスを追加する必要がある場合は、ハードウェアウォレットで操作する必要があります。その後、アドレスをホットウォレットに同期します。操作手順は以下の通りです：\n
1.ハードウェアウォレットを開いて、Solanaチェーンを選択し、受取コードページに進んでください。\n2.右上のアイコンをクリックして、アドレス管理ページに進んでください。\n3.同期するアドレスを選択し、同期アドレスページに進んでください。\n4.下部の同期ボタンをクリックし、ハードウェアウォレットのQRコードをスキャンしてください。"
stringSynChainError,stringSynChainError,主链不匹配,Main chain does not match,主鏈不匹配,メインチェーンが一致しません
stringSynAddressError,stringSynAddressError,同步地址错误,Synchronization address error,同步地址錯誤,アドレスの同期エラー
stringBuildError,stringBuildError,构建交易失败，请重新构建,"Failed to build transaction, please rebuild",建置交易失敗，請重新建構,取引の構築に失敗しました。再度構築してください。
stringInputSolTip1,stringInputSolTip1,请输入有效的 Compute Unit Limit,Please enter a valid Compute Unit Limit,請輸入有效的 Compute Unit Limit,有効な Compute Unit Limit を入力してください 
stringInputSolTip2,stringInputSolTip2,请输入有效的 Compute Unit Price,Please enter a valid Compute Unit Price,請輸入有效的 Compute Unit Price,有効な Compute Unit Price を入力してください
stringInputSolTip3,stringInputSolTip3,请发送至少 @value 以支付租金,Please send at least @value to pay the rent,請發送至少 @value 以支付租金,少なくとも @value を支払いして家賃を支払ってください
stringSolanaTxError1,stringSolanaTxError1,交易已过期或Blockhash错误,The transaction has expired or the Blockhash is incorrect.,交易已過期或Blockhash錯誤,トランザクションの期限切れまたは Blockhash エラー
stringBnbRecieveTip,stringBnbRecieveTip,官方已不支持充币,Officially no longer supports currency deposit,官方已不支援充幣,公式がチャージをサポートしておらず
stringAddressSuccess,stringAddressSuccess,添加成功,Added successfully,添加成功,追加成功
stringSynSuccess,stringSynSuccess,同步成功,Synchronized successfully,同步成功,同期成功
stringBackgroundUpgrade,stringBackgroundUpgrade,已开始后台升级，请耐心等待！,The background upgrade has started. Please be patient!,已開始後台升級，請耐心等待！,バックグラウンドアップグレードが開始されました。しばらくお待ちください！
stringBackgroundUpgradeIng,stringBackgroundUpgradeIng,升级中...,Upgrading...,升級中...,アップグレード中...
stringContactCustomerServiceToAdd,stringContactCustomerServiceToAdd,联系客服添加,Contact support,聯絡客服添加, サポートに連絡
stringContinueTx,stringContinueTx,仍旧执行,Continue,仍然執行,まだ実行中
stringXrpAddressError,stringXrpAddressError,查询XRP接收地址开户信息失败,Wrong address format,地址格式錯誤,住所の形式が正しくありません
stringXripFeeError,stringXripFeeError,请输入有效的矿工费,Please enter a valid mining fee,請輸入有效的礦工費,有効なマイニング料金を入力してください
stringSolPriceMinTip,stringSolPriceMinTip,Compute Unit Price 低于 @value 可能导致交易失败,Compute Unit Price lower than @value may cause transaction failure,Compute Unit Price 低於 @value 可能導致交易失敗,Compute Unit Priceが@valueを下回ると取引に失敗する可能性がある
stringBabylonError,stringBabylonError,Dapp 项目存在风险，暂不支持交易,Dapp project has risks and does not currently support transactions,Dapp 專案有風險，暫不支援交易,Dappプロジェクトにはリスクがあり、取引はしばらくサポートされていない
stringWebUserName,stringWebUserName,用户名,Username,使用者名稱,ユーザー名
stringWebPassword,stringWebPassword,密码,Password,密碼,パスワード
stringWebLogin,stringWebLogin,登录,Login,登入,ログイン
stringTouchChineseTip,stringTouchChineseTip,中文输入法无安全键盘，输入助记词可能存在安全风险，慎用中文助记词,The Chinese input method does not have a secure keyboard. There may be security risks when entering the mnemonic phrase. Use Chinese mnemonic phrases with caution.,中文輸入法無安全鍵盤，輸入助記詞可能存在安全風險，慎用中文助記詞。,中国語入力法にはセキュアなキーボードがありません。助記語を入力する際にはセキュリティリスクが存在する可能性がありますので、中国語の助記語の使用には注意してください 。
stringBestFeeTitle,stringBestFeeTitle,请输入转账数额，以便计算预估矿工费,Please enter the transfer amount so that the estimated miner fee can be calculated.,請輸入轉賬數額，以便計算預估礦工費。,送金額を入力してください。推定マイナー料金を計算するためです 。
stringMinerFee,stringMinerFee,预估矿工费,Estimated Miner Fee,預估礦工費,推定マイナー料金
stringBestFee,stringBestFee,最佳矿工费,Optimal Miner Fee,最佳礦工費,最適マイナー料金
stringThemeMode,stringThemeMode,主题模式,Theme Mode,主題模式,テーマモード
stringLightMode,stringLightMode,日间模式,Light Mode,日間模式,ライトモード
stringDarkMode,stringDarkMode,夜间模式,Dark Mode,夜間模式,ダークモード
stringThemeModeText,stringThemeModeText,我们将根据您设备的系统设置调整应用的主题模式,We will adjust the app\'s theme mode based on your device\'s system settings.,我哋會根據你裝置嘅系統設定來調整應用程式嘅主題模式。,お使いのデバイスのシステム設定に基づいて、アプリのテーマモードを調整します。
stringSecurityVerification,stringSecurityVerification,安全验证,Security Verification,安全驗證,セキュリティ認証
stringOpenSecurityTitle,stringOpenSecurityTitle,启动安全验证,Start security verification,啟動安全驗證,セキュリティ認証を開始する
stringVerMethodTitle,stringVerMethodTitle,验证方式,Verification method,驗證方式,認証方法
stringVerPasswordTitle,stringVerPasswordTitle,密码,Password,密碼,パスワード
stringBiometricsTitle,stringBiometricsTitle,生物识别,Biometrics,生物識別,バイオメトリック認証
stringVerChangePswTitle,stringVerChangePswTitle,更改密码,Change password,更改密碼,パスワードの変更
stringUnlockAppTitle,stringUnlockAppTitle,解锁应用,Unlock app,解鎖應用,アプリケーションのロック解除
stringUnlockAppSub1Title,stringUnlockAppSub1Title,需要密码才能解锁应用,Require password to unlock app,需要密碼才能解鎖應用,アプリケーションをロック解除するにはパスワードが必要である
stringUnlockAppSub2Title,stringUnlockAppSub2Title,需要生物识别才能解锁应用,Require biometrics to unlock app,需要生物辨識才能解鎖應用,アプリケーションをロック解除するにはバイオメトリック認証が必要である
stringAuthTsTitle,stringAuthTsTitle,授权交易,Authorize transaction,授權交易,取引の承認
stringAuthTsSub2Title,stringAuthTsSub2Title,使用生物识别或密码授权交易或添加账户,Authorize transactions or add accounts using biometric recognition or password,使用生物識別或密碼授權交易或添加帳戶,生体認証またはパスワードを使用して取引を承認したり、アカウントを追加したりします
stringCreatePasswordTitle,stringCreatePasswordTitle,创建密码,Create password,建立密碼,パスワードを作成
stringPasswordNotEqualTip,stringPasswordNotEqualTip,两次密码不一致，请重新输入,"The two passwords do not match, please re-enter",兩次密碼不一致，請重新輸入,2つのパスワードが一致しません。再入力してください
stringOpenBiometricsTip,stringOpenBiometricsTip,是否启用生物识别，以便解锁应用或进行安全验证？,Would you like to enable biometric authentication for unlocking the app or security verification?,是否啟用生物識別，以解鎖應用程式或進行安全驗證？,"アプリのロック解除やセキュリティ認証のために、生体認証を有効にしますか？
4o"
stringInputPassword,stringInputPassword,输入密码,Enter password,輸入密碼,現在のパスワードを入力してください
stringForgetPassword,stringForgetPassword,忘记密码？,Forgot your password?,忘記密碼？,パスワードを忘れましたか？
stringPasswordErrorNumber,stringPasswordErrorNumber,"密码错误,还可以尝试@value次","Wrong password, you can try @value more times","密碼錯誤,還可以嘗試@value次",パスワードが間違っています。あと @value 回試すことができます
stringPasswordError,stringPasswordError,密码错误,Wrong password,密碼錯誤,パスワードが間違っています
stringIsOpenSecurity,stringIsOpenSecurity,为了提升安全性，我们新增了 PIN 码功能，配合指纹或人脸识别。完成设置后，您将能够更便捷地解锁应用和授权交易。\n请注意，如果您选择不设置 PIN 码，指纹或人脸识别验证功能将默认关闭,"To enhance security, we have introduced a new PIN code feature, along with support for fingerprint or facial recognition. Once the setup is complete, you will be able to unlock the app and authorize transactions more. \nPlease note that if you choose not to set a PIN code, the fingerprint or facial recognition verification feature will be disabled by default.",為了提升安全性，我們新增了 PIN 碼功能，並支援指紋或人臉識別。完成設定後，您將能夠更便捷地解鎖應用程式和授權交易。\n請注意，如果您選擇不設置 PIN 碼，指紋或人臉識別驗證功能將默認關閉。,セキュリティを向上させるために、新しいPINコード機能を追加し、指紋認識または顔認識に対応しました。設定が完了すると、アプリのロック解除や取引の承認がより便利になります。\nなお、PINコードを設定しない場合、指紋認識または顔認識の検証機能はデフォルトで無効になります。
stringPasswordErrorMax,stringPasswordErrorMax,"密码错误次数已达上限,请@value后重试",The number of incorrect password attempts has reached the upper limit. Please try again after @value,"密碼錯誤次數已達上限,請@value後重試",間違ったパスワードの数が上限に達しました。@value 後にもう一度お試しください。
stringPaswordSecond,stringPaswordSecond,@value 秒,@value seconds,@value 秒,@値 秒
stringPaswordMinute,stringPaswordMinute,@value 分,@value minutes,@value 分,@値ポイント
stringPaswordHour,stringPaswordHour,@value 小时,@value hours,@value 小時,@value 時間
stringSoloTokenOpenTip,stringSoloTokenOpenTip,SOL余额不足以支付租金,Insufficient SOL balance to pay rent,SOL餘額不足以支付租金,家賃を支払うためのSOL残高が不足しています
stringAppLockInfo,stringAppLockInfo,通过生物识别或密码来解锁应用，授权交易及添加地址时验证。,"Unlock the app using biometric recognition or password, and verify when authorizing transactions and adding addresses",通過生物識別或密碼來解鎖應用程式，在授權交易及添加地址時進行驗證。,生体認証またはパスワードを使用してアプリをロック解除し、取引を承認したり、アドレスを追加したりする際に確認します
stringChangeSuccess,stringChangeSuccess,更改成功 ,Change Success,更改成功,変化の成功
strinTalkAboutItlater,strinTalkAboutItlater,以后再说,We \'ll talk about it later,以後再說,後で話しましょう
stringCosmosTip1,stringCosmosTip1,Gas 价格需不低于 @value,Gas price must not be lower than @value,Gas 價格需不低於 @value,ガス価格は@valueより低くてはいけません
stringCosmosTip2,stringCosmosTip2,Gas 数量需大于 0,Gas quantity must be greater than 0,Gas 數量需大於 0,ガス量は0より大きくなければなりません
stringCreatePassCode,stringCreatePassCode,创建一个6位数密码,Create a 6-digit passcode,建立一個6位數密碼,6桁のパスワードを作成する
stringCreateNewPasscode,stringCreateNewPasscode,创建一个新的6位数密码,Create a new 6-digit passcode,建立一個新的6位數密碼,新しい6桁のパスワードを作成する
stringResetPasscode,stringResetPasscode,重置密码,Reset Password,重設密碼,パスワードをリセット
stringAddToken,stringAddToken,添加币种,Add Token,添加貨幣,通貨を追加
stringBabyTsTip,stringBabyTsTip,重要提示：提币到交易所（如币安）时，请务必填写 MEMO 标签！未填写或填写错误的 MEMO 将导致资产丢失，且无法找回！,"Important Notice: When withdrawing to an exchange (e.g., Binance), you MUST fill in the MEMO tag! Missing or incorrect MEMO will result in permanent loss of your assets!",重要提示：提幣到交易所（如幣安）時，請務必填寫 MEMO 標籤！未填寫或填寫錯誤的 MEMO 會導致資產遺失，且無法恢復！,"重要なお知らせ：取引所（例：Binance）への出金時、MEMOタグの入力は必須です！
MEMOの未入力または誤記入は、資産の永久消失につながります！"
stringSelectChain,stringSelectChain,选择网络,Select Chain,選擇鏈,チェーンを選択
stringNoMorePrompts,stringNoMorePrompts,以后不再提示,No more prompts in the future,以後不再提示,今後はもう表示しません
stringTronNetworkFees,stringTronNetworkFees,TRON网络费用,TRON Network Fees,TRON網絡費用,TRONネットワーク料金
stringContractInteraction,stringContractInteraction,合约交互,Contract Interaction,合約交互,契約のやり取り
stringNotFullySupportETH,stringNotFullySupportETH,当前硬件钱包版本过旧，对ETH EIP155未能完全兼容，可能导致一定机率广播失败，建议更换新设备以确保正常使用。,"Your current hardware wallet version is outdated and may not fully support ETH EIP155, which could lead to transaction failures. We recommend replacing it with a new device to ensure normal operation.",當前硬件錢包版本過舊，未能完全兼容ETH EIP155，可能導致交易廣播失敗。為確保正常使用，建議更換新設備。,現在ご使用のハードウェアウォレットのバージョンが古く、ETH EIP155に完全に対応していないため、取引が失敗する可能性があります。正常にご利用いただくために、新しいデバイスへの交換を推奨します。
strinInputMax,strinInputMax,检测到你此次交易的体积较大，可能导致签名失败。为确保转账顺利完成，建议将此次转账金额拆分为两笔或多笔进行操作。例如，如计划转账100个，可以先转账50个，再转剩余的50个，以降低签名失败的风险。,"It is detected that the size of your transaction is large, which may cause signature failure. To ensure the smooth completion of the transfer, it is recommended to split the transfer amount into two or more transactions. For example, if you plan to transfer 100, you can transfer 50 first and then transfer the remaining 50 to reduce the risk of signature failure.",偵測到你此次交易的體積較大，可能導致簽名失敗。為確保轉帳順利完成，建議將此轉帳金額分割為兩筆或多筆進行操作。例如，如計畫轉帳100個，可以先轉帳50個，再轉剩餘的50個，以降低簽章失敗的風險。,トランザクションの金額が大きいため、署名に失敗する可能性があります。送金をスムーズに完了させるため、送金金額を2回以上のトランザクションに分割することをお勧めします。例えば、100を送金する場合、最初に50を送金し、残りの50を送金することで、署名に失敗するリスクを軽減できます。
stringCanceTx,stringCanceTx,取消交易,Cancel transaction,取消交易,取引をキャンセル
stringContinueTx2,stringContinueTx2,继续交易,Continue transaction,繼續交易,取引を続行