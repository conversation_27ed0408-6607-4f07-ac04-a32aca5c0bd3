
(function () {
    var config = {
        ethereum: {
            chainId: parseInt('%3$s'),
            rpcUrl: '%2$s',
            address: '%1$s',
            isMetaMask: true,
        },
        solana: {
            cluster: "mainnet-beta",
        },
        isDebug: false
    };
    trustwallet.ethereum = new trustwallet.Provider(config);
    trustwallet.solana = new trustwallet.SolanaProvider(config);
    trustwallet.postMessage = (json) => {
        window.flutter_inappwebview.callHandler('OrangeHandler', JSON.stringify(json));
    }
    window.ethereum = trustwallet.ethereum;
})();


