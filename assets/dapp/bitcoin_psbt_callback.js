/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2025-02-26 17:15:00
 */
(function () {
    window.g_coldlar_wallet = {};
    window.g_coldlar_wallet.getAddress = (jsonStr) => {
        window.flutter_inappwebview.callHandler('_getAddress_', jsonStr)
    };
    window.g_coldlar_wallet.getPublicKeyHex = (jsonStr) => {
        window.flutter_inappwebview.callHandler('_getPublicKeyHex_', jsonStr)
    };
    window.g_coldlar_wallet.signPsbt = (psbtsHexes) => {
        window.flutter_inappwebview.callHandler('_signPsbt_', psbtsHexes)
    };
    window.g_coldlar_wallet.signMessage = (message, type) => {
        window.flutter_inappwebview.callHandler('_signMessage_', message, type)
    };
    window.g_coldlar_wallet.accountChanged = (eventName, callBack) => {
        window.flutter_inappwebview.callHandler('_accountChanged_', callBack)
    };
    window.g_coldlar_wallet.getNetwork = (jsonStr) => {
        window.flutter_inappwebview.callHandler('_getNetwork_', jsonStr)
    };
    window.btcwallet = new bitcoin_wallet.ColdlarWallet();


    window.g_coldlar_wallet.getBBNAddress = (jsonStr) => {
        window.flutter_inappwebview.callHandler('_getBBNAddress_', jsonStr)
        // Flutter 中调用 ColdlarBBNWallet.setAddress()
    };

    window.g_coldlar_wallet.getBBNPublicKeyHex = (jsonStr) => {
        window.flutter_inappwebview.callHandler('_getBBNPublicKeyHex_', jsonStr)
        // Flutter 中调用 ColdlarBBNWallet.setPublicKey()
        // 入参是公钥字符串（02 或 03 开头）
    };

    window.g_coldlar_wallet.signBBN = (message, address, display) => {
        window.flutter_inappwebview.callHandler('_signBBN_', message, address, display);
        // Flutter 中调用 ColdlarBBNWallet.setSignature()
        // 它的入参是签名字符串 base64 编码
    };
    window.bbnwallet = new bitcoin_wallet.ColdlarBBNWallet();
})();


