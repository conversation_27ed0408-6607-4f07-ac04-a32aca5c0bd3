<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Ydg-fD-yQy"/>
                        <viewControllerLayoutGuide type="bottom" id="xbc-2k-c8Z"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Vmg-g8-fc6">
                                <rect key="frame" x="125.66666666666669" y="355" width="142" height="142"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="launch_logo.png" translatesAutoresizingMaskIntoConstraints="NO" id="VMq-4y-sEV">
                                        <rect key="frame" x="0.0" y="0.0" width="142" height="142"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="142" id="JAm-Te-DkK"/>
                                            <constraint firstAttribute="height" constant="142" id="e7p-QJ-bOZ"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="Vmg-g8-fc6" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="8k4-xH-ff5"/>
                            <constraint firstItem="Vmg-g8-fc6" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="VFt-Zx-G9f"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="80.916030534351137" y="264.08450704225356"/>
        </scene>
    </scenes>
    <resources>
        <image name="launch_logo.png" width="142" height="142"/>
    </resources>
</document>
