#import "AppDelegate.h"
#import "GeneratedPluginRegistrant.h"
#import <wallet_core/CBKeyBoardManager.h>


@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  [GeneratedPluginRegistrant registerWithRegistry:self];
  // Override point for customization after application launch.
    
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (BOOL)application:(UIApplication *)application shouldAllowExtensionPointIdentifier:(NSString *)extensionPointIdentifier NS_AVAILABLE_IOS(8_0) {
    
    if ([extensionPointIdentifier isEqualToString:@"com.apple.keyboard-service"]) {
        if (CBKeyBoardManager.isSecureKeyboard) {
            return NO;
        }
    }
    return YES;
}


@end
