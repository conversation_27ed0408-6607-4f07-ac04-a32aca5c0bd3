# Coinbag Flutter App

## 项目介绍

基于 Flutter 开发的加密货币钱包应用。

## 环境要求

### 必需环境

- Flutter: 3.32.7
- Dart: 3.8.1
- Android SDK: 35
- Gradle: 8.7.3
- Ruby: 3.4.4
- CocoaPods: 1.16.2

### 应用信息

- AppId: com.coin.bag

## 技术框架

- `GetX`: 状态管理、路由管理、国际化
- `Dio`: 网络请求
- `get_storage`: SP数据存储
- `drift`: ORM数据库

## 项目结构

- `app`: 配置、主题、国际化、初始化服务
- `base`: 基类封装
- `http`: 网络请求封装
- `modules`: 业务模块（页面、控制器、模型、组件）
- `widgets`: 通用组件
- `routes`: 路由定义
- `utils`: 工具类
- `res`: 资源文件（文字、颜色、样式）
- `assets`: 静态资源（图片、字体、多语言）

## 开发配置

### VS Code 设置

用于统一代码风格的 VS Code 配置：

```json
{
    "editor.fontSize": 14,
    "[dart]": {
        "editor.formatOnSave": true,
        "editor.formatOnType": true,
        "editor.rulers": [80],
        "editor.selectionHighlight": false,
        "editor.suggest.snippetsPreventQuickSuggestions": false,
        "editor.suggestSelection": "first",
        "editor.tabCompletion": "onlySnippets",
        "editor.wordBasedSuggestions": "off",
        "editor.bracketPairColorization.enabled": true
    },
    "editor.formatOnSave": true,
    "dart.debugExternalPackageLibraries": true,
    "dart.debugSdkLibraries": false,
    "editor.stickyScroll.enabled": true,
    "dart.previewFlutterUiGuides": true,
    "files.autoSave": "onFocusChange",
    "jsonToDart.nullSafety": true,
    "dart.flutterSdkPath": "/Users/<USER>/Library/flutter"
}
```

## 开发指南

### 环境准备

1. 代理配置（如需要，端口号 9999）：

   ```bash
   export https_proxy=http://127.0.0.1:9999
   export http_proxy=http://127.0.0.1:9999
   ```

2. 环境变量配置：

   ```bash
   export LC_ALL=en_US.UTF-8
   export LANG=en_US.UTF-8
   source ~/.bash_profile
   ```

### 项目初始化

1. Flutter 依赖安装：

   ```bash
   flutter clean
   flutter pub get
   ```

2. iOS 依赖安装：

   ```bash
   cd ios
   pod install
   ```

3. Android 依赖安装：

   ```bash
   flutter clean
   flutter pub get
   cd android
   ./gradlew clean
   cd ..
   flutter build apk --debug
   ```

### 开发工具

1. 代码生成：

   ```bash
   # 生成 .g.dart 文件
   flutter pub run build_runner build
   
   # 处理文件冲突
   flutter pub run build_runner build --delete-conflicting-outputs
   ```

2. 一键脚本（在 shell 目录下）：
   - `./build.sh`: 生成代码
   - `./pub.sh`: 更新依赖
   - `./aye.sh`: 代码分析
   - `./ios.sh`: iOS 一键配置

### 国际化

1. 准备语言文件：
   - 从 [语言配置表](https://ljwpt1k6khr.sg.larksuite.com/sheets/Y3AOs242xh9WaptU5gVlwQ6Lggf?sheet=0DmOSl) 下载 CSV
   - 重命名为 language.csv 并放入 assets/locale/ 目录

2. 生成国际化代码：

   ```bash
   cd shell
   ./intl.sh
   ```

## 打包发布

### Android

1. Google Play 发布：

   ```bash
   cd shell
   ./google_play.sh
   ```

   生成的 AAB 文件上传至 [Google Play Console](https://play.google.com/console/)

2. 官网渠道：

   ```bash
   # 正式版
   ./apk.sh
   
   # 测试版（包含测试币）
   ./apk_t.sh
   ```

### iOS

1. TestFlight：

   ```bash
   # 正式版
   ./tf.sh
   
   # 测试版（包含测试币）
   ./tf_t.sh
   ```

2. App Store：

   ```bash
   ./app_store.sh
   ```

## 常见问题

### Android 构建问题

清理 Gradle 缓存：

```bash
find ~/.gradle/caches/ -name "*.lock" -type f -delete
find ./android/.gradle/ -name "*.lock" -type f -delete
rm -rf ~/.gradle ~/Android ~/.config/Google ~/.java ~/.android ~/.local/share/Google ~/.cache/Google
```

### iOS SQLite3 版本低问题

安装特定配置：

```bash
cd ios
pod install --repo-update
pod update sqlite3
gem install sqlite3 -- --with-sqlite3-dir=/opt/local
```
