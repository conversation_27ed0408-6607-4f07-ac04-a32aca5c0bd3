import 'package:coinbag/main/main_page.dart';
import 'package:coinbag/modules/wallet/connect/connect_wallet_page.dart';
import 'package:coinbag/modules/wallet/manager/pages/wallet_details_page.dart';
import 'package:coinbag/modules/wallet/manager/wallet_manager_page.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'test_utils.dart';

class WalletManagerPageTest {
  static Future<void> runTest(WidgetTester tester) async {
    Log.g("Case 5: 钱包管理测试开始...\n");

    // 0. 点击钱包首页右上角菜单按钮，进入钱包管理页
    final menuBtn = find.byWidgetPredicate((widget) {
      return widget is ImageWidget && widget.assetUrl == 'icon_titlebar_menu';
    });
    expect(menuBtn, findsOneWidget);
    await tester.tap(menuBtn);
    await tester.pumpAndSettle();

    // 等待钱包管理页完全加载
    await TestUtils.waitSeconds(tester, seconds: 2);
    expect(find.byType(WalletManagerPage), findsOneWidget);

    // 点击钱包管理页右上角加号按钮，进入连接钱包页面
    final addBtn = find.byWidgetPredicate((widget) {
      return widget is ImageWidget && widget.assetUrl == 'add_scan';
    });
    expect(addBtn, findsOneWidget);
    await tester.tap(addBtn);
    await tester.pumpAndSettle();

    // 断言进入连接钱包页面
    expect(find.byType(ConnectWalletPage), findsOneWidget);

    // 返回钱包管理页
    await TestUtils.goBackToHome(tester);
    await tester.pumpAndSettle();

    await TestUtils.waitSeconds(tester, seconds: 2);
    await tester.pumpAndSettle();

    // 2. 点击第一项的详情按钮（_moreInfo）
    final moreInfoBtn = find
        .descendant(
          of: find.byKey(const Key('wallet_item_0')),
          matching: find.byType(GestureDetector),
        )
        .last;
    await tester.tap(moreInfoBtn);
    await tester.pumpAndSettle();

    // 3. 断言进入详情页
    expect(find.byType(WalletDetailsPage), findsOneWidget);
    await TestUtils.waitSeconds(tester, seconds: 1);

    // 4. 返回钱包管理页
    await TestUtils.goBackToHome(tester);
    expect(find.byType(WalletManagerPage), findsOneWidget);

    // 5. 再次点击第一项整体（切换钱包）
    await tester.tap(find.byKey(const Key('wallet_item_0')));
    await tester.pumpAndSettle();

    // 6. 断言跳转到主页面（或钱包首页）
    expect(find.byType(MainPage), findsOneWidget);

    await tester.pumpAndSettle();

    Log.g("Case 5: 钱包管理测试完成\n");
  }
}
