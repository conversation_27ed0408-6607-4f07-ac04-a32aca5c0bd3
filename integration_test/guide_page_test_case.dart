import 'package:coinbag/modules/splash/page/guide_page.dart';
import 'package:coinbag/res/strings.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'test_utils.dart';

class GuidePageTest {
  static Future<void> runTest(WidgetTester tester) async {
    Log.g("Case 2: 引导页测试开始...\n");

    await TestUtils.waitSeconds(tester, seconds: 3);
    await tester.pumpAndSettle();
    expect(find.byType(GuidePage), findsOneWidget);

    final experienceBtn = find.widgetWithText(
        ElevatedButton, ID.stringGuideimmediatelyExperience.tr);
    if (experienceBtn.evaluate().isNotEmpty) {
      await tester.tap(experienceBtn);
      await tester.pumpAndSettle();
      Log.p("点击了立即体验按钮 \n");
    }
    await TestUtils.waitSeconds(tester);
    Log.g("Case 2: 引导页测试完成\n");
  }
}
