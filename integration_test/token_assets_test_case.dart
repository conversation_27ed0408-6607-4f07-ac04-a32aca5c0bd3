import 'package:coinbag/modules/wallet/assets/token_assets_page.dart';
import 'package:coinbag/res/strings.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'test_utils.dart';

class TokenAssetsTest {
  static Future<void> runTest(WidgetTester tester) async {
    Log.g("Case 6: 币种详情测试开始...\n");
    expect(find.byType(TokenAssetsPage), findsOneWidget);
    Log.p("正在加载交易记录...");

    // 使用公共方法等待骨架屏消失
    await TestUtils.waitForSkeletonGone(tester);
    await TestUtils.waitSeconds(tester);

    Log.p("点击了质押...");
    final stakeKey = Key('token_assets_action_item_${ID.stringStakeTitle.tr}');
    final stakeItem = find.byKey(stakeKey);
    expect(stakeItem, findsOneWidget);
    await tester.tap(stakeItem);
    await tester.pumpAndSettle();
    await TestUtils.waitSeconds(tester);
    await TestUtils.goBackToHome(tester);
    Log.g("Case 6: 币种详情测试完成\n");
  }
}
