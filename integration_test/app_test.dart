import 'package:coinbag/main.test.dart' as app;
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'guide_page_test_case.dart';
import 'send_page_test_case.dart';
import 'splash_screen_test_case.dart';
import 'token_assets_test_case.dart';
import 'wallet_page_test_case.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets('正在自动化测试...', (WidgetTester tester) async {
    app.main();
    await tester.pumpAndSettle();
    await SplashScreenTest.runTest(tester);
    await GuidePageTest.runTest(tester);
    await WalletPageTest.runTest(tester);
    await TokenAssetsTest.runTest(tester);
    await SendPageTest.runTest(tester);
    Log.g("所有测试用例执行完成！");
  });
}
