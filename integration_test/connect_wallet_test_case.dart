import 'package:coinbag/modules/wallet/connect/connect_wallet_page.dart';
import 'package:coinbag/modules/wallet/connect/pages/connect_success_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

class ConnectWaleltPageTest {
  static Future<void> runTest(WidgetTester tester) async {
    Log.g("Case 4: 连接钱包页面测试开始...\n");

    expect(find.byType(ConnectWalletPage), findsOneWidget);
    Log.p("进入连接钱包页面..\n");
    Log.p("点击了二维码连接...\n");
    final connectKey = Key('connect_wallet_action_item_qr');
    final stakeItem = find.byKey(connectKey);
    expect(stakeItem, findsOneWidget);
    await tester.tap(stakeItem);
    await tester.pumpAndSettle();

    Log.g("连接硬件钱包成功...\n");

    expect(find.byType(ConnectSuccessfullyPage), findsOneWidget);
    final connectSuccessBtn =
        find.widgetWithText(OutlinedButton, ID.openWallet.tr).first;
    expect(connectSuccessBtn, findsOneWidget,
        reason: '未找到 connectSuccessBtn，绑定钱包失败');
    Log.p("点击了绑定钱包成功\n");
    await tester.tap(connectSuccessBtn);
    await tester.pumpAndSettle();

    Log.g("Case 4: 连接钱包页面测试完成\n");
  }
}
