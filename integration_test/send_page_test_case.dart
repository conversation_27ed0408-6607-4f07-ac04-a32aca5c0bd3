/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-21 14:15:10
 */
import 'package:coinbag/modules/wallet/qrcode/wallet_qr_code_page.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';

import 'test_utils.dart';

class SendPageTest {
  static Future<void> runTest(WidgetTester tester) async {
    Log.g("Case 7: 发送页面测试开始...\n");
    await TestUtils.waitSeconds(tester);
    await TestUtils.goBackToHome(tester);
    await TestUtils.waitSeconds(tester);

    Log.p("滑动到币种列表顶部\n");
    await TestUtils.scrollPage(tester, offset: 2000);
    await TestUtils.waitSeconds(tester, seconds: 2);

    final trxKey = Key('walelt_token_item_ETH');
    final trxItem = find.byKey(trxKey);
    expect(trxItem, findsOneWidget);
    Log.p("点击ETH选项,进入币种详情");
    await tester.tap(trxItem);
    await tester.pumpAndSettle();

    Log.p("正在加载交易记录...");

    // 使用公共方法等待骨架屏消失
    await TestUtils.waitForSkeletonGone(tester);
    await TestUtils.waitSeconds(tester);

    Log.p("点击了发送..");
    final stakeKey = Key('token_assets_action_item_${ID.send.tr}');
    final stakeItem = find.byKey(stakeKey);
    expect(stakeItem, findsOneWidget);
    await tester.tap(stakeItem);
    await tester.pumpAndSettle();

    Log.p("转账设置接收地址和转账数量");
    await tester.pumpAndSettle(Duration(seconds: 2));
    final addressTextWidget = find.byKey(Key('send_recieve_address'));
    final amountTextWidget = find.byKey(Key('send_recieve_amount'));

    expect(addressTextWidget, findsOneWidget);
    expect(amountTextWidget, findsOneWidget);

    SendController controller = Get.find<SendController>();

    await setupTextValue(
      tester: tester,
      controller: controller,
      addressTextWidget: addressTextWidget,
      amountTextWidget: amountTextWidget,
    );

    await TestUtils.verifyToastExists(tester);

    /// 收起键盘
    KeyboardUtils.hideKeyboardNoContext();
    await TestUtils.waitSeconds(tester);

    final buttonFinder = find.widgetWithText(ButtonWidget, ID.sendButton.tr);

    if (buttonFinder.evaluate().isEmpty) {
      Log.p("未找到按钮: ${ID.sendButton.tr}");
    } else {
      final button = tester.widget(buttonFinder) as ButtonWidget;
      if (button.buttonStatus != ButtonStatus.enable) {
        Log.p("${ID.sendButton.tr} button 不可点击");
        await TestUtils.goBackToHome(tester);
        Log.g("Case 7: 发送页面测试完成\n");
        return;
      }
    }

    await tester.tap(buttonFinder);

    await tester.pumpAndSettle();

    bool isTost = await TestUtils.verifyToastExists(tester);
    if (isTost) {
      await TestUtils.goBackToHome(tester);
      Log.g("Case 7: 发送页面测试完成\n");
      return;
    }

    await TestUtils.waitSeconds(tester);

    await TestUtils.tapButtonByText(tester, ID.stringConfirm.tr);

    await TestUtils.waitSeconds(tester);

    TestUtils.verifyPageExists(tester, WalletQRCodePage);

    await TestUtils.goBackToHome(tester);
    Log.g("Case 7: 发送页面测试完成\n");
  }

  static Future<void> setupTextValue({
    required WidgetTester tester,
    required SendController controller,
    required Finder addressTextWidget,
    required Finder amountTextWidget,
  }) async {
    final coinType = controller.coinType;
    String address = '';
    String amount = coinType.dust;
    if (coinType is EthereumChain) {
      address = controller.addressModel?.address ?? '';
    } else if (coinType is BitcoinChain) {
      address =
          '**************************************************************';
    }

    await tester.enterText(addressTextWidget, address);
    await tester.enterText(amountTextWidget, amount);
  }
}
