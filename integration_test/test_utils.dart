import 'package:coinbag/modules/wallet/wallet_page.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

/// 测试工具类，包含常用的测试方法
class TestUtils {
  /// 等待指定秒数，默认2秒
  static Future<void> waitSeconds(WidgetTester tester,
      {int seconds = 2}) async {
    Log.p("等待 $seconds 秒...");
    await tester.pump(Duration(seconds: seconds));
    await tester.pumpAndSettle();
  }

  /// 等待加载完成（等待loading消失）
  static Future<bool> waitForLoading(WidgetTester tester,
      {String loadingKey = 'wallet_loading', int timeoutSeconds = 30}) async {
    Log.p("等待加载完成...");
    bool loadingGone = false;
    for (int i = 0; i < timeoutSeconds; i++) {
      await tester.pump(const Duration(seconds: 1));
      if (find.byKey(Key(loadingKey)).evaluate().isEmpty) {
        loadingGone = true;
        break;
      }
    }
    if (loadingGone) {
      Log.p("加载完成");
    } else {
      Log.p("加载超时");
    }
    return loadingGone;
  }

  /// 等待骨架屏消失
  static Future<bool> waitForSkeletonGone(WidgetTester tester,
      {int timeoutSeconds = 30}) async {
    Log.p("等待骨架屏消失...");
    bool skeletonGone = false;
    for (int i = 0; i < timeoutSeconds; i++) {
      await tester.pump(const Duration(seconds: 1));
      if (find.byType(CupertinoActivityIndicator).evaluate().isEmpty) {
        skeletonGone = true;
        break;
      }
    }
    if (skeletonGone) {
      Log.p("骨架屏已消失");
    } else {
      Log.p("骨架屏消失超时");
    }
    return skeletonGone;
  }

  /// 回到首页（通过返回按钮）
  static Future<void> goBackToHome(WidgetTester tester) async {
    Log.p("回到首页...");
    try {
      // 尝试点击返回按钮
      final backButton = find.byIcon(Icons.arrow_back);
      if (backButton.evaluate().isNotEmpty) {
        await tester.tap(backButton);
        await tester.pumpAndSettle();
        Log.p("通过返回按钮回到首页");
      } else {
        // 如果没有返回按钮，使用Get.back()
        Get.back();
        await tester.pumpAndSettle();
        Log.p("通过Get.back()回到首页");
      }
    } catch (e) {
      Log.p("回到首页失败: $e");
    }
  }

  /// 重新启动应用
  static Future<void> restartApp(WidgetTester tester) async {
    Log.p("重新启动应用...");
    // 重新调用main函数
    await tester.pumpAndSettle();
    Log.p("应用已重新启动");
  }

  /// 点击按钮（带文本查找）
  static Future<void> tapButtonByText(WidgetTester tester, String text) async {
    Log.p("点击按钮: $text");
    final button = find.widgetWithText(ElevatedButton, text);
    if (button.evaluate().isNotEmpty) {
      await tester.tap(button);
      await tester.pumpAndSettle();
      Log.p("成功点击按钮: $text");
    } else {
      Log.p("未找到按钮: $text");
    }
  }

  /// 点击带Key的Widget
  static Future<void> tapWidgetByKey(WidgetTester tester, String key) async {
    final widget = find.byKey(Key(key));
    if (widget.evaluate().isNotEmpty) {
      await tester.tap(widget);
      await tester.pumpAndSettle();
    }
  }

  /// 滑动页面
  static Future<void> scrollPage(WidgetTester tester,
      {double offset = -300}) async {
    Log.p("滑动页面，偏移量: $offset");
    final scrollViewFinder = find.byType(CustomScrollView);
    if (scrollViewFinder.evaluate().isNotEmpty) {
      await tester.drag(scrollViewFinder, Offset(0, offset));
      await tester.pumpAndSettle();
      Log.p("页面滑动完成");
    } else {
      Log.p("未找到可滑动的页面");
    }
  }

  /// 验证页面是否存在
  static bool verifyPageExists(WidgetTester tester, Type pageType) {
    final page = find.byType(pageType);
    bool exists = page.evaluate().isNotEmpty;
    Log.p("页面 ${pageType.toString()} 存在: $exists");
    return exists;
  }

  /// 等待余额数据加载
  static Future<bool> waitForBalanceData(WidgetTester tester,
      {int timeoutSeconds = 30}) async {
    Log.p("等待余额数据加载...");
    bool hasBalance = false;
    for (int i = 0; i < timeoutSeconds; i++) {
      await tester.pump(const Duration(seconds: 1));
      final allTextWidgets = find.byType(Text);
      for (var element in allTextWidgets.evaluate()) {
        final widget = element.widget as Text;
        if (widget.data != null &&
            widget.data!.trim().isNotEmpty &&
            widget.data != '--') {
          hasBalance = true;
          break;
        }
      }
      if (hasBalance) break;
    }
    if (hasBalance) {
      Log.p("余额数据加载完成");
    } else {
      Log.p("余额数据加载超时");
    }
    return hasBalance;
  }

  /// 检查是否在钱包首页
  static bool isOnWalletPage(WidgetTester tester) {
    return verifyPageExists(tester, WalletPage);
  }

  /// 等待并验证页面跳转
  static Future<bool> waitForPageNavigation(
      WidgetTester tester, Type targetPage,
      {int timeoutSeconds = 10}) async {
    Log.p("等待跳转到页面: ${targetPage.toString()}");
    for (int i = 0; i < timeoutSeconds; i++) {
      await tester.pump(const Duration(seconds: 1));
      if (find.byType(targetPage).evaluate().isNotEmpty) {
        Log.p("成功跳转到页面: ${targetPage.toString()}");
        return true;
      }
    }
    Log.p("页面跳转超时: ${targetPage.toString()}");
    return false;
  }

  /// 打印当前页面信息
  static void printCurrentPageInfo(WidgetTester tester) {
    Log.p("当前页面信息:");
    final allWidgets = find.byType(Widget);
    for (var element in allWidgets.evaluate().take(5)) {
      Log.p("  - ${element.widget.runtimeType}");
    }
  }

  /// 验证当前是否有Toast
  static Future<bool> verifyToastExists(WidgetTester tester) async {
    await tester.pump();

    final toastFinder = find.byKey(Key('coinbag_toast'));

    if (toastFinder.evaluate().isEmpty) return false;

    final test = tester.widget(toastFinder) as Text;

    Log.t('toast_message: ${test.textSpan?.toPlainText()}');

    return true;
  }
}
