import 'package:coinbag/modules/wallet/wallet_page.dart';
import 'package:coinbag/res/strings.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'connect_wallet_test_case.dart';
import 'test_utils.dart';
import 'wallet_manager_page_test_case.dart';

class WalletPageTest {
  static Future<void> runTest(WidgetTester tester) async {
    Log.g("Case 3: 钱包首页测试开始...\n");
    expect(find.byType(WalletPage), findsOneWidget);
    final connectBtn =
        find.widgetWithText(ElevatedButton, ID.bindHardwareWallet.tr).first;
    await tester.tap(connectBtn);
    await tester.pumpAndSettle();
    Log.p("点击了连接硬件钱包\n");

    await ConnectWaleltPageTest.runTest(tester);

    // 使用公共方法等待加载完成
    await TestUtils.waitForLoading(tester);

    // 使用公共方法等待余额数据
    await TestUtils.waitForBalanceData(tester);

    await WalletManagerPageTest.runTest(tester);

    Log.p("点击钱包隐藏余额\n");

    final hideImage = find.byWidgetPredicate((widget) {
      return widget is ImageWidget &&
          (widget.assetUrl == 'icon_eye_close' ||
              widget.assetUrl == 'icon_eye_open');
    });
    expect(hideImage, findsOneWidget);
    await tester.tap(hideImage);
    await tester.pumpAndSettle();

    await TestUtils.waitSeconds(tester);

    expect(hideImage, findsOneWidget);
    await tester.tap(hideImage);
    await tester.pumpAndSettle();

    Log.p("钱包首页三项测试\n");
    final actionTitles = [ID.receive.tr, ID.send.tr, ID.activity.tr];
    for (final title in actionTitles) {
      final key = 'walelt_home_action_item_$title';
      final action = find.byKey(Key(key));
      expect(action, findsOneWidget);
      await tester.tap(action);
      await tester.pumpAndSettle();
      await TestUtils.waitSeconds(tester, seconds: 2);
      await TestUtils.goBackToHome(tester);
      await tester.pumpAndSettle();
      expect(find.byType(WalletPage), findsOneWidget);
    }

    final scrollViewFinder = find.byType(CustomScrollView);
    expect(scrollViewFinder, findsOneWidget);
    Log.p("滑动币种列表...\n");

    // 使用公共方法滑动页面
    await TestUtils.scrollPage(tester, offset: -2000);
    await TestUtils.waitSeconds(tester);
    await TestUtils.scrollPage(tester, offset: 2000);

    await TestUtils.scrollPage(tester, offset: -300);
    final trxKey = Key('walelt_token_item_TRX');
    final trxItem = find.byKey(trxKey);
    expect(trxItem, findsOneWidget);
    Log.p("点击了币种列表");
    await tester.tap(trxItem);
    await tester.pumpAndSettle();
    Log.g("Case 3: 钱包首页测试完成\n");
  }
}
