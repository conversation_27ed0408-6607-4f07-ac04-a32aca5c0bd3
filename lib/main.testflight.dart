/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-08-21 09:23:49
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/main.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

/// ios TestFlight入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  AppConfig.appConfigType = AppConfigType.testFlight;
  AppConfig.instance.setApiConfig();
  startApp();
}
