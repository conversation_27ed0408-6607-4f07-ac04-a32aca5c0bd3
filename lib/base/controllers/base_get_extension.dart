/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2025-04-14 09:59:46
 */
import 'dart:convert';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/modules/profile/security/model/security_setting_model.dart';
import 'package:coinbag/modules/profile/upgrade/upgrade_dialog.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/connect/connect_wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/permissions_service.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/dialog/alert_dialog.dart';
import 'package:coinbag/widgets/dialog/bottom_sheet_dialog.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wallet_core/chain/binance/bnb.dart';
import 'package:wallet_core/chain/bitcoin/bch.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/wallet/wallet.dart';
import 'package:wallet_core/wallet_core.dart';

/// @description :getx 扩展函数
extension GetExtension on GetInterface {
  // 全局AppController
  AppController get appController => Get.find<AppController>();

  // 全局PermissionsService
  PermissionsService get permissionsService => Get.find<PermissionsService>();

// 全局WalletCore
  WalletCore get walletCore => Get.find<WalletCore>();

  /// 数据库
  AppDatabase get database => Get.find<AppDatabase>();

  /// 连接钱包
  ConnectWalletController get connectWallet =>
      Get.find<ConnectWalletController>();

  /// 首次启动
  bool isFirstLaunch() {
    return StorageManager.getValue(
          key: StorageKey.fristLaunch,
        ) ??
        true;
  }

  /// 启动是否初始化币种
  bool isInitCoinBase() {
    //修改如果新增链 这里版本initCoinBaseV4 加1
    return StorageManager.getValue(
          key: StorageKey.initCoinBaseV4,
        ) ??
        true;
  }

  /// 是否展示VIP引导
  bool isShowVIPGuide() {
    return StorageManager.getValue(
          key: StorageKey.isShowVIPGuide,
        ) ??
        false;
  }

  //String判断是否空或者空字符
  bool isEmptyString(String? str) {
    if (str == null) return true;
    bool result = ObjectUtil.isEmptyString(str);
    if (!result) {
      if (str == 'null') return true;
    }
    return result;
  }

  /// 是否开启了安全验证
  bool isSecurity() {
    final securityModel = SecuritySettingModel.init();
    return securityModel.isOpen;
  }

  /// 是否开启授权交易
  bool isAuthorizeTransaction() {
    return isSecurity() &&
        (StorageManager.getValue(
              key: StorageKey.authorizeTransaction,
            ) ??
            false);
  }

  bool isTronResoueceResponseDataValid(dynamic data,
      {bool isShowToast = false}) {
    if (data is Map) {
      if (data.containsKey(APIConstant.error) || data.containsKey('Error')) {
        String json = jsonEncode(data);
        String message = '';
        if (json.contains('not time to unfreeze')) {
          message = ID.stringTronErrorMessage1.tr;
        } else if (json.contains('cannot transfer trx to yourself')) {
          message = ID.stringTronErrorMessage2.tr;
        } else if (json.contains('no frozenBalance')) {
          message = ID.stringTronErrorMessage3.tr;
        } else {
          if (data.containsKey(APIConstant.error)) {
            message = data[APIConstant.error] ?? '';
          } else {
            message = data['Error'] ?? '';
          }
        }
        if (isShowToast) {
          Get.showToast(message, toastMode: ToastMode.failed);
        }
        return false;
      }
    }
    return true;
  }

  /// 验证Response是否有效
  bool isResponseDataValid(dynamic data, {bool isShowToast = false}) {
    if (data is Map) {
      if (data.containsKey(APIConstant.error)) {
        final error = data[APIConstant.error];
        if (error is String) {
          if (error.contains(':')) {
            List<String> list = error.split(':');
            if (isShowToast) {
              Get.showToast(list.last, toastMode: ToastMode.failed);
            }
          }
        } else {
          if (isShowToast) {
            String msg = '';
            try {
              msg = jsonEncode(error);
            } catch (_) {}
            Get.showToast(msg, toastMode: ToastMode.failed);
          }
        }
        return false;
      }
    }
    return true;
  }

  //Returns true  String or List or Map is empty.
  bool isEmpty(Object? object) {
    return ObjectUtil.isEmpty(object);
  }

  //底部Button安全距离
  double getSafetyBottomPadding() {
    return Get.mediaQuery.padding.bottom > 0
        ? Get.mediaQuery.padding.bottom
        : Get.setHeight(16);
  }

  //适配大小屏幕 等宽图片
  double setImageSize(double size) {
    return size.h;
  }

  //适配大小屏幕 Padding
  double setPaddingSize(double size) {
    return size.h;
  }

  //适配大小屏幕 宽度
  double setWidth(double width) {
    return width.w;
  }

  //适配大小屏幕 高度
  double setHeight(double height) {
    return height.h;
  }

  //适配大小屏幕 弧度
  double setRadius(double radius) {
    return radius.r;
  }

  //适配大小屏幕 字体
  double setFontSize(double fontSize) {
    return fontSize.sp;
  }

  //是否平板
  bool isTablet() {
    bool isTab = false;
    var shortestSide = Get.size.shortestSide;
    if (shortestSide > 600) {
      isTab = true;
    } else {
      isTab = false;
    }
    return isTab;
  }

  //设置字体
  String setFontFamily() {
    return AppFonts.fontFamilyPingFang;
  }

  //设置纯数字英文字体
  String setNumberFontFamily() {
    return AppFonts.fontFamilyManrope;
  }

  //获取计价单位
  String getUnit() {
    return CurrencyController.getCurrencyUnit();
  }

  void showToast(
    String? msg, {
    bool isShortToast = true,
    ToastMode toastMode = ToastMode.normal,
  }) {
    if (GetUtils.isNull(msg)) return;
    showToastMessage(msg, isShortToast: isShortToast, toastMode: toastMode);
  }

  ///隐藏dialog
  void dismissDialog() {
    if (Get.isDialogOpen != null && Get.isDialogOpen!) {
      Get.back();
    }
  }

  ///跳转扫码
  Future<String> toScanner({dynamic arguments}) async {
    if (await permissionsService.requestCameraPermission()) {
      final result =
          await Get.toNamed(AppRoutes.scanPage, arguments: arguments)!;
      if (!Get.isEmptyString(result)) {
        return result;
      }
    }
    return "";
  }

  ///跳转App Web浏览器
  void toWeb({
    required String? url,
    String? title,
    bool showTitle = true,
  }) {
    Get.toNamed(AppRoutes.webPage, arguments: {
      GetArgumentsKey.url: url,
      GetArgumentsKey.title: title,
      GetArgumentsKey.showTitle: showTitle,
    });
  }

  ///跳转到Dapp浏览器
  void openDapp({
    String? url,
    DappModels? dappModels,
    WalletModel? walletModel,
    Wallet? wallet,
    bool isOpenDappBrowserFromSearch = false,
  }) {
    Get.toNamed(AppRoutes.webPage, arguments: {
      GetArgumentsKey.url: url,
      GetArgumentsKey.dappModel: dappModels,
      GetArgumentsKey.isOpenDappBrowser: true,
      GetArgumentsKey.walletModel: walletModel,
      GetArgumentsKey.wallet: wallet,
      GetArgumentsKey.isOpenDappBrowserFromSearch: isOpenDappBrowserFromSearch
    });
  }

  ///跳转接收
  Future toReceive({String? address, CoinModel? coinModel}) async {
    await Get.toNamed(AppRoutes.receivePage, arguments: {
      GetArgumentsKey.address: address,
      GetArgumentsKey.coinModel: coinModel
    });
  }

  ///跳转发送
  Future toSend({
    TokenModel? tokenModel,
    AddressModel? addressModel,
    CoinModel? coinModel,
    WalletAction? action,
  }) async {
    await Get.toNamed(AppRoutes.sendPage, arguments: {
      GetArgumentsKey.addressModel: addressModel,
      GetArgumentsKey.tokenModel: tokenModel,
      GetArgumentsKey.coinModel: coinModel,
      GetArgumentsKey.action: action,
    });
  }

  ///跳转币种详情
  Future toAssets({
    TokenModel? tokenModel,
    AddressModel? addressModel,
    CoinModel? coinModel,
    required WalletModel walletModel,
  }) async {
    await Get.toNamed(AppRoutes.tokenAssetsPage, arguments: {
      GetArgumentsKey.addressModel: addressModel,
      GetArgumentsKey.tokenModel: tokenModel,
      GetArgumentsKey.coinModel: coinModel,
      GetArgumentsKey.walletModel: walletModel,
    });
  }

  /// 同步余额
  Future<void> toSyncBalance({
    required String walletId,
    required String deviceId,
    required String address,
    required String chain,
    required int? batchId,
    bool isP1P2 = false,
    bool isFromScan = false,
    String? contractAddress,
  }) async {
    CoinModel? coinModel = await Get.database.coinDao
        .getCoinModel(chain: chain, contract: contractAddress ?? '');

    WalletModel? walletModel = await Get.database.walletDao
        .findWalletsByWalletWalletOrDeviceId(walletId, deviceId);

    if (walletModel == null && !isP1P2) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }

    if (coinModel!.isToken!) {
      address = address.toLowerCase();
    }

    AddressModel? addressModel = await Get.database.addressDao.getAddressModel(
      walletId: walletId,
      deviceId: deviceId,
      chain: chain,
      address: address,
    );

    ///如果是比特币现金 地址查不到，切换一下新旧地址格式在查询一次
    if (addressModel == null && chain == BitcoinCashChain.get.chain) {
      String? converAddres =
          await Get.walletCore.convertBitcoinCashAddress(address);
      if (converAddres!.isEmpty) {
        Get.showToast(ID.stringNoMonitorAddress.tr);

        return;
      }
      addressModel = await Get.database.addressDao.getAddressModel(
        walletId: walletId,
        deviceId: deviceId,
        chain: chain,
        address: converAddres,
      );
    }

    if (addressModel == null) {
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNoMonitorAddress.tr);

      return;
    }

    TokenModel? tokenModel;
    if (coinModel.isToken == true) {
      tokenModel = await Get.database.tokenDao.getTokenModel(
        walletId: walletId,
        chain: chain,
        contract: contractAddress!,
        address: address,
      );
    }

    if (isFromScan == true) {
      Get.offAndToNamed(AppRoutes.syncBalance, arguments: {
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.tokenModel: tokenModel,
        GetArgumentsKey.coinModel: coinModel,
        GetArgumentsKey.walletModel: walletModel,
      });
    } else {
      Get.toNamed(AppRoutes.syncBalance, arguments: {
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.tokenModel: tokenModel,
        GetArgumentsKey.coinModel: coinModel,
        GetArgumentsKey.walletModel: walletModel,
      });
    }
  }

  ///复制
  void copy(String? text) {
    if (ObjectUtil.isEmpty(text)) return;
    Clipboard.setData(ClipboardData(text: text!)).then((result) =>
        showToast(ID.stringCopySuccess.tr, toastMode: ToastMode.success));
  }

  ///分享
  void share(String? text) {
    if (ObjectUtil.isEmpty(text)) return;
    Share.share(text!);
  }

  ///跳转外部浏览器
  Future<void> openLink(String? url, {Function? onError}) async {
    if (GetUtils.isNull(url)) {
      return;
    }
    final Uri uri = Uri.parse(url!);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (onError != null) {
        await onError();
      } else {
        Get.showToast(ID.dataError.tr);
      }
    }
  }

  Future<void> makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    await launchUrl(launchUri);
  }

  Future<void> showOldWalletBindBottomSheet(Wallet wallet) async {
    Get.showBottomSheet(
      title: ID.bindWallet.tr,
      bodyWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(4)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                getWalletBindInfo(wallet),
                style: TextStyle(
                    fontSize: Get.setFontSize(16),
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.regular,
                    color: Get.theme.textPrimary),
              ),
              const SizedBox(
                height: 20,
              )
            ],
          ),
        ),
      ),
      bottomWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        child: ButtonWidget(
            text: ID.scanQrBind.tr,
            onPressed: () async {
              Get.back();

              Get.toScanner();
            }),
      ),
    );
  }

  String getWalletBindInfo(Wallet wallet) {
    String bindInfo = '';
    switch (wallet.type) {
      case WalletType.ultraPlus:
        break;
      case WalletType.ultra:
        break;
      case WalletType.pro3Plus:
        break;
      case WalletType.pro3:
        return ID.stringP3info.tr;

      case WalletType.touch:
        break;
      case WalletType.smart:
        break;
      case WalletType.pro2Plus:
        return ID.stringP2Plusinfo.tr;
      case WalletType.pro2:
        return ID.stringP2info.tr;

      case WalletType.pro1:
        return ID.stringP1info.tr;

      case WalletType.pro1Pro2:
        return ID.stringP1info.tr;

      case WalletType.unknown:
        break;
    }
    return bindInfo;
  }

  Future<void> navigateWallet(WalletAction action,
      {required CoinModel? coinModel,
      AddressModel? addressModel,
      WalletModel? walletModel,
      TokenModel? tokenModel,
      bool neeback = false,
      Function? callBack}) async {
    if (neeback) {
      Get.back();
      await Future.delayed(const Duration(milliseconds: 300));
    }
    switch (action) {
      case WalletAction.receive:
        {
          CoinType? coinType = CoinBase.getCoinTypeByChain(coinModel?.chain);
          if (coinType is BinanceChain) {
            Get.showToast(ID.stringBnbRecieveTip.tr,
                toastMode: ToastMode.waring);
          } else {
            Get.toReceive(
              coinModel: coinModel,
              address: coinModel!.isToken!
                  ? tokenModel!.address
                  : addressModel!.address,
            ).then((value) {
              if (callBack != null) {
                callBack();
              }
            });
          }
        }
        break;
      case WalletAction.send:
      case WalletAction.fee:
        Get.toSend(
          coinModel: coinModel,
          addressModel: addressModel,
          tokenModel: tokenModel,
          action: action,
        ).then((value) {
          if (callBack != null) {
            callBack();
          }
        });

        break;
      case WalletAction.asstes:
        Get.toAssets(
                coinModel: coinModel,
                addressModel: addressModel,
                tokenModel: tokenModel,
                walletModel: walletModel!)
            .then((value) {
          if (callBack != null) {
            callBack();
          }
        });

        break;
      case WalletAction.syncBalance:
        toSyncBalance(
          chain: coinModel!.chain!,
          batchId: walletModel!.batchId!,
          walletId: walletModel.walletId!,
          deviceId: walletModel.deviceId!,
          address: addressModel?.address ?? tokenModel!.address!,
          contractAddress: tokenModel?.contract,
        );
        break;
      case WalletAction.all:
        break;
    }
  }

  ///全局加载弹窗
  void showLoadingDialog({String? loadingText}) {
    EasyLoading.show(status: loadingText ?? ID.loading.tr);
  }

  ///关闭全局加载弹窗
  void dismissLoadingDialog() {
    EasyLoading.dismiss();
  }

  ///中间Alertdialog
  Future<void> showAlertDialog(
      {String? title,
      String? content,
      bool barrierDismissible = true, //点击外部关闭
      String? onConfirmText,
      String? onCancelText,
      VoidCallback? onConfirm,
      bool disableBack = true, // 禁用Android back键，默认不禁用
      VoidCallback? onBackPressed, //Android back键 事件
      VoidCallback? onCancel,
      VoidCallback? onContinue,
      Widget? child}) async {
    if (Get.isDialogOpen != null && Get.isDialogOpen!) {
      Get.back();
    }
    await Get.dialog(
      barrierDismissible: barrierDismissible,
      PopScope(
          canPop: disableBack,
          onPopInvokedWithResult: (didPop, result) async {
            if (result != null) {
              return; // 如果 result 不为空，直接返回
            }
            if (didPop) {
              return;
            }

            if (onBackPressed != null) {
              onBackPressed();
            }
          },
          child: child ??
              AlertDialog(
                  title: title,
                  content: content,
                  onConfirmText: onConfirmText,
                  onCancelText: onCancelText,
                  onContinue: onContinue,
                  onConfirm: onConfirm,
                  onCancel: onCancel)),
    );
  }

  ///底部dialog
  Future<void> showBottomSheet({
    String? title,
    bool barrierDismissible = true, //点击外部关闭
    bool isScrollControlled = true, //设置为false，BottomSheet将根据其内容的大小来决定其高度
    bool enableDrag = true, // 是否可以通过拖动BottomSheet来关闭它
    bool hideHeader = false, // 隐藏Header
    bool disableBack = true, // 禁用Android back键，默认不禁用
    Widget? bodyWidget, // 自适应高度的body
    Widget? fullScreenBodyWidget, // 高度充满屏幕的 body
    Widget? bottomWidget, // 底部区域 如底部Buttonn
    VoidCallback? onBackPressed, //Android back键 事件
    VoidCallback? onCancel, //右上角关闭对话框
    double? paddingBottom, //自定义距底部高度 如果底部框是个列表，这里设置 为0
    Widget? customHeadWidget, //自定义顶部
    EdgeInsetsGeometry? padding,
    Color? bgColor,
    Function()? onBack, // 监听关闭回调
  }) async {
    await Get.bottomSheet(
      BottomSheetDialog(
          title: title,
          onBackPressed: onBackPressed,
          onCancel: onCancel,
          bgColor: bgColor,
          bodyWidget: bodyWidget,
          fullScreenBodyWidget: fullScreenBodyWidget,
          disableBack: disableBack,
          hideHeader: hideHeader,
          bottomWidget: bottomWidget,
          paddingBottom: paddingBottom,
          customHeadWidget: customHeadWidget,
          padding: padding),

      backgroundColor: bgColor ?? Get.theme.bgColor,
      ignoreSafeArea: false, //是否忽略安全区域（如iPhone X的刘海屏和底部指示条
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
    ).then((value) => onBack != null ? onBack() : {});
  }

  void showUpgradeDialog() {
    if (Get.isDialogOpen != null && Get.isDialogOpen!) {
      Get.back();
    }
    Get.dialog(const UpgradeDialog());
  }
}
