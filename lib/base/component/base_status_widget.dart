/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-03-28 13:11:30
 */
// ignore_for_file: must_be_immutable

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:coinbag/widgets/status/app_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

///包含错误状态，空状态，加载状态的公共组件
abstract class BaseStatusWidget<T extends BaseController> extends GetView<T> {
  const BaseStatusWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return controller.obx((state) => buildContent(context),
        onLoading: const AppLoadingWidget(),
        onError: (error) => AppErrorWidget(onRefresh: () {
              controller.showLoading();
              controller.loadData();
            }),
        onEmpty: const AppEmptyWidget());
  }

  ///showSuccess展示成功的布局
  Widget buildContent(BuildContext context);
}
