/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-11-22 14:18:16
 */

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/service/wallet_database_service.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/modules/scan/models/ultra_scan_model.dart';
import 'package:coinbag/modules/wallet/connect/controllers/parse_cold_wallet_qr_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:fl_mlkit_scanning/fl_mlkit_scanning.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/wallet.dart';

enum ScanAction {
  resultAction, //扫码后立即退出返回扫码结果
  synAddress, // 同步地址
  unknown,
}

class ScanController extends BaseController<ApiService>
    with GetTickerProviderStateMixin {
  final bool isChangingCameraLens = false;

  var fullScreenSize = Rx<Size?>(null); // 相机宽高比

  bool isParsingQR = false; //是否正在解析
  int lastCmd = 0;

  Map<int, UltraScanModel> resultMap = {};

  RxBool isCameraInitialized = true.obs;
  late Wallet? wallet; //传进来的钱包对象
  RxBool visiblePage = false.obs; //显示扫码进度

  RxBool hideAnimation = true.obs; //隐藏扫描
  final int throttleTimeMs = 2000; // 设置节流时间
  List<UltraScanModel> ultraScanModelDataList =
      <UltraScanModel>[]; // ultra 扫码集合
  ScanAction scanAction = ScanAction.unknown;
  late AnimationController animationController; //扫描动画
  late AppDatabase appDatabase = Get.database;
  late WalletDatabaseService walletRepository;
// 扫码进度
  var progress = 0.0.obs; // 初始化进度为0

// 扫码进度 值
  var percentage = "".obs;

// 扫码进度提示
  var percentageInfo = ''.obs;

// Pro 3如果是第一页，需要充值随机数
  int firstRandom = -1;
  // Pro 3当前页数据
  String currentData = '';
  // Pro 3下一页
  int nextPage = 1;

  ///扫描成功计数
  int scanCount = 0;

  int scanType = -1;
// Pro3 QT类型//1查询余额，2广播交易，3监控账户，4监控多签地址，5EOS同步账户，6EOS账户查询，7EOS发送，8EOS监控账户，9EOS签名结果，10防伪验证
  int mPro3QtType = 0;

  /// 交易model
  TransferModel? tsModel;

  @override
  void onInit() {
    wallet = Get.arguments?[GetArgumentsKey.wallet];
    scanAction =
        Get.arguments?[GetArgumentsKey.scanAction] ?? ScanAction.unknown;
    tsModel = Get.arguments?[GetArgumentsKey.transferModel];
    scanType = Get.arguments?[GetArgumentsKey.scanType] ?? -1;
    _initAnimation();

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
    AppController.resetBrightness();
  }

  @override
  void loadData() {
    _animateScanAnimation();
    hideAnimation.value = false;
  }

  ///或前台返回后台
  @override
  void onPaused() {
    pausePreview();
  }

  ///或后台返回前台回调
  @override
  void onResumed() {
    _resumePreview();
  }

  @override
  void onHidden() {
    pausePreview();
  }

  void restart() {
    isParsingQR = false;
  }

  //扫描动画
  void _initAnimation() {
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1200), vsync: this);
    animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animateScanAnimation();
      } else if (status == AnimationStatus.dismissed) {
        _animateScanAnimation();
      }
    });
  }

  void _animateScanAnimation() {
    animationController.reverse(from: 1.0);
  }

  void _resumePreview() async {
    isParsingQR = false;
    await FlMlKitScanningController().startScanning();
    _animateScanAnimation();
    hideAnimation.value = false;
  }

  void pausePreview() async {
    try {
      isParsingQR = false;
      await FlMlKitScanningController().stopPreview();
      animationController.stop();
      hideAnimation.value = true;
    } catch (_) {}
  }

  ///分析二维码图片
  void scanImage(List<Barcode>? barcodes) async {
    if (barcodes == null) {
      return;
    }

    List<String> list =
        barcodes.map((barcode) => barcode.displayValue ?? '').toList();

    String result = list[0];

    if (Get.isEmpty(result)) {
      Get.showToast(ID.emptyData.tr);
      return;
    }

    if (isColdWalletQr(result)) {
      await parserColdWalletQr(result, wallet);
    } else {
      await playSound();
      pausePreview();
      if (scanAction == ScanAction.resultAction) {
        Get.back(result: result);
      } else {
        toScanResult(result);
      }
    }
  }

  Future<void> playSound() async {
    await Get.appController.play('sounds/scan.mp3');
  }

  void toScanResult(String result) {
    Get.offNamed(AppRoutes.scanResultPage, arguments: {
      GetArgumentsKey.scanResult: result,
    });
  }

  bool isScanErrorCode(String result) {
    return GetUtils.isNumericOnly(result) && result.length == 8;
  }

  @override
  void onClose() {
    animationController.dispose();
    super.onClose();
    AppController.resetBrightness();
  }
}

class ScanBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ScanController());
  }
}
