/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-12 09:28:51
 * @LastEditTime: 2024-10-16 17:36:01
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/scan/widgets/scan_animated_view.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:fl_mlkit_scanning/fl_mlkit_scanning.dart';
import 'package:flutter/material.dart';

class ScanPage extends BaseStatefulWidget<ScanController> {
  const ScanPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: const Color.fromRGBO(0, 0, 0, 50),
        appBar: baseAppBar(
          titleWidget: Text(
            ID.scan.tr,
            style: TextStyle(
              color: Get.theme.white,
              fontSize: Get.setFontSize(16),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setFontFamily(),
            ),
          ),
          isLiaghtStatusBarBrightness: true,
          backgroundColor: Colors.transparent,
          leading: IconButton(
            onPressed: () => Get.back(),
            padding: EdgeInsets.only(left: Get.setWidth(6)),
            icon: ImageWidget(
              assetUrl: "icon_back_light",
              cacheRawData: true,
              shape: BoxShape.circle,
              width: Get.setImageSize(28),
              height: Get.setImageSize(28),
            ),
          ),
        ),
        body: Obx(
          () => Stack(
            fit: StackFit.expand,
            alignment: Alignment.center,
            children: <Widget>[
              _buildScanView(),
              _buildAnimation(),
              _buildPageView()
            ],
          ),
        ));
  }

  SizedBox _buildUninitializedView() => const SizedBox.shrink();

  Visibility _buildAnimation() => Visibility(
        visible: !controller.hideAnimation.value,
        child: ScannerAnimation(
          animation: controller.animationController as Animation<double>,
        ),
      );

  FlMlKitScanning _buildScanView() => FlMlKitScanning(
      frequency: 600,
      resolution: CameraResolution.veryHigh,
      autoScanning: true,
      barcodeFormats: const [BarcodeFormat.all],
      fit: BoxFit.cover,
      uninitialized: _buildUninitializedView(),
      onDataChanged: (AnalysisImageModel data) {
        controller.scanImage(data.barcodes);
      });

  Visibility _buildPageView() => Visibility(
        visible: controller.visiblePage.value,
        child: Positioned(
          bottom: Get.setHeight((Get.height / 2) - 200), // 距离底部的距离
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                ID.completionProgress.tr,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFeatures: const [FontFeature.tabularFigures()],
                ),
              ),
              SizedBox(
                height: Get.setHeight(10),
              ),
              SizedBox(
                width: Get.setWidth(180),
                child: LinearProgressIndicator(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  value: controller.progress.value,
                  backgroundColor: const Color(0xFFD3D3D3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  minHeight: 5,
                ),
              ),
              SizedBox(
                height: Get.setHeight(10),
              ),
              Text(controller.percentage.value,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: Get.setFontSize(14),
                    fontWeight: FontWeightX.regular,
                    fontFamily: Get.setNumberFontFamily(),
                    fontFeatures: const [FontFeature.tabularFigures()],
                  )),
              Text(
                controller.percentageInfo.value,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFeatures: const [FontFeature.tabularFigures()],
                ),
              ),
            ],
          ),
        ),
      );
}
