/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-18 10:57:00
 * @LastEditTime: 2024-04-01 16:35:21
 */
import 'package:coinbag/modules/scan/models/ultra_scan_model.dart';

class ScanUtils {
  //扫描到一页二维码，添加到数据列表
  static List<UltraScanModel> addUltralScanModelToList(
      List<UltraScanModel> dataList, UltraScanModel newModel) {
    List<UltraScanModel> newdataList = dataList;
    // 检查用具有特定id的用户是否已存在于列表中
    bool isExists = dataList.any((model) => model.page == newModel.page);
    if (!isExists) {
      // 如果用户不存在，则添加到列表中
      newdataList.add(newModel);
    }
    return newdataList;
  }

//收集完成的二维码，按照顺序拼装出数据
  static String getData(List<UltraScanModel> oldDataList) {
    // 创建一个副本
    List<UltraScanModel> sortedDataList = List<UltraScanModel>.from(oldDataList)
      ..sort((a, b) => a.page!.compareTo(b.page!));

    StringBuffer sb = StringBuffer();
    for (var model in sortedDataList) {
      sb.write(model.data);
    }
    return sb.toString();
  }

  static int getRemindPage(List<UltraScanModel> list) {
    // 如果列表为空，直接返回 0 表示没有缺失的页码
    if (list.isEmpty) return 0;

    // 对列表按 page 属性进行排序
    list.sort((a, b) => a.page!.compareTo(b.page!));

    // 如果列表是连续的，返回下一个缺失的 page
    if (list.last.page == list.length) {
      return list.length + 1;
    }

    // 遍历排序后的列表，查找缺失的最小 page
    for (int i = 0; i < list.length; i++) {
      if (list[i].page != i + 1) {
        // 返回缺失的最小 page
        return i + 1;
      }
    }

    // 如果所有 page 都是连续的，则返回 0
    return 0;
  }
}
