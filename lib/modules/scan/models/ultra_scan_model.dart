/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-18 10:25:25
 * @LastEditTime: 2024-02-18 11:24:48
 */
import 'package:json_annotation/json_annotation.dart';

part 'ultra_scan_model.g.dart';

@JsonSerializable()
class UltraScanModel {
  String? data;
  int? page;
  int? remindPage;
  int? totalPage;

  UltraScanModel({
    this.data,
    this.page,
    this.remindPage,
    this.totalPage,
  });

  factory UltraScanModel.fromJson(Map<String, dynamic> json) =>
      _$UltraScanModelFromJson(json);
  Map<String, dynamic> toJson() => _$UltraScanModelToJson(this);
}
