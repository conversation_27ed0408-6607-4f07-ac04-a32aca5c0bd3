/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-18 10:20:29
 * @LastEditTime: 2024-02-18 10:31:06
 */
import 'package:json_annotation/json_annotation.dart';

part 'wallet_scan_model.g.dart';

@JsonSerializable()
class WalletScanModel {
  String? check;
  int? cmd;
  String? data;
  int? index;
  int? total;

  WalletScanModel({
    this.check,
    this.cmd,
    this.data,
    this.index,
    this.total,
  });

  factory WalletScanModel.fromJson(Map<String, dynamic> json) =>
      _$WalletScanModelFromJson(json);
  Map<String, dynamic> toJson() => _$WalletScanModelToJson(this);
}
