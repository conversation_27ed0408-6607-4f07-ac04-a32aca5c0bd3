/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-12 09:28:51
 * @LastEditTime: 2024-08-16 16:45:10
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/material.dart';

class ScanResultPage extends BaseStatelessWidget {
  const ScanResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: ID.scanResult.tr,
      ),
      body: SingleChildScrollView(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(Get.arguments[GetArgumentsKey.scanResult] ?? "",
                style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(14),
                    fontFamily: Get.setFontFamily())),
            SizedBox(
              height: Get.setHeight(20),
            ),
            ButtonWidget(
                buttonSize: ButtonSize.full,
                text: ID.copy.tr,
                onPressed: () =>
                    Get.copy(Get.arguments[GetArgumentsKey.scanResult])),
          ],
        ),
      )),
    );
  }
}
