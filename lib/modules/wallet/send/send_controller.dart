/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-10 15:26:40
 */
import 'dart:async';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/modules/nft/models/nft_assets_item_model.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_initialize_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_request_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_sol_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_trx_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_verify_transfer_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/modules/wallet/send/models/fil_gas_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:wallet_core/chain/binance/bnb.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/model/transfer_model.dart';

class SendController extends BaseController<BlockChainService> {
  CoinModel? coinModel;
  // 主链
  CoinModel? mainCoinModel;
  AddressModel? addressModel;
  WalletModel? walletModel;
  TokenModel? tokenModel;
  bool isNft = false;
  NftAssetsItemModel? nftModel;

  WalletAction? action;

  /// 可用余额
  RxString availableBalance = '0'.obs;

  /// 不可用余额
  RxString unavailableBalance = '0'.obs;

  /// 总余额
  RxString totalBalance = '0'.obs;

  ///以后不再提示
  var noMorePrompts = false.obs;

  /// 主链余额
  String? mainChainBalance;
  late TransferModel tsModel =
      TransferModel(type: TransferType.transfer, chain: coinModel!.chain!);
  Rx<FeeType> feeType = FeeType.normal.obs;
  late CoinType coinType;
  List<Map<String, dynamic>> utxoList = [];
  Timer? timer;
  // 接收地址controller
  late Rx<TextEditingController> addressController =
      TextEditingController().obs;

  FocusNode focusNode = FocusNode(); // 创建一个焦点节点

  // 转账数量controller
  late Rx<TextEditingController> amountController = TextEditingController().obs;
  RxString amountFiat = ''.obs;

  RxList<FeeGearModel> feeGearList = <FeeGearModel>[].obs;
  FeeGearModel? gearModel;

  // fee
  String fee = '';
  RxString feeText = ''.obs;
  RxString fiatText = ''.obs;

  Rx<ButtonStatus> buttonStatus = ButtonStatus.disable.obs;
  bool isButtonAction = false;

  /// tron 接收地址是否已激活
  RxBool tronActivated = true.obs;

  /// 是否显示接收地址不在地址本提示
  RxBool showAddressNotInBookWarning = false.obs;

  /// 显示接收地址在地址本提示
  RxBool isExitAddrssBook = false.obs;

  /// 接收地址在地址本中备注
  RxString addressContactName = "".obs;

  /// 是否显示粘贴按钮
  RxBool showPasteButton = false.obs;

// 使用可观察的变量管理焦点状态
  RxBool hasFocus = false.obs;

  /// solana最低转账数量
  String solMinAmount = '0';

  /// sol token 开户费用
  String solTokenOpenAccountFee = '0';

  FilGasModel? filGasModel;

  TextEditingController remarkController = TextEditingController();

  bool get isShowFeeWidget => (coinType.isBitcoinSeries ||
      coinType.isEthereumSeries ||
      coinType is FilecoinChain ||
      coinType is SolanaChain ||
      coinType is RippleChain ||
      coinType.isBabyCoinNetWork);
  bool get isShowRemarkWidget => (coinType is BinanceChain ||
      coinType is RippleChain ||
      coinType is EosChain ||
      coinType is NemChain ||
      coinType.isBabyCoinNetWork ||
      _isTrxRemark);
  RxBool isShowTronRemarkTip = false.obs;

  bool get _isTrxRemark {
    if (coinType is TronChain) {
      String trcType = TronChain.get.getTokenType(coinModel!.tokenType);
      if (trcType == TronChain.trc20) {
        return false;
      } else {
        return true;
      }
    }
    return false;
  }

  /// 是否已经加载Tron资源
  RxBool isLoadedTronResource = false.obs;

  @override
  void onInit() async {
    await initialize();
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    setupData();
    loadData();
  }

  @override
  void onClose() {
    timer?.cancel();
    addressController.value.dispose();
    amountController.value.dispose();
    focusNode.dispose();
    super.onClose();
  }

  @override
  void loadData() {
    if (coinType.isBitcoinSeries) {
      utxoBalanceRequest();
    }
    getCoinFee();
    getSolMinBalance();
  }

  void addressChangeAction(String address) {
    tsModel.toAddress = address;
    updateButtonStatus();
    showPasteAction();
    checkTrxAddressStatus(address);
    checkAndShowAddressWarning(address);
  }

  void updateButtonStatus() {
    if (Get.isEmptyString(tsModel.toAddress) ||
        Get.isEmptyString(tsModel.amount) ||
        (Get.isEmptyString(tsModel.fee) && isShowFeeWidget)) {
      buttonStatus.value = ButtonStatus.disable;
    } else {
      buttonStatus.value = ButtonStatus.enable;
    }
  }

  bool get showMaxAmount => !(coinType.isBitcoinSeries);

  void sendButtonAction(BuildContext context) {
    verifyAction(context);
  }

  /// 页面返回刷新定时器
  static void updateTimer() {
    try {
      SendController sendCtr = Get.find<SendController>();
      Log.s('route: ${Get.currentRoute}');
      if (Get.currentRoute == AppRoutes.sendPage) {
        sendCtr.startTimer();
      }
    } catch (_) {}
  }

  Future<void> showPasteAction() async {
    if (!Get.isEmptyString(addressController.value.text)) {
      showPasteButton.value = false;
      return;
    }

    final data = await Clipboard.getData(Clipboard.kTextPlain);
    showPasteButton.value =
        hasFocus.value && (data != null && !Get.isEmptyString(data.text));
  }

  Future<void> pasteAction() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && !Get.isEmptyString(data.text)) {
      addressController.value.text = data.text!;
      addressChangeAction(data.text!);
    }
  }

  Future<void> checkAndShowAddressWarning(String toAddress) async {
    // 如果地址为空，隐藏警告并返回
    if (Get.isEmptyString(toAddress)) {
      showAddressNotInBookWarning.value = false;
      isExitAddrssBook.value = false;
      return;
    }
    if (coinModel!.isToken!) {
      if (tokenModel!.address!.toLowerCase() == toAddress.toLowerCase()) {
        showAddressNotInBookWarning.value = false;
        isExitAddrssBook.value = false;
        return;
      }
    } else {
      if (addressModel!.address!.toLowerCase() == toAddress.toLowerCase()) {
        showAddressNotInBookWarning.value = false;
        isExitAddrssBook.value = false;
        return;
      }
    }

    // 验证地址的有效性
    bool isValidAddress = await Get.walletCore.verifyAddress(
      chain: coinType.chain,
      address: toAddress,
    );

    // 如果地址无效，隐藏警告并返回
    if (!isValidAddress) {
      showAddressNotInBookWarning.value = false;
      isExitAddrssBook.value = false;

      return;
    }

    // 检查地址是否在地址簿中
    String? contatcsName =
        await Get.database.addressBookDao.checkAddressExistsOrGetName(
      address: toAddress,
      chain: coinType.chain,
    );

    if (!Get.isEmptyString(contatcsName)) {
      isExitAddrssBook.value = true;
      addressContactName.value = contatcsName!;
      showAddressNotInBookWarning.value = false;
    } else {
      showAddressNotInBookWarning.value = true;
      addressContactName.value = "";
    }
  }
}

class SendBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SendController());
  }
}
