/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-12 09:28:51
 * @LastEditTime: 2025-07-10 15:25:35
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/modules/wallet/send/widgets/send/send_button_widget.dart';
import 'package:coinbag/modules/wallet/send/widgets/send/send_fee_widget.dart';
import 'package:coinbag/modules/wallet/send/widgets/send/send_from_widget.dart';
import 'package:coinbag/modules/wallet/send/widgets/send/send_receive_widget.dart';
import 'package:coinbag/modules/wallet/send/widgets/send/send_remark_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:flutter/material.dart';

class SendPage extends BaseStatefulWidget<SendController> {
  const SendPage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: baseAppBar(
          title: controller.action == WalletAction.fee
              ? ID.stringBestFee.tr
              : ID.send.tr,
        ),
        bottomNavigationBar: const SendButtonWidget(),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SendFromWidget(),
              DividerWidget(),
              SendReceiveWidget(),
              SendFeeWidget(),
              SendRemarkWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
