import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text/animation_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/cosmos/baby.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class FeeGearWidget extends BaseStatelessWidget<FeeController> {
  const FeeGearWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: Obx(() => ListView.builder(
          padding: EdgeInsets.only(
              top: Get.setPaddingSize(16),
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16)),
          itemCount: controller.gearList.length,
          itemBuilder: (_, index) => _itemWidget(controller.gearList[index]))),
    );
  }

  Padding _itemWidget(FeeGearModel model) {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.setPaddingSize(12)),
      child: Container(
        decoration: BoxDecoration(
            color: Get.theme.colorF9F9F9,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Get.theme.colorECECEC, width: 1)),
        child: HighLightInkWell(
          onTap: () => controller.updateModelAction(model),
          borderRadius: BorderRadius.circular(12.0),
          highlightColor: Get.theme.colorECECEC,
          child: Padding(
            padding: EdgeInsets.all(Get.setPaddingSize(16)),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _imageWidget(model.feeType),
                    SizedBox(
                      width: Get.setPaddingSize(12),
                    ),
                    _feeContentWidget(model),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Expanded _feeContentWidget(FeeGearModel model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _feeTitleWidget(model),
          SizedBox(
            height: Get.setPaddingSize(4),
          ),
          _feeValueWidget(model)
        ],
      ),
    );
  }

  Row _feeValueWidget(FeeGearModel model) {
    return Row(
      children: [
        AnimationTextWidget(
            text: _fee(model),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                color: Get.theme.textPrimary)),
        SizedBox(
          width: Get.setPaddingSize(4),
        ),
        Expanded(
          child: AnimationTextWidget(
              text: _gas(model),
              textAlign: TextAlign.end,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  color: Get.theme.textSecondary)),
        )
      ],
    );
  }

  Obx _feeTitleWidget(FeeGearModel model) {
    return Obx(() => Row(
          children: [
            Text(
              _gearTitle(model.feeType),
              style: TextStyle(
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  color: controller.feeType.value == model.feeType
                      ? Get.theme.colorFF6A16
                      : Get.theme.textPrimary),
            ),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            Expanded(
              child: Text(
                _timeTitle(model),
                textAlign: TextAlign.end,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: Get.setFontSize(14),
                    fontWeight: FontWeightX.medium,
                    color: controller.feeType.value == model.feeType
                        ? Get.theme.colorFF6A16
                        : Get.theme.textSecondary),
              ),
            )
          ],
        ));
  }

  Obx _imageWidget(FeeType feeType) {
    return Obx(() => Padding(
          padding: EdgeInsets.only(top: Get.setPaddingSize(2)),
          child: ImageWidget(
              assetUrl: controller.feeType.value == feeType
                  ? 'cl_fee_button_s'
                  : 'cl_fee_button',
              width: Get.setImageSize(18),
              height: Get.setImageSize(18)),
        ));
  }

  String _gearTitle(FeeType type) {
    if (type == FeeType.fast) {
      return ID.fastTitle.tr;
    } else if (type == FeeType.normal) {
      return ID.normalTitle.tr;
    } else if (type == FeeType.slow) {
      return ID.slowTitle.tr;
    }
    return '';
  }

  String _timeTitle(FeeGearModel model) {
    int time = model.timeSecond ?? 0;
    int m = time ~/ 60; // 分钟
    String timeTitle = '';
    if (m == 0) {
      // 秒
      time = time;
      timeTitle = ID.secondTitle.tr;
    } else if (m > 0 && m < 59) {
      // 分钟
      time = m;
      timeTitle = ID.minuteTitle.tr;
    } else {
      // 小时
      time = time ~/ 360;
      timeTitle = ID.hourTitle.tr;
    }

    return controller.feeType.value == model.feeType
        ? '${ID.estimateTxTime.trParams({
                'minute': time.toString()
              })} $timeTitle'
        : '${time.toString()} $timeTitle';
  }

  String _gas(FeeGearModel model) {
    if (controller.coinType.isEthereumSeries) {
      if (controller.tsModel.isEip1559) {
        String? maxFee = model.maxFee;
        if (Get.isEmptyString(maxFee)) {
          maxFee = '0';
        }
        return 'MaxFee: ${maxFee!.decimal(scale: 2)} GWEI';
      }
      return '${model.fee ?? '0'} GWEI';
    } else if (controller.coinType is SolanaChain) {
      return '${model.fee ?? '0'} microLamport';
    } else if (controller.coinType.isBabyCoinNetWork) {
      return '${model.fee ?? '0'} ubbn';
    }
    return '';
  }

  String _fee(FeeGearModel model) {
    if (controller.coinType.isBitcoinSeries) {
      return '${model.fee} sat/vB';
    } else if (controller.coinType.isEthereumSeries) {
      String? fee = '';
      if (controller.tsModel.isEip1559) {
        fee = EthereumChain.get.calculateETHSeriesEip1559Fee(
            gasLimit: controller.tsModel.rawGasLimit, maxFee: model.maxFee);
      } else {
        fee = EthereumChain.get.calculateETHSeriesFee(
            gasLimit: controller.tsModel.rawGasLimit, gasPrice: model.fee);
      }
      return BalanceManager.calculateFiatValue(fee, controller.coinModel!);
    } else if (controller.coinType is FilecoinChain) {
      return '${controller.getFilNano(controller.filGasModel?.gasFeeCap)} NanoFIL';
    } else if (controller.coinType is SolanaChain) {
      String? fee = SolanaChain.get.calculateSolFee(
          gasLimit: controller.tsModel.rawGasLimit,
          gasPrice: model.fee,
          baseFee: model.baseFee);
      return BalanceManager.calculateFiatValue(fee, controller.coinModel!);
    } else if (controller.coinType is RippleChain) {
      String? fee = RippleChain.get.calculateXRPFee(model.fee);
      if (Get.isEmptyString(fee)) return '--';
      return '$fee ${controller.coinType.symbol}';
    } else if (controller.coinType.isBabyCoinNetWork) {
      String? fee = BabylonChain.get.calculateCosmosFee(
        gasLimit: controller.tsModel.rawGasLimit,
        cosmoseGas: model.fee,
        decimals: controller.coinType.decimals,
      );
      if (Get.isEmptyString(fee)) return '--';
      return '$fee ${controller.coinType.symbol}';
    }
    return '';
  }
}
