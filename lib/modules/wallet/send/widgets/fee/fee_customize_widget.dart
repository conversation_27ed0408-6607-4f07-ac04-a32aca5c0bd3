/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-02 17:59:08
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_customize_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';

class FeeCustomizeWidget extends BaseStatelessWidget<FeeController> {
  const FeeCustomizeWidget({super.key});

  String get title1 {
    if (controller.coinType.isBitcoinSeries) {
      return ID.minerFeeTitle.tr;
    } else if (controller.coinType.isEthereumSeries) {
      return 'Gas Price';
    } else if (controller.coinType is FilecoinChain) {
      return ID.minerFeeTitle.tr;
    } else if (controller.coinType is SolanaChain) {
      return 'Compute Unit Price';
    } else if (controller.coinType is RippleChain) {
      return ID.feeTitle.tr;
    } else if (controller.coinType.isBabyCoinNetWork) {
      return 'Gas Price';
    }
    return '';
  }

  String get title1SuffixText {
    if (controller.coinType.isBitcoinSeries) {
      return 'sat/vB';
    } else if (controller.coinType.isEthereumSeries) {
      return 'GWEI';
    } else if (controller.coinType is FilecoinChain) {
      return 'NanoFIL';
    } else if (controller.coinType is SolanaChain) {
      return 'microLamport';
    } else if (controller.coinType is RippleChain) {
      return controller.coinType.symbol;
    } else if (controller.coinType.isBabyCoinNetWork) {
      return 'ubbn';
    }
    return '';
  }

  Color textTipColor(FeeTipTextStatus status) {
    if (status == FeeTipTextStatus.enable) {
      return Get.theme.bgColor;
    } else if (status == FeeTipTextStatus.disable) {
      return Get.theme.colorF44D4D;
    } else if (status == FeeTipTextStatus.warning) {
      return Get.theme.colorFF6A16;
    }
    return Get.theme.bgColor;
  }

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: Obx(() => SingleChildScrollView(
            padding: EdgeInsets.only(
                top: Get.setPaddingSize(16),
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                bottom: Get.getSafetyBottomPadding()),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title1,
                  style: styleSecond_14,
                ),
                SizedBox(
                  height: Get.setPaddingSize(8),
                ),
                TextFieldWidget(
                  controller: controller.textController1,
                  keyboardType: TextInputType.numberWithOptions(
                      decimal:
                          controller.coinType is SolanaChain ? false : true),
                  onValueChanged: (value) =>
                      controller.updateText1Action(value),
                  suffixIcon: Text(
                    title1SuffixText,
                    style: TextStyle(
                        fontSize: Get.setFontSize(14),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.medium,
                        color: Get.theme.textSecondary),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: Get.setPaddingSize(2)),
                  child: Text(
                    controller.textTip1.value,
                    style: TextStyle(
                        fontSize: Get.setFontSize(12),
                        color: textTipColor(controller.textTip1Status.value)),
                  ),
                ),
                Visibility(
                  visible: controller.coinType.isEthereumSeries ||
                      controller.coinType is FilecoinChain ||
                      controller.coinType is SolanaChain ||
                      controller.coinType.isBabyCoinNetWork,
                  child: _textField2Widget(),
                ),
                Padding(
                  padding: EdgeInsets.only(top: Get.setPaddingSize(24)),
                  child: ButtonWidget(
                    text: ID.stringConfirm.tr,
                    buttonSize: ButtonSize.full,
                    buttonStatus: controller.buttonStatus.value,
                    onPressed: () => controller.customButtonAction(),
                  ),
                ),
              ],
            ),
          )),
    );
  }

  String _gasLimitTitle() {
    if (controller.coinType.isEthereumSeries ||
        controller.coinType.isBabyCoinNetWork ||
        controller.coinType is FilecoinChain) {
      return 'Gas Limit';
    } else if (controller.coinType is SolanaChain) {
      return 'Compute Unit Limit';
    }
    return '';
  }

  Column _textField2Widget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: Get.setPaddingSize(12),
        ),
        Text(
          _gasLimitTitle(),
          style: styleSecond_14,
        ),
        SizedBox(
          height: Get.setPaddingSize(8),
        ),
        TextFieldWidget(
          controller: controller.textController2,
          onValueChanged: (value) => controller.updateText2Action(value),
          keyboardType: TextInputType.numberWithOptions(
              decimal: controller.coinType is SolanaChain ? false : true),
        ),
        Padding(
          padding: EdgeInsets.only(top: Get.setPaddingSize(2)),
          child: Text(
            controller.textTip2.value,
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                color: textTipColor(controller.textTip2Status.value)),
          ),
        ),
      ],
    );
  }
}
