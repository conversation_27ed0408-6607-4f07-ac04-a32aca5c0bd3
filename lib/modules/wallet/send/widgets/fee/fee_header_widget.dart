/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-01-14 09:27:51
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text/animation_text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/solana/sol.dart';

class FeeHeaderWidget extends BaseStatelessWidget<FeeController> {
  const FeeHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
          child: Container(
            padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(12)),
            decoration: BoxDecoration(
                color: Get.theme.colorF9F9F9,
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(color: Get.theme.colorECECEC, width: 1)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _fiatWidget(),
                SizedBox(
                  height: Get.setPaddingSize(4),
                ),
                _feeWidget(),
                _detailWidget()
              ],
            ),
          ),
        ));
  }

  Visibility _detailWidget() {
    return Visibility(
        visible: controller.isShowMore.value,
        child: Padding(
          padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
          child: AnimationTextWidget(
            text: controller.detailText.value,
            maxLines: 5,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                fontFamily: Get.setNumberFontFamily(),
                color: Get.theme.textSecondary),
          ),
        ));
  }

  GestureDetector _feeWidget() {
    return GestureDetector(
      onTap: () => (controller.coinType.isEthereumSeries ||
              controller.coinType is FilecoinChain ||
              controller.coinType is SolanaChain)
          ? controller.isShowMore.value = !controller.isShowMore.value
          : {},
      behavior: HitTestBehavior.translucent,
      child: Row(
        children: [
          Expanded(
            child: AnimationTextWidget(
              text: controller.feeText.value,
              textAlign: TextAlign.end,
              style: TextStyle(
                  fontSize: Get.setFontSize(12),
                  fontFamily: Get.setNumberFontFamily(),
                  color: Get.theme.textSecondary),
            ),
          ),
          Visibility(
            visible: controller.coinType.isEthereumSeries ||
                controller.coinType is FilecoinChain ||
                controller.coinType is SolanaChain,
            child: Padding(
              padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
              child: ImageWidget(
                assetUrl: controller.isShowMore.value == false
                    ? 'arrow_down_gray'
                    : 'arrow_up_gray',
                width: Get.setImageSize(10),
                height: Get.setImageSize(10),
              ),
            ),
          )
        ],
      ),
    );
  }

  Row _fiatWidget() {
    return Row(
      children: [
        Text(
          ID.feeTitle.tr,
          style: TextStyle(
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setNumberFontFamily(),
              color: Get.theme.textPrimary,
              fontWeight: FontWeightX.medium),
        ),
        SizedBox(
          width: Get.setPaddingSize(4),
        ),
        Expanded(
          child: AnimationTextWidget(
            text: controller.fiatText.value,
            maxLines: 1,
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setNumberFontFamily(),
                color: Get.theme.textPrimary,
                fontWeight: FontWeightX.semibold),
          ),
        )
      ],
    );
  }
}
