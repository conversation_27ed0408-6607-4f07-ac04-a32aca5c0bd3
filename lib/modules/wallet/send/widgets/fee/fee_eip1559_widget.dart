import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_customize_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class FeeEIP1559Widget extends BaseStatelessWidget<FeeController> {
  const FeeEIP1559Widget({super.key});

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: Obx(() => SingleChildScrollView(
            padding: EdgeInsets.only(
                top: Get.setPaddingSize(16),
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                bottom: Get.getSafetyBottomPadding()),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _inputItemWidget(
                  title: 'MaxFee',
                  tipText: controller.textTip1.value,
                  tipStatus: controller.textTip1Status.value,
                  controller: controller.textController1,
                  onValueChanged: (value) =>
                      controller.updateText1Action(value),
                ),
                _verticalSizeBox(height: Get.setPaddingSize(24)),
                _inputItemWidget(
                  title: 'MaxPriorityFee',
                  tipText: controller.textTip2.value,
                  tipStatus: controller.textTip2Status.value,
                  controller: controller.textController2,
                  onValueChanged: (value) =>
                      controller.updateText2Action(value),
                ),
                _verticalSizeBox(height: Get.setPaddingSize(24)),
                _inputItemWidget(
                  title: 'Gas',
                  tipText: controller.textTip3.value,
                  tipStatus: controller.textTip3Status.value,
                  controller: controller.textController3,
                  isSuffixIcon: false,
                  onValueChanged: (value) =>
                      controller.updateText3Action(value),
                ),
                _verticalSizeBox(height: Get.setPaddingSize(24)),
                ButtonWidget(
                  text: ID.stringConfirm.tr,
                  buttonSize: ButtonSize.full,
                  buttonStatus: controller.buttonStatus.value,
                  onPressed: () => controller.customButtonAction(),
                )
              ],
            ),
          )),
    );
  }

  Column _inputItemWidget(
      {required String title,
      TextEditingController? controller,
      required String tipText,
      required FeeTipTextStatus tipStatus,
      bool isSuffixIcon = true,
      ValueChanged<String>? onValueChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleWidget(title),
        _verticalSizeBox(),
        SizedBox(
          child: TextFieldWidget(
            controller: controller,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            onValueChanged: onValueChanged,
            suffixIcon: isSuffixIcon
                ? Text(
                    'GWEI',
                    style: TextStyle(
                        fontSize: Get.setFontSize(14),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.medium,
                        color: Get.theme.textSecondary),
                  )
                : null,
          ),
        ),
        Visibility(
          visible: tipStatus != FeeTipTextStatus.enable,
          child: Padding(
            padding: EdgeInsets.only(top: Get.setPaddingSize(2)),
            child: Text(
              tipText,
              style: TextStyle(
                  fontSize: Get.setFontSize(12),
                  color: textTipColor(tipStatus)),
            ),
          ),
        )
      ],
    );
  }

  SizedBox _verticalSizeBox({double height = 8}) => SizedBox(
        height: Get.setPaddingSize(height),
      );

  Text _titleWidget(String title) {
    return Text(
      title,
      style: styleSecond_14,
    );
  }

  Color textTipColor(FeeTipTextStatus status) {
    if (status == FeeTipTextStatus.enable) {
      return Get.theme.bgColor;
    } else if (status == FeeTipTextStatus.disable) {
      return Get.theme.colorF44D4D;
    } else if (status == FeeTipTextStatus.warning) {
      return Get.theme.colorFF6A16;
    }
    return Get.theme.bgColor;
  }
}
