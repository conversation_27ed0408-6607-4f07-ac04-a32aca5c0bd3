/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-11 09:57:33
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/models/nft_assets_item_model.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_amount_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_max_amount_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class SendReceiveWidget extends BaseStatelessWidget<SendController> {
  const SendReceiveWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(24)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _addressWidget(context),
              _amountWidget(),
            ],
          ),
        ));
  }

  RenderObjectWidget _addressWidget(BuildContext context) {
    if (controller.action == WalletAction.fee) return SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              ID.receiveAddress.tr,
              style: styleSecond_14,
            ),
          ],
        ),
        SizedBox(
          height: Get.setPaddingSize(8),
        ),
        TextFieldWidget(
            key: Key('send_recieve_address'),
            maxLines: 3,
            onTap: () => controller.showPasteAction(),
            focusNode: controller.focusNode,
            controller: controller.addressController.value,
            onValueChanged: (value) => controller.addressChangeAction(value),
            suffixIcon: _addressSuffixIcon()),
        Visibility(
          visible: controller.showPasteButton.value,
          child: _pasteWidget(),
        ),
        Visibility(
            visible: controller.showAddressNotInBookWarning.value,
            child: Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
              child: Row(children: [
                Text(ID.stringNotAddedToAddressBook.tr,
                    style: TextStyle(
                        fontSize: Get.setFontSize(12),
                        fontWeight: FontWeightX.regular,
                        color: Get.theme.textTertiary)),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () async {
                    KeyboardUtils.hideKeyboard(context);
                    final result =
                        await Get.toNamed(AppRoutes.addContact, arguments: {
                      GetArgumentsKey.walletModel: controller.walletModel,
                      GetArgumentsKey.chain: controller.coinType.chain,
                      GetArgumentsKey.address:
                          controller.addressController.value.text,
                      GetArgumentsKey.sendToaddressBookAction: true,
                    });

                    if (result != null) {
                      controller.checkAndShowAddressWarning(
                          controller.addressController.value.text);
                    }
                  },
                  child: Text(
                    ID.stringAddNow.tr,
                    style: TextStyle(
                      fontSize: Get.setFontSize(12),
                      color: Get.theme.textPrimary,
                      fontWeight: FontWeightX.regular,
                      decoration: TextDecoration.underline, // 下划线
                    ),
                  ),
                ),
              ]),
            )),
        Visibility(
            visible: controller.isExitAddrssBook.value,
            child: Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => Get.showAlertDialog(
                    title: ID.stringNotices.tr,
                    content: ID.stringAddressInTheAddressBook.tr),
                child: Row(children: [
                  ImageWidget(
                    assetUrl: "icon_contacts",
                    width: Get.setImageSize(20),
                    height: Get.setImageSize(20),
                  ),
                  SizedBox(
                    width: Get.setPaddingSize(4),
                  ),
                  Text(controller.addressContactName.value,
                      style: TextStyle(
                          fontSize: Get.setFontSize(12),
                          fontWeight: FontWeightX.regular,
                          color: Get.theme.textTertiary)),
                ]),
              ),
            )),
        Visibility(
          visible: !controller.tronActivated.value,
          child: Padding(
            padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
            child: Text(
              ID.tronActivatedTip.tr,
              style: TextStyle(
                  fontSize: Get.setFontSize(12),
                  color: Get.theme.textSecondary),
            ),
          ),
        ),
      ],
    );
  }

  Visibility _amountWidget() {
    return Visibility(
      visible: controller.isNft == false ||
          (controller.isNft == true &&
              controller.nftModel!.ercTypeValue == NftErcType.erc1155),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: Get.setPaddingSize(24)),
          _amountTitleWidget(),
          SizedBox(
            height: Get.setPaddingSize(8),
          ),
          TextFieldWidget(
            key: Key('send_recieve_amount'),
            keyboardType: TextInputType.numberWithOptions(
                decimal: controller.isNft ? false : true),
            controller: controller.amountController.value,
            scale: controller.tsModel.coinDecimal,
            onValueChanged: (value) => controller.amountChangeAction(value),
            suffixIcon: _amountSuffixIcon(),
          ),
          Visibility(
            visible: controller.coinType is SolanaChain &&
                controller.coinModel!.isToken == false &&
                !Get.isEmptyString(controller.tsModel.amount) &&
                controller.solMinAmount
                    .moreThan(controller.tsModel.amount ?? '0') &&
                controller.solMinAmount.moreThan('0'),
            child: Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
              child: Text(
                ID.stringInputSolTip3.trParams({
                  'value':
                      '${controller.solMinAmount} ${controller.coinModel?.symbol}'
                }),
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    color: Get.theme.colorF44D4D),
              ),
            ),
          ),
          Visibility(
              visible: Get.isEmptyString(controller.amountFiat.value),
              child: SizedBox(height: Get.setPaddingSize(6))),
          Visibility(
            visible: !Get.isEmptyString(controller.amountFiat.value),
            child: Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(6)),
              child: Text(
                controller.amountFiat.value,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    color: Get.theme.textSecondary),
              ),
            ),
          ),
          Visibility(
            visible: controller.coinType.isBitcoinSeries,
            child: _utxoBalanceWidget(),
          ),
        ],
      ),
    );
  }

  Container _pasteWidget() {
    return Container(
      color: Colors.transparent,
      padding: EdgeInsets.only(
          top: Get.setPaddingSize(8),
          bottom: Get.setPaddingSize(12),
          right: Get.setPaddingSize(20)),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => controller.pasteAction(),
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          ImageWidget(
            assetUrl: "icon_paste",
            width: Get.setImageSize(14),
            height: Get.setImageSize(14),
          ),
          SizedBox(
            width: Get.setPaddingSize(4),
          ),
          Text(ID.stringPaste.tr,
              style: TextStyle(
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.regular,
                  color: Get.theme.textPrimary)),
        ]),
      ),
    );
  }

  Row _amountTitleWidget() {
    return Row(
      children: [
        Text(
          ID.sendNumber.tr,
          style: styleSecond_14,
        ),
        SizedBox(
          width: Get.setPaddingSize(8),
        ),
        Expanded(
          child: Text(
            ID.sendAvailable.tr,
            textAlign: TextAlign.end,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              color: Get.theme.textSecondary,
            ),
          ),
        ),
        SizedBox(
          width: Get.setPaddingSize(4),
        ),
        _availableBalanceWidget(),
      ],
    );
  }

  dynamic _availableBalanceWidget() {
    if (controller.coinType.isBitcoinSeries) {
      if (Get.isEmptyString(controller.availableBalance.value)) {
        return _loadingWidget();
      }
    }

    String? symbol = controller.isNft ? 'NFT' : controller.coinModel?.symbol;

    String value = controller.availableBalance.value;
    if (Get.isEmptyString(value)) {
      value = '--';
    } else {
      value = '$value $symbol';
    }

    return Text(
      value,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontSize: Get.setFontSize(14),
        fontFamily: Get.setNumberFontFamily(),
        fontWeight: FontWeightX.medium,
        color: Get.theme.textPrimary,
      ),
    );
  }

  Padding _loadingWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(8)),
      child: SizedBox(
        width: Get.setImageSize(12),
        height: Get.setImageSize(12),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Get.theme.textSecondary),
        ),
      ),
    );
  }

  Row _amountSuffixIcon() {
    return Row(
      children: [
        Text(
          controller.isNft ? 'NFT' : controller.coinModel?.symbol ?? '',
          style: TextStyle(
              fontSize: Get.setFontSize(16),
              color: Get.theme.textSecondary,
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setNumberFontFamily()),
        ),
        Visibility(
          visible: controller.showMaxAmount,
          child: Row(
            children: [
              Container(
                color: Get.theme.colorD3D3D3,
                margin:
                    EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
                height: Get.setHeight(14),
                width: Get.setWidth(1),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () => controller
                    .maxAmountAction(controller.availableBalance.value),
                child: Text(
                  ID.sendMaxText.tr,
                  style: TextStyle(
                      fontSize: Get.setFontSize(16),
                      color: Get.theme.colorFF6A16,
                      fontWeight: FontWeightX.medium,
                      fontFamily: Get.setNumberFontFamily()),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  Row _addressSuffixIcon() {
    return Row(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            KeyboardUtils.hideKeyboardNoContext();
            final result = await Get.toScanner(arguments: {
              GetArgumentsKey.scanAction: ScanAction.resultAction
            });

            if (!Get.isEmptyString(result)) {
              updateAddress(result);
              controller.addressChangeAction(result);
            }
          },
          child: ImageWidget(
            assetUrl: 'icon_scan_black',
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
          ),
        ),
        Container(
          color: Get.theme.colorD3D3D3,
          margin: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
          height: Get.setHeight(14),
          width: Get.setWidth(1),
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            KeyboardUtils.hideKeyboardNoContext();
            final result =
                await Get.toNamed(AppRoutes.addressBookPage, arguments: {
              GetArgumentsKey.chain: controller.coinType.chain,
              GetArgumentsKey.walletModel: controller.walletModel,
            });

            if (result != null) {
              updateAddress(result);
              controller.addressChangeAction(result);
            }
          },
          child: ImageWidget(
            assetUrl: 'icon_contact',
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
          ),
        ),
      ],
    );
  }

  Column _utxoBalanceWidget() => Column(
        children: [
          SizedBox(height: Get.setPaddingSize(8)),
          _utxoWidget(
            title: ID.stringUnavailableBalance.tr,
            balance: controller.unavailableBalance.value,
            type: 0,
          ),
          SizedBox(height: Get.setPaddingSize(8)),
          _utxoWidget(
            title: ID.stringTotalBalance.tr,
            balance: controller.totalBalance.value,
            type: 1,
          ),
        ],
      );

  Row _utxoWidget({
    required String title,
    required String? balance,
    required int type,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: TextStyle(
              fontSize: Get.setFontSize(12), color: Get.theme.textSecondary),
        ),
        Visibility(
          visible: type == 0,
          child: GestureDetector(
            onTap: () {
              if (type == 0) {
                _showUnavailableTip();
              }
            },
            behavior: HitTestBehavior.translucent,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(4)),
              child: ImageWidget(
                assetUrl: 'trx_source_detail',
                width: Get.setImageSize(12),
                height: Get.setImageSize(12),
              ),
            ),
          ),
        ),
        Visibility(
          visible: type == 0,
          child: Text(
            ':',
            style: TextStyle(
                fontSize: Get.setFontSize(12), color: Get.theme.textSecondary),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Expanded(
          child: Text(
            '${balance ?? '0'} ${controller.coinModel?.symbol}',
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                fontFamily: Get.setNumberFontFamily(),
                color: Get.theme.textSecondary),
          ),
        ),
      ],
    );
  }

  void _showUnavailableTip() {
    Get.showBottomSheet(
      title: ID.stringUnavailableBalance.tr,
      bodyWidget: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(14)),
        child: Text(
          ID.stringUnconfirmedUtxo.tr,
          style: TextStyle(
            color: Get.theme.textPrimary,
            fontSize: Get.setFontSize(16),
            fontFamily: Get.setFontFamily(),
          ),
        ),
      ),
      bottomWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: ButtonWidget(
          text: ID.stringConfirm.tr,
          width: Get.width,
          onPressed: () => Get.back(),
        ),
      ),
    );
  }

  void updateAddress(String? result) {
    if (!Get.isEmptyString(result)) {
      controller.tsModel.toAddress = result;
      controller.addressController.value.text = result!;
      controller.addressController.value = controller.addressController.value;
    }
  }
}
