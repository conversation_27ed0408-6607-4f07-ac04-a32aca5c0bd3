/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-23 10:45:50
 * @LastEditTime: 2025-03-10 17:49:30
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text/animation_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class SendFeeWidget extends BaseStatelessWidget<SendController> {
  const SendFeeWidget({super.key});

  String get feeTypeText {
    FeeType? type = controller.feeType.value;
    if (type == FeeType.fast) {
      return ID.fastTitle.tr;
    } else if (type == FeeType.slow) {
      return ID.slowTitle.tr;
    } else if (type == FeeType.normal) {
      return ID.normalTitle.tr;
    } else if (type == FeeType.customize) {
      return ID.customizeTitle.tr;
    }
    return '';
  }

  bool get _isBestFee {
    if (controller.action == WalletAction.fee) {
      if (Get.isEmptyString(controller.tsModel.amount)) return true;
      if (controller.tsModel.amount!.moreThan('0')) return false;
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return controller.isShowFeeWidget
        ? Obx(() => _isBestFee
            ? Text(
                controller.feeText.value,
                style: TextStyle(
                  color: Colors.transparent,
                ),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DividerWidget(
                    padding: EdgeInsets.only(bottom: Get.setPaddingSize(24)),
                  ),
                  Text(
                    ID.feeTitle.tr,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: styleSecond_14,
                  ),
                  SizedBox(
                    height: Get.setPaddingSize(8),
                  ),
                  _feeContentWidget(),
                ],
              ))
        : const SizedBox.shrink();
  }

  Stack _feeContentWidget() {
    return Stack(
      children: [
        _loadFeeWidget(),
        Visibility(
          visible: _loadingVisible(),
          child: _loadingWidget(),
        ),
      ],
    );
  }

  GestureDetector _loadFeeWidget() {
    return GestureDetector(
      onTap: () {
        if (controller.action == WalletAction.fee) return;
        Get.toNamed(AppRoutes.feePage, arguments: {
          GetArgumentsKey.tsModel: controller.tsModel,
          GetArgumentsKey.gearList: controller.feeGearList,
          GetArgumentsKey.coinModel: controller.mainCoinModel,
          GetArgumentsKey.filGasModel: controller.filGasModel
        })?.then((result) {
          if (result != null) {
            controller.tsModel = result[GetArgumentsKey.tsModel];
            controller.gearModel = result[GetArgumentsKey.gearModel];
            controller.updateFee(model: controller.gearModel);
          }
        });
      },
      child: Container(
        decoration: BoxDecoration(
            color: Get.theme.colorF9F9F9,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Get.theme.colorECECEC, width: 0.5)),
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(16),
              vertical: Get.setPaddingSize(16)),
          child: Row(
            children: [
              _feeWidget(),
              SizedBox(width: Get.setPaddingSize(8)),
              Visibility(
                visible: controller.action != WalletAction.fee,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    AnimationTextWidget(
                      text: feeTypeText,
                      style: styleSecond_14,
                    ),
                    ImageWidget(
                      assetUrl: 'icon_new_arrow01',
                      width: Get.setImageSize(12),
                      height: Get.setImageSize(12),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _feeWidget() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Visibility(
            visible: controller.tsModel.isEip1559,
            child: Padding(
              padding: EdgeInsets.only(bottom: Get.setPaddingSize(8)),
              child: Text(ID.feeRang.tr,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: styleSecond_14),
            ),
          ),
          Visibility(
            visible: controller.action == WalletAction.fee,
            child: Padding(
              padding: EdgeInsets.only(bottom: Get.setPaddingSize(8)),
              child: Text(ID.stringMinerFee.tr,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: styleSecond_14),
            ),
          ),
          AnimationTextWidget(
            text: controller.fiatText.value,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setNumberFontFamily(),
                color: Get.theme.textPrimary,
                fontWeight: FontWeightX.medium),
          ),
          SizedBox(
            height: Get.setPaddingSize(4),
          ),
          AnimationTextWidget(
            text: controller.feeText.value,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setNumberFontFamily(),
                color: Get.theme.textSecondary,
                fontWeight: FontWeightX.medium),
          )
        ],
      ),
    );
  }

  Positioned _loadingWidget() => Positioned(
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
        child: Container(
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: Get.setPaddingSize(24)),
          decoration: BoxDecoration(
            color: Get.theme.colorF9F9F9,
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Get.theme.colorECECEC, width: 0.5),
          ),
          child: SizedBox(
            height: Get.setPaddingSize(22),
            width: Get.setPaddingSize(22),
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor:
                    AlwaysStoppedAnimation<Color>(Get.theme.colorD3D3D3),
              ),
            ),
          ),
        ),
      );

  bool _loadingVisible() {
    if (controller.coinType.isBitcoinSeries) {
      return (controller.utxoList.isEmpty ||
              Get.isEmptyString(controller.tsModel.btcSatB)) &&
          Get.isEmptyString(controller.fee);
    }
    return Get.isEmptyString(controller.fee);
  }
}
