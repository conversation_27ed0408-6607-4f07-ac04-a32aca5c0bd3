/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-14 10:06:42
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/tron/trx.dart';

class SendRemarkWidget extends BaseStatelessWidget<SendController> {
  const SendRemarkWidget({super.key});

  int? _maxLength() {
    if (controller.coinType is RippleChain) {
      return 9;
    } else if (controller.coinType is NemChain) {
      return 15;
    } else if (controller.coinType is EosChain) {
      return 50;
    } else if (controller.isShowTronRemarkTip.value) {
      return 200;
    }
    return null;
  }

  TextInputType? _keyboardType() {
    if (controller.coinType is RippleChain) {
      return TextInputType.number;
    }
    return null;
  }

  String get _title {
    if (controller.coinType is RippleChain) {
      return ID.remarkTag.tr;
    } else if (controller.coinType is TronChain) {
      return ID.remark.tr;
    }
    return ID.remarkMemo.tr;
  }

  @override
  Widget build(BuildContext context) {
    return controller.isShowRemarkWidget
        ? Padding(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(24)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: styleSecond_14,
                ),
                SizedBox(
                  height: Get.setPaddingSize(8),
                ),
                TextFieldWidget(
                  controller: controller.remarkController,
                  hintText: ID.remarkHitText.tr,
                  keyboardType: _keyboardType(),
                  maxLength: _maxLength(),
                  suffixIcon: _suffixIconWidget(),
                  onValueChanged: (value) async {
                    controller.tsModel.remark = value;
                    if (controller.coinType is NemChain) {
                      controller.tsModel.fee =
                          await NemChain.get.getFee(controller.tsModel);
                    }
                    controller.isShowTronRemarkTip.value =
                        controller.coinType is TronChain &&
                            controller.isShowRemarkWidget &&
                            !Get.isEmptyString(value);

                    Log.logPrint(controller.isShowTronRemarkTip);
                  },
                ),
                _xrpTipWidget(),
                _tronTipWidget(),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _suffixIconWidget() {
    if (!controller.coinType.isBabyCoinNetWork) return SizedBox.shrink();
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () async {
        KeyboardUtils.hideKeyboardNoContext();
        final result = await Get.toScanner(
            arguments: {GetArgumentsKey.scanAction: ScanAction.resultAction});
        controller.remarkController.text = result;
        controller.tsModel.remark = result;
      },
      child: ImageWidget(
        assetUrl: 'icon_scan_black',
        width: Get.setImageSize(20),
        height: Get.setImageSize(20),
      ),
    );
  }

  Visibility _xrpTipWidget() {
    return Visibility(
        visible: controller.coinType is RippleChain,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: Get.setPaddingSize(24),
            ),
            const DividerWidget(),
            SizedBox(
              height: Get.setPaddingSize(16),
            ),
            Text(
              ID.xrpRemarkTips
                  .trParams({'value': RippleChain.get.reserveAmount}),
              style: styleSecond_14,
            )
          ],
        ));
  }

  Obx _tronTipWidget() {
    return Obx(() => Visibility(
        visible: controller.isShowTronRemarkTip.value,
        child: Padding(
          padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
          child: Text(
            ID.tronRemarkTip.tr,
            style: TextStyle(
                fontSize: Get.setFontSize(12), color: Get.theme.textSecondary),
          ),
        )));
  }
}
