/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-28 16:57:37
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class SendFromWidget extends BaseStatelessWidget<SendController> {
  const SendFromWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
      child: Column(
        children: [
          _itemWidget(
            title: ID.sendAddress.tr,
            value: AddressUtils.omitAddress(controller.tsModel.fromAddress),
          ),
          _itemWidget(
            title: controller.isNft ? ID.stringSendNft.tr : ID.sendCoin.tr,
            value: controller.isNft
                ? controller.nftModel?.tokenNameValue
                : controller.coinModel?.symbol,
            isCoin: true,
          )
        ],
      ),
    );
  }

  Padding _itemWidget(
      {required String title, String? value, bool isCoin = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: styleSecond_14,
          ),
          SizedBox(width: Get.setPaddingSize(48)),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Visibility(
                  visible: isCoin,
                  child:
                      controller.isNft ? _nftImageWidget() : _coinImageWidget(),
                ),
                Flexible(
                  child: Text(
                    value ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(14),
                      fontWeight: FontWeightX.semibold,
                      fontFamily: Get.setNumberFontFamily(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Padding _coinImageWidget() => Padding(
        padding: EdgeInsets.only(right: Get.setPaddingSize(4)),
        child: SymbolWidget(
          coinModel: controller.coinModel,
          size: Get.setImageSize(24),
          chainImageSzie: Get.setImageSize(12),
        ),
      );

  Padding _nftImageWidget() => Padding(
        padding: EdgeInsets.only(right: Get.setPaddingSize(4)),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(Get.setRadius(12)),
          child: ImageWidget(
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
            imageUrl: controller.nftModel?.imageUri ?? '',
            loadingWidget: Container(
              color: Get.theme.colorFF6A16.withAlpha(1),
              child: ImageWidget(
                width: Get.setImageSize(20),
                height: Get.setImageSize(20),
                assetUrl: 'icon_nft_empty',
              ),
            ),
            errorWidget: ImageWidget(
              width: Get.setImageSize(20),
              height: Get.setImageSize(20),
              assetUrl: 'icon_nft_failed',
            ),
          ),
        ),
      );
}
