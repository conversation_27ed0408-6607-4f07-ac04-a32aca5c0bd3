/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-27 14:38:26
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/widgets/fee/fee_header_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:flutter/material.dart';
import 'package:toggle_switch/toggle_switch.dart';

class FeePage extends BaseStatelessWidget<FeeController> {
  const FeePage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
        appBar: baseAppBar(
          title: ID.feeSettingTitle.tr,
          leading: IconButton(
            onPressed: () => controller.leadingAction(),
            padding: EdgeInsets.only(left: Get.setPaddingSize(6)),
            icon: ImageWidget(
              assetUrl: 'titlebar',
              cacheRawData: true,
              shape: BoxShape.circle,
              width: Get.setImageSize(28),
              height: Get.setImageSize(28),
            ),
          ),
        ),
        body: Column(
          children: [
            const FeeHeaderWidget(),
            _switchWidget(),
            Expanded(
              child: PageView.builder(
                  itemCount: controller.pagerList.length,
                  controller: controller.pagerController,
                  onPageChanged: (value) {
                    controller.pageIndex.value = value;
                    controller.updatePage();
                  },
                  itemBuilder: (_, int index) => controller.pagerList[index]),
            )
          ],
        ),
      ),
    );
  }

  Padding _switchWidget() {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(24)),
      child: Container(
        decoration: BoxDecoration(
            color: Get.theme.colorF9F9F9,
            borderRadius: BorderRadius.circular(22),
            border: Border.all(color: Get.theme.primary, width: 1)),
        child: Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(1)),
          child: Obx(() => ToggleSwitch(
                minWidth: Get.setWidth(90),
                minHeight: Get.setHeight(34),
                cornerRadius: Get.setRadius(17),
                activeBgColors: [
                  [Get.theme.primary],
                  [Get.theme.primary]
                ],
                activeFgColor: Get.theme.bgColor,
                inactiveBgColor: Get.theme.colorF9F9F9,
                inactiveFgColor: Get.theme.textSecondary,
                initialLabelIndex: controller.pageIndex.value,
                totalSwitches: 2,
                fontSize: Get.setFontSize(14),
                labels: [ID.gearSelect.tr, ID.customizeTitle.tr],
                radiusStyle: true,
                onToggle: (index) =>
                    controller.pagerController.jumpToPage(index!),
              )),
        ),
      ),
    );
  }
}
