/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-10 16:40:41
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/nft/models/nft_assets_item_model.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_cache_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fil_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_utxo_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/ethereum/etc.dart';
import 'package:wallet_core/chain/ethereum/layer/arb.dart';
import 'package:wallet_core/chain/ethereum/layer/zks.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

extension SendInitializeController on SendController {
  Future<void> initialize() async {
    if (Get.arguments!.containsKey(GetArgumentsKey.action)) {
      action = Get.arguments![GetArgumentsKey.action];
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.coinModel)) {
      coinModel = Get.arguments![GetArgumentsKey.coinModel] as CoinModel?;
      tsModel.coinDecimal = coinModel!.chainDecimal;
      tsModel.isToken = coinModel!.isToken == false ? false : true;
      tsModel.coinSymbol = coinModel!.symbol;
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.isNft)) {
      isNft = Get.arguments![GetArgumentsKey.isNft];
      tsModel.type = TransferType.nft;
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.model)) {
      nftModel = Get.arguments![GetArgumentsKey.model];
      // erc721 不包含输入框 amount默认一张
      if (nftModel!.ercTypeValue == NftErcType.erc721) {
        tsModel.amount = '1';
      }
      tsModel.nftImageUrl = nftModel!.imageUri;
      tsModel.nftTokenName = nftModel!.tokenNameValue;
      tsModel.nftContract = nftModel!.contractAddress;
      tsModel.nftTokenId = nftModel!.tokenId;
      tsModel.nftErcType = nftModel!.ercType;
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.addressModel)) {
      addressModel =
          Get.arguments![GetArgumentsKey.addressModel] as AddressModel?;
      if (addressModel != null) {
        tsModel.fromAddress = addressModel!.address;
        tsModel.addressPath = addressModel!.path;
        tsModel.xpub = addressModel!.xpubData;
        tsModel.addressPublickey = addressModel!.publickey;
      }
    }
    if (Get.arguments!.containsKey(GetArgumentsKey.tokenModel)) {
      tokenModel = Get.arguments![GetArgumentsKey.tokenModel] as TokenModel?;
      if (tokenModel != null) {
        tsModel.fromAddress = tokenModel!.address;
        availableBalance.value = tokenModel?.balance ?? '';
        tsModel.contract = tokenModel!.contract;
      }
    }
    coinType = CoinBase.getCoinTypeByChain(coinModel!.chain!)!;

    tsModel.wallet = await getWallet;
    tsModel.walletId = walletModel!.walletId!;

    if (addressModel != null) {
      _setupBalance();
    }

    if (coinModel?.isToken != true) {
      mainCoinModel = coinModel;
    }

    noMorePrompts.value =
        StorageManager.getValue(key: StorageKey.noMorePrompts) ?? false;

    /// 加载缓存矿工费
    await loadCacheFee();

    // 监听焦点变化
    focusNode.addListener(() {
      hasFocus.value = focusNode.hasFocus; // 更新焦点状态
    });
  }

  Future<void> _setupBalance() async {
    //  nft
    if (isNft) {
      tsModel.availableBalance = nftModel!.amount ?? '0';
      availableBalance.value = nftModel?.amount ?? '';
      mainChainBalance = addressModel?.balance;
      return;
    }

    if (coinType.isBitcoinSeries) {
      AddressModel? model = await Get.database.addressDao.getAddressModel(
        walletId: walletModel!.walletId!,
        deviceId: walletModel!.deviceId!,
        chain: coinType.chain,
        address: addressModel!.address!,
      );
      totalBalance.value = model?.totalBalance ?? '0';
      availableBalance.value = model?.availableBalance ?? '0';
      unavailableBalance.value = model?.unavailableBalance ?? '0';
      mainChainBalance = availableBalance.value;
      tsModel.availableBalance = model?.availableBalance ?? '0';
      if (availableBalance.value.equal('0')) {
        /// 显示loading
        availableBalance.value = '';
      }
    } else {
      tsModel.availableBalance = addressModel!.balance ?? '0';
      availableBalance.value = addressModel?.balance ?? '';
      mainChainBalance = addressModel?.balance;
    }
  }

  Future<Wallet?> get getWallet async {
    walletModel ??= await Get.database.walletDao.getCheckedWallets();
    Wallet wallet = Wallet.getWalletByBatch(walletModel!.batchId!);
    // 以太系列除ETC外U2
    if (coinType.isEthereumSeries &&
        coinType is! EthClassicChain &&
        wallet is UltraPlusWallet) {
      tsModel.isEip1559 = true;
    }
    return wallet;
  }

  // 设置初始化数据
  Future<void> setupData() async {
    mainCoinModel ??=
        await Get.database.coinDao.getMainCoinModel(chain: coinModel?.chain);

    _setupBTCSeriesInfo();
    _setupETHSeriesInfo();
    _setupFILInfo();
    _setupTRXInfo();
  }

  void _setupBTCSeriesInfo() {
    if (!coinType.isBitcoinSeries) return;

    /// 取utxo缓存
    if (addressModel?.utxoCache != null) {
      updateUtxo(jsonDecode(addressModel!.utxoCache!));
    }
  }

  /// 以太系列是否需要请求gasLimit
  bool get isETHSeriesRequestGasLimit => (coinType.isEthereumSeries &&
          (coinModel!.isToken == true ||
              coinType is ArbitrumChain ||
              coinType is ZkSyncEraChain) ||
      isNft);

  void _setupETHSeriesInfo() {
    if (!coinType.isEthereumSeries) return;
    if (isETHSeriesRequestGasLimit) {
      tsModel.gasLimit = coinModel!.gasLimitCache;
      tsModel.rawGasLimit = tsModel.gasLimit;
    } else {
      // 主链
      tsModel.gasLimit = coinType.gasLimit;
      tsModel.rawGasLimit = tsModel.gasLimit;
    }
  }

  void _setupFILInfo() {
    if (coinType is FilecoinChain) {
      if (!Get.isEmptyString(coinModel!.filGasJsonStr)) {
        updateFILGas(jsonDecode(coinModel!.filGasJsonStr!));
      }
    }
  }

  Future<void> _setupTRXInfo() async {
    if (coinType is TronChain) {
      tsModel.trxTokenType = TronChain.get.getTokenType(coinModel?.tokenType);
    }
  }
}
