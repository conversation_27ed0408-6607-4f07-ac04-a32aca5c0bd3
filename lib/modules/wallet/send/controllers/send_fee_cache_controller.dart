/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-07 10:12:12
 */
// import 'package:coinbag/database/dao/wallet/coin_dao.dart';
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fil_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_gaslimit_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_gear_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_utxo_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/model/transfer_model.dart';

extension SendFeeCacheController on SendController {
  Future<void> loadCacheFee() async {
    if (!isShowFeeWidget) {
      return;
    }
    String? gearStr = await Get.database.coinDao.getCacheTier(coinModel!);
    if (coinType.isEthereumSeries) {
      String? gasLimit;
      if (isNft) {
        gasLimit = await Get.database.coinDao.getNftCacheGasLimit(coinModel!);
      } else {
        gasLimit = await Get.database.coinDao.getCacheGasLimit(coinModel!);
      }
      if (!Get.isEmptyString(gearStr)) {
        updateGears(jsonDecode(gearStr!), type: FeeType.normal);
        getGasLimit(gasLimit);
        updateFee(model: gearModel);
      }
    } else if (coinType.isBitcoinSeries) {
      String? utxoStr = await Get.database.addressDao.getutxoCache(
        walletId: walletModel!.walletId!,
        deviceId: walletModel!.deviceId!,
        chain: coinType.chain,
        address: addressModel!.address ?? '',
      );

      if (!Get.isEmptyString(gearStr) && !Get.isEmptyString(utxoStr)) {
        updateGears(jsonDecode(gearStr!), type: FeeType.normal);
        updateUtxo(jsonDecode(utxoStr!));
        updateFee(model: gearModel);
      }
    } else if (coinType is FilecoinChain) {
      String? filGasStr =
          await Get.database.coinDao.getCacheFilGasJsonStr(coinModel!);
      if (!Get.isEmptyString(gearStr) && !Get.isEmptyString(filGasStr)) {
        updateGears(jsonDecode(gearStr!), type: FeeType.normal);
        updateFILGas(jsonDecode(filGasStr!));
        updateFee(model: gearModel);
      }
    } else if (coinType is SolanaChain) {
      if (!Get.isEmptyString(addressModel?.solMinimumRent)) {
        solMinAmount = addressModel!.solMinimumRent!;
      }
      if (!Get.isEmptyString(gearStr)) {
        String? gasLimit =
            await Get.database.coinDao.getCacheGasLimit(coinModel!);
        updateGears(jsonDecode(gearStr!), type: FeeType.normal);
        getGasLimit(gasLimit);
        updateFee(model: gearModel);
      }
    } else if (coinType is RippleChain) {
      if (!Get.isEmptyString(gearStr)) {
        updateGears(jsonDecode(gearStr!), type: FeeType.normal);
        updateFee(model: gearModel);
      }
    } else if (coinType.isBabyCoinNetWork) {
      if (!Get.isEmptyString(gearStr)) {
        dynamic gasLimit =
            await Get.database.coinDao.getCacheGasLimit(coinModel!);
        updateGears(jsonDecode(gearStr!), type: FeeType.normal);
        if (gasLimit != null) {
          gasLimit = double.tryParse(gasLimit);
        }
        getGasLimit(gasLimit);
        updateFee(model: gearModel);
      }
    }
  }
}
