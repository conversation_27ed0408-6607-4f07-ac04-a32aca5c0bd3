/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-09 10:44:03
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/http/apiService/utxo_service.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/utxo/utxo_model.dart';

extension SendUtxoController on SendController {
  Future<Future> getUtxo() async {
    return UtxoService()
        .getUtxo(chain: coinType.chain, address: addressModel!.address!)
        .then((value) => _utxoResult(value));
  }

  Future<void> _utxoResult(List value) async {
    /// 更新档位矿工费
    if (coinModel != null) {
      await Get.database.addressDao.updateUtxos(
        addressModel!,
        jsonEncode(value),
      );
    }
    if (timer != null) {
      updateUtxo(value);
    }
  }

  void updateUtxo(List value) {
    utxoList = value.map((e) {
      UtxoModel model = UtxoModel.fromJson(e);
      String value = model.value ?? '0';
      model.sourceValue = value;
      model.value = value.div(pow(10, coinType.decimals).toString(),
          scale: coinType.decimals);
      return model.toJson();
    }).toList();
    tsModel.utxoList = utxoList;
  }
}
