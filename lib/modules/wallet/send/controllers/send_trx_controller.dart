/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-03 14:04:52
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/model/transfer_tron_source.dart';

extension SendTrxController on SendController {
  /// 检查trx接受地址是否需要激活
  Future<void> checkTrxAddressStatus(String? address) async {
    tronActivated.value = true;
    if (coinType is! TronChain) return;
    // Trc20不需要激活地址
    if (tsModel.trxTokenType == TronChain.trc20) return;
    //  检查地址是否是TRX地址
    bool result = await Get.walletCore
        .verifyAddress(chain: coinType.chain, address: address ?? '');
    if (result) {
      httpRequest(
          api.trxAccountInfo(BlockChainParamsManager.createParams(
              method: BlockChainAPI.trxAccountInfo,
              requestParams: RequestParams()
                  .put(APIConstant.chain, coinType.chain)
                  .put(APIConstant.address, address)
                  .getRequestBody())),
          handleError: false,
          handleSuccess: false, (value) {
        if (value.data != null) {
          TronAccountInfo accountModel = TronAccountInfo.fromJson(value.data);
          tsModel.activated = accountModel.activated ?? false;
          tronActivated.value = accountModel.activated ?? false;
        }
      });
    }
  }

  Future<void> tronTransferInfo({Function(bool result)? callback}) async {
    buttonStatus.value = ButtonStatus.loading;
    Map params = await _params();

    Future<BaseResponseV1<dynamic>> future = api.getTrxTransactionInfo(
        BlockChainParamsManager.createParams(
            method: BlockChainAPI.trxTransactionInfo,
            requestParams: RequestParams()
                .put(APIConstant.chain, coinType.chain)
                .put(APIConstant.params, params)
                .getRequestBody()));

    httpRequest(
        future,
        (value) {
          if (value.data != null) {
            var data = value.data;

            if (Get.isResponseDataValid(data, isShowToast: true)) {
              if (tsModel.trxTokenType == TronChain.trc20) {
                data = data[APIConstant.transaction];
              }
              tsModel.rawDataHex = data[APIConstant.rawDataHex];
              tsModel.rawData = jsonEncode(data[APIConstant.rawData]);
              tsModel.txID = data[APIConstant.txID];
              buttonStatus.value = ButtonStatus.enable;
              if (callback != null) {
                callback(true);
              }
            } else {
              buttonStatus.value = ButtonStatus.enable;
              if (callback != null) {
                callback(false);
              }
            }
          } else {
            buttonStatus.value = ButtonStatus.enable;
            Get.showToast(value.error?.message, toastMode: ToastMode.failed);
            if (callback != null) {
              callback(false);
            }
          }
        },
        handleSuccess: false,
        handleError: false,
        error: (e) {
          buttonStatus.value = ButtonStatus.enable;
          Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
        });
  }

  Future<void> energyRequest() async {
    Map params = await _params();

    if (tsModel.trxTokenType != TronChain.trc20) {
      _getTrxFee();
      return;
    }

    Future<BaseResponseV1<dynamic>> future = api.getTrx20TsEnergy(
        BlockChainParamsManager.createParams(
            method: BlockChainAPI.trx20TsEnergy,
            requestParams: RequestParams()
                .put(APIConstant.chain, coinType.chain)
                .put(APIConstant.params, params)
                .getRequestBody()));

    httpRequest(
        future,
        (value) {
          final data = value.data;
          if (data != null) {
            String? energyPenalty = data[APIConstant.energyPenalty].toString();
            String? energyUsed = data[APIConstant.energyUsed].toString();
            tsModel.energyPenalty = energyPenalty;
            tsModel.energyUsed = energyUsed;
            _getTrxFee();
          } else {
            tsModel.energyPenalty = '';
            tsModel.energyUsed = '';
            tsModel.tronTsSourceModel = null;
          }
        },
        handleSuccess: false,
        handleError: false,
        error: (e) {
          tsModel.energyPenalty = '';
          tsModel.energyUsed = '';
          tsModel.tronTsSourceModel = null;
          isLoadedTronResource.value = true;
        });
  }

  void _getTrxFee() {
    String? type = tsModel.trxTokenType;
    if (Get.isEmptyString(type)) {
      type = 'trx';
    }

    httpRequest(
      api.getTrxTransactionResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.trxTransactionSource,
          requestParams: RequestParams()
              .put(APIConstant.chain, tsModel.chain)
              .put(APIConstant.address, tsModel.fromAddress)
              .put(APIConstant.energyUsed, tsModel.energyUsed ?? '0')
              .put(APIConstant.rawDataHex, tsModel.rawDataHex)
              .put(APIConstant.type, type)
              .put(APIConstant.addressIsActivated, tsModel.activated)
              .put(APIConstant.isRemark, !Get.isEmptyString(tsModel.remark))
              .getRequestBody())),
      handleSuccess: false,
      handleError: false,
      (value) {
        if (value.data != null) {
          TransferTronSourceModel sourceModel =
              TransferTronSourceModel.fromJson(value.data);
          tsModel.tronTsSourceModel = sourceModel;
        }
        isLoadedTronResource.value = true;
      },
      error: (e) {
        tsModel.tronTsSourceModel = null;
        isLoadedTronResource.value = true;
      },
    );
  }

  Future<Map> _params() async {
    String amount =
        tsModel.amount!.mul(pow(10, tsModel.coinDecimal!).toString());
    String? remark = tsModel.remark;
    if (Get.isEmptyString(remark)) {
      remark = '';
    }
    remark = remark!.stringToHex;

    Map map = {
      APIConstant.amount: amount,
      APIConstant.type: coinType.chain,
      APIConstant.addressTo: tsModel.toAddress,
      APIConstant.ownerAddress: tsModel.fromAddress,
      APIConstant.extraData: remark
    };

    if (tsModel.trxTokenType == TronChain.trc10) {
      map[APIConstant.type] = tsModel.trxTokenType;
      String contract = tsModel.contract ?? '';
      map[APIConstant.assetName] = contract.stringToHex;
    } else if (tsModel.trxTokenType == TronChain.trc20) {
      String toAddress = tsModel.toAddress!;
      String? parameter = await Get.walletCore.getTrc20Parameter(
        toAddress: toAddress,
        amount: tsModel.amount!,
        decimal: tsModel.coinDecimal!,
      );
      map[APIConstant.parameter] = parameter;
      map[APIConstant.functionSelector] = APIConstant.functionSelectorValue;
      map[APIConstant.type] = tsModel.trxTokenType;
      map[APIConstant.callValue] = 0;
      map[APIConstant.feeLimit] = 40000000;
      map[APIConstant.contractAddress] = tsModel.contract;
    }

    return map;
  }

  Future<bool> verityTronRawDataHex() async {
    return await Get.walletCore.verityTronRawDataHex(
          rawDataHex: tsModel.rawDataHex!,
          fromAddress: tsModel.fromAddress!,
          toAddress: tsModel.toAddress!,
          contract: tsModel.contract ?? "",
          amount: tsModel.amount!,
          decimal: tsModel.coinDecimal!,
        ) ??
        false;
  }
}
