/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-11 14:12:31
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';

extension SendEOSController on SendController {
  void getEosBlockInfo({Function(bool result)? callback}) {
    buttonStatus.value = ButtonStatus.loading;

    List<Future> futures = [];
    futures.add(api.getEOSBlockInfo(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getBlockInfo,
        requestParams: RequestParams()
            .put(APIConstant.chain, tsModel.chain)
            .getRequestBody())));

    multiHttpRequest(
        futures,
        (value) async {
          String? headBlockId;
          int? nodeHeight;

          if (value != null) {
            BaseResponseV1 infoResponse = value[0];
            buttonStatus.value = ButtonStatus.enable;
            bool result = true;
            if (infoResponse.data != null) {
              Map? data = infoResponse.data;
              String? blockTime = data?[APIConstant.blockTime]?.toString();
              tsModel.blockTime = blockTime;
              nodeHeight = data?[APIConstant.nodeHeight];
              Map? exact = data?[APIConstant.extra];
              // chain_id
              String? chainId = exact?[APIConstant.chainId];
              tsModel.eosChainId = chainId;

              // head_block_id
              headBlockId = exact?[APIConstant.headBlockId];
              tsModel.headBlockId = headBlockId;
              if (Get.isEmptyString(blockTime) ||
                  Get.isEmptyString(chainId) ||
                  Get.isEmptyString(headBlockId)) {
                Get.showToast(ID.stringNodeTimeOut.tr,
                    toastMode: ToastMode.waring);
                result = false;
              } else {
                // headBlockId 长度需要大于16
                if (headBlockId!.length > 16) {
                  _updateEosBlock(headBlockId);
                } else {
                  result = false;
                }
              }
            }

            if (tsModel.wallet! is Pro3Wallet) {
              tsModel.action = "transfer";

              ///这里实际应该按照EOS公钥对应permissions 下对比找到对应permission，这里因为库神生成的owner 和active 所以 默认active也可以
              tsModel.permission = "active";

              String? transactionJson =
                  await Get.walletCore.getEosTransactionOrSignHash(
                action: tsModel.action!,
                accountContract: tsModel.isToken
                    ? tsModel.contract!
                    : EosChain.get.getEosMainContract(),
                from: tsModel.fromAddress!,
                to: tsModel.toAddress!,
                amount: tsModel.amount!,
                memo: tsModel.remark ?? '',
                symbol: tsModel.coinSymbol!,
                permission: tsModel.permission!,
                chainId: tsModel.eosChainId!,
                headBlockId: tsModel.headBlockId!,
                nodeHeight: nodeHeight?.toString() ?? '0',
                headBlockTime: tsModel.blockTime!,
              );
              if (transactionJson != null) {
                var transactionMap = json.decode(transactionJson);
                tsModel.signDataHash = transactionMap['signHash'];
                tsModel.transaction = transactionMap['transaction'];
                Log.r(tsModel.transaction);
              }
            }

            if (tsModel.isToken || tsModel.addressPublickey!.isEmpty) {
              AddressModel? addressModel =
                  await Get.database.addressDao.getAddressModel(
                walletId: tsModel.walletId!,
                deviceId: walletModel!.deviceId!,
                chain: tsModel.chain,
                address: tsModel.fromAddress!,
              );
              tsModel.addressPublickey = addressModel!.publickey!;
            }

            if (callback != null) {
              callback(result);
            }
          }
        },
        handleError: false,
        error: (e) {
          buttonStatus.value = ButtonStatus.enable;
          Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.waring);
          if (callback != null) {
            callback(false);
          }
        });
  }

  Future<void> _updateEosBlock(String headBlockId) async {
    // 区块高度
    String blockHeight = headBlockId.substring(0, 8);
    blockHeight = blockHeight.hexToDecimal;
    tsModel.blockHeight = blockHeight;
    // 区块信息
    String blockInfo = headBlockId.substring(16, 24);
    tsModel.blockInfo =
        await Get.walletCore.getEosBlockInfo(blockInfo: blockInfo);

    Log.logPrint('block_height: $blockInfo block_info: ${tsModel.blockInfo}');
  }
}
