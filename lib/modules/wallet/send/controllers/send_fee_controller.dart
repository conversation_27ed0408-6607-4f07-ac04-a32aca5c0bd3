/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-03 09:17:03
 */

import 'dart:async';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_cosmose_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fil_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_gaslimit_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_gear_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_initialize_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_utxo_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/model/utxo/utxo_result_model.dart';

extension SendCoinFeeController on SendController {
  Future<void> getCoinFee() async {
    if (!isShowFeeWidget) return;
    // 开启定时器
    startTimer();
  }

  Future<void> _request() async {
    List<Future<dynamic>> futures = [];
    futures.add(gearFeeApi());
    if (coinType.isBitcoinSeries) {
      futures.add(getUtxo());
    } else if (isETHSeriesRequestGasLimit || coinType is SolanaChain) {
      futures.add(getGasLimitApi());
    } else if (coinType is FilecoinChain) {
      futures.add(filGas());
    } else if (coinType.isBabyCoinNetWork) {
      futures.add(cosmosSimulate());
    }

    multiHttpRequest(futures, handleSuccess: false, (value) {
      if (tsModel.feeType != FeeType.customize) {
        Future.delayed(const Duration(milliseconds: 500))
            .then((value) => updateFee(model: gearModel));
      }
    });
  }

  Future<void> updateFee({
    required FeeGearModel? model,
    bool showToast = false,
  }) async {
    gearModel = model;
    feeType.value = model?.feeType ?? FeeType.normal;

    if (coinType.isBitcoinSeries) {
      tsModel.btcSatB = gearModel?.fee;
    } else if (coinType.isEthereumSeries) {
      if (feeType.value != FeeType.customize) {
        tsModel.gasLimit = tsModel.rawGasLimit;
      }

      if (tsModel.isEip1559) {
        tsModel.maxFee = gearModel?.maxFee;
        tsModel.baseFee = gearModel?.baseFee;
        tsModel.maxPriorityFee = gearModel?.maxPriorityFee;
        tsModel.minPriorityFee = gearModel?.minPriorityFee;
      } else {
        tsModel.gasPrice = gearModel?.fee;
      }
    } else if (coinType is SolanaChain) {
      tsModel.gasPrice = gearModel?.fee;
      tsModel.baseFee = gearModel?.baseFee;
    } else if (coinType is RippleChain) {
      tsModel.xrpFee = gearModel?.fee;
    } else if (coinType.isBabyCoinNetWork) {
      tsModel.cosmoseGas = gearModel?.fee;
    }

    if (coinType.isBitcoinSeries) {
      fee = await getBitcoinSeriesFeeOrBestUtxos();
      if (fee.equal('0') &&
          (tsModel.amount ?? '0').moreThan('0') &&
          showToast == true) {
        Get.showToast(ID.insufficientBalanceFee.tr,
            toastMode: ToastMode.waring);
      }
    } else {
      fee = await coinType.getFee(tsModel) ?? '';
    }

    tsModel.fee = fee;
    _updateFeeText();
    updateButtonStatus();
  }

  void _updateFeeText() {
    if (tsModel.isEip1559) {
      String? max = _maxFee();
      String? min = _minFee();
      if (Get.isEmptyString(max)) {
        feeText.value = CommonConstant.emptyAsstes;
        fiatText.value = CommonConstant.emptyAsstes;
        return;
      }

      if (max!.equal(min ?? '') || Get.isEmptyString(min)) {
        feeText.value = '$max ${coinType.symbol}';
        fiatText.value = BalanceManager.calculateFiatValue(max, mainCoinModel);
        return;
      }

      feeText.value = '$min ${coinType.symbol} - $max ${coinType.symbol}';
      fiatText.value =
          '${BalanceManager.calculateFiatValue(min, mainCoinModel)} - ${BalanceManager.calculateFiatValue(max, mainCoinModel)}';
    } else {
      if (Get.isEmptyString(fee)) {
        fee = '0';
      }

      if (fee.equal('0')) {
        feeText.value = CommonConstant.emptyAsstes;
        fiatText.value = CommonConstant.emptyAsstes;
      } else {
        feeText.value = Get.isEmptyString(fee)
            ? CommonConstant.emptyAsstes
            : '$fee ${coinType.symbol}';
        fiatText.value = BalanceManager.calculateFiatValue(fee, mainCoinModel);
      }
    }
  }

  String? _maxFee() => tsModel.fee;

  String? _minFee() {
    if (tsModel.isEip1559) {
      String? baseFee = tsModel.baseFee;
      if (Get.isEmptyString(baseFee)) {
        baseFee = '0';
      }
      String? minPriorityFee = tsModel.minPriorityFee;
      if (Get.isEmptyString(minPriorityFee)) {
        minPriorityFee = '0';
      }

      String? minFee = baseFee!.add(minPriorityFee!);
      if (Get.isEmptyString(minFee)) {
        minFee = '0';
      }
      return EthereumChain.get.calculateETHSeriesEip1559Fee(
          gasLimit: tsModel.gasLimit, maxFee: minFee);
    }
    return null;
  }

  Future<String> getBitcoinSeriesFeeOrBestUtxos() async {
    // 判空处理，防止 btcSatB 为 null
    final btcSatB = tsModel.btcSatB;
    if (btcSatB == null || btcSatB.isEmpty) {
      return '0';
    }
    UtxoFeeResultModel? result =
        await Get.walletCore.getBitcoinSeriesFeeOrBestUtxos(
      chain: tsModel.chain,
      amount: tsModel.amount ?? '0',
      btcSatB: btcSatB,
      dust: coinType.dust,
      decimal: tsModel.coinDecimal!,
      fromAddress: tsModel.fromAddress!,
      toAddress: tsModel.toAddress,
      availableBalance: tsModel.availableBalance ?? '0',
      utxoList: utxoList,
    );

    tsModel.fee = result?.fee;
    tsModel.utxoResultModel = result;

    return tsModel.fee ?? '0';
  }

  void startTimer() {
    if (!isShowFeeWidget) return;
    if (timer != null) return;
    _request();
    timer = Timer.periodic(const Duration(seconds: 7), (timer) => _request());
  }

  void stopTimer() {
    if (timer == null) return;
    timer?.cancel();
    timer = null;
  }
}
