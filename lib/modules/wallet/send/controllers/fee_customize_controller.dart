import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/ethereum/layer/arb.dart';
import 'package:wallet_core/chain/ethereum/layer/opt.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

extension FeeCustomize on FeeController {
  void setupTextController() {
    String? fee;
    String? maxFee;
    String? maxPriorityFee;
    String? baseFee;
    String? minPriorityFee;
    bool isCustomize = tsModel.feeType == FeeType.customize;

    if (isCustomize) {
      if (coinType.isEthereumSeries) {
        if (tsModel.isEip1559) {
          textController1 = TextEditingController(text: tsModel.maxFee ?? '');
          textController2 =
              TextEditingController(text: tsModel.maxPriorityFee ?? '');
          textController3 = TextEditingController(text: tsModel.gasLimit ?? '');
          inputText1 = tsModel.maxFee;
          inputText2 = tsModel.maxPriorityFee;
          inputText3 = tsModel.gasLimit;
          maxFee = inputText1;
          maxPriorityFee = inputText2;
          minPriorityFee = tsModel.minPriorityFee;
          if (tsModel.feeType == FeeType.customize) {
            baseFee = tsModel.baseFee;
          } else {
            baseFee = currentModel?.value?.baseFee;
          }
        } else {
          textController1 = TextEditingController(text: tsModel.gasPrice ?? '');
          textController2 = TextEditingController(text: tsModel.gasLimit ?? '');
          inputText1 = tsModel.gasPrice;
          inputText2 = tsModel.gasLimit;
          fee = inputText1;
        }
      } else if (coinType.isBitcoinSeries) {
        textController1 = TextEditingController(text: tsModel.btcSatB ?? '');
        inputText1 = tsModel.btcSatB;
        fee = inputText1;
      } else if (coinType is FilecoinChain) {
        textController1 =
            TextEditingController(text: getFilNano(tsModel.gasPrice));
        textController2 = TextEditingController(text: tsModel.gasLimit ?? '');
        inputText1 = getFilNano(tsModel.gasPrice);
        inputText2 = tsModel.gasLimit;
        fee = '0';
      } else if (coinType is SolanaChain) {
        textController1 = TextEditingController(text: tsModel.gasPrice ?? '');
        textController2 = TextEditingController(text: tsModel.gasLimit ?? '');
        inputText1 = tsModel.gasPrice;
        inputText2 = tsModel.gasLimit;
        fee = inputText1;
      } else if (coinType is RippleChain) {
        textController1 = TextEditingController(text: tsModel.fee ?? '');
        inputText1 = tsModel.fee;
        fee = tsModel.fee?.mul(pow(10, coinType.decimals).toString());
      } else if (coinType.isBabyCoinNetWork) {
        textController1 = TextEditingController(text: tsModel.cosmoseGas ?? '');
        textController2 = TextEditingController(text: tsModel.gasLimit ?? '');
        inputText1 = tsModel.cosmoseGas;
        inputText2 = tsModel.gasLimit;
        fee = inputText1;
      }
    } else {
      FeeGearModel? model = currentModel?.value;
      if (coinType.isEthereumSeries) {
        if (tsModel.isEip1559) {
          textController1 = TextEditingController(text: model?.maxFee ?? '');
          textController2 =
              TextEditingController(text: model?.maxPriorityFee ?? '');
          textController3 = TextEditingController(text: tsModel.gasLimit ?? '');
          inputText1 = tsModel.maxFee;
          inputText2 = tsModel.maxPriorityFee;
          inputText3 = tsModel.gasLimit;
          maxFee = inputText1;
          baseFee = tsModel.baseFee;
          minPriorityFee = tsModel.minPriorityFee;
          maxPriorityFee = inputText2;
        } else {
          textController1 = TextEditingController(text: model?.fee);
          textController2 = TextEditingController(text: tsModel.rawGasLimit);
          inputText1 = textController1?.text;
          inputText2 = textController2?.text;
          fee = inputText1;
        }
      } else if (coinType.isBitcoinSeries) {
        textController1 = TextEditingController(text: model?.fee);
        inputText1 = textController1?.text;
        fee = inputText1;
      } else if (coinType is FilecoinChain) {
        textController1 =
            TextEditingController(text: getFilNano(filGasModel?.gasFeeCap));
        textController2 = TextEditingController(text: tsModel.rawGasLimit);
        inputText1 = textController1?.text;
        inputText2 = textController2?.text;
        fee = '0';
      } else if (coinType is SolanaChain) {
        textController1 = TextEditingController(text: model?.fee);
        textController2 = TextEditingController(text: tsModel.rawGasLimit);
        inputText1 = textController1?.text;
        inputText2 = textController2?.text;
        fee = inputText1;
      } else if (coinType is RippleChain) {
        fee = model?.fee;
        String? text = RippleChain.get.calculateXRPFee(model?.fee);
        textController1 = TextEditingController(text: text ?? '');
        inputText1 = text;
      } else if (coinType.isBabyCoinNetWork) {
        textController1 = TextEditingController(text: tsModel.cosmoseGas ?? '');
        textController2 = TextEditingController(text: tsModel.gasLimit ?? '');
        inputText1 = tsModel.cosmoseGas;
        inputText2 = tsModel.gasLimit;
        fee = inputText1;
      }
    }

    /// 自定义
    customizeModel = FeeGearModel(
        fee: fee,
        maxFee: maxFee,
        baseFee: baseFee,
        minPriorityFee: minPriorityFee,
        maxPriorityFee: maxPriorityFee,
        name: APIConstant.customize);
    if (tsModel.feeType == FeeType.customize && currentModel == null) {
      currentModel = customizeModel.obs;
    }
  }

  String _updateText(String text) {
    if (text.endsWith('.')) {
      text = text.replaceRange(text.length - 1, null, '');
    }
    return text;
  }

  void updateText1Action(String text) {
    text = _updateText(text);
    if (tsModel.isEip1559) {
      customizeModel?.maxFee = text;
    } else {
      if (coinType is RippleChain) {
        customizeModel?.fee = text.mul(pow(10, coinType.decimals).toString());
      } else {
        customizeModel?.fee = text;
      }
    }
    inputText1 = text;
    _updateStatus();
  }

  void updateText2Action(String text) {
    text = _updateText(text);
    inputText2 = text;
    if (tsModel.isEip1559) {
      customizeModel?.maxPriorityFee = text;
      updateFee(model: customizeModel, gas: inputText3);
    } else {
      updateFee(model: customizeModel, gas: text);
    }
    _updateStatus();
  }

  void updateText3Action(String text) {
    text = _updateText(text);
    inputText3 = text;
    if (tsModel.isEip1559) {
      updateFee(model: customizeModel, gas: text);
    }
    _updateStatus();
  }

  void _updateStatus() {
    if (tsModel.isEip1559) {
      _eip1559TipText();
    } else {
      _updateTip1Text();
      _updateTip2Text();
    }

    // 输入框内容有改变即标记为自定义
    isCustomize = true;

    // 更新按钮状态
    if (textTip1Status.value != FeeTipTextStatus.disable &&
        textTip2Status.value != FeeTipTextStatus.disable &&
        textTip3Status.value != FeeTipTextStatus.disable) {
      buttonStatus.value = ButtonStatus.enable;
    } else {
      buttonStatus.value = ButtonStatus.disable;
    }

    // 更新矿工费
    if (coinType is FilecoinChain) {
      String? cap = textController1?.text;
      if (Get.isEmptyString(cap)) {
        cap = '0';
      }
      String gasFeeCap = cap!.mul(pow(10, 9).toString());
      updateFee(gas: textController2?.text, gasFeeCap: gasFeeCap);
    } else {
      if (tsModel.isEip1559) {
        updateFee(model: customizeModel, gas: textController3?.text);
      } else {
        updateFee(model: customizeModel, gas: textController2?.text);
      }
    }
  }

  void _updateTip2Text() {
    if (coinType.isEthereumSeries) {
      if (Get.isEmptyString(inputText2)) {
        inputText2 = '0';
      }

      if (inputText2!.lessThan(tsModel.rawGasLimit ?? '0') ||
          inputText2!.moreThan('15000000')) {
        textTip2.value = ID.ethGasLimitTip
            .trParams({'limit': (tsModel.rawGasLimit ?? '').format});
        textTip2Status.value = FeeTipTextStatus.disable;
        return;
      }
    } else if (coinType is FilecoinChain) {
      if (Get.isEmptyString(inputText2)) {
        textTip2.value = ID.filGasEmtyTips.tr;
        textTip2Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText2!.equal('0')) {
        textTip2.value = ID.filGasEmtyTips.tr;
        textTip2Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText2!.lessThan(filGasModel?.gasLimit ?? '0')) {
        textTip2.value = ID.filGasSlowTips.tr;
        textTip2Status.value = FeeTipTextStatus.warning;
        return;
      }
    } else if (coinType is SolanaChain) {
      if (Get.isEmptyString(inputText2)) {
        textTip2.value = ID.stringInputSolTip1.tr;
        textTip2Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText2!.equal('0')) {
        textTip2.value = ID.stringInputSolTip1.tr;
        textTip2Status.value = FeeTipTextStatus.disable;
        return;
      }
    } else if (coinType.isBabyCoinNetWork) {
      String? minGasLimit = inputText2;
      if (Get.isEmptyString(minGasLimit)) {
        minGasLimit = '0';
      }
      if (minGasLimit!.lessThanEqual('0')) {
        textTip2.value = ID.stringCosmosTip2.tr;
        textTip2Status.value = FeeTipTextStatus.disable;
        return;
      }

      textTip2.value = '';
      textTip2Status.value = FeeTipTextStatus.enable;
    }

    textTip2.value = '';
    textTip2Status.value = FeeTipTextStatus.enable;
  }

  void _updateTip1Text() {
    String? slowFee = slowModel?.fee;
    String? fastFee = fastModel?.fee;

    if (coinType.isEthereumSeries) {
      String? priceMin = '1';
      if (coinType is ArbitrumChain || coinType is OptimismChain) {
        priceMin = '0.1';
      }

      if (Get.isEmptyString(slowFee)) {
        slowFee = priceMin;
      }

      if (Get.isEmptyString(fastFee)) {
        fastFee = slowFee;
      }

      // empty
      if (Get.isEmptyString(inputText1)) {
        textTip1.value = ID.feeOkTips.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      // 不低于
      if (inputText1!.lessThan(priceMin)) {
        textTip1.value = ID.ethGasPriceMinTip.trParams({'gas': priceMin});
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      // 可能等待较长时间
      if (inputText1!.lessThan(slowFee!)) {
        textTip1.value = ID.ethGasPriceSlowTip.trParams({'fee': slowFee});
        textTip1Status.value = FeeTipTextStatus.warning;
        return;
      }

      // 浪费
      if (inputText1!.moreThan(fastFee!)) {
        textTip1.value = ID.ethGasPriceLargeTip.tr;
        textTip1Status.value = FeeTipTextStatus.warning;
        return;
      }

      textTip1.value = '';
      textTip1Status.value = FeeTipTextStatus.enable;
    } else if (coinType.isBitcoinSeries) {
      // empty
      if (Get.isEmptyString(inputText1)) {
        textTip1.value = ID.feeOkTips.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText1!.equal('0')) {
        textTip1.value = ID.feeOkTips.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (Get.isEmptyString(slowFee)) {
        slowFee = '1';
      }

      // 过低
      if (inputText1!.lessThan(slowFee!)) {
        textTip1.value = ID.feeSlowTips.tr;
        textTip1Status.value = FeeTipTextStatus.warning;
        return;
      }

      if (Get.isEmptyString(fastFee)) {
        fastFee = slowFee;
      }

      // 过高
      if (inputText1!.moreThan(fastFee!)) {
        textTip1.value = ID.feeFastTips.tr;
        textTip1Status.value = FeeTipTextStatus.warning;
        return;
      }

      textTip1.value = '';
      textTip1Status.value = FeeTipTextStatus.enable;
    } else if (coinType is FilecoinChain) {
      // empty
      if (Get.isEmptyString(inputText1)) {
        textTip1.value = ID.feeOkTips.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText1!.equal('0')) {
        textTip1.value = ID.feeOkTips.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      slowFee = filGasModel?.gasFeeCap;
      if (Get.isEmptyString(slowFee)) {
        slowFee = '0';
      }
      slowFee = slowFee!.div(pow(10, 9).toString(), scale: 18);

      // 过低
      if (inputText1!.lessThan(slowFee)) {
        textTip1.value = ID.feeSlowTips.tr;
        textTip1Status.value = FeeTipTextStatus.warning;
        return;
      }

      textTip1.value = '';
      textTip1Status.value = FeeTipTextStatus.enable;
    } else if (coinType is SolanaChain) {
      // empty
      if (Get.isEmptyString(inputText1)) {
        textTip1.value = ID.stringInputSolTip2.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText1!.equal('0')) {
        textTip1.value = ID.stringInputSolTip2.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      // 过低
      if (!Get.isEmptyString(normalModel?.fee)) {
        if (inputText1!.lessThan(normalModel!.fee!)) {
          textTip1.value =
              ID.stringSolPriceMinTip.trParams({'value': normalModel!.fee!});
          textTip1Status.value = FeeTipTextStatus.warning;
          return;
        }
      }

      textTip1.value = '';
      textTip1Status.value = FeeTipTextStatus.enable;
    } else if (coinType is RippleChain) {
      // empty
      if (Get.isEmptyString(inputText1)) {
        textTip1.value = ID.stringXripFeeError.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      if (inputText1!.equal('0')) {
        textTip1.value = ID.stringXripFeeError.tr;
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      textTip1.value = '';
      textTip1Status.value = FeeTipTextStatus.enable;
    } else if (coinType.isBabyCoinNetWork) {
      String? gas = inputText1;
      String? minGas = slowModel?.fee;
      if (Get.isEmptyString(gas)) {
        gas = '0';
      }

      if (Get.isEmptyString(minGas)) {
        minGas = '0';
      }

      if (gas!.lessThan(minGas!)) {
        textTip1.value = ID.stringCosmosTip1.trParams({'value': minGas});
        textTip1Status.value = FeeTipTextStatus.disable;
        return;
      }

      textTip1.value = '';
      textTip1Status.value = FeeTipTextStatus.enable;
    }
  }

  void _eip1559TipText() {
    _eip1559MaxFeeTip();
    _eip1559MaxProrityFeeTip();
    _eip1559GasTipText();
  }

  void _eip1559GasTipText() {
    String? gas = inputText3;
    String? rawGasLimit = tsModel.rawGasLimit;
    if (Get.isEmptyString(gas)) {
      gas = '0';
    }

    if (Get.isEmptyString(rawGasLimit)) {
      rawGasLimit = '0';
    }

    /// gas
    if (gas!.lessThan(rawGasLimit!) || gas.moreThan('15000000')) {
      textTip3.value = ID.ethGasLimitTip
          .trParams({'limit': (tsModel.rawGasLimit ?? '').format});
      textTip3Status.value = FeeTipTextStatus.disable;
      return;
    }

    textTip3.value = '';
    textTip3Status.value = FeeTipTextStatus.enable;
  }

  void _eip1559MaxProrityFeeTip() {
    String? maxFee = inputText1;
    String? rawMaxFee = normalModel?.maxFee;
    String? maxProrityFee = inputText2;
    String? baseFee = normalModel?.baseFee;

    if (Get.isEmptyString(maxFee)) {
      maxFee = '0';
    }

    if (Get.isEmptyString(rawMaxFee)) {
      rawMaxFee = '0';
    }
    rawMaxFee = rawMaxFee!.decimal(scale: 2);

    if (Get.isEmptyString(maxProrityFee)) {
      maxProrityFee = '0';
    }

    if (Get.isEmptyString(baseFee)) {
      baseFee = '0';
    }

    /// maxProrityFee > (rawMaxFee * 10)
    if (maxProrityFee!.moreThan(rawMaxFee.mul('4'))) {
      textTip2.value = ID.maxPriorityFeeHeightTip.tr;
      textTip2Status.value = FeeTipTextStatus.warning;
      return;
    }

    /// (maxProrityFee + baseFee) > maxFee
    if (maxProrityFee.add(baseFee!).moreThan(maxFee!)) {
      textTip2.value = ID.maxPriorityFeeTip.trParams({
        'inputMax': maxProrityFee,
        'rawMax': maxProrityFee,
        'baseFee': baseFee,
        'maxFee': maxFee
      });
      textTip2Status.value = FeeTipTextStatus.warning;
      return;
    }

    textTip2.value = '';
    textTip2Status.value = FeeTipTextStatus.enable;
  }

  void _eip1559MaxFeeTip() {
    String? maxFee = inputText1;
    String? rawMaxFee = normalModel?.maxFee;
    String? maxProrityFee = inputText2;

    if (Get.isEmptyString(maxFee)) {
      maxFee = '0';
    }

    if (Get.isEmptyString(rawMaxFee)) {
      rawMaxFee = '0';
    }
    rawMaxFee = rawMaxFee!.decimal(scale: 2);

    if (Get.isEmptyString(maxProrityFee)) {
      maxProrityFee = '0';
    }

    /// 输入有效的maxFee
    if (maxFee!.equal('0')) {
      textTip1.value = ID.maxFeeValidTip.tr;
      textTip1Status.value = FeeTipTextStatus.disable;
      textTip2Status.value = FeeTipTextStatus.enable;
      return;
    }

    /// maxFee 大于 4倍预估值
    if (maxFee.moreThan(rawMaxFee.mul('4'))) {
      textTip1.value = ID.maxFeeHeightTip.trParams({'rawFee': rawMaxFee});
      textTip1Status.value = FeeTipTextStatus.warning;
      return;
    }

    /// maxFee 小于 maxProrityFee
    if (maxFee.lessThan(maxProrityFee!)) {
      textTip1.value = ID.maxFeeNoLessMaxPriorityFeeTip.tr;
      textTip1Status.value = FeeTipTextStatus.disable;
      return;
    }

    textTip1.value = '';
    textTip1Status.value = FeeTipTextStatus.enable;
  }

  void customButtonAction() {
    Get.showToast(ID.customFeeSuccess.tr, toastMode: ToastMode.success);
    tsModel.feeType = FeeType.customize;
    String? fee;
    if (!tsModel.isEip1559) {
      if (coinType is RippleChain) {
        fee = inputText1?.mul(pow(10, coinType.decimals).toString());
      } else {
        fee = inputText1;
      }
    }

    /// 自定义矿工工费
    customizeModel?.fee = fee;
    if (coinType.isEthereumSeries) {
      if (tsModel.isEip1559) {
        tsModel.maxFee = inputText1;
        tsModel.maxPriorityFee = inputText2;
        tsModel.gasLimit = inputText3;
        customizeModel?.maxFee = inputText1;
        customizeModel?.maxPriorityFee = inputText2;
      } else {
        tsModel.gasPrice = inputText1;
        tsModel.gasLimit = inputText2;
      }
    } else if (coinType is FilecoinChain) {
      tsModel.gasPrice = inputText1!.mul(pow(10, 9).toString());
      tsModel.gasLimit = inputText2;
      customizeModel?.fee = '0';
    } else if (coinType.isBitcoinSeries) {
      tsModel.btcSatB = inputText1;
    } else if (coinType is SolanaChain) {
      tsModel.gasPrice = inputText1;
      tsModel.gasLimit = inputText2;
      customizeModel?.baseFee = tsModel.baseFee;
    } else if (coinType is RippleChain) {
      tsModel.xrpFee = customizeModel?.fee;
    } else if (coinType.isBabyCoinNetWork) {
      tsModel.cosmoseGas = inputText1;
      tsModel.gasLimit = inputText2;
    }

    Get.back(result: {
      GetArgumentsKey.tsModel: tsModel,
      GetArgumentsKey.gearModel: customizeModel
    });
  }
}
