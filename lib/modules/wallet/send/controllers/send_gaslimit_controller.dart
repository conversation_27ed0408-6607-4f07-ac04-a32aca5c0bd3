/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-07 10:04:23
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/common/wallet_common.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/model/transfer_model.dart';

extension SendGasLimitController on SendController {
  Future<dynamic> getGasLimitApi() async {
    String to = WalletConstant.gasLimitPlaceholderAddress;
    String? data;
    Map? extra;
    if (isNft) {
      data = await Get.walletCore.safeTransferFrom(
        ercType: nftModel!.ercType ?? '',
        from: addressModel!.address!,
        to: WalletConstant.gasLimitPlaceholderAddress,
        tokenId: nftModel!.tokenId ?? '',
      );
      extra = {APIConstant.coinType: 'nft'};
      to = nftModel!.contractAddress ?? to;
    } else {
      data = await Get.walletCore.getErc20Data(
        toAddress: to,
        amount: '1',
        decimal: coinModel!.chainDecimal!,
      );
      if (coinModel!.isToken == true && tokenModel != null) {
        to = tokenModel!.contract!;
      }
    }

    Map<String, dynamic> params = {};
    if (coinType is SolanaChain) {
      params = RequestParams()
          .put(APIConstant.chain, coinModel!.chain!)
          .getRequestBody();
    } else {
      params = RequestParams()
          .put(APIConstant.chain, coinModel!.chain!)
          .put(APIConstant.extra, extra)
          .put(
              APIConstant.params,
              RequestParams()
                  .put(APIConstant.to, to)
                  .put(APIConstant.data, data ?? '')
                  .put(APIConstant.from, tsModel.fromAddress!)
                  .getRequestBody())
          .getRequestBody();
    }

    return api
        .getGasLimit(BlockChainParamsManager.createParams(
            method: coinType is SolanaChain
                ? BlockChainAPI.solGasLimit
                : BlockChainAPI.gasLimit,
            requestParams: params))
        .then((value) => gasLimitResult(value));
  }

  Future<void> gasLimitResult(BaseResponseV1<dynamic> value) async {
    final data = value.data;

    String gasLimit = getGasLimit(data);

    // 刷新
    updateFee(model: gearModel);
    // 缓存gasLimit
    if (isNft) {
      await Get.database.coinDao.updateNftGasLimit(coinModel!, gasLimit);
    } else {
      await Get.database.coinDao.updateGasLimit(coinModel!, gasLimit);
    }
  }

  String getGasLimit(dynamic data) {
    // 默认
    String gasLimit = coinType.gasLimit ?? '';

    if (data != null) {
      if (data is String) {
        gasLimit = data.hexToDecimal;
      } else if (data is int || data is double) {
        gasLimit = data.toString().decimal(scale: 0, roundMode: RoundMode.down);
      }
    }

    // 不是自定义才刷新
    if (tsModel.feeType != FeeType.customize) {
      tsModel.gasLimit = gasLimit;
    }

    /// 记录gas
    tsModel.rawGasLimit = gasLimit;

    return gasLimit;
  }
}
