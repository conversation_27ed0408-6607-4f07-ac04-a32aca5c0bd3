/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 10:08:22
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_amount_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';

extension SendMaxAmountController on SendController {
  Future<void> maxAmountAction(String? maxAmount) async {
    if (isNft) {
      _nftMaxAmount(maxAmount);
    } else {
      if (coinModel!.isToken == false) {
        // 主链
        String extra = '0';
        if (coinType is RippleChain) {
          extra = RippleChain.get.reserveAmount;
        } else if (coinType is SolanaChain) {
          extra = solMinAmount;
        }
        if (!Get.isEmptyString(maxAmount)) {
          String fee = tsModel.fee ?? '0';
          if (coinType is NemChain) {
            fee = NemChain.get.calculateXEMFee(
                  remark: tsModel.remark,
                  amount: maxAmount,
                ) ??
                NemChain.get.maxFee;
          }
          maxAmount = maxAmount!.sub(fee).sub(extra);
          if (!maxAmount.moreThan('0')) {
            maxAmount = '0';
          }
        }
      }
      amountController.value.text = maxAmount ?? '';
      amountChangeAction(maxAmount ?? '');
    }
  }

  void _nftMaxAmount(String? maxAmount) {
    amountController.value.text = maxAmount ?? '';
    amountChangeAction(maxAmount ?? '');
  }
}
