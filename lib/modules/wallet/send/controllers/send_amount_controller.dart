/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-04 13:58:32
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:wallet_core/chain/nem/xem.dart';

extension SendAmountController on SendController {
  Future<void> amountChangeAction(String amount) async {
    if (Get.isEmptyString(amount)) {
      amount = '0';
    }
    tsModel.amount = amount;
    updateButtonStatus();

    /// 比特币系列实时计算矿工费
    if (coinType.isBitcoinSeries) {
      updateFee(
        model: gearModel,
        showToast: true,
      );
    } else if (coinType is NemChain) {
      tsModel.fee = await NemChain.get.getFee(tsModel);
    }

    if (isNft) {
      amountFiat.value = '';
    } else {
      if (Get.isEmptyString(amount)) {
        amountFiat.value = '';
      } else {
        amountFiat.value =
            '≈ ${BalanceManager.calculateFiatValue(amount, coinModel)}';
      }
    }
  }
}
