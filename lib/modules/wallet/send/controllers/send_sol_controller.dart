import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';

extension SendSolController on SendController {
  void getSolMinBalance() {
    if (coinType is! SolanaChain) return;

    if (coinModel!.isToken == false) {
      httpRequest(
          api.blockChainRequest(BlockChainParamsManager.createParams(
              method: BlockChainAPI.getSolMinBalance,
              requestParams: RequestParams()
                  .put(GetArgumentsKey.chain, coinType.chain)
                  .getRequestBody())), (value) async {
        if (value.data != null && value.data is int) {
          int data = value.data;
          String min = data.toString().div(
              pow(10, coinType.decimals).toString(),
              scale: coinType.decimals);
          solMinAmount = min;
          await Get.database.addressDao.updateSolMinimumRent(
            addressModel!,
            solMinAmount,
          );
        }
      });
    } else {
      httpRequest(
          api.blockChainRequest(BlockChainParamsManager.createParams(
              method: BlockChainAPI.solTokenMinimum,
              requestParams: RequestParams()
                  .put(GetArgumentsKey.chain, coinType.chain)
                  .getRequestBody())), (value) async {
        if (value.data != null && value.data is Map) {
          int? token = value.data['token'];
          int? token2022 = value.data['token2022'];
          String tokenType =
              SolanaChain.get.getTokenType(tokenModel?.tokenType);
          if (tokenType == SolanaChain.solToken) {
            solTokenOpenAccountFee = (token ?? 0).toString().div(
                pow(10, coinType.decimals).toString(),
                scale: coinType.decimals);
          } else if (tokenType == SolanaChain.solToken2022) {
            solTokenOpenAccountFee = (token2022 ?? 0).toString().div(
                pow(10, coinType.decimals).toString(),
                scale: coinType.decimals);
          }
        }
      });
    }
  }

  void getSolanaInfo(Function(bool result) callback) {
    List<Future> futures = [];
    futures.add(api.blockChainRequest(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getSolanaBlockHash,
        requestParams: RequestParams()
            .put(GetArgumentsKey.chain, coinType.chain)
            .getRequestBody())));
    if (coinModel!.isToken! == true) {
      futures.add(api.blockChainRequest(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getAccountInfo,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinType.chain)
              .put(APIConstant.tokenType, tokenModel?.tokenType)
              .put(APIConstant.address, tsModel.toAddress)
              .put(APIConstant.contract, tokenModel?.contract)
              .getRequestBody())));
    }

    multiHttpRequest(
      futures,
      handleSuccess: false,
      handleError: false,
      (value) {
        if (value is List && value.isNotEmpty) {
          BaseResponseV1 response1 = value[0];
          bool hashResult = _blockHash(response1);
          if (hashResult == false) {
            callback(false);
            return;
          }

          if (value.length == 2) {
            BaseResponseV1 response2 = value[1];
            bool accountResult = _accountInfo(response2);
            if (accountResult == false) {
              callback(false);
              return;
            }
          }
          callback(true);
        } else {
          Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
          callback(false);
          return;
        }
      },
    );
  }

  bool _accountInfo(BaseResponseV1 value) {
    final data = value.data;
    bool? isNewTokenAccount;
    try {
      isNewTokenAccount = !data[GetArgumentsKey.openedAccount];
    } catch (_) {}

    if (isNewTokenAccount == null) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      return false;
    }
    tsModel.isNewTokenAccount = isNewTokenAccount;

    if (tsModel.isNewTokenAccount == true) {
      String feeValue = Get.isEmptyString(fee) ? '0' : fee;
      if ((mainChainBalance ?? '0')
          .lessThan(solTokenOpenAccountFee.add(feeValue))) {
        Get.showToast(
          ID.stringSoloTokenOpenTip.tr,
          toastMode: ToastMode.waring,
        );
        return false;
      }
    }

    return true;
  }

  bool _blockHash(BaseResponseV1 value) {
    final data = value.data;
    String? blockhash;
    if (data != null) {
      blockhash = data[GetArgumentsKey.blockhash] ?? '';
      tsModel.blockhash = blockhash;
    }
    if (Get.isEmptyString(blockhash)) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      return false;
    } else {
      return true;
    }
  }
}
