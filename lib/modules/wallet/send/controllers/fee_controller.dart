/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-03 14:44:36
 */
import 'dart:math';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/modules/dapp/browser/ethereum/etherum_dapp_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_customize_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/modules/wallet/send/models/fil_gas_model.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/modules/wallet/send/widgets/fee/fee_customize_widget.dart';
import 'package:coinbag/modules/wallet/send/widgets/fee/fee_eip1559_widget.dart';
import 'package:coinbag/modules/wallet/send/widgets/fee/fee_gear_widget.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/cosmos/baby.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/filcoin/fil.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/model/utxo/utxo_result_model.dart';

enum FeeTipTextStatus { enable, disable, warning }

class FeeController extends BaseController<BlockChainService> {
  @override
  void loadData() {}

  RxBool isShowMore = false.obs;
  RxInt pageIndex = 0.obs;
  final PageController pagerController = PageController();
  List pagerList = [];

  /// 自定义
  TextEditingController? textController1;
  TextEditingController? textController2;
  TextEditingController? textController3;
  String? inputText1;
  String? inputText2;
  String? inputText3;
  RxString textTip1 = ''.obs;
  Rx<FeeTipTextStatus> textTip1Status = FeeTipTextStatus.enable.obs;
  RxString textTip2 = ''.obs;
  Rx<FeeTipTextStatus> textTip2Status = FeeTipTextStatus.enable.obs;
  RxString textTip3 = ''.obs;
  Rx<FeeTipTextStatus> textTip3Status = FeeTipTextStatus.enable.obs;
  Rx<ButtonStatus> buttonStatus = ButtonStatus.enable.obs;
  bool isCustomize = false;

  /// 档位
  RxList<FeeGearModel> gearList = <FeeGearModel>[].obs;
  late TransferModel tsModel;
  Rx<FeeType> feeType = FeeType.normal.obs;
  late CoinType coinType;
  CoinModel? coinModel;
  FilGasModel? filGasModel;

  /// 头部
  RxString feeText = ''.obs;
  RxString detailText = ''.obs;
  RxString fiatText = ''.obs;

  FeeGearModel? customizeModel;
  FeeGearModel? slowModel;
  FeeGearModel? fastModel;
  FeeGearModel? normalModel;
  Rx<FeeGearModel?>? currentModel;

  @override
  void onInit() {
    tsModel = Get.arguments[GetArgumentsKey.tsModel];
    coinType = CoinBase.getCoinTypeByChain(tsModel.chain)!;
    feeType.value = tsModel.feeType;
    if (tsModel.isEip1559) {
      pagerList = [const FeeGearWidget(), const FeeEIP1559Widget()];
    } else {
      pagerList = [const FeeGearWidget(), const FeeCustomizeWidget()];
    }
    pageIndex.value = tsModel.feeType == FeeType.customize ? 1 : 0;

    gearList.value = Get.arguments[GetArgumentsKey.gearList];
    coinModel = Get.arguments[GetArgumentsKey.coinModel];
    filGasModel = Get.arguments[GetArgumentsKey.filGasModel];
    _updateGearList(gearList);
    setupTextController();
    updateGearModel(currentModel?.value);
    super.onInit();
  }

  @override
  onReady() {
    super.onReady();
    ever(gearList, (callback) {
      _updateGearList(callback);
      if (pageIndex.value == 0) {
        updateGearModel(currentModel?.value);
      }
    });

    pagerController.jumpToPage(pageIndex.value);
  }

  void _updateGearList(List<FeeGearModel> list) {
    for (var model in list) {
      if (model.feeType == FeeType.slow) {
        slowModel = model;
      } else if (model.feeType == FeeType.fast) {
        fastModel = model;
      } else if (model.feeType == FeeType.normal) {
        normalModel = model;
      }

      if (tsModel.feeType == model.feeType) {
        if (currentModel == null) {
          currentModel = model.obs;
        } else {
          currentModel?.value = model;
        }
      }
    }
  }

  void updateGearModel(FeeGearModel? model) {
    if (model == null) return;
    tsModel.feeType = model.feeType;
    currentModel?.value = model;

    if (coinType is FilecoinChain) {
      feeType.value = model.feeType;
      updateFee(gas: tsModel.gasLimit, gasFeeCap: tsModel.gasPrice);
    } else {
      tsModel.feeType = model.feeType;
      feeType.value = model.feeType;
      updateFee(model: model, gas: tsModel.rawGasLimit);
    }
  }

  // 档位切换
  void updateModelAction(FeeGearModel? model) {
    isCustomize = false;
    updateGearModel(model);
    try {
      if (Get.isRegistered<SendController>()) {
        SendController sendCtr = Get.find<SendController>();
        sendCtr.gearModel = model;
        sendCtr.updateFee(model: model);
      }

      if (Get.isRegistered<WebController>()) {
        WebController webController = Get.find<WebController>();
        webController.gearModel = model;
        webController.handelFeeValue();
      }
    } catch (_) {}
  }

  void updatePage() {
    if (pageIndex.value == 0 && feeType.value != FeeType.customize) {
      if (coinType is FilecoinChain) {
        updateFee(gas: tsModel.gasLimit, gasFeeCap: tsModel.gasPrice);
      } else {
        updateFee(model: currentModel?.value, gas: tsModel.rawGasLimit);
      }
    } else {
      String? gas;
      if (tsModel.isEip1559) {
        gas = inputText3;
      } else {
        gas = inputText2;
      }

      String gasFeeCap = inputText1 ?? '0';

      if (coinType is FilecoinChain) {
        gasFeeCap = gasFeeCap.mul(pow(10, 9).toString());
      }

      updateFee(model: customizeModel, gas: gas, gasFeeCap: gasFeeCap);
    }
  }

  Future<void> updateFee(
      {FeeGearModel? model, String? gas, String? gasFeeCap}) async {
    String? modelFee = model?.fee;
    if (Get.isEmptyString(modelFee)) {
      modelFee = '0';
    }

    if (Get.isEmptyString(gas)) {
      gas = '0';
    }

    if (coinType.isEthereumSeries) {
      if (tsModel.isEip1559) {
        String? max = _maxFee(model, gas!);
        String? min = _minFee(model, gas);
        if (Get.isEmptyString(max)) {
          feeText.value = CommonConstant.emptyAsstes;
          fiatText.value = CommonConstant.emptyAsstes;
          detailText.value = CommonConstant.emptyAsstes;
          return;
        }

        String? maxFee = model?.maxFee;
        if (Get.isEmptyString(maxFee)) {
          maxFee = '0';
        }
        maxFee = maxFee!.decimal(scale: 2);

        if (max!.equal(min ?? '') || Get.isEmptyString(min)) {
          feeText.value = '$max ${coinType.symbol}';
          fiatText.value = BalanceManager.calculateFiatValue(max, coinModel);
          detailText.value =
              '$max ${coinType.symbol} = MaxFee($maxFee) * Gas($gas)';
          return;
        }

        feeText.value = '$min ${coinType.symbol} - $max ${coinType.symbol}';
        fiatText.value =
            '${BalanceManager.calculateFiatValue(min, coinModel)} - ${BalanceManager.calculateFiatValue(max, coinModel)}';
        detailText.value =
            '$max ${coinType.symbol} = MaxFee($maxFee) * Gas($gas)';
      } else {
        String fee = EthereumChain.get
                .calculateETHSeriesFee(gasLimit: gas, gasPrice: modelFee) ??
            '';
        feeText.value = '$fee ${coinModel?.symbol ?? ''}';
        fiatText.value = BalanceManager.calculateFiatValue(fee, coinModel);
        detailText.value = 'Gas($gas) * GasPrice($modelFee GWEI)';
      }
    } else if (coinType.isBitcoinSeries) {
      UtxoFeeResultModel? resultModel =
          await Get.walletCore.getBitcoinSeriesFeeOrBestUtxos(
        chain: coinType.chain,
        amount: tsModel.amount ?? '0',
        btcSatB: tsModel.btcSatB ?? '0',
        dust: coinType.dust,
        decimal: tsModel.coinDecimal!,
        utxoList: tsModel.utxoList ?? [],
        fromAddress: tsModel.fromAddress!,
        availableBalance: tsModel.availableBalance ?? '0',
      );
      String fee = resultModel?.fee ?? '0';
      feeText.value = '$fee ${coinModel?.symbol ?? ''}';
      fiatText.value = BalanceManager.calculateFiatValue(fee, coinModel);
    } else if (coinType is FilecoinChain) {
      String filPrice = getFilNano(gasFeeCap);
      String? fee =
          FilecoinChain.get.calculateFILFee(gasPrice: gasFeeCap, gasLimit: gas);
      feeText.value = '$fee ${coinModel?.symbol ?? ''}';
      fiatText.value = BalanceManager.calculateFiatValue(fee, coinModel);
      detailText.value = 'GasPrice($filPrice NanoFIL) * Gas($gas)';
    } else if (coinType is SolanaChain) {
      String? fee = SolanaChain.get.calculateSolFee(
        gasLimit: gas,
        gasPrice: modelFee,
        baseFee: tsModel.baseFee,
      );
      feeText.value = '$fee ${coinModel?.symbol ?? ''}';
      fiatText.value = BalanceManager.calculateFiatValue(fee, coinModel);
      detailText.value =
          'Compute Unit Limit($gas) * (Compute Unit Price($modelFee microLamport) + Base Fee(${tsModel.baseFee} microLamport))';
    } else if (coinType is RippleChain) {
      String? fee = RippleChain.get.calculateXRPFee(modelFee);
      feeText.value = '$fee ${coinModel?.symbol ?? ''}';
      fiatText.value = BalanceManager.calculateFiatValue(fee, coinModel);
      detailText.value = '';
    } else if (coinType.isBabyCoinNetWork) {
      String? fee = BabylonChain.get.calculateCosmosFee(
        gasLimit: gas,
        cosmoseGas: modelFee,
        decimals: coinType.decimals,
      );
      feeText.value = '$fee ${coinModel?.symbol ?? ''}';
      fiatText.value = BalanceManager.calculateFiatValue(fee, coinModel);
      detailText.value = '';
    }
  }

  String? _maxFee(FeeGearModel? model, String gas) {
    String? maxFee = model?.maxFee;
    if (Get.isEmptyString(maxFee)) {
      maxFee = '0';
    }

    return EthereumChain.get
        .calculateETHSeriesEip1559Fee(gasLimit: gas, maxFee: maxFee);
  }

  String? _minFee(FeeGearModel? model, String gas) {
    String? baseFee = model?.baseFee;
    if (Get.isEmptyString(baseFee)) {
      baseFee = '0';
    }
    String? minPriorityFee = model?.minPriorityFee;
    if (Get.isEmptyString(minPriorityFee)) {
      minPriorityFee = '0';
    }

    String? minFee = baseFee!.add(minPriorityFee!);
    if (Get.isEmptyString(minFee)) {
      minFee = '0';
    }
    return EthereumChain.get
        .calculateETHSeriesEip1559Fee(gasLimit: gas, maxFee: minFee);
  }

  String getFilNano(String? gasPrice) {
    return (Get.isEmptyString(gasPrice) ? '0' : gasPrice!)
        .div(pow(10, 9).toString(), scale: 9);
  }

  String getSolFee(String? fee) {
    return fee?.div(pow(10, coinType.decimals).toString(),
            scale: coinType.decimals) ??
        '';
  }

  void leadingAction() {
    if (isCustomize == false) {
      Get.back();
      return;
    }
    Get.showBottomSheet(
        title: ID.stringTips.tr,
        bottomWidget: Padding(
          padding: EdgeInsets.only(
              top: Get.setPaddingSize(12),
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16)),
          child: ButtonWidget(
            text: ID.stringConfirm.tr,
            onPressed: () {
              Get.back();
              Get.back();
            },
          ),
        ),
        bodyWidget: Padding(
          padding: EdgeInsets.only(
              bottom: Get.setPaddingSize(24),
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16)),
          child: Text(
            ID.customFeeConfirm.tr,
            style: TextStyle(
                fontSize: Get.setFontSize(14), color: Get.theme.textPrimary),
          ),
        ));
  }

  @override
  void onClose() {
    super.onClose();
    pagerController.dispose();
    textController1?.dispose();
    textController2?.dispose();
  }
}

class FeeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => FeeController());
  }
}
