/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-19 15:42:35
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/send/models/fil_gas_model.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/model/transfer_model.dart';

extension SendFILController on SendController {
  Future<dynamic> filGas() async {
    return api
        .getFilLimit(
          BlockChainParamsManager.createParams(
              method: BlockChainAPI.getFilGas,
              requestParams: RequestParams()
                  .put(APIConstant.chain, coinModel!.chain!)
                  .put(APIConstant.exact, true)
                  .put(APIConstant.revert, true)
                  .getRequestBody()),
        )
        .then((value) => _filGasLimitResult(value));
  }

  Future<void> _filGasLimitResult(BaseResponseV1<dynamic> value) async {
    final data = value.data;
    if (data != null && data is Map) {
      updateFILGas(Map<String, dynamic>.from(data));
      await Get.database.coinDao
          .updateFilGasJsonStr(coinModel!, jsonEncode(data));
    }
  }

  /// 更新FIL GAS
  void updateFILGas(Map<String, dynamic> data) {
    filGasModel = FilGasModel.fromJson(data);
    if (tsModel.feeType != FeeType.customize) {
      tsModel.gasLimit = filGasModel!.gasLimit;
      tsModel.gasPrice = filGasModel!.gasFeeCap;
    }
    tsModel.rawGasLimit = filGasModel!.gasLimit;
    tsModel.gasFeePremium = filGasModel!.gasFeePremium;
  }
}
