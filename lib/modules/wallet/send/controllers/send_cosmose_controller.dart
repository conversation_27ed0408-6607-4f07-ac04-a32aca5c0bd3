/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-15 11:07:38
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_gaslimit_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_verify_transfer_controller.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/checkbox/checkbox_widget.dart';
import 'package:flutter/material.dart';

extension SendCosmoseController on SendController {
  Future<dynamic> cosmosSimulate() async => api
      .blockChainRequest(BlockChainParamsManager.createParams(
          method: BlockChainAPI.cosmosSimulate,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinType.chain)
              .put(APIConstant.fromAddress, tsModel.fromAddress)
              .getRequestBody()))
      .then((value) => gasLimitResult(value));

  void cosmosMemo() {
    Get.showBottomSheet(
      title: ID.stringTips.tr,
      onCancel: () {
        Get.back();
      },
      bodyWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: Get.setHeight(10)),
            Text(
              ID.stringBabyTsTip.tr,
              style: TextStyle(
                  fontSize: Get.setFontSize(16),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                  color: Get.theme.textPrimary),
            ),
            SizedBox(height: Get.setHeight(20)),
            Obx(() => GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onChanged,
                child: Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: CheckboxWidget(
                        isCheck: noMorePrompts.value,
                        onChanged: (value) => onChanged(),
                      ),
                    ),
                    SizedBox(width: Get.setPaddingSize(6)),
                    Text(
                      ID.stringNoMorePrompts.tr,
                      style: TextStyle(
                          fontSize: Get.setFontSize(14),
                          fontFamily: Get.setFontFamily(),
                          fontWeight: FontWeightX.regular,
                          color: Get.theme.textPrimary),
                    ),
                  ],
                ))),
            SizedBox(height: Get.setHeight(30)),
          ],
        ),
      ),
      bottomWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        child: ButtonWidget(
          text: ID.stringConfirm.tr,
          onPressed: () async {
            Get.back();
            verifyActionAgain();
          },
        ),
      ),
    );
  }

  void onChanged() {
    noMorePrompts.value = !noMorePrompts.value;
    StorageManager.saveValue(
        key: StorageKey.noMorePrompts, value: noMorePrompts.value);
  }
}
