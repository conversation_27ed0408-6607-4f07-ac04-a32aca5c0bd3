/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-10 17:53:13
 */

import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_initialize_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

extension SendGearController on SendController {
  /// 获取矿工费档位
  Future<void> gearFeeApi() async {
    tsModel.wallet ??= await getWallet;

    final requestParams =
        RequestParams().put(APIConstant.chain, coinType.chain).getRequestBody();
    if (coinType is SolanaChain) {
      requestParams[GetArgumentsKey.address] = tsModel.fromAddress;
    }
    try {
      api
          .getFeeGear(BlockChainParamsManager.createParams(
              method: tsModel.isEip1559
                  ? BlockChainAPI.feeGearEIP1559
                  : BlockChainAPI.feeGear,
              requestParams: requestParams))
          .then((value) => _gearFeeResult(value));
    } catch (e) {}
  }

  Future<void> _gearFeeResult(BaseResponseV1<dynamic> value) async {
    final data = value.data;

    if (timer == null) return;

    if (data != null) {
      /// 更新档位矿工费
      updateGears(data);
      if (coinModel != null) {
        await Get.database.coinDao.updateTier(coinModel!, jsonEncode(data));
      }
    }
  }

  void updateGears(List data, {FeeType? type}) {
    feeGearList.value = data.map((e) {
      FeeGearModel model = gearModelFromJson(
        json: e,
        coinType: coinType,
        type: type,
      );

      // 为null则为第一次进入页面
      if (gearModel == null && model.feeType == FeeType.normal) {
        gearModel = model;
      } else {
        if (gearModel?.feeType == model.feeType) {
          gearModel = model;
        }
      }

      return model;
    }).toList();

    try {
      FeeController feeCtr = Get.find<FeeController>();
      feeCtr.gearList.value = feeGearList;
      if (feeCtr.currentModel != null) {
        ever(feeCtr.currentModel!, (callback) {
          gearModel = callback;
          updateFee(model: callback);
        });
      }
    } catch (_) {}
  }

  static FeeGearModel gearModelFromJson({
    required Map<String, dynamic> json,
    required CoinType coinType,
    FeeType? type,
  }) {
    FeeGearModel model = FeeGearModel.fromJson(json, coinType);
    if (Get.isEmptyString(model.fee)) {
      model.fee = '0';
    }

    if (model.fee! == 'null') {
      model.fee = '0';
    }

    if (coinType.isBitcoinSeries) {
      model.fee = model.fee!.div('1000', scale: 0, roundMode: RoundMode.round);
    } else if (coinType.isEthereumSeries) {
      model.fee =
          model.fee!.div('1000000000', scale: 2, roundMode: RoundMode.round);
    }
    if (type != null) {
      if (type == FeeType.slow) {
        model.name = 'rear_block_fee';
      } else if (type == FeeType.normal) {
        model.name = 'estimatefee';
      } else if (type == FeeType.fast) {
        model.name = 'top_block_fee';
      }
      model.name = 'name';
    }
    return model;
  }
}
