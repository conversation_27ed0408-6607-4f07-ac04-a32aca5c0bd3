/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-03 13:19:33
 */

import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_cosmose_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_eos_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_request_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_sol_controller.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_trx_controller.dart';
import 'package:coinbag/modules/wallet/send/dialog/transfer_info_dialog.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:pub_semver/pub_semver.dart';
import 'package:wallet_core/chain/bitcoin/zec.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/ethereum/layer/arb.dart';
import 'package:wallet_core/chain/ethereum/layer/opt.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/pro3_plus_wallet.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

extension SendVerifyController on SendController {
  Future<void> verifyAction(BuildContext context) async {
    KeyboardUtils.hideKeyboard(context);
    stopTimer();

    if (!_eosIsSupported()) {
      return;
    }

    /// p3+ 不支持转账 软件版本1.3.2不支持XRP的转账 软件版本1.4.3以下不支持ETH和zcash的转账
    if (tsModel.wallet is Pro3PlusWallet) {
      Version maxVersionForEth = Version.parse('1.4.6');

      Version maxVersionForEthZec = Version.parse('1.4.2');

      Version maxVersionForXrp = Version.parse('1.3.2');
      String appVersion = walletModel!.appVersion!.toLowerCase();
      appVersion = appVersion.replaceAll('v', '');
      Version version = Version.parse(appVersion);

      // 检查ETH和Zcash是否受版本限制
      if (version <= maxVersionForEthZec) {
        if (coinType is EthereumChain) {
          Get.showAlertDialog(
              title: ID.stringNotices.tr,
              content: ID.stringEthNotSupportedTips.tr,
              onCancelText: ID.stringContinueTx.tr,
              onContinue: () => verifyActionAgain(),
              onConfirmText: ID.stringConfirm.tr);
          startTimer();
          return;
        } else if (coinType is ZcashChain) {
          Get.showAlertDialog(
              title: ID.stringNotices.tr,
              content: ID.stringZecNotSupportedTips.tr,
              onCancelText: ID.stringContinueTx.tr,
              onContinue: () => verifyActionAgain(),
              onConfirmText: ID.stringConfirm.tr);
          startTimer();
          return;
        }
      }
      if (version < maxVersionForEth && version > maxVersionForEthZec) {
        if (coinType is EthereumChain) {
          Get.showAlertDialog(
              title: ID.stringNotices.tr,
              content: ID.stringNotFullySupportETH.tr,
              onCancelText: ID.stringContinueTx.tr,
              onContinue: () => verifyActionAgain(),
              onConfirmText: ID.stringConfirm.tr);
          startTimer();
          return;
        }
      }

      // 检查XRP是否受版本限制
      if (version <= maxVersionForXrp) {
        if (coinType is RippleChain) {
          Get.showAlertDialog(
              title: ID.stringNotices.tr,
              content: ID.stringXrpNotSupportedTips.tr,
              onCancelText: ID.stringContinueTx.tr,
              onContinue: () => verifyActionAgain(),
              onConfirmText: ID.stringConfirm.tr);
          startTimer();
          return;
        }
      }
    }

    /// BABY memo 弹窗提示
    if (coinType.isBabyCoinNetWork &&
        Get.isEmptyString(tsModel.remark) &&
        noMorePrompts.value == false) {
      if (await _verifyToAddress() == false) {
        startTimer();
        return;
      }
      cosmosMemo();
      return;
    }

    verifyActionAgain();
  }

  Future<void> verifyActionAgain() async {
    if (await _verifyToAddress() == false) {
      startTimer();
      return;
    }

    // 查询余额或者nonce
    if (coinType.isBitcoinSeries) {
      String feeStr = await getBitcoinSeriesFeeOrBestUtxos();
      if (feeStr.equal('0')) {
        Get.showToast(ID.insufficientBalanceFee.tr,
            toastMode: ToastMode.waring);
        buttonStatus.value = ButtonStatus.enable;
        startTimer();
        return;
      }
      tsModel.fee = feeStr;

      buttonStatus.value = ButtonStatus.loading;
      if (coinType.isBitCoinNetWork) {
        bool isPsbt = await _isPsbtTransfer();
        if (isPsbt && addressModel!.segwitType == CoinAddressProtocol.legacy) {
          psbtRequest(callback: (result) {
            if (result == true) {
              utxoBalanceRequest(callback: () {
                if (!_verifyAmount()) {
                  startTimer();
                }
              });
            } else {
              buttonStatus.value = ButtonStatus.enable;
              startTimer();
              return;
            }
          });
          return;
        }
      }

      utxoBalanceRequest(callback: () {
        if (!_verifyAmount()) {
          startTimer();
          return;
        }
      });
    } else {
      // Tron 不查询余额
      if (coinType is TronChain) {
        _verifyAmount();
        return;
      }

      if (coinModel!.isToken == true) {
        buttonStatus.value = ButtonStatus.loading;
        balanceRequest(
          address: tsModel.fromAddress!,
          isMainChain: true,
          callback: ({
            balanceModel,
            required result,
          }) {
            if (result) {
              String balance = balanceModel!.balance ?? '0';
              int decimal = balanceModel.decimal ?? coinModel!.chainDecimal!;
              balance =
                  balance.div(pow(10, decimal).toString(), scale: decimal);
              mainChainBalance = balance;
            } else {
              buttonStatus.value = ButtonStatus.enable;
              startTimer();
              return;
            }
          },
        );
      }

      if (coinType is ArbitrumChain || coinType is OptimismChain) {
        getArbAndOptNonce(({nonce}) {
          if (!Get.isEmptyString(nonce)) {
            tsModel.nonce = nonce;
            if (!_verifyAmount()) {
              startTimer();
            }
          } else {
            startTimer();
          }
        });
      } else {
        getBalanceAndNonce(
          callback: ({
            balanceModel,
            required result,
          }) {
            buttonStatus.value = ButtonStatus.enable;
            if (balanceModel != null) {
              tsModel.nonce = balanceModel.nonce;
              tsModel.bnbAccountNumber = balanceModel.extra?.accountNumber;
              tsModel.cosmoseAccountNumber = balanceModel.accountNumber;
              String? balance = balanceModel.balance;

              if (!Get.isEmptyString(balance) && isNft == false) {
                balance = balance!.div(
                  pow(10, coinModel!.chainDecimal!).toString(),
                  scale: coinModel!.balanceDecimal!,
                );
                availableBalance.value = balance.decimal(scale: 9);
                if (tsModel.isToken == false) {
                  mainChainBalance = availableBalance.value;
                }
              }
              if (!_verifyAmount()) {
                startTimer();
              }
            } else {
              startTimer();
              buttonStatus.value = ButtonStatus.enable;
            }
          },
        );
      }
    }
  }

  Future<bool> _verifyToAddress() async {
    // token 查询主链addressModel
    if (tokenModel != null && addressModel == null) {
      addressModel = await Get.database.addressDao.getAddressModel(
        walletId: walletModel!.walletId!,
        deviceId: walletModel!.deviceId!,
        chain: tokenModel!.chain ?? '',
        address: tokenModel!.address ?? '',
      );
      tsModel.addressPath = addressModel?.path;
      tsModel.walletId = addressModel?.walletId;
      tsModel.xpub = addressModel?.xpubData;
      mainChainBalance = addressModel?.balance;
    }

    // 验证接收地址
    String? toAddress = tsModel.toAddress;
    if (Get.isEmptyString(toAddress)) {
      _showToast(ID.inputToAddressTip.tr);
      return false;
    }

    bool verify = await Get.walletCore
        .verifyAddress(chain: coinType.chain, address: toAddress ?? '');

    if (verify != true) {
      _showToast(ID.inputOkToAddressTip.tr);
      return false;
    }

    // Taproot地址判断
    if (coinType.isBitCoinNetWork && !tsModel.wallet!.isSupportTaprootAddress) {
      bool? isTapRootAddress =
          await Get.walletCore.isTapRootAddress(toAddress: toAddress ?? '');
      if (isTapRootAddress!) {
        _showToast(ID.stringNotSupportedTaproot.tr);
        return false;
      }
    }

    // 是否能给自己转账
    if (!coinType.isTransferToSelf &&
        tsModel.fromAddress == tsModel.toAddress) {
      _showToast(ID.transferToSelfTip.tr);
      return false;
    }

    // 验证转账数量
    String? amount = tsModel.amount;
    if (Get.isEmptyString(amount)) {
      _showToast(ID.inputAmountTip.tr);
      return false;
    }

    if (amount!.isNumeric == false) {
      _showToast(ID.amounNumberError.tr);
      return false;
    }

    if (coinModel!.isToken == true) {
      if (coinType is SolanaChain) {
        if (!amount.moreThan('0')) {
          _showToast(ID.sendAmountSmall.tr);
          return false;
        }
      }
      return true;
    }

    if (coinType.isEthereumSeries) {
      if (!(tsModel.wallet!.isUltraSeries || tsModel.wallet! is TouchWallet)) {
        if (!amount.moreThan('0')) {
          _showToast(ID.sendAmountSmall.tr);
          return false;
        }
      }
    } else if (coinType is SolanaChain) {
      if (!amount.moreThan('0')) {
        _showToast(ID.sendAmountSmall.tr);
        return false;
      }
      if (amount.lessThan(solMinAmount)) {
        _showToast(ID.stringInputSolTip3
            .trParams({'value': '$solMinAmount ${coinModel?.symbol}'}));
        return false;
      }
    } else {
      if (coinType.dust.moreThan('0')) {
        if (!amount.moreThanEqual(coinType.dust)) {
          _showToast(ID.sendAmountSmall.tr);
          return false;
        }
      } else {
        if (!amount.moreThan(coinType.dust)) {
          _showToast(ID.sendAmountSmall.tr);
          return false;
        }
      }
    }

    return true;
  }

  /// 是否是psbt交易 - 发送地址或者接收地址任意一个是taproot地址则为psbt
  Future<bool> _isPsbtTransfer() async {
    if (!tsModel.wallet!.isSupportPsbt) return false;

    bool isFromTaproot = await Get.walletCore
            .isTapRootAddress(toAddress: tsModel.fromAddress!) ??
        false;
    bool isToTaproot =
        await Get.walletCore.isTapRootAddress(toAddress: tsModel.toAddress!) ??
            false;
    tsModel.isPsbtTransfer = isFromTaproot || isToTaproot;
    return tsModel.isPsbtTransfer;
  }

  bool _verifyAmount() {
    buttonStatus.value = ButtonStatus.enable;
    String amount = tsModel.amount!;
    String feeValue = Get.isEmptyString(fee) ? '0' : fee;
    if (coinType is NemChain) {
      feeValue = tsModel.fee ?? '0';
    }

    // 主链
    if (coinModel!.isToken == false && isNft == false) {
      /// xrp
      if (coinType is RippleChain) {
        if (amount
            .add(feeValue)
            .add(RippleChain.get.reserveAmount)
            .moreThan(mainChainBalance ?? '0')) {
          Get.showAlertDialog(
              title: ID.stringNotices.tr,
              content: ID.xrpTsTips
                  .trParams({'value': RippleChain.get.reserveAmount}));
          return false;
        }
      }

      /// Tron 接收地址未激活消耗1TRX，有备注消耗1TRX
      if (coinType is TronChain) {
        if (!tsModel.activated) {
          feeValue = feeValue.add('1');
        }
        if (!Get.isEmptyString(tsModel.remark)) {
          feeValue = feeValue.add('1');
        }
      }

      /// Solana 判断最低转账数量
      if (coinType is SolanaChain) {
        if (amount.lessThan(solMinAmount)) {
          _showToast(ID.stringInputSolTip3
              .trParams({'value': '$solMinAmount ${coinModel?.symbol}'}));
          return false;
        }
      }

      String solAmount = amount.add(solMinAmount);

      if (solAmount.moreThan(mainChainBalance ?? '0')) {
        _showToast(ID.insufficientBalance.tr);
        return false;
      }

      if (solAmount.add(feeValue).moreThan(mainChainBalance ?? '0')) {
        _showToast(ID.insufficientMainFee.tr);
        return false;
      }
    } else {
      if (amount.moreThan(availableBalance.value)) {
        _showToast(ID.insufficientBalance.tr);
        return false;
      }

      if (feeValue.moreThan(mainChainBalance ?? '0')) {
        _showToast(ID.insufficientBalanceFee.tr);
        return false;
      }
    }

    if (coinType is ZcashChain) {
      buttonStatus.value = ButtonStatus.loading;
      zcashChainBranchId((result) {
        buttonStatus.value = ButtonStatus.enable;
        if (result == false) {
          startTimer();
        } else {
          _showInfo();
        }
      });
      return true;
    }

    if (coinType is TronChain) {
      tronTransferInfo(callback: (result) async {
        if (result) {
          bool status = await verityTronRawDataHex();
          if (status == true) {
            _showInfo();
          } else {
            Get.showToast(ID.stringTronRawDataHexError.tr,
                toastMode: ToastMode.failed);
          }
        }
      });
      return false;
    }

    if (coinType is EosChain) {
      getEosBlockInfo(callback: (result) {
        if (result) {
          _showInfo();
        }
      });
      return false;
    }

    if (coinType is SolanaChain) {
      buttonStatus.value = ButtonStatus.loading;
      getSolanaInfo((result) async {
        buttonStatus.value = ButtonStatus.enable;
        if (result) {
          String? waitSignature = await Get.walletCore.buildTransactionData(
            chain: coinType.chain,
            decimal: coinModel!.chainDecimal!,
            tokenType: SolanaChain.get.getTokenType(tokenModel?.tokenType),
            blockHash: tsModel.blockhash!,
            from: tsModel.fromAddress!,
            to: tsModel.toAddress!,
            amount: amount,
            contract: tokenModel?.contract,
            isNewTokenAccount: tsModel.isNewTokenAccount,
            computeUnitLimit: tsModel.gasLimit,
            computeUnitPrice: tsModel.gasPrice,
          );

          if (Get.isEmptyString(waitSignature)) {
            _showToast(ID.stringBuildError.tr);
          } else {
            tsModel.waitSignature = waitSignature;
            _showInfo();
          }
        } else {
          startTimer();
        }
      });
      return true;
    }

    _showInfo();

    return true;
  }

  void _showInfo() {
    if (coinType.isBitcoinSeries) {
      final inputs = tsModel.utxoResultModel?.inputs ?? [];
      if (inputs.length > 350) {
        Get.showAlertDialog(
          barrierDismissible: false,
          title: ID.stringNotices.tr,
          content: ID.strinInputMax.tr,
          onCancelText: ID.stringCanceTx.tr,
          onConfirm: () {
            Get.back();
            _showInfoDialog();
          },
          onCancel: () => startTimer(),
          onContinue: () => startTimer(),
          onConfirmText: ID.stringContinueTx2.tr,
        );
      } else {
        _showInfoDialog();
      }
    } else {
      _showInfoDialog();
    }
  }

  void _showInfoDialog() {
    isLoadedTronResource.value = false;
    TransferInfoDialog(
      tsModel: tsModel,
      coinModel: coinModel!,
      mainCoinModel: mainCoinModel!,
      addressModel: addressModel!,
      cancelOnTap: () => startTimer(),
      isShowFee: isShowFeeWidget,
      isShowRemark: isShowRemarkWidget,
      sendController: this,
      onTap: () async {
        if (Get.isAuthorizeTransaction() && tsModel.wallet is! TouchWallet) {
          final result = await Get.toNamed(AppRoutes.securityPage,
              arguments: SecurityState.security);
          if (result == true) {
            navigateToQRCodePage();
          }
        } else {
          navigateToQRCodePage();
        }
      },
    ).showBottomSheet();
  }

  void navigateToQRCodePage() {
    if (tsModel.wallet is TouchWallet) {
      Get.toNamed(AppRoutes.touchSignPage, arguments: {
        GetArgumentsKey.transferModel: tsModel,
        GetArgumentsKey.walletModel: walletModel,
      });
    } else {
      Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
        GetArgumentsKey.transferModel: tsModel,
        GetArgumentsKey.qrType: QRCodeType.transfer
      });
    }
  }

  void _showToast(String msg) {
    Get.showToast(msg, toastMode: ToastMode.waring);
  }

  /// 是否支持eos转账
  bool _eosIsSupported() {
    if (coinType is EosChain &&
        GetPlatform.isIOS &&
        tsModel.wallet is Pro3Wallet) {
      _showToast(ID.stringNoSupportedFeature.tr);
      return false;
    }
    return true;
  }
}
