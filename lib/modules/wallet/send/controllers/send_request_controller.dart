/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 14:28:55
 */
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/extra.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/utxo/utxo_inputs_model.dart';

extension SendRequestController on SendController {
  /// 余额，nonce
  void getBalanceAndNonce(
      {Function({BalanceModel? balanceModel, required bool result})?
          callback}) {
    // loading
    buttonStatus.value = ButtonStatus.loading;

    if (coinType is RippleChain) {
      _xrpToAddressActivity(callback: callback);
      return;
    }

    balanceRequest(address: tsModel.fromAddress!, callback: callback);
  }

  /// XRP 接收地址是否激活，激活
  void _xrpToAddressActivity(
      {Function({BalanceModel? balanceModel, required bool result})?
          callback}) {
    /// 验证接收地址是否激活
    balanceRequest(
        address: tsModel.toAddress!,
        callback: ({
          balanceModel,
          required result,
        }) {
          if (balanceModel == null) {
            buttonStatus.value = ButtonStatus.enable;
            return;
          }
          String balance = Get.isEmptyString(balanceModel.balance)
              ? '0'
              : balanceModel.balance!;
          if (balance.moreThan('0')) {
            // 已激活
            balanceRequest(address: tsModel.fromAddress!, callback: callback);
          } else {
            // 未激活
            if (!tsModel.amount!.moreThanEqual(RippleChain.get.reserveAmount)) {
              Get.showAlertDialog(
                title: ID.stringNotices.tr,
                content: ID.xrpNoActivityTips
                    .trParams({'value': RippleChain.get.reserveAmount}),
              );

              buttonStatus.value = ButtonStatus.enable;
            } else {
              balanceRequest(address: tsModel.fromAddress!, callback: callback);
            }
          }
        });
  }

//
  void balanceRequest({
    required String address,
    bool isMainChain = false,
    Function({BalanceModel? balanceModel, required bool result})? callback,
  }) {
    late String chain;
    Extra extra = Extra();
    Map<String, dynamic> extraMap = extra.toJson();
    if (isMainChain == true) {
      chain = coinType.chain;
    } else {
      chain = coinModel!.chain!;
      if (coinModel!.isToken!) {
        if (coinType is SolanaChain) {
          extraMap = extra.toSolContractBalanceJson(
            contract: tokenModel!.contract ?? '',
            derivedAddresses: tokenModel!.derivedAddresses ?? '',
          );
        } else {
          extraMap = extra.toContractBalanceJson(tokenModel!.contract ?? '');
        }
      }
    }

    if (coinType is EosChain) {
      if (coinModel!.isToken!) {
        extraMap
            .addAll(extra.toContractBalanceJson(tokenModel!.contract ?? ''));
        extraMap[APIConstant.symbol] = coinModel!.symbol;
      } else {
        extraMap.addAll(extra.toContractBalanceJson(APIConstant.eosioToken));
        extraMap[APIConstant.symbol] = EosChain.get.chain;
      }
    }

    httpRequest<BaseResponseV1<dynamic>>(
        api.getTokenBalance(BlockChainParamsManager.createParams(
            method: BlockChainAPI.getTokenBalance,
            requestParams: RequestParams()
                .put(APIConstant.chain, chain)
                .put(APIConstant.address, address)
                .put(
                    APIConstant.type,
                    (coinType is TronChain && coinModel!.isToken!)
                        ? TronChain.get.getTokenType(coinModel!.tokenType)
                        : '')
                .put(APIConstant.extra, extraMap)
                .getRequestBody())),
        handleError: false,
        handleSuccess: false, (value) {
      BalanceModel? balanceModel;
      if (value.data != null) {
        if (Get.isResponseDataValid(value.data)) {
          balanceModel =
              BalanceModel.fromJson(Map<String, dynamic>.from(value.data));
        }
      } else {
        if (coinType is RippleChain &&
            value.error?.message
                    ?.contains("'NoneType' object is not subscriptable") ==
                true) {
          Get.showAlertDialog(
              title: ID.stringNotices.tr, content: ID.stringXrpAddressError.tr);
        } else {
          if (value.error != null &&
              !Get.isEmptyString(value.error!.message!)) {
            Get.showToast(value.error!.message, toastMode: ToastMode.failed);
          } else {
            Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
          }
        }
      }
      if (callback != null && balanceModel != null) {
        callback(balanceModel: balanceModel, result: true);
      } else {
        if (callback != null) {
          callback(result: false);
        }
      }
    }, error: (_) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      if (callback != null) {
        callback(result: false);
      }
    });
  }

  void getArbAndOptNonce(Function({String? nonce}) callback) {
    buttonStatus.value = ButtonStatus.loading;

    httpRequest<BaseResponseV1<dynamic>>(
      api.getArbAndOptNonce(
        BlockChainParamsManager.createParams(
          method: BlockChainAPI.getNonce,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinType.chain)
              .put(APIConstant.address, tsModel.fromAddress!)
              .getRequestBody(),
        ),
      ),
      handleError: false,
      handleSuccess: false,
      (value) {
        if (value.data != null) {
          callback(nonce: value.data);
        } else {
          callback();
          Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
          buttonStatus.value = ButtonStatus.enable;
        }
      },
      error: (_) {
        callback();
        Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
        buttonStatus.value = ButtonStatus.enable;
      },
    );
  }

  void utxoBalanceRequest({VoidCallback? callback}) {
    httpRequest(
      api.getUtxoBalance(
        BlockChainParamsManager.createParams(
          method: BlockChainAPI.getUtxoBalance,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinType.chain)
              .put(APIConstant.address, tsModel.fromAddress!)
              .getRequestBody(),
        ),
      ),
      handleError: false,
      handleSuccess: false,
      (value) async {
        Get.dismissLoadingDialog();
        final data = value.data;
        if (data is Map && value.error == null) {
          String availableBalance =
              data[GetArgumentsKey.availableBalance].toString();
          availableBalance = availableBalance.div(
              pow(10, coinType.decimals).toString(),
              scale: coinType.decimals,
              roundMode: RoundMode.down);
          String unavailableBalance =
              data[GetArgumentsKey.unavailableBalance].toString();
          unavailableBalance = unavailableBalance.div(
              pow(10, coinType.decimals).toString(),
              scale: coinType.decimals,
              roundMode: RoundMode.down);
          String totalBalance = data[GetArgumentsKey.totalBalance].toString();
          totalBalance = totalBalance.div(pow(10, coinType.decimals).toString(),
              scale: coinType.decimals, roundMode: RoundMode.down);

          this.totalBalance.value = totalBalance;
          this.availableBalance.value = availableBalance;
          this.unavailableBalance.value = unavailableBalance;
          tsModel.availableBalance = availableBalance;
          mainChainBalance = availableBalance;

          /// 更新utxo balance
          await Get.database.addressDao.updateUtxoBalance(
            addressModel: addressModel!,
            totalBalance: totalBalance,
            availableBalance: availableBalance,
            unavailableBalance: unavailableBalance,
          );
        }

        if (callback != null) {
          callback();
        }
      },
      error: (e) {
        Get.dismissLoadingDialog();
        if (callback != null) {
          callback();
        }
      },
    );
  }

  Future<void> psbtRequest({required Function(bool result) callback}) async {
    List hashList = [];

    for (var model in tsModel.utxoResultModel!.inputs!) {
      hashList.add(model.preHash);
    }

    httpRequest(
      api.getPsbtTxHexs(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getPsbtTxHexs,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinType.chain)
              .put(APIConstant.txIds, hashList)
              .getRequestBody())),
      handleError: false,
      handleSuccess: false,
      (value) {
        final data = value.data;
        if (data is List) {
          if (data.isEmpty) {
            Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
            callback(false);
            return;
          }

          /// 更新 rawTx
          List<UtxoInputsModel> inputs =
              tsModel.utxoResultModel!.inputs!.map((model) {
            List resultList = data.where((json) {
              String? txId = json['tx_id'];
              return model.preHash == txId;
            }).toList();
            if (resultList.isNotEmpty) {
              Map<String, dynamic> json = resultList.first;
              String? txHex = json['tx_hex'];
              model.rawTx = txHex;
            } else {
              model.rawTx = '';
            }

            return model;
          }).toList();

          tsModel.utxoResultModel?.inputs = inputs;

          callback(true);
        } else {
          Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
          callback(false);
        }
      },
      error: (e) {
        Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
        callback(false);
      },
    );
  }

  void zcashChainBranchId(Function(bool result) callback) {
    httpRequest(
        api.zcashChainBranchId(BlockChainParamsManager.createParams(
            method: BlockChainAPI.zecBranchId,
            requestParams: RequestParams()
                .put(GetArgumentsKey.chain, coinType.chain)
                .getRequestBody())),
        handleError: false,
        handleSuccess: false, (value) {
      final data = value.data;
      if (data != null && data is Map) {
        String? version = data['version'];
        String? branchId = data['branch_id'];

        if (!Get.isEmptyString(version) && !Get.isEmptyString(branchId)) {
          tsModel.version = version;
          tsModel.branchId = branchId;
          callback(true);
          return;
        }
      }
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      callback(false);
    }, error: (_) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      callback(false);
    });
  }
}
