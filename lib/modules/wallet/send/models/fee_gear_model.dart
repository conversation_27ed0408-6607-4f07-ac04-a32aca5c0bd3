/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-28 10:15:25
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class FeeGearModel {
  String? fee;
  String? name;
  int? timeSecond;
  String? maxFee;
  String? baseFee;
  String? maxPriorityFee;
  String? minPriorityFee;

  FeeGearModel(
      {this.fee,
      this.name,
      this.timeSecond,
      this.maxFee,
      this.baseFee,
      this.maxPriorityFee,
      this.minPriorityFee});

  FeeType get feeType {
    if (name == 'rear_block_fee') {
      return FeeType.slow;
    } else if (name == 'estimatefee') {
      return FeeType.normal;
    } else if (name == 'top_block_fee') {
      return FeeType.fast;
    } else if (name == APIConstant.customize) {
      return FeeType.customize;
    }
    return FeeType.normal;
  }

  factory FeeGearModel.fromJson(Map<String, dynamic> json, CoinType? coinType) {
    String? fee =
        json['fee'] is String ? json['fee'] as String? : json['fee'].toString();
    if (!Get.isEmptyString(fee) && coinType is SolanaChain) {
      fee = fee!.decimal(scale: 0);
    }

    String? baseFee = json['base_fee'] is String
        ? json['base_fee'] as String?
        : json['base_fee'].toString();
    if (!Get.isEmptyString(baseFee) && coinType is SolanaChain) {
      baseFee = baseFee!.decimal(scale: 0);
    }

    return FeeGearModel(
      fee: fee,
      name: json['name'] as String?,
      timeSecond: json['time_second'] as int?,
      maxFee: json['max_fee'] as String?,
      baseFee: baseFee,
      maxPriorityFee: json['max_priority_fee'] as String?,
      minPriorityFee: json['min_priority_fee'] as String?,
    );
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'fee': fee,
        'name': name,
        'time_second': timeSecond,
        'max_fee': maxFee,
        'base_fee': baseFee,
        'max_priority_fee': maxPriorityFee,
        'min_priority_fee': minPriorityFee,
      };
}
