import 'package:json_annotation/json_annotation.dart';

part 'fil_gas_model.g.dart';

@JsonSerializable()
class FilGasModel {
  @JsonKey(name: 'base_fee')
  String? baseFee;
  @JsonKey(name: 'gas_fee_cap')
  String? gasFeeCap;
  @JsonKey(name: 'gas_fee_premium')
  String? gasFeePremium;
  @JsonKey(name: 'gas_limit')
  String? gasLimit;

  FilGasModel({
    this.baseFee,
    this.gasFeeCap,
    this.gasFeePremium,
    this.gasLimit,
  });

  factory FilGasModel.fromJson(Map<String, dynamic> json) {
    return _$FilGasModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$FilGasModelToJson(this);
}
