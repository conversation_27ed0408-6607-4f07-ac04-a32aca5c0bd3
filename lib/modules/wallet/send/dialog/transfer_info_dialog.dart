/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-03 14:30:30
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_trx_controller.dart';
import 'package:coinbag/modules/wallet/send/dialog/widget/transfer_alert_widget.dart';
import 'package:coinbag/modules/wallet/send/dialog/widget/transfer_source_widget.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/cold/ultra_wallet.dart';

enum TransferInfoItemType { type, from, to, fee, remark, resource, amount }

class TransferInfoDialog extends BaseStatelessWidget {
  final TransferModel tsModel;
  final CoinModel coinModel;
  final CoinModel mainCoinModel;
  final AddressModel addressModel;
  final VoidCallback? onTap;
  final VoidCallback? cancelOnTap;
  final bool? isShowFee;
  final bool? isShowRemark;
  final SendController? sendController;
  const TransferInfoDialog(
      {super.key,
      required this.tsModel,
      required this.coinModel,
      required this.addressModel,
      required this.mainCoinModel,
      this.sendController,
      this.onTap,
      this.isShowFee,
      this.isShowRemark,
      this.cancelOnTap});

  String _popTitle() {
    if (tsModel.type == TransferType.delegate) {
      if (tsModel.resourceType == TronResourceType.energy) {
        return ID.stringDeleagteEnergy.tr;
      } else {
        return ID.stringDelegateBandwidth.tr;
      }
    } else if (tsModel.type == TransferType.reclaim) {
      if (tsModel.resourceType == TronResourceType.energy) {
        return ID.stringReclaimEnergy.tr;
      } else {
        return ID.stringReclaimBandwidth.tr;
      }
    } else if (tsModel.type == TransferType.stake) {
      if (tsModel.resourceType == TronResourceType.energy) {
        return ID.stringStakeEnergy.tr;
      } else {
        return ID.stringStakeBandwidth.tr;
      }
    } else if (tsModel.type == TransferType.unstake) {
      if (tsModel.resourceType == TronResourceType.energy) {
        return ID.stringUnstakeEnergy.tr;
      } else {
        return ID.stringUnstakeBandwidth.tr;
      }
    } else if (tsModel.type == TransferType.withdraw) {
      return ID.stringWithdrawTrx.tr;
    } else if (tsModel.type == TransferType.nft) {
      return ID.stringNftSend.tr;
    }

    return ID.coinTransferTitle.trParams({'symbol': coinModel.symbol ?? ''});
  }

  void showBottomSheet() {
    Get.showBottomSheet(
        paddingBottom: 0,
        onBack: () {
          if (Get.routing.isBack == true) {
            SendController.updateTimer();
          }
        },
        onCancel: () {
          Get.back();
          if (cancelOnTap != null) {
            cancelOnTap!();
          }
        },
        title: _popTitle(),
        bodyWidget: TransferInfoDialog(
          tsModel: tsModel,
          coinModel: coinModel,
          mainCoinModel: mainCoinModel,
          addressModel: addressModel,
          onTap: onTap,
          isShowFee: isShowFee,
          isShowRemark: isShowRemark,
          cancelOnTap: cancelOnTap,
          sendController: sendController,
        ));
  }

  CoinType? get _coinType => CoinBase.getCoinTypeByChain(tsModel.chain);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        tsModel.type == TransferType.nft ? _nftHeaderWidget() : _amountWidget(),
        _dividerWidget(),
        Visibility(
          visible: tsModel.type != TransferType.nft,
          child: _contentItemWidget(TransferInfoItemType.type),
        ),
        Visibility(
          visible: tsModel.type == TransferType.nft,
          child: _contentItemWidget(TransferInfoItemType.amount),
        ),
        Visibility(
          visible: tsModel.type == TransferType.transfer ||
              tsModel.type == TransferType.stake ||
              tsModel.type == TransferType.nft,
          child: _contentItemWidget(TransferInfoItemType.from),
        ),
        Visibility(
          visible: tsModel.type == TransferType.delegate ||
              tsModel.type == TransferType.reclaim,
          child: _contentItemWidget(TransferInfoItemType.resource),
        ),
        Visibility(
          visible: tsModel.type != TransferType.stake,
          child: _contentItemWidget(TransferInfoItemType.to),
        ),
        _contentItemWidget(TransferInfoItemType.fee),
        _contentRemarkWidget(),
        _tronWidget(),
        _buttonWidget()
      ],
    );
  }

  Widget _tronWidget() {
    if (sendController != null && _coinType is TronChain) {
      sendController!.energyRequest();
      return Obx(() => Column(
            children: [
              TronSourceWidget(
                tsModel: tsModel,
                coinModel: mainCoinModel,
                addressModel: addressModel,
                isLoading: !sendController!.isLoadedTronResource.value,
                callback: () {
                  sendController!.isLoadedTronResource.value = false;
                  sendController!.energyRequest();
                },
              ),
              Visibility(
                visible: sendController!.isLoadedTronResource.value,
                child: _resourceTipWidget(),
              )
            ],
          ));
    }
    return Column(
      children: [
        Visibility(
          visible: _coinType is TronChain,
          child: TronSourceWidget(
            tsModel: tsModel,
            coinModel: mainCoinModel,
            addressModel: addressModel,
          ),
        ),
        _resourceTipWidget(),
      ],
    );
  }

  Visibility _resourceTipWidget() {
    return Visibility(
      visible: tsModel.wallet is UltraWallet &&
          (tsModel.type == TransferType.delegate ||
              tsModel.type == TransferType.reclaim),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: SizedBox(
          width: Get.width,
          child: Text(
            tsModel.type == TransferType.delegate
                ? ID.stringResourcePopTip2.tr
                : ID.stringResourcePopTip1.tr,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(12),
              fontWeight: FontWeightX.regular,
              fontFamily: Get.setFontFamily(),
            ),
          ),
        ),
      ),
    );
  }

  SingleChildRenderObjectWidget _contentItemWidget(TransferInfoItemType type) {
    if (type == TransferInfoItemType.fee &&
        !['xrp', 'xem'].contains(tsModel.chain)) {
      if (isShowFee != true) return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(10), horizontal: Get.setPaddingSize(16)),
      child: GestureDetector(
        onTap: () {
          if (type == TransferInfoItemType.from) {
            Get.copy(tsModel.fromAddress);
          } else if (type == TransferInfoItemType.to) {
            Get.copy(tsModel.toAddress);
          }
        },
        child: Row(
          children: [
            Text(
              _contentItemTitle(type),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: styleSecond_14,
            ),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            Expanded(
              child: Text(
                _contentItemValue(type),
                style: stylePrimary_14_m,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
            ),
            Visibility(
              visible: type == TransferInfoItemType.from ||
                  type == TransferInfoItemType.to,
              child: GestureDetector(
                onTap: () => Get.copy(type == TransferInfoItemType.from
                    ? tsModel.fromAddress!
                    : tsModel.toAddress!),
                behavior: HitTestBehavior.translucent,
                child: Padding(
                  padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                  child: ImageWidget(
                      assetUrl: 'copy',
                      width: Get.setPaddingSize(16),
                      height: Get.setPaddingSize(16)),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  SingleChildRenderObjectWidget _contentRemarkWidget() {
    return isShowRemark != true
        ? const SizedBox.shrink()
        : Padding(
            padding: EdgeInsets.symmetric(
                vertical: Get.setPaddingSize(10),
                horizontal: Get.setPaddingSize(16)),
            child: Column(
              children: [
                DividerWidget(
                  padding: EdgeInsets.only(bottom: Get.setPaddingSize(18)),
                ),
                Row(
                  children: [
                    Text(
                      _coinType is RippleChain
                          ? ID.remarkTag.tr
                          : ID.remarkMemo.tr,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: styleSecond_14,
                    ),
                    SizedBox(
                      width: Get.setPaddingSize(8),
                    ),
                    Expanded(
                      child: Text(
                        Get.isEmptyString(tsModel.remark)
                            ? ID.remarkNoText.tr
                            : tsModel.remark!,
                        style: stylePrimary_14_m,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.end,
                      ),
                    )
                  ],
                ),
              ],
            ),
          );
  }

  DividerWidget _dividerWidget() {
    return DividerWidget(
      padding: EdgeInsets.only(
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.setPaddingSize(8)),
    );
  }

  Padding _amountWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Get.setPaddingSize(16),
        vertical: Get.setPaddingSize(24),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SymbolWidget(coinModel: coinModel),
              SizedBox(
                width: Get.setPaddingSize(8),
              ),
              Flexible(
                child: Text(
                  _amountText(),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: _amountTextColor(),
                      fontFamily: Get.setNumberFontFamily(),
                      fontSize: Get.setFontSize(28),
                      fontWeight: FontWeightX.semibold),
                ),
              ),
            ],
          ),
          Visibility(
            visible: tsModel.type == TransferType.delegate,
            child: Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
              child: Text(ID.stringDelegatePopAmount.tr, style: styleSecond_14),
            ),
          ),
        ],
      ),
    );
  }

  Padding _nftHeaderWidget() => Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16),
          vertical: Get.setPaddingSize(24),
        ),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(Get.setRadius(6)),
              child: ImageWidget(
                width: _nftImageWidth,
                height: _nftImageWidth,
                imageUrl: tsModel.nftImageUrl,
                loadingWidget: Container(
                  color: Get.theme.colorFF6A16.withAlpha(1),
                  child: ImageWidget(
                    width: _nftImageWidth,
                    height: _nftImageWidth,
                    assetUrl: 'icon_nft_empty',
                  ),
                ),
                errorWidget: ImageWidget(
                  width: _nftImageWidth,
                  height: _nftImageWidth,
                  assetUrl: 'icon_nft_failed',
                ),
              ),
            ),
            SizedBox(height: Get.setPaddingSize(8)),
            Text(
              tsModel.nftTokenName ?? '',
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(20),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily(),
              ),
            )
          ],
        ),
      );

  double get _nftImageWidth => Get.setImageSize(60);

  String _amountText() {
    if (tsModel.type == TransferType.reclaim ||
        tsModel.type == TransferType.withdraw) {
      return '+ ${tsModel.amount} ${coinModel.symbol}';
    } else if (tsModel.type == TransferType.unstake) {
      return '${tsModel.amount} ${coinModel.symbol}';
    } else {
      return '- ${tsModel.amount} ${coinModel.symbol}';
    }
  }

  Color _amountTextColor() {
    if (tsModel.type == TransferType.reclaim ||
        tsModel.type == TransferType.withdraw) {
      return Get.theme.color02B58A;
    } else if (tsModel.type == TransferType.unstake) {
      return Get.theme.textPrimary;
    } else {
      return Get.theme.colorF44D4D;
    }
  }

  Padding _buttonWidget() {
    return Padding(
      padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding()),
      child: ButtonWidget(
        text: ID.stringConfirm.tr,
        width: Get.width,
        onPressed: () {
          CoinType? coinType = CoinBase.getCoinTypeByChain(tsModel.chain);
          if (coinType is TronChain && tsModel.type == TransferType.transfer) {
            if (!_isTronDeduction()) {
              Get.showAlertDialog(
                  child: TransferAlertWidget(
                type: TronSourceType.transfer,
                tsModel: tsModel,
                callback: () => _back(),
              ));
              return;
            }
          }

          _back();
        },
      ),
    );
  }

  void _back() {
    Get.back();
    if (onTap != null) {
      onTap!();
    }
  }

  /// 是否足够抵扣矿工费
  bool _isTronDeduction() {
    String? feeValue = tsModel.tronTsSourceModel?.fee;
    if (Get.isEmptyString(feeValue)) {
      feeValue = '0';
    }

    String amount = feeValue!;
    if (!tsModel.isToken) {
      amount = amount.add(tsModel.amount ?? '0');
    }
    return !amount.moreThan(addressModel.balance ?? '0');
  }

  String _contentItemTitle(TransferInfoItemType type) {
    String title = '';
    switch (type) {
      case TransferInfoItemType.type:
        title = ID.coinTsTypeTitle.tr;
        break;
      case TransferInfoItemType.from:
        {
          if (_coinType is EosChain) {
            title = ID.fromAccount.tr;
          } else {
            title = ID.stringSendAddress.tr;
          }
          break;
        }

      case TransferInfoItemType.to:
        {
          if (tsModel.type == TransferType.reclaim) {
            title = ID.stringReclaimAddress.tr;
          } else {
            if (_coinType is EosChain) {
              title = ID.toAccount.tr;
            } else {
              title = ID.stringReciveAddress.tr;
            }
          }
          break;
        }
      case TransferInfoItemType.fee:
        title = ID.feeTitle.tr;
        break;
      case TransferInfoItemType.resource:
        {
          if (tsModel.type == TransferType.delegate) {
            title = ID.stringDelegateAmount.tr;
          } else if (tsModel.type == TransferType.reclaim) {
            title = ID.stringReclaimAmount1.tr;
          }
        }
        break;
      case TransferInfoItemType.amount:
        title = ID.sendNumber.tr;
        break;

      default:
    }
    return title;
  }

  String _contentItemValue(TransferInfoItemType type) {
    String value = '';
    switch (type) {
      case TransferInfoItemType.type:
        if (tsModel.type == TransferType.delegate) {
          if (tsModel.resourceType == TronResourceType.energy) {
            value = ID.stringDeleagteEnergy.tr;
          } else {
            value = ID.stringDelegateBandwidth.tr;
          }
        } else if (tsModel.type == TransferType.reclaim) {
          if (tsModel.resourceType == TronResourceType.energy) {
            value = ID.stringReclaimEnergy.tr;
          } else {
            value = ID.stringReclaimBandwidth.tr;
          }
        } else if (tsModel.type == TransferType.stake) {
          if (tsModel.resourceType == TronResourceType.energy) {
            value = ID.stringStakeEnergy.tr;
          } else {
            value = ID.stringStakeBandwidth.tr;
          }
        } else if (tsModel.type == TransferType.unstake) {
          if (tsModel.resourceType == TronResourceType.energy) {
            value = tsModel.isV1
                ? ID.stringUnstakeEnergy.tr
                : ID.stringUnstakeEnergy2.tr;
          } else {
            value = tsModel.isV1
                ? ID.stringUnstakeBandwidth.tr
                : ID.stringUnstakeBandwidth2.tr;
          }
        } else if (tsModel.type == TransferType.withdraw) {
          value = ID.stringWithdrawTrx.tr;
        } else {
          value =
              ID.coinTransferTitle.trParams({'symbol': coinModel.symbol ?? ''});
        }
        break;
      case TransferInfoItemType.from:
        value = AddressUtils.omitAddress(tsModel.fromAddress!);
        break;
      case TransferInfoItemType.to:
        value = AddressUtils.omitAddress(tsModel.toAddress!);
        break;
      case TransferInfoItemType.fee:
        value = '${tsModel.fee} ${mainCoinModel.symbol ?? ''}';
        break;
      case TransferInfoItemType.resource:
        {
          if (tsModel.resourceType == TronResourceType.energy) {
            value = ID.valueEnergy
                .trParams({'energy': tsModel.resourceAmount ?? ''});
          } else {
            value = ID.valueBandwidth
                .trParams({'bandwidth': tsModel.resourceAmount ?? ''});
          }
        }
        break;
      case TransferInfoItemType.amount:
        value = tsModel.amount ?? '';
        break;
      default:
    }
    return value;
  }
}
