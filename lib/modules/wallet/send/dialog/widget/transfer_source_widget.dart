/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-03 14:34:31
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/send/dialog/widget/transfer_alert_widget.dart';
import 'package:coinbag/modules/wallet/send/dialog/widget/transfer_tron_fee_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/status/app_loading_widget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

enum TronSourceType { source, fee, energy, transfer }

class TronSourceWidget extends StatefulWidget {
  final TransferModel tsModel;
  final CoinModel coinModel;
  final AddressModel addressModel;
  final bool isLoading;
  final Function()? callback;
  const TronSourceWidget({
    super.key,
    required this.tsModel,
    required this.coinModel,
    required this.addressModel,
    this.isLoading = false,
    this.callback,
  });

  @override
  State<TronSourceWidget> createState() => _TronSourceWidgetState();
}

class _TronSourceWidgetState extends State<TronSourceWidget> {
  late TransferModel tsModel;
  bool showFeeDetail = false;

  @override
  void initState() {
    tsModel = widget.tsModel;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(10), horizontal: Get.setPaddingSize(16)),
      child: Column(
        children: [
          _sourceWidget(),
          _feeWidget(),
          _tipTronWidget(),
        ],
      ),
    );
  }

  RenderObjectWidget _feeWidget() {
    if (tsModel.type == TransferType.delegate) return const SizedBox.shrink();
    if (widget.isLoading == true) return const SizedBox.shrink();

    String? fee = tsModel.tronTsSourceModel?.fee;
    if (Get.isEmptyString(fee)) {
      fee = '0';
    }
    return fee!.moreThan('0') == false
        ? const SizedBox.shrink()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              SizedBox(
                height: Get.setPaddingSize(20),
              ),
              Row(
                children: [
                  _titleWidget(
                      title: ID.stringTronNetworkFees.tr,
                      type: TronSourceType.fee),
                  SizedBox(
                    width: Get.setPaddingSize(8),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => setState(() {
                        showFeeDetail = !showFeeDetail;
                      }),
                      child: Text(
                        '≈${tsModel.tronTsSourceModel?.fee} TRX',
                        style: TextStyle(
                            color: Get.theme.textPrimary,
                            fontSize: Get.setFontSize(14),
                            fontWeight: FontWeightX.medium,
                            fontFamily: Get.setNumberFontFamily()),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: Get.setPaddingSize(4),
                  ),
                  ImageWidget(
                    assetUrl:
                        showFeeDetail ? 'arrow_up_gray' : 'arrow_down_gray',
                    width: Get.setImageSize(10),
                    height: Get.setImageSize(10),
                  )
                ],
              ),
              SizedBox(
                height: Get.setPaddingSize(2),
              ),
              Text(
                '≈${BalanceManager.calculateFiatValue(tsModel.tronTsSourceModel?.fee, widget.coinModel)}',
                textAlign: TextAlign.end,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    color: Get.theme.textSecondary),
              ),
              Visibility(
                visible: showFeeDetail,
                child: TronFeeWidget(sourceModel: tsModel.tronTsSourceModel),
              ),
            ],
          );
  }

  Row _sourceWidget() {
    return Row(
      children: [
        _titleWidget(title: ID.tronNeedSource.tr),
        SizedBox(
          width: Get.setPaddingSize(8),
        ),
        Expanded(
          child: _tronSourceWiget(),
        ),
      ],
    );
  }

  Widget _tronSourceWiget() {
    return widget.isLoading == true
        ? Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              AppLoadingWidget(),
            ],
          )
        : _sourceValueWidget();
  }

  Widget _sourceValueWidget() {
    if (tsModel.type == TransferType.delegate ||
        tsModel.type == TransferType.reclaim ||
        tsModel.type == TransferType.stake ||
        tsModel.type == TransferType.unstake ||
        tsModel.type == TransferType.withdraw) {
      return Text(
        ID.valueBandwidth.trParams({'bandwidth': tsModel.costResource}),
        textAlign: TextAlign.end,
        style: stylePrimary_14_m,
      );
    }

    if (Get.isEmptyString(tsModel.tronTsSourceModel?.bandwidthUsed) ||
        Get.isEmptyString(tsModel.tronTsSourceModel?.energyUsed)) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () {
              if (widget.callback != null) {
                widget.callback!();
              }
            },
            child: Icon(
              Icons.refresh,
              color: Get.theme.colorD3D3D3,
              size: Get.setImageSize(28), // 设置图标大小
            ),
          ),
        ],
      );
    }

    return Text.rich(
      TextSpan(
          text: ID.valueBandwidth.trParams({
            GetArgumentsKey.bandwidth:
                tsModel.tronTsSourceModel?.bandwidthUsed ?? '0'
          }),
          children: tsModel.trxTokenType != TronChain.trc20
              ? null
              : [
                  TextSpan(text: ' + ', children: [
                    TextSpan(
                        text: ID.valueEnergy.trParams({
                          GetArgumentsKey.energy:
                              tsModel.tronTsSourceModel?.energyUsed ?? ''
                        }),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () => Get.showAlertDialog(
                                  child: TransferAlertWidget(
                                tsModel: tsModel,
                                type: TronSourceType.energy,
                              )),
                        style: TextStyle(
                            color: Get.theme.textPrimary,
                            fontSize: Get.setFontSize(14),
                            fontWeight: FontWeightX.medium,
                            fontFamily: Get.setFontFamily(),
                            decoration: TextDecoration.underline))
                  ])
                ]),
      style: stylePrimary_14_m,
      textAlign: TextAlign.end,
    );
  }

  Widget _tipTronWidget() {
    if (tsModel.type == TransferType.delegate) return const SizedBox.shrink();
    if (widget.isLoading == true) return const SizedBox.shrink();

    String? feeValue = tsModel.tronTsSourceModel?.fee;
    if (Get.isEmptyString(feeValue)) {
      feeValue = '0';
    }

    String amount = feeValue!;
    if (!tsModel.isToken) {
      amount = amount.add(tsModel.amount ?? '0');
    }

    return Visibility(
      visible: tsModel.type == TransferType.transfer &&
          amount.moreThan(widget.addressModel.balance ?? '0'),
      child: Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(2)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ImageWidget(
              assetUrl: 'trx_tx_alert',
              width: Get.setImageSize(20),
              height: Get.setImageSize(20),
            ),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            Text(
              ID.tronPopTip.tr,
              style: TextStyle(
                  fontSize: Get.setFontSize(12), color: Get.theme.colorF44D4D),
            )
          ],
        ),
      ),
    );
  }

  GestureDetector _titleWidget(
      {required String title, TronSourceType type = TronSourceType.source}) {
    return GestureDetector(
      onTap: () => Get.showAlertDialog(
          child: TransferAlertWidget(
        tsModel: tsModel,
        type: type,
      )),
      behavior: HitTestBehavior.translucent,
      child: Row(
        children: [
          Text(
            title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: styleSecond_14,
          ),
          SizedBox(
            width: Get.setPaddingSize(4),
          ),
          ImageWidget(
            assetUrl: 'trx_source_detail',
            width: Get.setImageSize(12),
            height: Get.setImageSize(12),
          ),
        ],
      ),
    );
  }
}
