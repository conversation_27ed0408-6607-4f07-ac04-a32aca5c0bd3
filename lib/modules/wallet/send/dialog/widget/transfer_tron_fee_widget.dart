import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_tron_source.dart';

class TronFeeWidget extends StatelessWidget {
  final TransferTronSourceModel? sourceModel;
  final SendController? sendController;
  const TronFeeWidget(
      {super.key, required this.sourceModel, this.sendController});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
      child: Container(
        height: contentHeight(),
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
        decoration: BoxDecoration(
          color: Get.theme.colorF9F9F9,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Visibility(
                visible: sourceModel?.activated == false,
                child: _feeItemWidget(
                    title: ID.activedAccount.tr, value: '1 TRX')),
            Visibility(
                visible: sourceModel?.remark == true,
                child: _feeItemWidget(title: ID.remark.tr, value: '1 TRX')),
            Visibility(
                visible: deductEnergy.moreThan('0'),
                child: _feeItemWidget(
                    title: ID.deductionValueEnergy.trParams({
                      GetArgumentsKey.energy: '${deductEnergy.div('1000')}K'
                    }),
                    value: '≈${sourceModel?.energyTrx} TRX')),
            Visibility(
                visible: deductBandwidth.moreThan('0'),
                child: _feeItemWidget(
                    title: ID.deductionValueBandwidth
                        .trParams({GetArgumentsKey.bandwidth: deductBandwidth}),
                    value: '≈${sourceModel?.bandwidthTrx} TRX')),
          ],
        ),
      ),
    );
  }

  Padding _feeItemWidget({required String title, required String value}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(4)),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
                fontSize: Get.setFontSize(12), color: Get.theme.textSecondary),
          ),
          SizedBox(
            width: Get.setPaddingSize(4),
          ),
          Expanded(
              child: Text(
            value,
            textAlign: TextAlign.end,
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                fontFamily: Get.setNumberFontFamily(),
                color: Get.theme.textSecondary),
          ))
        ],
      ),
    );
  }

  String get bandwidthTrx {
    String? bandwidth = sourceModel?.bandwidthTrx;
    if (Get.isEmptyString(bandwidth)) {
      bandwidth = '0';
    }
    return bandwidth!.div('1000');
  }

  String get energyTrx {
    String? energy = sourceModel?.energyTrx;
    if (Get.isEmptyString(energy)) {
      energy = '0';
    }
    return energy!.div('1000');
  }

  String get deductBandwidth {
    String? bandwidth = sourceModel?.deductionBandwidth;
    if (Get.isEmptyString(bandwidth)) {
      bandwidth = '0';
    }
    return bandwidth!;
  }

  String get deductEnergy {
    String? energy = sourceModel?.deductionEnergy;
    if (Get.isEmptyString(energy)) {
      energy = '0';
    }
    return energy!;
  }

  double contentHeight() {
    double height = 16;
    if (sourceModel?.remark == true) {
      height += 28;
    }
    if (sourceModel?.activated == false) {
      height += 28;
    }

    if (deductBandwidth.moreThan('0')) {
      height += 28;
    }

    if (deductEnergy.moreThan('0')) {
      height += 28;
    }

    return height;
  }
}
