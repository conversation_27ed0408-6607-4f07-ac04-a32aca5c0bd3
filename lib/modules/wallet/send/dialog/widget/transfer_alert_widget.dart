/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 11:17:23
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/modules/wallet/send/dialog/widget/transfer_source_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TransferAlertWidget extends StatelessWidget {
  final TronSourceType type;
  final TransferModel tsModel;
  final Function()? callback;
  const TransferAlertWidget(
      {super.key, required this.type, required this.tsModel, this.callback});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: Get.width - Get.setPaddingSize(80),
        child: Material(
          borderRadius: BorderRadius.circular(Get.setRadius(16)),
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: Get.setPaddingSize(24),
                horizontal: Get.setPaddingSize(16)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _titleWidget(),
                SizedBox(
                  height: Get.setPaddingSize(24),
                ),
                _textWidget(text: _text()),
                _energyWidget(),
                _buttonWidget(),
                _cancelWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Visibility _energyWidget() => Visibility(
        visible: type == TronSourceType.energy,
        child: Column(
          children: [
            _energyItemWidget(title: ID.basicEnergy.tr, value: _baseEnergy()),
            SizedBox(
              height: Get.setPaddingSize(12),
            ),
            _energyItemWidget(
                title: ID.extraEnergy.tr, value: _energyPenalty()),
            SizedBox(
              height: Get.setPaddingSize(12),
            ),
            GestureDetector(
              onTap: () {
                Get.back();
                Get.toWeb(
                  url: AppController.languageModel.value.type == LanguageType.en
                      ? TronUrl.tronlinkUS
                      : TronUrl.tronlinkZH,
                );
              },
              behavior: HitTestBehavior.translucent,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      ID.tronLearnMore.tr,
                      style: TextStyle(
                        color: Get.theme.textPrimary,
                        fontSize: Get.setFontSize(14),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: Get.setPaddingSize(8),
                  ),
                  ImageWidget(
                    assetUrl: 'icon_new_arrow01',
                    width: Get.setImageSize(12),
                    height: Get.setImageSize(12),
                  )
                ],
              ),
            ),
          ],
        ),
      );

  Row _energyItemWidget({required String title, required String value}) {
    return Row(
      children: [
        Text(title,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
            )),
        SizedBox(
          width: Get.setPaddingSize(4),
        ),
        Expanded(
          child: Text(
              ID.valueEnergy.trParams({GetArgumentsKey.energy: value.format}),
              textAlign: TextAlign.end,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(14),
              )),
        )
      ],
    );
  }

  Widget _textWidget({required String text}) => Get.isEmptyString(_text())
      ? const SizedBox.shrink()
      : Text(
          text,
          textAlign: type == TronSourceType.transfer ? TextAlign.center : null,
          style: TextStyle(
              fontSize: Get.setFontSize(14), color: Get.theme.textPrimary),
        );

  Text _titleWidget() {
    return Text(
      ID.stringTips.tr,
      style: TextStyle(
          fontSize: Get.setFontSize(18),
          fontWeight: FontWeightX.medium,
          color: Get.theme.textPrimary),
    );
  }

  Padding _buttonWidget() => Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(24)),
        child: ButtonWidget(
          text: ID.stringConfirm.tr,
          width: Get.width,
          height: 44,
          onPressed: () {
            Get.back();
            if (callback != null) {
              callback!();
            }
          },
        ),
      );

  SingleChildRenderObjectWidget _cancelWidget() =>
      type != TronSourceType.transfer
          ? const SizedBox.shrink()
          : Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
              child: GestureDetector(
                onTap: () => Get.back(),
                behavior: HitTestBehavior.translucent,
                child: SizedBox(
                  height: 44,
                  width: Get.width,
                  child: Center(
                    child: Text(
                      ID.stringCancel.tr,
                      style: TextStyle(
                          color: Get.theme.textSecondary,
                          fontSize: Get.setFontSize(16),
                          fontWeight: FontWeightX.medium,
                          fontFamily: Get.setFontFamily()),
                    ),
                  ),
                ),
              ),
            );

  String _text() {
    if (tsModel.type == TransferType.delegate ||
        tsModel.type == TransferType.reclaim ||
        tsModel.type == TransferType.stake ||
        tsModel.type == TransferType.unstake ||
        tsModel.type == TransferType.withdraw) {
      return ID.stringResouecePopTips.tr;
    }
    if (type == TronSourceType.source) {
      return ID.tronResourceDetail.tr;
    } else if (type == TronSourceType.fee) {
      return ID.tronFeeDetail.tr;
    } else if (type == TronSourceType.transfer) {
      return ID.tronRiskTip.tr;
    }
    return '';
  }

  String _energyPenalty() {
    String? energyPenalty = tsModel.energyPenalty;
    if (Get.isEmptyString(energyPenalty)) {
      energyPenalty = '0';
    }
    return energyPenalty!;
  }

  String _baseEnergy() {
    String? used = tsModel.energyUsed;
    if (Get.isEmptyString(used)) {
      used = '0';
    }

    String base = used!.sub(_energyPenalty());
    return base;
  }
}
