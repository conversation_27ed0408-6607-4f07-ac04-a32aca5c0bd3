/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-10-24 11:29:56
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';

class TransactionConfirmationDialog extends BaseStatelessWidget {
  final ScanController? scanController;
  final String? fee;
  final String? amount;
  final List<String>? fromAddressList;
  final List<String>? toAddressList;
  final String? rawData;
  final String? remark;
  final CoinType? coinType;
  final CoinModel? coinModel;
  final VoidCallback? onPressed;

  const TransactionConfirmationDialog(
      {super.key,
      this.scanController,
      this.onPressed,
      this.amount,
      this.fromAddressList,
      this.toAddressList,
      this.rawData,
      this.fee,
      this.remark,
      this.coinModel,
      this.coinType});
  void showBottomSheet() {
    Get.showBottomSheet(
        barrierDismissible: false,
        enableDrag: false,
        disableBack: false,
        title: ID.stringConfirmTransaction.tr,
        bodyWidget: this,
        bottomWidget: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ButtonWidget(
              text: ID.stringConfirm.tr,
              buttonSize: ButtonSize.full,
              onPressed: onPressed),
        ));
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [_dataItem(), _rawDataItem()]),
    );
  }

  Column _dataItem() =>
      Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        _amountItem(),
        _transationTypeItem(ID.stringSignTransaction.tr),
        _addressItem(ID.stringSendAddress.tr, fromAddressList!),
        _addressItem(ID.stringReciveAddress.tr, toAddressList!),
        _remarkItem(remark),
        _feeItem()
      ]);

  RenderObjectWidget _amountItem() => Get.isEmptyString(amount)
      ? const SizedBox.shrink()
      : Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Row(
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text.rich(
                          TextSpan(children: [
                            WidgetSpan(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: SymbolWidget(coinModel: coinModel!),
                              ),
                            ),
                            TextSpan(
                                text: "$amount ${coinModel!.symbol!}",
                                style: TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  color: Get.theme.textSecondary,
                                  fontSize: Get.setFontSize(
                                      coinType!.isEthereumSeries ? 20 : 28),
                                  fontFamily: Get.setNumberFontFamily(),
                                  fontWeight: FontWeightX.semibold,
                                )),
                          ]),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const DividerWidget(padding: EdgeInsets.symmetric(vertical: 16)),
          ],
        );

  SingleChildRenderObjectWidget _addressItem(
          String transferInfoItemType, List<String> addressList) =>
      addressList.isEmpty
          ? const SizedBox.shrink()
          : Padding(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
              child:
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Text(transferInfoItemType,
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setFontFamily(),
                      fontWeight: FontWeightX.regular,
                    )),
                SizedBox(
                  width: Get.setPaddingSize(12),
                ),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: addressList
                      .map((item) => _addressDetailsItem(item))
                      .toList(),
                )),
              ]));

  Widget _addressDetailsItem(String? address) => Padding(
        padding: const EdgeInsets.only(bottom: 2),
        child: Row(
          children: [
            Expanded(
              child: Text(
                AddressUtils.omitAddress(address),
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.semibold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
            ),
            GestureDetector(
              onTap: () => Get.copy(address),
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: ImageWidget(
                    assetUrl: 'copy',
                    width: Get.setPaddingSize(16),
                    height: Get.setPaddingSize(16)),
              ),
            ),
          ],
        ),
      );

  Widget _remarkItem(String? remark) => remark!.isEmpty
      ? const SizedBox.shrink()
      : Padding(
          padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(10),
          ),
          child: Row(
            children: [
              Text(
                coinType is RippleChain ? ID.remarkTag.tr : ID.remarkMemo.tr,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: styleSecond_14,
              ),
              SizedBox(
                width: Get.setPaddingSize(8),
              ),
              Expanded(
                child: Text(
                  Get.isEmptyString(remark) ? ID.remarkNoText.tr : remark,
                  style: stylePrimary_14_m,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              )
            ],
          ),
        );

  Padding _transationTypeItem(String type) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(ID.coinTsTypeTitle.tr,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            Row(
              children: [
                SymbolWidget(
                  coinModel: coinModel!,
                  size: 20,
                ),
                SizedBox(
                  width: Get.setPaddingSize(4),
                ),
                Text(coinModel!.symbol ?? "",
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setNumberFontFamily(),
                      fontWeight: FontWeightX.medium,
                    )),
                SizedBox(
                  width: Get.setPaddingSize(4),
                ),
                Text(type,
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontWeight: FontWeightX.medium,
                    )),
              ],
            )
          ],
        ),
      );

  Padding _feeItem() => Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(ID.feeTitle.tr,
            maxLines: 1,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.regular,
            )),
        SizedBox(
          width: Get.setPaddingSize(12),
        ),
        Expanded(
            child: coinType!.isEthereumSeries
                ? _ethereumFeeItem()
                : _btiCoinFeeItem()),
      ]));

  Row _ethereumFeeItem() => Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                fee ?? "",
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.semibold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
              const SizedBox(
                height: 2,
              ),
            ],
          ),
        ],
      );

  Column _btiCoinFeeItem() => Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            fee ?? "",
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setNumberFontFamily(),
              fontWeight: FontWeightX.semibold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
          ),
        ],
      );

  Visibility _rawDataItem() => Visibility(
      visible: rawData!.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(ID.stringData.tr,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            GestureDetector(
              onTap: () => Get.copy(rawData ?? ""),
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: Container(
                  height: Get.setFontSize(150), // 固定高度
                  width: Get.width,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Get.theme.colorF9F9F9,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Scrollbar(
                    child: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(rawData ?? "",
                                style: TextStyle(
                                  color: Get.theme.textSecondary,
                                  fontSize: Get.setFontSize(14),
                                  fontFamily: Get.setNumberFontFamily(),
                                  fontWeight: FontWeightX.regular,
                                ))
                          ]),
                    ),
                  ),
                ),
              ),
            ),
          ])));
}
