/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-28 14:06:04
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/broadcast/broadcast_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/solana/sol.dart';

extension BroadcastErrorMessageController on BroadcastController {
  String? broadcastErrorMessage(Map? response) {
    if (response == null) return null;

    dynamic error;
    if (response.containsKey('result')) {
      final result = response['result'];
      error = _resultError(result);
    } else if (response.containsKey('error')) {
      error = response['error'];
      if (error is Map) {
        if (error.containsKey('error')) {
          error = error['error'];
        }
      }
    }

    if (coinType is EosChain) {
      return _eosError(error);
    } else {
      String? rawErrorMessage = _rawErrorMessage(error);
      return _errorMessage(rawErrorMessage);
    }
  }

  String _eosError(dynamic error) {
    String errorStr = '';
    if (error is! String) {
      try {
        errorStr = json.encode(error);
      } catch (e) {
        errorStr = '';
      }
    } else {
      errorStr = error;
    }

    return EosErrorHandler.getErrorEos(errorStr);
  }

  String? _errorMessage(String? rawErrorMessage) {
    if (rawErrorMessage == null) return null;

    if (rawErrorMessage.contains('replacement transaction underpriced')) {
      return ID.stringTxError22.tr;
    }

    if (coinType!.isBitcoinSeries) {
      if (rawErrorMessage.contains('Missing inputs') ||
          rawErrorMessage.contains('bad-txns-inputs') ||
          rawErrorMessage.contains('Transaction already in block chain') ||
          rawErrorMessage.contains('bad-txns-inputs-missingorspent') ||
          rawErrorMessage.contains('bad-txns-inputs-spent')) {
        /// 请更新余额后重新发送
        return ID.stringTxError01.tr;
      } else if (rawErrorMessage
              .contains('transaction already in block chain') ||
          rawErrorMessage.contains('txn-mempool-conflict') ||
          rawErrorMessage.contains('TX rejected')) {
        /// 交易已广播，请勿重复广播
        return ID.stringTxError02.tr;
      } else if (rawErrorMessage.contains('absurdly-high-fee') ||
          rawErrorMessage.contains('rejected')) {
        /// 矿工费太高，请调整矿工费重新发送！
        return ID.stringTxError11.tr;
      } else if (rawErrorMessage.contains('dust')) {
        /// 支付金额过低，请调整后重新发送！
        return ID.stringTxError03.tr;
      } else if (rawErrorMessage.contains('spend-of-coinbase')) {
        /// 发送失败，交易包含未成熟的币，挖矿奖励的币必须等成熟后才能发送，成熟期为100个确认
        return ID.stringTxError13.tr;
      } else if (rawErrorMessage.contains('min relay fee not met') ||
          rawErrorMessage.contains('insufficient priority')) {
        /// 矿工费过低，请调整矿工费重新发送。
        return ID.stringTxError14.tr;
      }
    } else if (coinType!.isEthereumSeries) {
      if (rawErrorMessage.contains('transaction underpriced') ||
          rawErrorMessage.contains('gas is too low')) {
        /// 该笔交易GAS过低，可能交易失败，若长时间未确认请调整GAS重新发送。
        return ID.stringTxError21.tr;
      } else if (rawErrorMessage.contains('nonce too low') ||
          rawErrorMessage.contains('nonce is too low') ||
          rawErrorMessage.contains('same nonce in the queue')) {
        /// 请更新余额后重新发送
        return ID.stringTxError10.tr;
      } else if (rawErrorMessage.contains('known transaction') ||
          rawErrorMessage.contains('already known') ||
          rawErrorMessage.contains('same hash was already imported')) {
        /// 交易已广播，请勿重复广播
        return ID.stringTxError02.tr;
      } else if (rawErrorMessage.contains('not enough gas')) {
        /// GasLimit过低，请调整后重新发送
        return ID.stringTxError23.tr;
      } else if (rawErrorMessage.contains('Insufficient funds') &&
          !rawErrorMessage.contains('gas * price')) {
        /// 余额不足
        return ID.balanceNot.tr;
      }
    } else if (coinType! is NemChain) {
      if (rawErrorMessage.contains('FAILURE_TIMESTAMP') ||
          rawErrorMessage.contains('FAILURE_PAST_DEADLINE')) {
        return ID.stringTxError15.tr;
      } else if (rawErrorMessage
          .contains('FAILURE_TRANSACTION_NOT_ALLOWED_FOR_REMOTE')) {
        /// 余额
        return ID.stringTxError16.tr;
      } else if (rawErrorMessage.contains('FAILURE_INSUFFICIENT_FEEt')) {
        /// 矿工费过低，请调整矿工费重新发送。
        return ID.stringTxError14.tr;
      }
    } else if (coinType is SolanaChain) {
      return SolanaErrorHandler.getErrorSolana(rawErrorMessage);
    }
    return rawErrorMessage;
  }

  String? _rawErrorMessage(dynamic error) {
    if (error == null) return null;
    if (error is String) return error;
    if (error is Map) {
      dynamic message = _resultError(error, key: 'message');
      if (message is String) return message;
      if (message is List) return message.join(' ');
      if (message is Map) return json.encode(message);
      try {
        return json.encode(message);
      } catch (_) {}
    } else {
      try {
        return json.encode(error);
      } catch (_) {}
    }
    return null;
  }

  dynamic _resultError(dynamic result, {String key = 'error'}) {
    if (result == null) return null;

    if (result is Map) {
      if (result.containsKey(key)) {
        return result[key];
      }
    } else if (result is String) {
      return result;
    } else if (result is List<String>) {
      return result.join(' ');
    }
    return null;
  }
}

class EosErrorHandler {
  static const String eosKnownTransaction = "Duplicate transaction";
  static const String eosLoseTransaction = "Expired Transaction";
  static const String actLoseTransaction = "expired transaction";
  static const String eosResourceInsufficient = "eosio_assert_messag";
  static const String eosCpuResourceInsufficient = "CPU usage limit";

  /// EOS 广播错误
  static String getErrorEos(String errorStr) {
    String error = errorStr;

    if (Get.isEmptyString(errorStr)) {
      error = "";
    } else if (errorStr.contains(eosKnownTransaction)) {
      error = ID.stringTxError02.tr;
    } else if (errorStr.contains(eosLoseTransaction) ||
        errorStr.contains(actLoseTransaction)) {
      error = ID.stringEosTxInvalid.tr;
    } else if (errorStr.contains(eosCpuResourceInsufficient)) {
      error = ID.stringInsufficientResources.tr;
    } else if (errorStr.contains(eosResourceInsufficient)) {
      error = ID.stringInsufficientResources.tr;
    }

    return error;
  }
}

class SolanaErrorHandler {
  static const String blockHashNotFound = "Blockhash not found";

  /// EOS 广播错误
  static String getErrorSolana(String errorStr) {
    String error = errorStr;

    if (Get.isEmptyString(errorStr)) {
      error = "";
    } else if (errorStr.contains(blockHashNotFound)) {
      error = ID.stringSolanaTxError1.tr;
    }

    return error;
  }
}
