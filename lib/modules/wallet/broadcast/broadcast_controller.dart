import 'dart:convert';

import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/dapp/browser/ethereum/etherum_dapp_controller.dart';
import 'package:coinbag/modules/wallet/assets/activity/token_activity_controller.dart';
import 'package:coinbag/modules/wallet/broadcast/broadcast_error_message_controller.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

enum BroadcastStatus { loading, success, error }

class BroadcastController extends BaseController<BlockChainService> {
  CoinType? coinType;
  TransferModel? tsModel;

  late RxString sendingText = statusText.obs;
  late RxString image = statusImage.obs;
  late Wallet? wallet;

  RxString errorText = ''.obs;

  RxString rawDataText = ''.obs;

  Rx<BroadcastStatus> sendStatus = BroadcastStatus.loading.obs;

  @override
  void onInit() {
    super.onInit();
    tsModel = Get.arguments is TransferModel ? Get.arguments : null;
    coinType = CoinBase.getCoinTypeByChain(tsModel?.chain);
    wallet = tsModel!.wallet;
  }

  @override
  void onReady() {
    super.onReady();

    ///测试网络 开发模式，手动广播，显示广播内容
    if (AppConfig.instance.isTestNetWorkDev) {
      rawDataText.value = json.encode(coinType!.broadcastTransaction(tsModel!));
      sendingText.value = ID.stringBroadcastTitle.tr;
    } else {
      loadData();
    }
  }

  @override
  void loadData() {
    sendStatus.value = BroadcastStatus.loading;
    sendingText.value = statusText;
    image.value = statusImage;
    errorText.value = '';

    httpRequest<BaseResponseV1<dynamic>>(
      api.broadcast(BlockChainParamsManager.createParams(
          method: (coinType is EosChain || wallet!.isP1P2P3Wallet)
              ? BlockChainAPI.noVerifyBroadcast
              : BlockChainAPI.broadcast,
          requestParams: coinType!.broadcastTransaction(tsModel!))),
      (value) {
        Map? data = value.data;

        String txid = '';
        String? errorMessage;
        if (data == null) {
          sendStatus.value = BroadcastStatus.error;
          errorMessage = value.error?.message;
        } else {
          if (data.containsKey('txid') && data['txid'] is String) {
            txid = data['txid'];
          } else {
            errorMessage = broadcastErrorMessage(data);
          }
        }

        // 广播成功
        if (!Get.isEmptyString(txid)) {
          sendStatus.value = BroadcastStatus.success;
          Get.showToast(ID.broadcastSuccess.tr, toastMode: ToastMode.success);

          broadcastSuccess(txid);
        } else {
          sendStatus.value = BroadcastStatus.error;
          errorText.value = errorMessage ?? '';
          Get.showToast(ID.broadcastError.tr, toastMode: ToastMode.success);
        }

        sendingText.value = statusText;
        image.value = statusImage;
      },
      showToast: false,
      handleSuccess: false,
      handleError: false,
      error: (e) {
        sendStatus.value = BroadcastStatus.error;
        sendingText.value = statusText;
        image.value = statusImage;
      },
    );
  }

  Future<void> broadcastSuccess(String txId) async {
    Log.r("broadcastSuccess");
    if (tsModel != null && tsModel!.isDapp) {
      if (Get.isRegistered<WebController>()) {
        Get.find<WebController>().onSendResutl(txId);
      }

      Future.delayed(const Duration(seconds: 2)).then((value) {
        Get.until((route) {
          return (route.settings.name == AppRoutes.webPage);
        });
      });
    } else {
      if (tsModel!.type == TransferType.transfer && !wallet!.isP1P2P3Wallet) {
        await Get.database.transactionsActivityDao.addPacking(
          tsModel: tsModel!,
          coinType: coinType!,
          txId: txId,
        );
      }

      Future.delayed(const Duration(seconds: 2))
          .then((value) => backPage(txId));
    }
  }

  void backPage(String txId) {
    try {
      if (wallet!.isP1P2P3Wallet && tsModel!.chain != EosChain.get.chain) {
        Get.back();
        if (wallet is Pro3Wallet) {
          _navigateToQRCodePage(txId);
        }
      } else {
        // 尝试刷新数据
        _refreshControllers();
        // 刷新首页和交易记录
        Get.until((route) =>
            route.settings.name == AppRoutes.tokenAssetsPage ||
            route.settings.name == AppRoutes.mainPage);
      }
    } catch (_) {}
  }

  // 定义包装函数来执行页面导航
  void _navigateToQRCodePage(String txId) {
    Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
      GetArgumentsKey.walletId: tsModel!.walletId,
      GetArgumentsKey.qrData: txId,
      GetArgumentsKey.qrType: QRCodeType.broad,
      GetArgumentsKey.transferModel: tsModel,
      GetArgumentsKey.wallet: tsModel!.wallet,
    });
  }

  void _refreshControllers() {
    // 刷新 TokenActivityController

    if (Get.isRegistered<TokenActivityController>(tag: "all")) {
      Get.find<TokenActivityController>(tag: "all").loadData();
    }
    AppController.refreshNft();
  }

  String get statusText {
    if (sendStatus.value == BroadcastStatus.loading) {
      return ID.broadcastSending.tr;
    } else if (sendStatus.value == BroadcastStatus.success) {
      return ID.broadcastSuccess.tr;
    } else if (sendStatus.value == BroadcastStatus.error) {
      return ID.broadcastError.tr;
    }
    return ID.broadcastSending.tr;
  }

  String get statusImage {
    if (sendStatus.value == BroadcastStatus.loading) {
      return 'icon_sending';
    } else if (sendStatus.value == BroadcastStatus.success) {
      return 'icon_success';
    } else if (sendStatus.value == BroadcastStatus.error) {
      return 'icon_failed';
    }
    return 'icon_sending';
  }
}

class BroadcastBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => BroadcastController());
  }
}
