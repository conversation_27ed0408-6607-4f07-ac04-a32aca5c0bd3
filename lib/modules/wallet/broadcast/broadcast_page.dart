/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-18 11:21:48
 * @LastEditTime: 2024-10-30 16:53:45
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/broadcast/broadcast_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class BroadcastPage extends BaseStatelessWidget<BroadcastController> {
  const BroadcastPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Obx(() => Scaffold(
            appBar: baseAppBar(title: '', hideLeading: true),
            bottomNavigationBar: _bottomNavigationBar(),
            body: _bodyWidget(),
          )),
    );
  }

  SingleChildScrollView _bodyWidget() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        top: Get.setPaddingSize(80),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ImageWidget(
            assetUrl: controller.image.value,
            width: Get.setImageSize(84),
            height: Get.setImageSize(84),
          ),
          SizedBox(
            height: Get.setPaddingSize(24),
            width: Get.width,
          ),
          Text(
            controller.sendingText.value,
            textAlign: TextAlign.center,
            style: stylePrimary_16_m,
          ),
          SizedBox(
            height: Get.setPaddingSize(24),
          ),
          Text(
            controller.errorText.value,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: Get.setFontSize(14), color: Get.theme.textSecondary),
          ),
          testNetDevWidget()
        ],
      ),
    );
  }

  Padding? _bottomNavigationBar() {
    return controller.sendStatus.value != BroadcastStatus.error
        ? null
        : Padding(
            padding: EdgeInsets.only(
              top: Get.setPaddingSize(16),
              bottom: Get.getSafetyBottomPadding(),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ButtonWidget(
                  text: ID.broadcastReset.tr,
                  width: Get.setWidth(240),
                  onPressed: () => controller.loadData(),
                ),
                SizedBox(
                  height: Get.setPaddingSize(16),
                ),
                OutlinedButtonWidget(
                  text: ID.stringBack.tr,
                  width: Get.setWidth(240),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
          );
  }

  RenderObjectWidget testNetDevWidget() {
    if (AppConfig.instance.isTestNetWorkDev) {
      return Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
        SizedBox(
          height: Get.setPaddingSize(8),
        ),
        Text(
          controller.rawDataText.value,
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: Get.setFontSize(14), color: Get.theme.textSecondary),
        ),
        SizedBox(
          height: Get.setPaddingSize(8),
        ),
        Padding(
          padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            bottom: Get.getSafetyBottomPadding(),
          ),
          child: ButtonWidget(
            buttonSize: ButtonSize.full,
            text: ID.copy.tr,
            onPressed: () => Get.copy(controller.rawDataText.value),
          ),
        ),
        SizedBox(
          height: Get.setPaddingSize(8),
        ),
        Padding(
          padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            bottom: Get.getSafetyBottomPadding(),
          ),
          child: ButtonWidget(
            buttonSize: ButtonSize.full,
            text: ID.stringBroadcast.tr,
            onPressed: () async => controller.loadData(),
          ),
        )
      ]);
    } else {
      return const SizedBox.shrink();
    }
  }
}
