/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-22 14:27:00
 */
import 'package:carousel_slider_plus/carousel_slider_plus.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/qrcode/wallet_qr_code_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:toggle_switch/toggle_switch.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';

class WalletQRCodePage extends BaseStatefulWidget<WalletQRCodeController> {
  WalletQRCodePage({super.key}) {
    // 确保 WalletQRCodeController 已注入，防止 Get.find 报错
    if (!Get.isRegistered<WalletQRCodeController>()) {
      Get.lazyPut<WalletQRCodeController>(() => WalletQRCodeController());
    }
  }

  @override
  void dispose() {
    AppController.cancelTxAction();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(),
      body: Obx(() => SingleChildScrollView(
            child: SizedBox(
              width: Get.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: Get.setPaddingSize(Get.setPaddingSize(32)),
                  ),
                  Text(
                    controller.title.value,
                    style: TextStyle(
                        color: Get.theme.textPrimary,
                        fontSize: Get.setFontSize(32),
                        fontFamily: Get.setFontFamily(),
                        fontWeight: FontWeightX.medium),
                  ),
                  SizedBox(height: Get.setPaddingSize(12)),
                  _titleWidget(),
                  Visibility(
                    visible: controller.qrImageList.length > 300,
                    child: Padding(
                      padding: EdgeInsets.only(top: Get.setPaddingSize(16)),
                      child: Text(
                        ID.stringQrMaxNumberTip.tr,
                        style: TextStyle(
                          color: Get.theme.colorF44D4D,
                          fontSize: Get.setFontSize(12),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: Get.setPaddingSize(16),
                  ),
                  _carouselSlider(),
                  Visibility(
                      visible: controller.qrImageList.length > 1,
                      child: Column(
                        children: [
                          SizedBox(
                            height: Get.setPaddingSize(16),
                          ),
                          _indicatorWidget(),
                          SizedBox(
                            height: Get.setPaddingSize(24),
                          ),
                          _pageMethodWidget(),
                        ],
                      )),
                  SizedBox(
                    height: Get.setPaddingSize(36),
                  ),
                  _scanWidget(),
                ],
              ),
            ),
          )),
    );
  }

  Container _pageMethodWidget() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(Get.setRadius(21))),
        border: Border.all(color: Get.theme.colorECECEC, width: 1),
      ),
      child: Padding(
        padding: EdgeInsets.all(Get.setPaddingSize(4)),
        child: Obx(() => ToggleSwitch(
              minWidth: Get.setWidth(90),
              minHeight: Get.setHeight(34),
              cornerRadius: Get.setRadius(17),
              activeBgColors: [
                [Get.theme.colorF3F3F5],
                [Get.theme.colorF3F3F5]
              ],
              activeFgColor: Get.theme.textPrimary,
              inactiveBgColor: Get.theme.bgColor,
              inactiveFgColor: Get.theme.textTertiary,
              initialLabelIndex: controller.isAuto.value ? 0 : 1,
              totalSwitches: 2,
              fontSize: Get.setFontSize(14),
              labels: [ID.qrAutoPage.tr, ID.qrManualPage.tr],
              radiusStyle: true,
              onToggle: (index) =>
                  controller.isAuto.value = !controller.isAuto.value,
            )),
      ),
    );
  }

  ButtonWidget _scanWidget() {
    return ButtonWidget(
        text: controller.buttonTitle.value,
        buttonStatus: ButtonStatus.icon,
        iconWidget: Padding(
          padding: EdgeInsets.only(right: Get.setPaddingSize(8)),
          child: ImageWidget(
              assetUrl: 'scan',
              width: Get.setImageSize(18),
              height: Get.setImageSize(18)),
        ),
        width: Get.setWidth(240),
        onPressed: () {
          if (controller.qrType == QRCodeType.export ||
              controller.qrType == QRCodeType.broad) {
            Get.back();
          } else {
            Get.toScanner(arguments: {
              GetArgumentsKey.transferModel: controller.tsModel,
            });
          }
        });
  }

  Text _titleWidget() {
    return controller.qrType == QRCodeType.verifyDivice
        ? Text(ID.p4verifyRemind2.tr,
            style: TextStyle(
                color: Get.theme.textSecondary,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.regular))
        : Text.rich(
            textAlign: TextAlign.center,
            TextSpan(
                text: ID.qrSettingQrsizeTitle1.tr,
                style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(14),
                    fontWeight: FontWeightX.regular),
                children: [
                  TextSpan(
                      text: ID.qrSettingQrsizeTitle2.tr,
                      style: TextStyle(
                          color: Get.theme.colorFF6A16,
                          fontSize: Get.setFontSize(14),
                          fontWeight: FontWeightX.regular),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () => Get.toNamed(AppRoutes.preferencesPage))
                ]));
  }

  SizedBox _carouselSlider() {
    return SizedBox(
      width: controller.qrImageSize,
      child: CarouselSlider(
        items: controller.qrImageList,
        controller: controller.carouselCtr,
        options: CarouselOptions(
            enableInfiniteScroll: controller.qrImageList.length > 1,
            autoPlay: controller.qrImageList.length <= 1
                ? false
                : controller.isAuto.value,
            autoPlayInterval: const Duration(milliseconds: 1500),
            autoPlayAnimationDuration: const Duration(milliseconds: 300),
            animateToClosest: false,
            height: controller.qrImageSize,
            aspectRatio: 1,
            viewportFraction: 1,
            onPageChanged: (index, _) {
              if (Get.isRegistered<WalletQRCodeController>()) {
                controller.selectedPage.value = index;
              }
            }),
      ),
    );
  }

  SizedBox _indicatorWidget() {
    double maxWidth = controller.qrImageSize - Get.setPaddingSize(24);
    double itemWidth = 18 + Get.setPaddingSize(4);
    int maxVisibleDots = maxWidth ~/ itemWidth;
    if ((maxVisibleDots % 2) == 0) {
      maxVisibleDots++;
    }

    if (maxVisibleDots < 5) {
      maxVisibleDots = 5;
    }

    return SizedBox(
      width: maxWidth,
      child: Center(
        child: AnimatedSmoothIndicator(
            activeIndex: controller.selectedPage.value,
            count: controller.qrImageList.length,
            effect: ScrollingDotsEffect(
                spacing: Get.setPaddingSize(4),
                radius: 2,
                dotWidth: 16,
                dotHeight: 4,
                paintStyle: PaintingStyle.fill,
                strokeWidth: 1,
                activeStrokeWidth: 1,
                activeDotScale: 1,
                maxVisibleDots: maxVisibleDots,
                dotColor: Get.theme.colorECECEC,
                activeDotColor: Get.theme.primary), // your preferred effect
            onDotClicked: (index) {}),
      ),
    );
  }
}
