/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-02-27 10:51:20
 */
import 'package:carousel_slider_plus/carousel_slider_plus.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/qr/qr_config.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/wallet.dart';

class WalletQRCodeController extends BaseController {
  RxInt selectedPage = 0.obs;
  RxList<Widget> qrImageList = <Widget>[].obs;
  RxString title = "".obs;
  RxString buttonTitle = ''.obs;
  double qrImageSize = QRConfig.qrSize;
  RxBool isAuto = true.obs;
  final CarouselSliderController carouselCtr = CarouselSliderController();

  String? qrData;
  String? walletId;
  TransferModel? tsModel;
  QRCodeType? qrType;
  Wallet? wallet;

  @override
  void onInit() {
    if (Get.arguments!.containsKey(GetArgumentsKey.qrType)) {
      qrType = Get.arguments![GetArgumentsKey.qrType];
    }
    assert(qrType != null, '二维码类型必传');

    if (Get.arguments!.containsKey(GetArgumentsKey.transferModel)) {
      tsModel = Get.arguments![GetArgumentsKey.transferModel];
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.qrData)) {
      qrData = Get.arguments![GetArgumentsKey.qrData];
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.walletId)) {
      walletId = Get.arguments![GetArgumentsKey.walletId];
    }

    if (Get.arguments!.containsKey(GetArgumentsKey.wallet)) {
      wallet = Get.arguments![GetArgumentsKey.wallet];
    }

    /// 监听二维码容量更新
    ever(AppController.qrModel, (callback) => loadData());

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
    AppController.setBrightness();
  }

  @override
  void loadData() {
    List<String> qrList = [];
    switch (qrType!) {
      case QRCodeType.export:
      case QRCodeType.transfer:
      case QRCodeType.signMessage:
      case QRCodeType.cosmosSignMessage:
        {
          buttonTitle.value = ID.scanQrSendTitle.tr;
          if (qrType == QRCodeType.transfer) {
            title.value = ID.qrTitleTransfer.tr;
          } else if (qrType == QRCodeType.signMessage ||
              qrType == QRCodeType.cosmosSignMessage) {
            title.value = ID.stringSignMessage.tr;
          } else if (qrType == QRCodeType.export) {
            title.value = ID.stringExportAccountTitle.tr;
            buttonTitle.value = ID.stringScanCompleteTitle.tr;
          }

          qrList = QRCodeGenerator.qrCode(
              codeType: qrType!,
              qrSize: AppController.qrModel.value.qrSize,
              tsModel: tsModel);
        }
        break;
      case QRCodeType.bindChain:
        {
          assert(qrData != null, 'qrData不能为空');
          assert(walletId != null, 'walletId不能为空');
          assert(wallet != null, 'wallet不能为空');

          title.value = ID.bindWallet.tr;
          buttonTitle.value = ID.scanQrBind.tr;
          qrList = QRCodeGenerator.qrCode(
            codeType: qrType!,
            qrSize: AppController.qrModel.value.qrSize,
            qrData: qrData!,
            walletId: walletId!,
            wallet: wallet!,
          );
        }
        break;
      case QRCodeType.verifyDivice:
        {
          assert(qrData != null, 'qrData不能为空');
          assert(wallet != null, 'walletType不能为空');

          title.value = ID.verifysn.tr;
          buttonTitle.value = ID.p4verifyRemind.tr;

          qrList = QRCodeGenerator.qrCode(
            codeType: qrType!,
            qrSize: AppController.qrModel.value.qrSize,
            qrData: qrData!,
            wallet: wallet!,
          );
        }

        break;

      case QRCodeType.broad:
        title.value = ID.stirngSyncBroadInfo.tr;
        buttonTitle.value = ID.stringScanCompleteTitle.tr;
        qrList = QRCodeGenerator.qrCode(
          codeType: qrType!,
          qrSize: AppController.qrModel.value.qrSize,
          qrData: qrData!,
          wallet: wallet!,
          tsModel: tsModel!,
        );
        break;

      default:
    }

    double padding = 10;
    qrImageList.value = qrList
        .map((e) => QRWidget(
            data: e,
            width: qrImageSize - padding,
            height: qrImageSize - padding,
            imageSize: (qrImageSize - padding) * 0.18,
            padding: EdgeInsets.all(padding),
            assetImage: const AssetImage('assets/images/icon_logo.png')))
        .toList();
  }

  @override
  void onClose() {
    super.onClose();
    SendController.updateTimer();
    AppController.resetBrightness();
  }
}

class WalletQRCodeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WalletQRCodeController());
  }
}
