/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-02-27 13:09:55
 */
import 'dart:convert';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/dapp/browser/bitcoin/bitcoin_dapp_controller.dart';
import 'package:coinbag/modules/dapp/browser/ethereum/etherum_dapp_controller.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/chains/controllers/message_sign_controller.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/wallet/cold/constant/common_constant.dart';

extension ScanTransferController on ScanController {
  Future<void> coinTransferCmd(int cmd, String? data) async {
    final json = jsonDecode(data!);
    String taskId = json['task_id'];
    String walletId = json['wallet_id'];
    String rawtx = json['rawtx'];

    CoinType? coinType = CoinBase.getCoinTypeByChain(tsModel?.chain);
    if (coinType == null) {
      Get.offAndToNamed(AppRoutes.scanResultPage,
          arguments: {GetArgumentsKey.scanResult: data});
      return;
    }

    if (coinType is SolanaChain) {
      rawtx = await Get.walletCore.splitTransactionSignature(
              chain: tsModel!.chain,
              waitSignature: tsModel!.waitSignature!,
              signature: rawtx) ??
          '';
    }

    WalletModel? walletModel = await Get.database.walletDao.getCheckedWallets();
    if (walletModel != null && tsModel != null) {
      if (walletModel.walletId == walletId && tsModel!.taskId == taskId) {
        tsModel!.rawtx = rawtx;
        Get.offAndToNamed(AppRoutes.broadcastPage, arguments: tsModel);
        return;
      }
    }

    Get.offAndToNamed(AppRoutes.scanResultPage,
        arguments: {GetArgumentsKey.scanResult: data});
  }

  void signMessageCmd(int cmd, String? message) {
    if (AppController.isDappSignMessageFlag.value) {
      AppController.isDappSignMessageFlag.value = false;
      Get.until((route) {
        return (route.settings.name == AppRoutes.webPage);
      });

      Map<String, dynamic> jsonObject = json.decode(message!);
      String data = jsonObject['data'];
      if (data.isEmpty) return;

      Log.r(message);
      switch (cmd) {
        case CmdCode.bitcoinSignMessage:
          if (Get.isRegistered<WebController>()) {
            Get.find<WebController>().onSendPsbtSignMessageData(data);
          }
          break;

        case CmdCode.ethereumSignMessage:
          if (Get.isRegistered<WebController>()) {
            Get.find<WebController>().onSendResutl(data);
          }
          break;
      }
    } else {
      Get.until((route) {
        return (route.settings.name == AppRoutes.messageSignPage);
      });
      try {
        final json = jsonDecode(message!);
        MessageSignController ctr = Get.find<MessageSignController>();
        ctr.showResultSignMessge(json[GetArgumentsKey.data]);
      } catch (_) {}
    }
  }

  void handleRawPsbtTransfer(String? result) {
    Get.until((route) {
      return (route.settings.name == AppRoutes.webPage);
    });
    Map<String, dynamic> jsonObject = json.decode(result!);
    String rawtx = jsonObject['rawtx'];

    Get.find<WebController>().onSendPsbtSignData(rawtx);
  }

  void signBabylonCmd(String? data) {
    // if (AppController.isDappSignMessageFlag.value) {
    //   AppController.isDappSignMessageFlag.value = false;
    Get.until((route) {
      return (route.settings.name == AppRoutes.webPage);
    });

    final json = jsonDecode(data!);

    String rawtx = json['rawtx'];

    Log.r(rawtx);
    if (Get.isRegistered<WebController>()) {
      Get.find<WebController>().onSetBBNSignature(rawtx);
    }
    //}
  }

  void signMessageCosmosCmd(String? data) {
    Get.until((route) {
      return (route.settings.name == AppRoutes.webPage);
    });

    final json = jsonDecode(data!);

    String rawtx = json['data'];

    Log.r(rawtx);
    if (Get.isRegistered<WebController>()) {
      Get.find<WebController>().onSetBBNSignature(rawtx);
    }
    //}
  }
}
