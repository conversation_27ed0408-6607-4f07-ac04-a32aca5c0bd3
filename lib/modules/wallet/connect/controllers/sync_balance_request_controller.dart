/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-01-13 11:01:11
 */
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/utxo_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/extra.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/connect/controllers/sync_balance_controller.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/send/controllers/send_gear_fee_controller.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/model/utxo/utxo_model.dart';

extension SyncBalanceRequestController on SyncBalanceController {
  void syncRequest() {
    List<Future> futures = [];
    futures.add(_balance());
    bool needFee = coinType.isEthereumSeries ||
        coinType.isBitcoinSeries ||
        coinType is RippleChain;
    if (needFee) {
      futures.add(_getFeeGear());
    }

    if (coinType.isBitcoinSeries) {
      futures.add(_getUtxo());
    }
    multiHttpRequest(futures, (value) {
      List data = value as List;
      if (data.isEmpty) {
        showError();
        return;
      }

      BaseResponseV1 response0 = data[0];
      bool balanceResult = _balanceResult(response0);

      bool feeResult = true;
      bool utxoResult = true;
      if (needFee) {
        BaseResponseV1 response1 = data[1];
        feeResult = _getFeeResult(response1);

        if (coinType.isBitcoinSeries) {
          utxoResult = _utxoResult(data[2]);
        } else if (coinType.isEthereumSeries) {
          tsModel.gasLimit = coinType.gasLimit;
        }
      }

      if (feeResult == false || balanceResult == false || utxoResult == false) {
        showError();
      } else {
        setupQRWidget();
      }
    });
  }

  Future<dynamic> _getUtxo() => UtxoService()
      .getUtxo(chain: coinType.chain, address: addressModel!.address!);
  bool _utxoResult(List<dynamic>? data) {
    if (data == null) return false;
    tsModel.utxoList = data.map((e) => UtxoModel.fromJson(e).toJson()).toList();
    return true;
  }

  Future<BaseResponseV1<dynamic>> _getFeeGear() =>
      api.getFeeGear(BlockChainParamsManager.createParams(
          method: BlockChainAPI.feeGear,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinType.chain)
              .getRequestBody()));

  bool _getFeeResult(BaseResponseV1 response) {
    if (response.data == null) {
      Get.showToast(response.error?.message, toastMode: ToastMode.failed);
      return false;
    }
    List data = response.data;
    List<FeeGearModel> models = data
        .map((e) =>
            SendGearController.gearModelFromJson(json: e, coinType: coinType))
        .toList();

    FeeGearModel model =
        models.where((e) => e.feeType == FeeType.normal).toList().first;
    if (coinType.isEthereumSeries) {
      tsModel.gasPrice = model.fee;
    } else if (coinType.isBitcoinSeries) {
      tsModel.btcSatB = model.fee;
    } else if (coinType is RippleChain) {
      tsModel.xrpFee = model.fee ?? "10";
      Log.logPrint(tsModel.xrpFee);
    }
    return true;
  }

  Future<BaseResponseV1<dynamic>> _balance() {
    late String chain;
    Extra extra = Extra();
    Map<String, dynamic> extraMap = extra.toJson();
    if (coinModel!.isToken! == false) {
      chain = coinType.chain;
    } else {
      chain = coinModel!.chain!;
      if (coinModel!.isToken!) {
        extraMap = extra.toContractBalanceJson(tokenModel!.contract ?? '');
      }
    }

    if (coinType is EosChain) {
      extraMap = extra.toContractBalanceJson(APIConstant.eosioToken);
      extraMap[APIConstant.symbol] = EosChain.get.chain;
    }

    return api.getTokenBalance(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getTokenBalance,
        requestParams: RequestParams()
            .put(APIConstant.chain, chain)
            .put(APIConstant.address, addressModel!.address)
            .put(
                APIConstant.type,
                (coinType is TronChain && coinModel!.isToken!)
                    ? TronChain.get.getTokenType(coinModel!.tokenType)
                    : '')
            .put(APIConstant.extra, extraMap)
            .getRequestBody()));
  }

  bool _balanceResult(BaseResponseV1 response) {
    BalanceModel? balanceModel;
    if (response.data != null) {
      if (Get.isResponseDataValid(response.data)) {
        balanceModel =
            BalanceModel.fromJson(Map<String, dynamic>.from(response.data));
        tsModel.nonce = balanceModel.nonce ?? '0';
        String balance = (balanceModel.balance ?? '0').div(
            pow(10, coinModel!.chainDecimal!).toString(),
            scale: coinModel!.chainDecimal!);
        this.balance.value =
            balance.decimal(scale: 6, roundMode: RoundMode.down);
        if (coinModel!.isToken!) {
          tsModel.tokenBalance = balance;
        } else {
          tsModel.mainBalance = balance;
        }

        if (coinType is RippleChain) {
          if (balance.lessThan(RippleChain.get.reserveAmount)) {
            xrpActivityDialog();
          }
        }

        return true;
      } else {
        return false;
      }
    } else {
      if (response.error != null &&
          !Get.isEmptyString(response.error!.message!)) {
        Get.showToast(response.error!.message, toastMode: ToastMode.failed);
      } else {
        Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      }
    }
    return false;
  }
}
