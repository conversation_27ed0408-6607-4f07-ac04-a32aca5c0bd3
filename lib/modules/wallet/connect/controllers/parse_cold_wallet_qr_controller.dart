import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/scan/common/scan_constant.dart';
import 'package:coinbag/modules/scan/common/scan_utils.dart';
import 'package:coinbag/modules/scan/models/ultra_scan_model.dart';
import 'package:coinbag/modules/scan/models/wallet_scan_model.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/connect/controllers/scan_transfer_controller.dart';
import 'package:coinbag/modules/wallet/connect/controllers/sync_address_controller.dart';
import 'package:coinbag/modules/wallet/connect/dialog/select_hardware_walelt.dialog.dart';
import 'package:coinbag/modules/wallet/send/dialog/transaction_confirmation_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:wallet_core/chain/bitcoin/bch.dart';
import 'package:wallet_core/chain/bitcoin/usdt.dart';
import 'package:wallet_core/chain/bitcoin/zec.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/encrypt/key/encrypt_key.dart';
import 'package:wallet_core/encrypt/utils/aes_helper.dart';
import 'package:wallet_core/ffi/model/parse_result_model.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/cold/constant/common_constant.dart';
import 'package:wallet_core/wallet/cold/pro1_pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro1_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

extension ParseColdWalletQrController on ScanController {
  ///是冷钱包格式的二维码
  bool isColdWalletQr(String result) {
    // 检查是否以冷钱包头部标志开头
    if ((result.startsWith(WalletHeaderTag.walletTag) ||
            result.startsWith(WalletHeaderTag.walletTagCoinbag)) &&
        scanAction != ScanAction.resultAction) {
      return true; // 如果是冷钱包头部，返回 false
    }

    return Wallet.isOldWalletQR(result);
  }

  ///解析冷钱包二维码数据
  Future<void> parserColdWalletQr(String result, Wallet? scanWallet) async {
    if (isParsingQR) {
      return;
    }

    isParsingQR = true;
    visiblePage.value = true;

    if (result.startsWith(WalletHeaderTag.walleltTagCoinbagU2)) {
      result = result.replaceFirst(WalletHeaderTag.walleltTagCoinbagU2,
          WalletHeaderTag.walleltTagU1Plus);
    }
    WalletType walletType = Wallet.getWalletTypeByHeader(result);
    Wallet? wallet = Wallet.getWalletByHeader(result);
    if (wallet != null &&
        scanWallet != null &&
        scanWallet.type != wallet.type) {
      Get.showToast(ID.scanNotMatchWallet.tr);
      return;
    }
    if (walletType != WalletType.pro1Pro2) {
      result = result.substring(result.indexOf(":") + 1, result.length);
    }
    switch (walletType) {
      case WalletType.ultraPlus:
      case WalletType.ultra:
      case WalletType.pro3Plus:
        _parserUltraOrP3PlusWallet(result);
        break;
      case WalletType.pro1Pro2:
      case WalletType.pro2Plus:
      case WalletType.pro3:
        _parserP1P2P3Wallet(result, walletType);
        break;
      case WalletType.touch:
        break;
      case WalletType.smart:
        break;

      case WalletType.pro2:
        break;
      case WalletType.pro1:
        break;
      case WalletType.unknown:
        break;
    }
  }

  /// 解析Ultra和P3+二维码数据
  Future<void> _parserUltraOrP3PlusWallet(String result) async {
    try {
      final walletScanModel = WalletScanModel.fromJson(json.decode(result));
      final scanModel = UltraScanModel()
        ..data = walletScanModel.data
        ..page = walletScanModel.index;

      // 判断当前 cmd 是否与上一个不同
      if (lastCmd != 0 && lastCmd != walletScanModel.cmd) {
        Get.showToast(ID.scanError.tr);
        toScanResult("${ScanConstant.errorCodeE1}:\\n$result");
        return;
      }

      // 更新 previousCmd
      lastCmd = walletScanModel.cmd!;

      // 更新 resultMap 并检查大小变化
      int beforeSize = resultMap.length;
      resultMap[walletScanModel.index!] = scanModel;
      int afterSize = resultMap.length;

      if (beforeSize != afterSize) {
        await playSound();
      }

      // 添加新的扫描模型到列表
      List<UltraScanModel> newDataList =
          ScanUtils.addUltralScanModelToList(ultraScanModelDataList, scanModel);

      // 计算进度
      if (walletScanModel.total != 0) {
        double value = newDataList.length / walletScanModel.total!;
        progress.value = value;
        percentage.value = '${(value * 100).toStringAsFixed(0)}%';

        if (newDataList.length == walletScanModel.total) {
          isParsingQR = true;
          pausePreview();
          _handleUltraOrP3PlusData(
              walletScanModel.cmd, ScanUtils.getData(newDataList));
        } else {
          isParsingQR = false;
        }
      }
    } catch (e) {
      Log.e(e.toString());
      Get.showToast(ID.scanError.tr);
      toScanResult("${ScanConstant.errorCodeE1}:\\n$result");
    }
  }

  /// 处理P3+ Ultra二维码各种业务数据
  Future<void> _handleUltraOrP3PlusData(int? cmd, String data,
      {Function? callBack}) async {
    String? result = "";
    result = AesHelpler(EncryptKey.keyUltraOrP3Plus, EncryptKey.aesIVKey)
        .decrypt(data);
    Log.s("cold qrCode result = $result");

    switch (cmd!) {
      case CmdCode.bindWallet:
        Get.connectWallet.handleConnectWallet(result, callBack: callBack);
        break;
      case CmdCode.authenticateDeviceID:
      case CmdCode.authenticateDevice:
        Get.offAndToNamed(AppRoutes.verifyDevice, arguments: {
          GetArgumentsKey.scanResult: result,
          GetArgumentsKey.cmd: cmd
        });
        break;
      case CmdCode.transferBTCSeries:
      case CmdCode.transferBTCSeriesPsbt:
      case CmdCode.transferETHSeries:
      case CmdCode.transferFIL:
      case CmdCode.transferBNB:
      case CmdCode.transferXRP:
      case CmdCode.transferXEM:
      case CmdCode.transferTRX:
      case CmdCode.transferEOS:
      case CmdCode.transferSolana:
      case CmdCode.transferBaby:
      case CmdCode.transferBabyTest:
        coinTransferCmd(cmd, result);
        break;
      case CmdCode.bitcoinSignMessage:
      case CmdCode.ethereumSignMessage:
        signMessageCmd(cmd, result);
        break;
      case CmdCode.rawPsbt:
        handleRawPsbtTransfer(result);
        break;
      case CmdCode.synAddress:
        synAddress(result);
        break;
      case CmdCode.dappBabyRaw:
        signBabylonCmd(result);
        break;
      case CmdCode.cosmosSignMessage:
        signMessageCosmosCmd(result);
        break;
    }

    Future.delayed(const Duration(milliseconds: 300), () {
      visiblePage.value = false;
    });
  }

  /// 解析P2+Pro3维码数据
  Future<void> _parserP1P2P3Wallet(String data, WalletType walletType) async {
    try {
      String? result = "";
      if (walletType == WalletType.pro1Pro2) {
        bool isP1P2WalletAction = Wallet.isP1P2WalletAction(data);
        if (isP1P2WalletAction) {
          String jsonData =
              AesHelpler(EncryptKey.aesMainKey, EncryptKey.aesIVKey)
                  .decrypt(data);
          if (jsonData.isNotEmpty) {
            if (Wallet.containsRequiredKeys(jsonData, ['a', 't'])) {
              mPro3QtType = QTType.balance;
            } else if (Wallet.containsRequiredKeys(jsonData, ['r', 'c', 'p'])) {
              mPro3QtType = QTType.broadcard;
            }
            result = jsonData;
          }
        } else {
          result = data;
        }
      } else {
        String key = EncryptKey.aesMainKey;
        if (walletType == WalletType.pro3) {
          key = EncryptKey.keyPro3;
        }
        result = AesHelpler(key, EncryptKey.aesIVKey).decrypt(data);
      }
      Log.s("cold qrCode result = $result");

      Map<String, dynamic> jsonObject = json.decode(result);

      if (jsonObject.containsKey('qt')) {
        mPro3QtType = jsonObject['qt'] ?? 0;
        //P2+ P3
        if (mPro3QtType != -1) {
          if (mPro3QtType == QTType.balance ||
              mPro3QtType == QTType.broadcard ||
              mPro3QtType == QTType.monitoringAddressV2) {
            if (jsonObject.containsKey('r') &&
                jsonObject.containsKey('c') &&
                jsonObject.containsKey('p')) {
              // 是多页，继续扫描
              _parserMultiPageData(
                result,
                walletType,
              );
            } else {
              // 单页
              _parserData(result, walletType);
            }
          } else if (Pro3Wallet.isPro3NewType(mPro3QtType)) {
            _parserData(result, walletType);
          } else if (mPro3QtType == QTType.antiCounterfeiting) {
          } else {
            Get.connectWallet.toScanResult(result);
          }
        }
      } else {
        //P1 P2
        if (jsonObject.containsKey('r') &&
            jsonObject.containsKey('c') &&
            jsonObject.containsKey('p')) {
          // 是多页，继续扫描
          _parserMultiPageData(
            result,
            walletType,
          );
        } else {
          // 单页直接处理
          _parserData(result, walletType);
        }
      }
    } catch (e) {
      Log.e(e.toString());
      Get.showToast(ID.scanError.tr);
      toScanResult("${ScanConstant.errorCodeE1}:\\n$data");
    }
  }

  // 处理P3多页数据
  void _parserMultiPageData(String result, WalletType walletType) {
    Log.r("_parserP3MultiPageData= $result");

    try {
      var jsonObj = jsonDecode(result);
      int random = jsonObj['r']; // 随机数
      int page = jsonObj['c']; // 当前页
      int pageCount = jsonObj['p']; // 总页数
      ///手动从1 到pageCount 收集扫码数据
      if (page == 1) {
        // 如果是第一页，需要记录随机数
        firstRandom = random;
        String data = jsonObj['d'];

        if (currentData != data) {
          // 避免第一张重复扫描
          currentData = data;
          scanCount++;
          if (page != pageCount) {
            nextPage = page + 1; // 下一页

            double value = scanCount / pageCount;
            progress.value = value;
            percentage.value = '${(value * 100).toStringAsFixed(0)}%';
            Get.showToast(
                ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()}));
            percentageInfo.value =
                ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()});
            restart();
            return;
          } else {
            progress.value = 100;
            percentage.value = '100%';
            percentageInfo.value = '';
            pausePreview();
            _parserData(currentData, walletType);
          }
        } else {
          // 提示该分片已经扫描
          Get.showToast(
              ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()}));
          percentageInfo.value =
              ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()});
          restart();
        }
      } else if (page == nextPage) {
        if (firstRandom != random) {
          Get.showToast(ID.stringQRScanError.tr);
          restart();
          return;
        } else {
          currentData += jsonObj['d'];
          scanCount++;
          if (page != pageCount) {
            nextPage = page + 1; // 下一页
            double value = scanCount / pageCount;
            progress.value = value;
            percentage.value = '${(value * 100).toStringAsFixed(0)}%';
            Get.showToast(
                ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()}));
            percentageInfo.value =
                ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()});
            restart();
          } else {
            progress.value = 100;
            percentage.value = '100%';
            percentageInfo.value = '';
            pausePreview();
            _parserData(currentData, walletType);
          }
        }
      } else {
        // 提示该扫描第nextpage页
        Get.showToast(
            ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()}));

        percentageInfo.value =
            ID.pleaseScanPage.trParams({'scanPage': nextPage.toString()});
        restart();
      }
    } catch (e) {
      Get.showToast(ID.zxingError.tr);
      restart();
    }
  }

  // 处理拼接完Q r数据
  Future<void> _parserData(String result, WalletType walletType) async {
    Log.r("_parserData= $result");
    scanCount = 0;
    try {
      if (walletType == WalletType.pro1Pro2) {
        switch (mPro3QtType) {
          case QTType.balance:
            SelectHardWareWalletDialog(onPressed: (name) {
              _syncP2P2Balance(
                  result, name == 'Pro 1' ? WalletType.pro1 : WalletType.pro2);
            }).showBottomSheet();
            break;
          case QTType.broadcard:
            SelectHardWareWalletDialog(onPressed: (name) {
              _brodcast(
                  result, name == 'Pro 1' ? WalletType.pro1 : WalletType.pro2);
            }).showBottomSheet();
            break;
          default:
            Get.connectWallet.handleP2WalleConnectWallet(result);
            break;
        }
      } else {
        if (Pro3Wallet.isPro3NewType(mPro3QtType)) {
          _parserP3EosQrData(mPro3QtType, result);
          return;
        }

        switch (mPro3QtType) {
          case QTType.monitoringAddressV2:
            Get.connectWallet
                .handleP2PlusP3WalleConnectWallet(result, mPro3QtType);
          case QTType.monitoringAddressV3:
            Get.connectWallet
                .handleP2PlusP3WalleConnectWallet(result, mPro3QtType);
            break;
          case QTType.balance:
            _syncBalance(result, walletType);
            break;
          case QTType.broadcard:
            _brodcast(result, walletType);
            break;
          default:
            Get.showToast(ID.stringNoSupportedFeature.tr);
            Get.back();

            break;
        }
      }
    } catch (e) {
      Get.showToast(ID.zxingError.tr);
      restart();
    }
  }

  void _parserP3EosQrData(mQtType, result) {
    Log.r("_parserP3EosQrData= $result");
    switch (mQtType) {
      case QTType.eosSyncAccount:
        _syncEosAccounc(result);
        break;
      case QTType.eosMonitoringAccount:
        break;
      case QTType.eosQrSearch:
        _checkEosResource(result);
        break;
      case QTType.eosSend:
        _sendPro3Eos(result);
        break;
      case QTType.eosSignResult:
        _brodcastPro3Eos(result);
        break;
      case QTType.signResult:
        break;
      default:
        break;
    }
  }

  //查看EOS资源
  Future<void> _checkEosResource(String result) async {
    Log.r("_checkEosResource= $result");
    if (Get.isDialogOpen!) {
      return;
    }

    var jsonObject = json.decode(result);
    CoinType? coinType = EosChain.get;

    String? deviceId = jsonObject['sn'];
    String? walletId = jsonObject['ai'];
    Get.showLoadingDialog();
    bool isExitst =
        await Get.database.walletDao.existsWallet(deviceId!, walletId);

    if (!isExitst) {
      Get.back();
      Get.dismissLoadingDialog();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }
    WalletModel? checkedWalletModel =
        await Get.database.walletDao.getCheckedWallets();
    if (checkedWalletModel!.deviceId != deviceId) {
      Get.back();
      Get.dismissLoadingDialog();
      Get.showAlertDialog(
          title: ID.stringNotices.tr,
          content: ID.stirngSwitchHardwareWallet.tr);
      return;
    }

    Map<String, dynamic>? daMap = jsonObject['da'];
    String? fromAddress = daMap!['an'];
    WalletModel? walletModel = await Get.database.walletDao
        .findWalletsByWalletWalletOrDeviceId(walletId!, deviceId);

    if (walletModel == null) {
      Get.back();
      Get.dismissLoadingDialog();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }

    AddressModel? addressModel = await Get.database.addressDao.getAddressModel(
        walletId: walletId,
        deviceId: deviceId,
        chain: coinType.chain,
        address: fromAddress!);

    if (addressModel == null) {
      Get.showToast(ID.stringNoMonitorAddress.tr);
      Get.back();
      Get.dismissLoadingDialog();
      return;
    }

    CoinModel? coinModel =
        await Get.database.coinDao.getMainCoinModel(chain: coinType.chain);
    if (coinModel == null) {
      Get.showToast(ID.stringNotBoundInfo.tr);
      Get.back();
      Get.dismissLoadingDialog();
      return;
    }

    Get.back();
    Get.dismissLoadingDialog();
    Get.toNamed(AppRoutes.tokenAssetsPage, arguments: {
      GetArgumentsKey.addressModel: addressModel,
      GetArgumentsKey.coinModel: coinModel,
      GetArgumentsKey.walletModel: walletModel,
    });
  }

  ///同步EOS账户
  Future<void> _syncEosAccounc(String result) async {
    Log.r("_syncEosAccounc= $result");

    var jsonObject = json.decode(result);
    CoinType? coinType = EosChain.get;

    String? deviceId = jsonObject['sn'];
    String? walletId = jsonObject['ai'];
    String? eosPublickey = jsonObject['da'];

    CoinModel? coinModel = await Get.database.coinDao
        .getCoinModel(chain: coinType.chain, contract: '');

    WalletModel? walletModel = await Get.database.walletDao
        .findWalletsByWalletWalletOrDeviceId(walletId!, deviceId!);

    if (walletModel == null) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }

    AddressModel? addressModel = await Get.database.addressDao
        .getEosAddressModel(
            walletId: walletId,
            deviceId: deviceId,
            chain: coinType.chain,
            eosPublickey: eosPublickey!);

    if (addressModel == null) {
      Get.showToast(ID.stringNoMonitorAddress.tr);

      return;
    }

    Get.back();

    Get.toNamed(AppRoutes.eosExportAccountPage, arguments: {
      GetArgumentsKey.coinModel: coinModel,
      GetArgumentsKey.addressModel: addressModel,
      GetArgumentsKey.wallet: Pro3Wallet(),
    });
  }

  ///发送EOS
  Future<void> _sendPro3Eos(String result) async {
    Log.r("_sendPro3Eos= $result");

    var jsonObject = json.decode(result);
    CoinType? coinType = EosChain.get;

    String? deviceId = jsonObject['sn'];
    String? walletId = jsonObject['ai'];
    Map<String, dynamic>? daMap = jsonObject['da'];
    String? fromAddress = daMap!['an'];

    CoinModel? coinModel = await Get.database.coinDao
        .getCoinModel(chain: coinType.chain, contract: '');

    WalletModel? walletModel = await Get.database.walletDao
        .findWalletsByWalletWalletOrDeviceId(walletId!, deviceId!);

    if (walletModel == null) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }

    WalletModel? checkedWalletModel =
        await Get.database.walletDao.getCheckedWallets();
    if (checkedWalletModel!.deviceId != deviceId) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr,
          content: ID.stirngSwitchHardwareWallet.tr);
      return;
    }

    AddressModel? addressModel = await Get.database.addressDao.getAddressModel(
      walletId: walletId,
      deviceId: walletId,
      chain: coinType.chain,
      address: fromAddress!,
    );

    if (addressModel == null) {
      Get.showToast(ID.stringNoMonitorAddress.tr);

      return;
    }

    Get.back();
    Get.toSend(addressModel: addressModel, coinModel: coinModel!);
  }

  ///P1P2同步余额
  Future<void> _syncP2P2Balance(String result, WalletType walletType) async {
    if (walletType == WalletType.pro1) {
      String p1WalletId = '${Pro1Pro2Wallet.walletId}_p1';
      WalletModel? p1WalletModel =
          await Get.database.walletDao.findWalletsByWalletId(p1WalletId);
      if (p1WalletModel != null) {
        _syncBalance(result, WalletType.pro1);
      } else {
        Get.connectWallet.handleP1WalleConnectWallet(result);
      }
    } else {
      _syncBalance(result, WalletType.pro2);
    }
  }

  ///同步余额
  Future<void> _syncBalance(String result, WalletType walletType) async {
    Log.r("_syncBalance= $result");

    var jsonObject = json.decode(result);

    String? address = jsonObject['a'];

    String? coinId = jsonObject['t'];

    String? walletId = jsonObject.containsKey('i') ? jsonObject['i'] : '';

    String deviceId = jsonObject.containsKey('l') ? jsonObject['l'] : '';

    int? batchId = jsonObject.containsKey('k') ? jsonObject['k'] : 0;

    CoinType? coinType = CoinBase.getCoinTypeById(coinId!);
    if (coinType == null || coinType is UsdtMain) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotSupportedChain.tr);
      return;
    }
    bool isP1P2 =
        walletType == WalletType.pro1 || walletType == WalletType.pro2;
    if (isP1P2) {
      if (walletType == WalletType.pro1) {
        walletId = '${Pro1Pro2Wallet.walletId}_p1';
        batchId = Pro1Wallet.maxBatch;
        deviceId = '${Pro1Pro2Wallet.deviceId}_p1';
      } else if (walletType == WalletType.pro2) {
        walletId = '${Pro1Pro2Wallet.walletId}_p2';
        batchId = Pro2Wallet.maxBatch;
        deviceId = '${Pro1Pro2Wallet.deviceId}_p2';
      }

      AddressModel? addressModel =
          await Get.database.addressDao.getAddressModel(
        walletId: walletId!,
        deviceId: deviceId,
        chain: coinType.chain,
        address: address!,
      );

      ///如果是比特币现金 地址查不到，切换一下新旧地址格式在查询一次
      if (addressModel == null &&
          coinType.chain == BitcoinCashChain.get.chain) {
        String? converAddres =
            await Get.walletCore.convertBitcoinCashAddress(address);
        if (converAddres!.isEmpty) {
          Get.showToast(ID.stringNoMonitorAddress.tr);

          return;
        }
        addressModel = await Get.database.addressDao.getAddressModel(
          walletId: walletId,
          deviceId: deviceId,
          chain: coinType.chain,
          address: converAddres,
        );
      }

      if (addressModel == null) {
        if (walletType == WalletType.pro1) {
          Get.connectWallet.handleP1WalleConnectWallet(result);
        } else if (walletType == WalletType.pro2) {
          String content =
              '${ID.stringNoMonitorAddress.tr}\n ${ID.stringP2info.tr}';
          Get.back();
          Get.showAlertDialog(title: ID.stringNotices.tr, content: content);
        }
        return;
      }
      if (coinType is NemChain || coinType is RippleChain) {
        Get.back();
        Get.showAlertDialog(
            title: ID.stringNotices.tr, content: ID.stringNotSupportedChain.tr);
        return;
      }
    }

    String contractAddress = jsonObject.containsKey('d') ? jsonObject['d'] : "";

    Get.toSyncBalance(
      chain: coinType.chain,
      batchId: batchId!,
      deviceId: deviceId,
      address: address!,
      isFromScan: true,
      contractAddress: contractAddress,
      isP1P2: isP1P2,
      walletId: walletId!,
    );
  }

  ///广播
  Future<void> _brodcast(String result, WalletType walletType) async {
    Log.r("_brodcast= $result");
    if (Get.isDialogOpen!) {
      return;
    }
    var jsonObject = json.decode(result);
    String? coinId = jsonObject['t'];
    CoinType? coinType = CoinBase.getCoinTypeById(coinId!);
    if (coinType == null || coinType is UsdtMain) {
      Get.back();
      Get.showToast(ID.stringNotSupportedChain.tr);
      return;
    }

    if (coinType is ZcashChain) {
      Get.back();
      Get.showToast(ID.stringZecNotSupportedTips.tr);
      return;
    }
    if (walletType == WalletType.pro1Pro2) {
      if (coinType is NemChain || coinType is RippleChain) {
        Get.back();
        Get.showAlertDialog(
            title: ID.stringNotices.tr, content: ID.stringNotSupportedChain.tr);
        return;
      }
    }

    int? batchId = jsonObject['k'];
    String? deviceId = jsonObject['l'];
    String? walletId = jsonObject['i'];

    if (walletType == WalletType.pro1) {
      walletId = '${Pro1Pro2Wallet.walletId}_p1';
      batchId = Pro1Wallet.maxBatch;
      deviceId = '${Pro1Pro2Wallet.deviceId}_p1';
    } else if (walletType == WalletType.pro2) {
      walletId = '${Pro1Pro2Wallet.walletId}_p2';
      batchId = Pro2Wallet.maxBatch;
      deviceId = '${Pro1Pro2Wallet.deviceId}_p2';
    }

    Wallet wallet = Wallet.getWalletByBatch(batchId!);

    bool isExitst =
        await Get.database.walletDao.existsWallet(deviceId!, walletId);

    if (!isExitst) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }

    if (coinType is EthereumChain && wallet.isP1P2P3Wallet) {
      Get.back();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringEthNotSupportedTips.tr);
      return;
    }

    Get.showLoadingDialog();
    List<String> fromAddressList = [];
    List<String> toAddressList = [];

    String? fromAddress, toAddress, remark, amount;

    String rawData = jsonObject.containsKey('d') ? jsonObject['d'] : "";

    CoinModel? coinModel =
        await Get.database.coinDao.getMainCoinModel(chain: coinType.chain);

    if (coinType.isEthereumSeries || coinType.isBitcoinSeries) {
      if (jsonObject.containsKey('f')) {
        fromAddress = jsonObject['f'];
        fromAddressList.add(fromAddress!);
      }
    } else if (coinType is NemChain || coinType is RippleChain) {
      fromAddress = jsonObject['m'] ?? '';
      toAddress = jsonObject['f'] ?? '';
      remark = jsonObject['ca'] ?? '';

      if (jsonObject['a']!.isNotEmpty) {
        amount = DecimalUtils.stripTrailingZeros(jsonObject['a'] ?? '');
      }

      if (fromAddress!.isNotEmpty) {
        fromAddressList.add(fromAddress);
      }
      if (toAddress!.isNotEmpty) {
        toAddressList.add(toAddress);
      }
    }

    String? feeValue = jsonObject['fe'] ?? "";
    String? contract = jsonObject['ca'] ?? "";
    bool isChangeZero = jsonObject['z'] == 1;

    ParseResultModel? parseData;
    try {
      parseData = await coinType.parseTransactionData(rawData, coinType.chain);
    } catch (_) {}

    if (parseData != null) {
      toAddressList.clear();
      toAddressList.addAll(
          parseData.outputAddressList(isChangeZero: isChangeZero) ?? []);
      amount = parseData.amount(
        decimals: coinType.decimals,
        isChangeZero: isChangeZero,
      );
    }

    String? fee = '';
    if (coinType.isEthereumSeries) {
      fee = "${parseData?.feeValue(coinType.decimals)} ${coinType.symbol}";
    } else if (coinType.isBitcoinSeries) {
      fee = "$feeValue ${coinType.symbol}";
    } else {
      fee = "$feeValue ${coinType.symbol}";
    }
    TransferModel? txModel = TransferModel(
        chain: coinType.chain,
        type: TransferType.transfer,
        rawtx: rawData,
        wallet: wallet,
        walletId: walletId);

    txModel.fromAddress = fromAddress ?? "";
    txModel.toAddress = toAddress ?? "";
    txModel.contract = contract ?? "";
    txModel.amount = amount ?? "";
    txModel.coinSymbol = coinType.symbol;
    txModel.fee = feeValue ?? "";
    txModel.remark = remark ?? "";
    Get.back();

    TransactionConfirmationDialog(
        coinType: coinType,
        coinModel: coinModel,
        fee: fee,
        amount: amount,
        fromAddressList: fromAddressList,
        toAddressList: toAddressList,
        rawData: rawData,
        remark: remark ?? "",
        onPressed: () {
          Get.back();
          Get.toNamed(AppRoutes.broadcastPage, arguments: txModel);
        }).showBottomSheet();

    Get.dismissLoadingDialog();
  }

  ///广播
  Future<void> _brodcastPro3Eos(String result) async {
    Log.r("_brodcastPro3Eos= $result");
    if (Get.isDialogOpen!) {
      return;
    }

    var jsonObject = json.decode(result);

    String? deviceId = jsonObject['sn'];
    String? walletId = jsonObject['ai'];

    Get.showLoadingDialog();
    bool isExitst =
        await Get.database.walletDao.existsWallet(deviceId!, walletId);

    if (!isExitst) {
      Get.back();

      Get.dismissLoadingDialog();
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringNotBoundInfo.tr);
      return;
    }
    WalletModel? checkedWalletModel =
        await Get.database.walletDao.getCheckedWallets();
    if (checkedWalletModel!.deviceId != deviceId) {
      Get.back();

      Get.dismissLoadingDialog();
      Get.showAlertDialog(
          title: ID.stringNotices.tr,
          content: ID.stirngSwitchHardwareWallet.tr);
      return;
    }

    Map<String, dynamic>? daMap = jsonObject['da'];

    String? signaturess = daMap!['sr'];
    if (tsModel == null || tsModel!.transaction!.isEmpty) {
      Get.back();
      Get.showToast(ID.stirngBroadcastNoData.tr);
      Get.dismissLoadingDialog();
      return;
    }

    String transaction = tsModel!.transaction!;

    Map<String, dynamic> transactionData = jsonDecode(transaction);
    if (transactionData["signatures"].isNotEmpty) {
      transactionData["signatures"][0] = signaturess;
    }
    tsModel!.txData = jsonEncode(transactionData);
    Log.r("txdata=${tsModel!.txData}");

    Get.back();
    Get.toNamed(AppRoutes.broadcastPage, arguments: tsModel);

    Get.dismissLoadingDialog();
  }
}
