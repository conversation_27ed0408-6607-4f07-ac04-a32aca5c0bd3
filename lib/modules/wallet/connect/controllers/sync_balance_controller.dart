/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-01-13 10:48:44
 */
import 'package:carousel_slider_plus/carousel_slider_plus.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/modules/wallet/connect/controllers/sync_balance_request_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/wallet.dart';

class SyncBalanceController extends BaseController<BlockChainService> {
  CoinModel? coinModel;
  AddressModel? addressModel;
  TokenModel? tokenModel;
  late WalletModel walletModel;
  late CoinType coinType;
  late Wallet wallet;

  late TransferModel tsModel;

  RxInt selectedPage = 0.obs;
  late RxString balance;

  List<Widget> qrImageList = [];
  final CarouselSliderController carouselCtr = CarouselSliderController();

  @override
  void onInit() {
    Map arguments = Get.arguments;
    coinModel = arguments[GetArgumentsKey.coinModel];
    addressModel = arguments[GetArgumentsKey.addressModel];
    tokenModel = arguments[GetArgumentsKey.tokenModel];
    walletModel = arguments[GetArgumentsKey.walletModel];
    coinType = CoinBase.getCoinTypeByChain(coinModel!.chain!)!;
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);

    tsModel = TransferModel(
      chain: coinType.chain,
      type: TransferType.syncBalance,
      wallet: wallet,
      fromAddress: addressModel?.address,
      coinDecimal: coinModel!.chainDecimal,
      contract: tokenModel?.contract,
      isToken: coinModel!.isToken!,
    );
    tsModel.mainBalance = addressModel?.balance;
    tsModel.tokenBalance = tokenModel?.balance;

    final raw = _getBalance();
    balance = (raw.isEmpty || double.tryParse(raw) == null ? "0" : raw)
        .decimal(scale: 6, roundMode: RoundMode.down)
        .obs;
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();

    loadData();

    AppController.setBrightness();

    /// 监听二维码容量更新
    ever(AppController.qrModel, (callback) {
      setupQRWidget();
      update([GetKey.syncBalance]);
    });
  }

  String getAddress() {
    if (tokenModel != null) {
      return tokenModel!.contract ?? '';
    }
    return addressModel?.address ?? '';
  }

  String _getBalance() {
    if (tokenModel != null) {
      return tokenModel!.balance ?? '';
    }
    return addressModel?.balance ?? '';
  }

  @override
  void loadData() => syncRequest();

  Future<void> setupQRWidget() async {
    String qrData = '';
    qrData = await wallet.getSyncBalanceData(
      coinType: coinType,
      tsModel: tsModel,
    );

    if (Get.isEmptyString(qrData)) {
      showEmpty();
      return;
    }

    List<String> qrList = QRCodeGenerator.qrCode(
      codeType: QRCodeType.syncBalance,
      qrSize: AppController.qrModel.value.qrSize,
      qrData: qrData,
      tsModel: tsModel,
    );

    qrImageList = qrList.map((e) => QRWidget(data: e)).toList();

    showSuccess();
  }

  void xrpActivityDialog() {
    Get.showBottomSheet(
      title: ID.stringTips.tr,
      bodyWidget: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16),
          vertical: Get.setPaddingSize(16),
        ),
        child: Text(
          ID.xrpNoActivityTips
              .trParams({"value": RippleChain.get.reserveAmount}),
          style: TextStyle(
            color: Get.theme.textPrimary,
            fontSize: Get.setFontSize(16),
            fontFamily: Get.setFontFamily(),
          ),
        ),
      ),
      bottomWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: ButtonWidget(
          text: ID.stringConfirm.tr,
          onPressed: () => Get.back(),
        ),
      ),
    );
  }

  @override
  void onClose() {
    super.onClose();
    AppController.resetBrightness();
  }
}

class BalanceBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SyncBalanceController());
  }
}
