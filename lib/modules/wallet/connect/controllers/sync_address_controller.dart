/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-22 15:54:45
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';

extension SynAddressController on ScanController {
  Future<void> synAddress(String? result) async {
    if (scanAction == ScanAction.synAddress) {
      if (Get.isEmptyString(result)) {
        _toResultPage(result);
        return;
      }

      try {
        var jsonObject = json.decode(result!);
        String? walletId = jsonObject['wallet_id'];
        String? deviceId = jsonObject['device_id'];

        WalletModel? walletModel =
            await Get.database.walletDao.getCheckedWallets();
        if (walletModel?.walletId != walletId ||
            walletModel?.deviceId != deviceId) {
          _toResultPage(result);
          return;
        }

        Get.back(result: result);
      } catch (_) {
        _toResultPage(result);
      }
    } else {
      _toResultPage(result);
    }
  }

  void _toResultPage(String? result) {
    Get.offAndToNamed(AppRoutes.scanResultPage,
        arguments: {GetArgumentsKey.scanResult: result});
  }
}
