/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-25 00:38:09
 * @LastEditTime: 2024-04-18 13:57:54
 */
import 'address_info.dart';

class WalletUploadModel {
  List<AddressInfo>? addressInfo;
  String? batchCode;
  String? deviceCode;
  int? hotWalletType;
  int? isRoot;
  String? phoneId;
  int? platform;
  int? seedType;
  int? time;
  int? uid;
  String? uuid;
  String? walletAccount;
  String? walletId;
  int? vipLevel;

  WalletUploadModel({
    this.addressInfo,
    this.batchCode,
    this.deviceCode,
    this.hotWalletType,
    this.isRoot,
    this.phoneId,
    this.platform,
    this.seedType,
    this.time,
    this.uid,
    this.uuid,
    this.walletAccount,
    this.walletId,
    this.vipLevel,
  });

  factory WalletUploadModel.fromJson(Map<String, dynamic> json) {
    return WalletUploadModel(
      addressInfo: (json['addressInfo'] as List<dynamic>?)
          ?.map((e) => AddressInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      batchCode: json['batchCode'] as String?,
      deviceCode: json['deviceCode'] as String?,
      hotWalletType: json['hotWalletType'] as int?,
      isRoot: json['isRoot'] as int?,
      phoneId: json['phoneId'] as String?,
      platform: json['platform'] as int?,
      seedType: json['seedType'] as int?,
      time: json['time'] as int?,
      uid: json['uid'] as int?,
      uuid: json['uuid'] as String?,
      walletAccount: json['walletAccount'] as String?,
      walletId: json['walletId'] as String?,
      vipLevel: json['vipLevel'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
        'addressInfo': addressInfo?.map((e) => e.toJson()).toList(),
        'batchCode': batchCode,
        'deviceCode': deviceCode,
        'hotWalletType': hotWalletType,
        'isRoot': isRoot,
        'phoneId': phoneId,
        'platform': platform,
        'seedType': seedType,
        'time': time,
        'uid': uid,
        'uuid': uuid,
        'walletAccount': walletAccount,
        'walletId': walletId,
        'vipLevel': vipLevel,
      };
}
