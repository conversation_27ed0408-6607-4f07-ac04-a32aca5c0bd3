/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-25 00:38:09
 * @LastEditTime: 2024-02-25 12:53:24
 */
class AddressInfo {
  String? coinAddress;
  String? coinCode;
  String? remark;
  String? slip44;

  AddressInfo({this.coinAddress, this.coinCode, this.remark, this.slip44});

  factory AddressInfo.fromJson(Map<String, dynamic> json) => AddressInfo(
        coinAddress: json['coinAddress'] as String?,
        coinCode: json['coinCode'] as String?,
        remark: json['remark'] as String?,
        slip44: json['slip44'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'coinAddress': coinAddress,
        'coinCode': coinCode,
        'remark': remark,
        'slip44': slip44,
      };
}
