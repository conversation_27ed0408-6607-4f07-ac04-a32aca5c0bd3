class AddressDeleteModel {
  String? address;
  String? coinType;
  String? phoneId;

  AddressDeleteModel({this.address, this.coinType, this.phoneId});

  factory AddressDeleteModel.fromJson(Map<String, dynamic> json) {
    return AddressDeleteModel(
      address: json['address'] as String?,
      coinType: json['coinType'] as String?,
      phoneId: json['phoneId'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'address': address,
        'coinType': coinType,
        'phoneId': phoneId,
      };
}
