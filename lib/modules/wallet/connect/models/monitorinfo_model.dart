class MonitorinfoModel {
  String? addressNumber;
  int? batch;
  String? coinNumber;
  String? devicesCode;
  String? devicesModel;
  String? phoneInfo;
  String? sign;
  int? time;
  String? uid;
  String? uuid;
  String? walletAccount;
  String? walletAccountId;

  MonitorinfoModel({
    this.addressNumber,
    this.batch,
    this.coinNumber,
    this.devicesCode,
    this.devicesModel,
    this.phoneInfo,
    this.sign,
    this.time,
    this.uid,
    this.uuid,
    this.walletAccount,
    this.walletAccountId,
  });

  factory MonitorinfoModel.fromJson(Map<String, dynamic> json) {
    return MonitorinfoModel(
      addressNumber: json['addressNumber'] as String?,
      batch: json['batch'] as int?,
      coinNumber: json['coinNumber'] as String?,
      devicesCode: json['devicesCode'] as String?,
      devicesModel: json['devicesModel'] as String?,
      phoneInfo: json['phoneInfo'] as String?,
      sign: json['sign'] as String?,
      time: json['time'] as int?,
      uid: json['uid'] as String?,
      uuid: json['uuid'] as String?,
      walletAccount: json['walletAccount'] as String?,
      walletAccountId: json['walletAccountId'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'addressNumber': addressNumber,
        'batch': batch,
        'coinNumber': coinNumber,
        'devicesCode': devicesCode,
        'devicesModel': devicesModel,
        'phoneInfo': phoneInfo,
        'sign': sign,
        'time': time,
        'uid': uid,
        'uuid': uuid,
        'walletAccount': walletAccount,
        'walletAccountId': walletAccountId,
      };
}
