import 'dart:convert';
import 'dart:typed_data';

import 'package:bs58/bs58.dart';
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/service/wallet_database_service.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/response/base_response.dart';
import 'package:coinbag/modules/dapp/models/dapp_address_model.dart';
import 'package:coinbag/modules/wallet/connect/models/address/address_delete_model.dart';
import 'package:coinbag/modules/wallet/connect/models/monitorinfo_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:coinbag/utils/deveice_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:drift/drift.dart' as drift;
import 'package:flutter/widgets.dart';
import 'package:hex/hex.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/bitcoin/ltc.dart';
import 'package:wallet_core/chain/bitcoin/testnet/sbtc.dart';
import 'package:wallet_core/chain/bitcoin/testnet/tbtc.dart';
import 'package:wallet_core/chain/bitcoin/usdt.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/wallet/cold/constant/common_constant.dart';
import 'package:wallet_core/wallet/cold/pro1_pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro1_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_plus_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro3_plus_wallet.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class ConnectWalletController extends BaseController {
  late WalletDatabaseService walletRepository;
  late ApiService apiService;

  ///Touch,P3+ Ultra系列连接钱包保存地址
  Future<void> handleConnectWallet(String result, {Function? callBack}) async {
    Log.r(result);
    Get.showLoadingDialog(loadingText: ID.binding.tr);
    if (!_validateBindData(result)) {
      toScanResult(result);
      return;
    }

    late Wallet wallet;
    walletRepository = WalletDatabaseService();
    apiService = Get.find<ApiService>();
    try {
      var jsonObject = json.decode(result);
      int? batchId = jsonObject['batch'];
      int? vipLevel = jsonObject['vip_level'] ?? 0;
      String? walletId = jsonObject['wallet_id'];
      bool? isPassphraseWallet = jsonObject['is_passphrase'] == 1;
      wallet = Wallet.getWalletByBatch(batchId!);
      List<MonitorTableCompanion> monitorModelList = <MonitorTableCompanion>[];
      List<AddressTableCompanion> addressModelList = <AddressTableCompanion>[];
      <AddressMappingTableCompanion>[];
      List<AddressTableCompanion> newAddressModelList =
          <AddressTableCompanion>[];

      String ethereumFristPubData = "";
      String ethereumFristAddress = "";
      String ethereumFristPublicKey = "";
      String ethereumFristAddressPath = "";

      ///冷钱包表
      WalletTableCompanion walletModel = WalletTableCompanion(
        deviceId: drift.Value(jsonObject['device_id']),
        walletType: const drift.Value(0),
        monitorTime: drift.Value(DateHeleper.getCurrentTimeFormatted()),
        batchId: drift.Value(batchId),
        appVersion: drift.Value(jsonObject['app_version'] ?? ""),
        chipVersion: drift.Value(jsonObject['se_version'] ?? ""),
        firmwareVersion: drift.Value(jsonObject['firmware_version'] ?? ""),
        chain: const drift.Value(Chain.all),
        nftChain: const drift.Value(Chain.eth),
        walletId: drift.Value(walletId!),
        walletName: drift.Value(jsonObject['wallet_name'] ?? ""),
        deviceType: drift.Value(wallet.name),
        isPassphraseWallet: drift.Value(isPassphraseWallet),
        checked: const drift.Value(false),
        vipLevel: drift.Value(vipLevel!),
      );

      var pubs = jsonObject['pubs'];
      var jsonArray = pubs != null ? List.from(pubs) : null;

      if (jsonArray == null) {
        toScanResult(result);
        return;
      }
      bool isOnlyOneChain = jsonArray.length == 1;
      bool isNotSupprotChain = false;

      for (var item in jsonArray) {
        String? pubData = item['pub_data'];
        String? path = item['path'];
        bool? isXPub = item['is_xpub'] == 1;
        int? addressNum = item['addr_num'];

        late int slip44Id;

        if (wallet.isUltraSeriesOrTouchWallet) {
          slip44Id = item['bip44Index'];
        } else if (wallet is Pro3PlusWallet) {
          slip44Id = CoinBase.getBip44ByPath(item['path']);
        } else {
          slip44Id = CoinBase.getBip44ByPath(item['path']);
        }

        CoinType? coinType = CoinBase.getCoinTypeBySlip44Id(slip44Id);
        if (coinType == null) {
          isNotSupprotChain = true;
          continue;
        }
        isNotSupprotChain = false;
        int bipProtoco = CoinBase.getBipProtocol(path!);
        if (coinType is EosChain) {
          String? eosPublicKey = await Get.walletCore.getPublicKey(
            chain: coinType.chain,
            pubData: pubData ?? '',
            index: 0,
            isXpub: isXPub,
            path: path,
          );
          monitorModelList.add(MonitorTableCompanion.insert(
              batchId: drift.Value(batchId),
              slip44Id: drift.Value(slip44Id),
              chainCode: drift.Value(coinType.id),
              chain: drift.Value(coinType.chain),
              publickey: drift.Value(eosPublicKey!),
              path: drift.Value(path),
              isXPub: drift.Value(isXPub),
              segwitType: drift.Value(bipProtoco),
              addressNum: drift.Value(addressNum!),
              keyTypekeyType: const drift.Value(0),
              walletId: drift.Value(walletId)));
        } else {
          if (Get.isEmptyString(pubData)) {
            continue;
          }
          for (int j = 0; j < addressNum!; j++) {
            String? address = "";
            String? publicKey = "";
            String addressLabel = "";
            int addressIndex = j + 1;
            int sortedId = 0;
            if (!coinType.isBitCoinNetWork || coinType is! LitecoinChain) {
              addressLabel = CoinBase.getDefaultLabel(coinType, j + 1);
              sortedId = j;
            }

            String addressPath = '';
            if (coinType is SolanaChain) {
              Uint8List hash = HEX.decode(pubData!) as Uint8List;
              address = base58.encode(hash);
              addressPath = path;
            } else {
              addressPath = "$path/$j";
              address = await Get.walletCore.createAddress(
                chain: coinType.chain,
                pubData: pubData!,
                isXpub: isXPub,
                path: path,
                index: j,
              );

              publicKey = await Get.walletCore.getPublicKey(
                chain: coinType.chain,
                pubData: pubData,
                index: j,
                isXpub: isXPub,
                path: path,
              );
            }

            if (coinType is EthereumChain && j == 0 && wallet.isUltraSeries) {
              ethereumFristAddress = address!;
              ethereumFristPublicKey = publicKey!;
              ethereumFristAddressPath = path;
              ethereumFristPubData = pubData;
            } else if (coinType is NemChain) {
              addressPath = "m/44'/43'/0'";
            }
            addressModelList.add(AddressTableCompanion.insert(
              walletId: drift.Value(walletId),
              chainCode: drift.Value(coinType.id),
              chain: drift.Value(coinType.chain),
              address: drift.Value(address!),
              addressIndex: drift.Value(addressIndex),
              isSelected: const drift.Value(false),
              slip44Id: drift.Value(slip44Id),
              publickey: drift.Value(publicKey!),
              xpubData: drift.Value(pubData),
              path: drift.Value(addressPath),
              isUploaded: const drift.Value(false),
              sortedId: drift.Value(sortedId),
              addressLabel: drift.Value(addressLabel),
              walletType: const drift.Value(1),
              segwitType: drift.Value(bipProtoco),
            ));
          }

          monitorModelList.add(MonitorTableCompanion.insert(
              batchId: drift.Value(batchId),
              slip44Id: drift.Value(slip44Id),
              chainCode: drift.Value(coinType.id),
              chain: drift.Value(coinType.chain),
              publickey: drift.Value(pubData!),
              path: drift.Value(path),
              isXPub: drift.Value(isXPub),
              segwitType: drift.Value(bipProtoco),
              addressNum: drift.Value(addressNum),
              keyTypekeyType: const drift.Value(0),
              walletId: drift.Value(walletId)));
        }
      }

      int btcNum = 0;
      int tbtcNum = 0;
      int sbtcNum = 0;
      int ltcNum = 0;

      for (int i = 0; i < addressModelList.length; i++) {
        AddressTableCompanion addrssModel = addressModelList[i];
        int sortedId;
        String addressLabel;

        switch (addrssModel.chain.value) {
          case Chain.btc:
            btcNum++;
            sortedId = btcNum;
            addressLabel = CoinBase.getDefaultLabel(BitcoinChain.get, btcNum);
            break;

          case Chain.tbtc:
            tbtcNum++;
            sortedId = tbtcNum;
            addressLabel =
                CoinBase.getDefaultLabel(BitcoinTestnetChain.get, tbtcNum);
            break;

          case Chain.sbtc:
            sbtcNum++;
            sortedId = sbtcNum;
            addressLabel =
                CoinBase.getDefaultLabel(BitcoinSignetChain.get, sbtcNum);
            break;

          case Chain.ltc:
            ltcNum++;
            sortedId = ltcNum;
            addressLabel = CoinBase.getDefaultLabel(LitecoinChain.get, ltcNum);
            break;

          default:
            newAddressModelList.add(addrssModel);
            continue; // Skip the current iteration and continue with the next
        }

        // Add the modified address model to the newAddressModelList
        newAddressModelList.add(addrssModel.copyWith(
          sortedId: drift.Value(sortedId),
          addressLabel: drift.Value(addressLabel),
        ));
      }

      ///Ultra系列新增EVM链
      if (wallet.isUltraSeries) {
        List<CoinType> ethereumNewChainsList =
            CoinBase.getEthereumNewChains(wallet.type);
        for (int i = 0; i < ethereumNewChainsList.length; i++) {
          CoinType coinType = ethereumNewChainsList[i];

          monitorModelList.add(MonitorTableCompanion.insert(
              batchId: drift.Value(batchId),
              slip44Id: drift.Value(coinType.bip44Id),
              chain: drift.Value(coinType.chain),
              chainCode: drift.Value(coinType.id),
              publickey: drift.Value(ethereumFristPubData),
              path: drift.Value(ethereumFristAddressPath),
              isXPub: const drift.Value(false),
              segwitType: drift.Value(
                  CoinBase.getBipProtocol(ethereumFristAddressPath)),
              addressNum: const drift.Value(0),
              keyTypekeyType: const drift.Value(0),
              walletId: drift.Value(walletId)));

          newAddressModelList.add(AddressTableCompanion.insert(
            walletId: drift.Value(walletId),
            chain: drift.Value(coinType.chain),
            chainCode: drift.Value(coinType.id),
            address: drift.Value(ethereumFristAddress),
            addressIndex: const drift.Value(1),
            isSelected: const drift.Value(false),
            slip44Id: drift.Value(coinType.bip44Id),
            publickey: drift.Value(ethereumFristPublicKey),
            xpubData: drift.Value(ethereumFristPubData),
            path: drift.Value("$ethereumFristAddressPath/0"),
            isUploaded: const drift.Value(false),
            sortedId: const drift.Value(0),
            addressLabel: drift.Value(CoinBase.getDefaultLabel(coinType, 1)),
            walletType: const drift.Value(1),
            segwitType:
                drift.Value(CoinBase.getBipProtocol(ethereumFristAddressPath)),
          ));
        }
        walletModel = insertDappAddressList(ethereumFristAddress, walletModel);
      }

      if (isNotSupprotChain && isOnlyOneChain) {
        _bindFailed(bindFailedTextStr: ID.stringNotSupportedChain.tr);
        return;
      }

      checkAndInsertWallet(
          wallet, walletModel, monitorModelList, newAddressModelList);
    } catch (e) {
      Log.e(e.toString());
      _bindFailed();
    }
  }

  ///Pro1连接钱包保存地址
  void handleP1WalleConnectWallet(String result) {
    try {
      Get.showLoadingDialog(loadingText: ID.binding.tr);

      late Wallet wallet;
      walletRepository = WalletDatabaseService();
      apiService = Get.find<ApiService>();

      int? batchId = Pro1Wallet.maxBatch;

      wallet = Pro1Wallet();

      String walletId = '${Pro1Pro2Wallet.walletId}_p1';

      wallet = Wallet.getWalletByBatch(batchId);
      List<MonitorTableCompanion> monitorModelList = <MonitorTableCompanion>[];
      List<AddressTableCompanion> addressModelList = <AddressTableCompanion>[];

      // ///冷钱包表
      WalletTableCompanion walletModel = WalletTableCompanion(
        deviceId: const drift.Value('${Pro1Pro2Wallet.deviceId}_p1'),
        walletType: const drift.Value(0),
        monitorTime: drift.Value(DateHeleper.getCurrentTimeFormatted()),
        batchId: drift.Value(batchId),
        chain: const drift.Value(Chain.all),
        nftChain: const drift.Value(Chain.eth),
        walletId: drift.Value(walletId),
        walletName: drift.Value(wallet.name),
        deviceType: drift.Value(wallet.name),
        isPassphraseWallet: const drift.Value(true),
        checked: const drift.Value(false),
        vipLevel: const drift.Value(0),
      );

      var jsonObject = jsonDecode(result);
      if (jsonObject.isEmpty) {
        toScanResult(result);
        return;
      }

      String address = jsonObject['a'];

      String? coinId = jsonObject['t'];

      CoinType? coinType = CoinBase.getCoinTypeById(coinId!);
      if (coinType == null) {
        Get.dismissLoadingDialog();
        Get.back();
        Get.showAlertDialog(
            title: ID.stringNotices.tr, content: ID.stringNotSupportedChain.tr);
        return;
      }

      int addressIndex = 0;
      String addressLabel =
          CoinBase.getDefaultLabel(coinType, addressIndex + 1);
      int sortedId = addressIndex;
      monitorModelList.add(MonitorTableCompanion.insert(
          batchId: drift.Value(batchId),
          chainCode: drift.Value(coinId),
          chain: drift.Value(coinType.chain),
          addressNum: const drift.Value(1),
          keyTypekeyType: const drift.Value(0),
          segwitType: const drift.Value(44),
          walletId: drift.Value(walletId)));

      addressModelList.add(AddressTableCompanion.insert(
        walletId: drift.Value(walletId),
        chainCode: drift.Value(coinType.id),
        chain: drift.Value(coinType.chain),
        address: drift.Value(address),
        addressIndex: drift.Value(addressIndex),
        isSelected: const drift.Value(false),
        slip44Id: drift.Value(coinType.bip44Id),
        isUploaded: const drift.Value(false),
        sortedId: drift.Value(sortedId),
        segwitType: const drift.Value(CoinAddressProtocol.legacy),
        addressLabel: drift.Value(addressLabel),
        walletType: const drift.Value(1),
      ));

      checkAndInsertWallet(
          wallet, walletModel, monitorModelList, addressModelList);
    } catch (e) {
      Log.e(e.toString());
      _bindFailed();
    }
  }

  ///明确已经知道是Pro2连接钱包保存地址
  void handleP2WalleConnectWallet(String result) {
    try {
      Get.showLoadingDialog(loadingText: ID.binding.tr);

      late Wallet wallet;
      walletRepository = WalletDatabaseService();
      apiService = Get.find<ApiService>();

      int? batchId = Pro2Wallet.maxBatch;
      wallet = Pro2Wallet();

      String walletId = '${Pro1Pro2Wallet.walletId}_p2';

      wallet = Wallet.getWalletByBatch(batchId);
      List<MonitorTableCompanion> monitorModelList = <MonitorTableCompanion>[];
      List<AddressTableCompanion> addressModelList = <AddressTableCompanion>[];

      // ///冷钱包表
      WalletTableCompanion walletModel = WalletTableCompanion(
        deviceId: const drift.Value('${Pro1Pro2Wallet.deviceId}_p2'),
        walletType: const drift.Value(0),
        monitorTime: drift.Value(DateHeleper.getCurrentTimeFormatted()),
        batchId: drift.Value(batchId),
        chain: const drift.Value(Chain.all),
        nftChain: const drift.Value(Chain.eth),
        walletId: drift.Value(walletId),
        walletName: drift.Value(wallet.name),
        deviceType: drift.Value(wallet.name),
        isPassphraseWallet: const drift.Value(true),
        checked: const drift.Value(false),
        vipLevel: const drift.Value(0),
      );

      // 将字符串解析为 Dart 对象（List<Map<String, dynamic>>）
      List<dynamic> jsonArray = jsonDecode(result);
      if (jsonArray.isEmpty) {
        toScanResult(result);
        return;
      }

      for (var item in jsonArray) {
        String address = item['address'];

        String? coinId = item['id'];

        if (UsdtMain.get.id == coinId) {
          continue; //P3生成钱包的默认监控二维码有问题,直接跳过
        }

        int addressNum = item['num'] ?? 1;
        CoinType? coinType = CoinBase.getCoinTypeById(coinId!);
        if (coinType == null) {
          if (jsonArray.length == 1) {
            Get.dismissLoadingDialog();
            Get.back();
            Get.showAlertDialog(
                title: ID.stringNotices.tr,
                content: ID.stringNotSupportedChain.tr);
            break;
          } else {
            continue;
          }
        }

        int addressIndex = 0;
        String addressLabel =
            CoinBase.getDefaultLabel(coinType, addressIndex + 1);
        int sortedId = addressIndex;
        monitorModelList.add(MonitorTableCompanion.insert(
            batchId: drift.Value(batchId),
            chainCode: drift.Value(coinId),
            chain: drift.Value(coinType.chain),
            addressNum: drift.Value(addressNum),
            keyTypekeyType: const drift.Value(0),
            segwitType: const drift.Value(44),
            walletId: drift.Value(walletId)));

        addressModelList.add(AddressTableCompanion.insert(
          walletId: drift.Value(walletId),
          chainCode: drift.Value(coinType.id),
          chain: drift.Value(coinType.chain),
          address: drift.Value(address),
          addressIndex: drift.Value(addressIndex),
          segwitType: const drift.Value(CoinAddressProtocol.legacy),
          isSelected: const drift.Value(false),
          slip44Id: drift.Value(coinType.bip44Id),
          isUploaded: const drift.Value(false),
          sortedId: drift.Value(sortedId),
          addressLabel: drift.Value(addressLabel),
          walletType: const drift.Value(1),
        ));
      }

      checkAndInsertWallet(
          wallet, walletModel, monitorModelList, addressModelList);
    } catch (e) {
      Log.e(e.toString());
      _bindFailed();
    }
  }

  ///P2Plus Pro3系列连接钱包保存地址
  Future<void> handleP2PlusP3WalleConnectWallet(
    String result,
    int qtType,
  ) async {
    Log.r("handleOldConnectWallet= $result");

    try {
      Get.showLoadingDialog(loadingText: ID.binding.tr);

      late Wallet wallet;
      walletRepository = WalletDatabaseService();
      apiService = Get.find<ApiService>();

      var jsonObject = json.decode(result);
      int? batchId = jsonObject['k'];
      String? walletId = jsonObject['i'];
      bool? isPassphraseWallet = true;

      if (batchId! < 3 && qtType == 4) {
        _bindFailed();
        return;
      }
      wallet = Wallet.getWalletByBatch(batchId);
      List<MonitorTableCompanion> monitorModelList = <MonitorTableCompanion>[];
      List<AddressTableCompanion> addressModelList = <AddressTableCompanion>[];

      ///冷钱包表
      WalletTableCompanion walletModel = WalletTableCompanion(
        deviceId: drift.Value(jsonObject['l']),
        walletType: const drift.Value(0),
        monitorTime: drift.Value(DateHeleper.getCurrentTimeFormatted()),
        batchId: drift.Value(batchId),
        appVersion: drift.Value(jsonObject['vn'] ?? ""),
        chain: const drift.Value(Chain.all),
        nftChain: const drift.Value(Chain.eth),
        walletId: drift.Value(walletId!),
        walletName: drift.Value(jsonObject['j'] ?? ""),
        deviceType: drift.Value(wallet.name),
        isPassphraseWallet: drift.Value(isPassphraseWallet),
        checked: const drift.Value(false),
        vipLevel: const drift.Value(0),
      );
      String? publickey;

      if (qtType == QTType.monitoringAddressV2) {
        // 从 jsonObject 中获取字符串 'd'

        String pubs = jsonObject['d'];

        // 将字符串解析为 Dart 对象（List<Map<String, dynamic>>）
        List<dynamic> jsonArray = jsonDecode(pubs);
        if (jsonArray.isEmpty) {
          toScanResult(result);

          return;
        }

        for (var item in jsonArray) {
          publickey = item['d'];

          String? coinId = item['t'];

          if (UsdtMain.get.id == coinId) {
            continue; //P3生成钱包的默认监控二维码有问题,直接跳过
          }

          int addressNum = item['num'] ?? 1;
          CoinType? coinType = CoinBase.getCoinTypeById(coinId!);
          if (coinType == null) {
            if (jsonArray.length == 1) {
              Get.dismissLoadingDialog();
              Get.back();
              Get.showAlertDialog(
                  title: ID.stringNotices.tr,
                  content: ID.stringNotSupportedChain.tr);
              break;
            } else {
              continue;
            }
          }

          if (coinType is EosChain) {
            monitorModelList.add(MonitorTableCompanion.insert(
                batchId: drift.Value(batchId),
                slip44Id: drift.Value(EosChain.get.bip44Id),
                chainCode: drift.Value(coinId),
                chain: drift.Value(coinType.chain),
                publickey: drift.Value(publickey),
                segwitType: const drift.Value(44),
                path: drift.Value(
                    CoinBase.getBip44PathByIndex(coinType.bip44Id, 0)),
                keyTypekeyType: const drift.Value(0),
                walletId: drift.Value(walletId)));
          } else {
            monitorModelList.add(MonitorTableCompanion.insert(
                batchId: drift.Value(batchId),
                chainCode: drift.Value(coinId),
                chain: drift.Value(coinType.chain),
                publickey: drift.Value(publickey),
                addressNum: drift.Value(addressNum),
                keyTypekeyType: const drift.Value(0),
                segwitType: const drift.Value(44),
                walletId: drift.Value(walletId)));

            if (coinType.isBitcoinSeries) {
              String chainBase58 = publickey!.substring(
                  0,
                  publickey.lastIndexOf("0") < 0
                      ? 0
                      : publickey.lastIndexOf("0"));
              String publicKeyBase58 = publickey.substring(
                  publickey.lastIndexOf("0") + 1, publickey.length);

              for (int j = 0; j < addressNum; j++) {
                int addressIndex = j + 1;
                String addressLabel = CoinBase.getDefaultLabel(coinType, j + 1);
                int sortedId = j;

                String? address = await Get.walletCore
                    .createAddressOrPublicKeyByBase58(
                        chain: coinType.chain,
                        chainBase58: chainBase58,
                        publicKeyBase58: publicKeyBase58,
                        addressIndex: j,
                        isAddress: true);

                String? addressPublicKey = await Get.walletCore
                    .createAddressOrPublicKeyByBase58(
                        chain: coinType.chain,
                        chainBase58: chainBase58,
                        publicKeyBase58: publicKeyBase58,
                        addressIndex: j,
                        isAddress: false);

                addressModelList.add(AddressTableCompanion.insert(
                  walletId: drift.Value(walletId),
                  chainCode: drift.Value(coinType.id),
                  chain: drift.Value(coinType.chain),
                  address: drift.Value(address!),
                  addressIndex: drift.Value(addressIndex),
                  isSelected: const drift.Value(false),
                  slip44Id: drift.Value(coinType.bip44Id),
                  publickey: drift.Value(addressPublicKey!),
                  segwitType: const drift.Value(CoinAddressProtocol.legacy),
                  path: drift.Value(
                      CoinBase.getBip44PathByIndex(coinType.bip44Id, j)),
                  isUploaded: const drift.Value(false),
                  sortedId: drift.Value(sortedId),
                  addressLabel: drift.Value(addressLabel),
                  walletType: const drift.Value(1),
                ));
              }
            } else {
              addressModelList.add(AddressTableCompanion.insert(
                walletId: drift.Value(walletId),
                chainCode: drift.Value(coinType.id),
                chain: drift.Value(coinType.chain),
                address: drift.Value(publickey),
                addressIndex: const drift.Value(0),
                isSelected: const drift.Value(false),
                slip44Id: drift.Value(coinType.bip44Id),
                isUploaded: const drift.Value(false),
                sortedId: const drift.Value(0),
                addressLabel:
                    drift.Value(CoinBase.getDefaultLabel(coinType, 1)),
                walletType: const drift.Value(1),
              ));
            }
          }
        }
      } else if (qtType == QTType.monitoringAddressV3) {
        publickey = jsonObject['a'];

        String? coinId = jsonObject['t'];
        int addressIndex = jsonObject['s'];

        CoinType? coinType = CoinBase.getCoinTypeById(coinId!);
        if (coinType != null) {
          if (coinType is EosChain) {
            monitorModelList.add(MonitorTableCompanion.insert(
                batchId: drift.Value(batchId),
                slip44Id: drift.Value(EosChain.get.bip44Id),
                chainCode: drift.Value(coinId),
                chain: drift.Value(coinType.chain),
                publickey: drift.Value(publickey),
                segwitType: const drift.Value(44),
                keyTypekeyType: const drift.Value(0),
                walletId: drift.Value(walletId)));
          } else {
            monitorModelList.add(MonitorTableCompanion.insert(
                batchId: drift.Value(batchId),
                chainCode: drift.Value(coinId),
                chain: drift.Value(coinType.chain),
                publickey: drift.Value(publickey),
                keyTypekeyType: const drift.Value(0),
                segwitType: const drift.Value(44),
                walletId: drift.Value(walletId)));

            if (coinType.isBitcoinSeries) {
              String chainBase58 = publickey!.substring(
                  0,
                  publickey.lastIndexOf("0") < 0
                      ? 0
                      : publickey.lastIndexOf("0"));
              String publicKeyBase58 = publickey.substring(
                  publickey.lastIndexOf("0") + 1, publickey.length);

              String addressLabel =
                  CoinBase.getDefaultLabel(coinType, addressIndex + 1);
              int sortedId = addressIndex;

              String? address = await Get.walletCore
                  .createAddressOrPublicKeyByBase58(
                      chain: coinType.chain,
                      chainBase58: chainBase58,
                      publicKeyBase58: publicKeyBase58,
                      addressIndex: addressIndex,
                      isAddress: true);

              String? addressPublicKey = await Get.walletCore
                  .createAddressOrPublicKeyByBase58(
                      chain: coinType.chain,
                      chainBase58: chainBase58,
                      publicKeyBase58: publicKeyBase58,
                      addressIndex: addressIndex,
                      isAddress: false);

              addressModelList.add(AddressTableCompanion.insert(
                walletId: drift.Value(walletId),
                chainCode: drift.Value(coinType.id),
                chain: drift.Value(coinType.chain),
                address: drift.Value(address!),
                addressIndex: drift.Value(addressIndex),
                isSelected: const drift.Value(false),
                slip44Id: drift.Value(coinType.bip44Id),
                publickey: drift.Value(addressPublicKey!),
                path: drift.Value(CoinBase.getBip44PathByIndex(
                    coinType.bip44Id, addressIndex)),
                isUploaded: const drift.Value(false),
                sortedId: drift.Value(sortedId),
                addressLabel: drift.Value(addressLabel),
                walletType: const drift.Value(1),
              ));
            } else {
              addressModelList.add(AddressTableCompanion.insert(
                walletId: drift.Value(walletId),
                chainCode: drift.Value(coinType.id),
                chain: drift.Value(coinType.chain),
                address: drift.Value(publickey),
                addressIndex: drift.Value(addressIndex),
                isSelected: const drift.Value(false),
                slip44Id: drift.Value(coinType.bip44Id),
                isUploaded: const drift.Value(false),
                sortedId: drift.Value(addressIndex),
                addressLabel: drift.Value(
                    CoinBase.getDefaultLabel(coinType, addressIndex)),
                walletType: const drift.Value(1),
              ));
            }
          }
        } else {
          Get.dismissLoadingDialog();
          Get.back();
          Get.showAlertDialog(
              title: ID.stringNotices.tr, content: ID.stringNoSupportChain.tr);
          return;
        }
      }
      checkAndInsertWallet(
          wallet, walletModel, monitorModelList, addressModelList);
    } catch (e) {
      Log.e(e.toString());
      _bindFailed();
    }
  }

  bool _validateBindData(String result) {
    bool status = true;
    try {
      var jsonObject = json.decode(result);

      var pubs = jsonObject['pubs'];
      var jsonArray = pubs != null ? List.from(pubs) : null;

      String? taskId = jsonObject['task_id'];
      int? batch = jsonObject['batch'];
      String? deviceId = jsonObject['device_id'];
      String? seVersion = jsonObject['se_version'];
      String? appVersion = jsonObject['app_version'];
      String? walletId = jsonObject['wallet_id'];
      int? isPassphrase = jsonObject['is_passphrase'];

      Wallet wallet = Wallet.getWalletByBatch(batch ?? 0);

      if ([taskId, deviceId, seVersion, appVersion, walletId]
          .any((element) => element == null || element.isEmpty)) {
        return false;
      } else if (batch == null ||
          batch <= 0 ||
          walletId!.isEmpty ||
          Get.isEmptyString(deviceId) ||
          isPassphrase == null ||
          isPassphrase > 1 ||
          isPassphrase < 0) {
        return false;
      } else {
        if (wallet is! TouchWallet && deviceId!.length != 20) {
          return false;
        }
      }

      if (jsonArray != null) {
        for (var item in jsonArray) {
          String? pubdata = item['pub_data'];
          String? path = item['path'];
          int? isXpub = item['is_xpub'];
          int? addressNum = item['addr_num'];

          if ([pubdata, path]
              .any((element) => element == null || element.isEmpty)) {
            return false;
          } else if (isXpub == null ||
              isXpub < 0 ||
              isXpub > 1 ||
              addressNum == null ||
              addressNum < 0) {
            return false;
          }
        }
      }
    } catch (e) {
      status = false;
    }
    return status;
  }

  //插入Dapp默认地址列表
  WalletTableCompanion insertDappAddressList(
      String address, WalletTableCompanion oldWalletModel) {
    List<DappAddressModel> dappAddressModelList = [];
    List<CoinType> coinTypesList = CoinBase.filterCoinTypesByDapp();
    for (CoinType coinType in coinTypesList) {
      DappAddressModel dappAddressModel = DappAddressModel(
        chain: coinType.chain,
        address: address,
      );
      dappAddressModelList.add(dappAddressModel);
    }
    return oldWalletModel.copyWith(
      dappChainList: drift.Value(jsonEncode(dappAddressModelList)),
    );
  }

  Future checkAndInsertWallet(
      Wallet newWalelt,
      WalletTableCompanion newWalletTableCompanion,
      List<MonitorTableCompanion> monitorList,
      List<AddressTableCompanion> addressList) async {
    WalletModel? oldWalletModel = await Get.database.walletDao
        .findWalletsByWalletWalletOrDeviceId(
            newWalletTableCompanion.walletId.value!,
            newWalletTableCompanion.deviceId.value!);

    if (newWalelt.isUltraSeriesOrTouchWallet) {
      if (oldWalletModel != null) {
        showSameWalletDialog(
          newWalletTableCompanion,
          oldWalletModel,
          monitorList,
          addressList,
        );
      } else {
        insertWallet(newWalletTableCompanion, monitorList, addressList,
            oldWalletModel: oldWalletModel);
      }
    } else if (newWalelt is Pro3PlusWallet ||
        newWalelt is Pro3Wallet ||
        newWalelt is Pro2PlusWallet ||
        newWalelt is Pro1Wallet ||
        newWalelt is Pro2Wallet ||
        newWalelt is Pro1Pro2Wallet) {
      if (oldWalletModel != null) {
        insertWallet(newWalletTableCompanion, monitorList, addressList,
            oldWalletModel: oldWalletModel);
      } else {
        List<String?> allOldWalletId = await Get.database.addressDao
            .getAllExistsAddressWalletIds(addressList);
        if (allOldWalletId.isEmpty) {
          insertWallet(
            newWalletTableCompanion,
            monitorList,
            addressList,
            oldWalletModel: oldWalletModel,
          );
        } else {
          // 遍历所有老钱包的 walletId
          for (String? oldWalletId in allOldWalletId) {
            // 确保 oldWalletId 不为 null
            if (oldWalletId != null) {
              // 查询对应的 WalletModel
              WalletModel? oldWallet = await Get.database.walletDao
                  .findWalletsByWalletId(oldWalletId);

              // 检查 oldWalet 是否存在
              if (oldWallet != null) {
                String? deviceId = oldWallet.deviceId;
                if (deviceId != null &&
                    deviceId == newWalletTableCompanion.deviceId.value) {
                  showSameWalletDialog(
                    newWalletTableCompanion,
                    oldWallet,
                    monitorList,
                    addressList,
                  );
                } else {
                  insertWallet(
                    newWalletTableCompanion,
                    monitorList,
                    addressList,
                    oldWalletModel: oldWallet,
                  );
                }
              }
            }
          }
        }
      }
    }
  }

  Future<void> showSameWalletDialog(
      WalletTableCompanion newWalletTableCompanion,
      WalletModel oldWalletModel,
      List<MonitorTableCompanion> monitorList,
      List<AddressTableCompanion> addressList) async {
    Get.dismissLoadingDialog();

    Get.showAlertDialog(
        title: ID.stringTips.tr,
        content: ID.sameMnemonicWallet.tr,
        onConfirmText: ID.stringConfirm.tr,
        barrierDismissible: false,
        disableBack: false,
        onCancel: () async {
          Get.back();
          Get.until((route) => route.settings.name == AppRoutes.mainPage);
        },
        onConfirm: () async {
          Get.back();
          Get.showLoadingDialog();
          insertWallet(newWalletTableCompanion, monitorList, addressList,
              oldWalletModel: oldWalletModel, isDeleteOldWallet: true);
        });
  }

  Future<void> insertWallet(
    WalletTableCompanion walletModel,
    List<MonitorTableCompanion> monitorList,
    List<AddressTableCompanion> addressList, {
    WalletModel? oldWalletModel,
    bool isDeleteOldWallet = false,
  }) async {
    if (isDeleteOldWallet) {
      await deleteOldWallet(oldWalletModel!);
    }
    await Future.wait([
      walletRepository.insertDatabase(
          Get.database, walletModel, monitorList, addressList),
      uploadAddress(walletModel, addressList),
    ]);

    await Get.database.walletDao
        .selectWallet(walletModel.walletId.value!, walletModel.deviceId.value!);
    await _bindSuccess(walletModel);
  }

  Future<void> deleteOldWallet(
    WalletModel oldWalletModel,
  ) async {
    List<AddressModel> oldAddressModelList = await Get.database.addressDao
        .findAddressesByWalletId(oldWalletModel.walletId!);
    await walletRepository.deleteWallet(Get.database, oldWalletModel);

    if (oldAddressModelList.isNotEmpty) {
      await deletePushAddress(oldAddressModelList);
    }
  }

  Future<void> uploadAddress(
    WalletTableCompanion walletModel,
    List<AddressTableCompanion> addressList,
  ) async {
    List<MonitorinfoModel> monitorModelList = [];

    if (Get.isEmpty(addressList)) return;

    // 监听信息保存
    MonitorinfoModel monitorModel = MonitorinfoModel();
    monitorModel.batch = walletModel.batchId.value; // 批次号
    monitorModel.devicesCode = walletModel.deviceId.value; // 硬件钱包设备ID
    monitorModel.devicesModel = walletModel.deviceType.value; // 硬件钱包设备型号
    monitorModel.walletAccountId = walletModel.walletId.value; // 账户ID
    monitorModel.walletAccount = walletModel.walletName.value; // 账户名
    monitorModel.sign = "";
    monitorModel.time = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    monitorModel.phoneInfo = await DeviceUtils.getDeviceInfo(); //用户设备详情信息
    monitorModel.uuid =
        await DeviceUtils.getDeviceId(); //用户设备 Android为AndroidId IOS为App UUID
    monitorModel.addressNumber = addressList.length.toString();

    List<String> coinList = [];
    for (int i = addressList.length - 1; i >= 0; i--) {
      coinList.add(addressList[i].chain.value!);
    }
    coinList = coinList.toSet().toList(); // 使用 Set 来去重，然后转回 List
    monitorModel.coinNumber = coinList.length.toString();
    monitorModelList.add(monitorModel);
    await Get.appController.upload(walletModel, monitorModelList, addressList);
  }

  Future<void> deletePushAddress(List<AddressModel> adddrssModelList) async {
    String? phoneId = await DeviceUtils.getDeviceInfo();
    if (adddrssModelList.isEmpty || Get.isEmptyString(phoneId)) return;
    for (var item in adddrssModelList) {
      await deleteAddrssRequest(jsonEncode(AddressDeleteModel(
        phoneId: phoneId,
        address: item.address,
        coinType: item.chainCode,
      ).toJson()));
    }
  }

  Future<void> deleteAddrssRequest(String content) async {
    httpRequest<BaseResponse<dynamic>>(
        handleError: true,
        showToast: false,
        apiService.deletePushAddress(content),
        (value) {},
        error: (e) {});
  }

  void toScanResult(String result) {
    Get.dismissLoadingDialog();

    Get.offNamed(AppRoutes.scanResultPage, arguments: {
      GetArgumentsKey.scanResult: result,
    });
  }

  Future<void> _bindFailed({String bindFailedTextStr = ""}) async {
    Get.dismissLoadingDialog();

    var result = await Get.offAndToNamed(
      AppRoutes.connectSuccessfullyPage,
      arguments: {
        GetArgumentsKey.coldWalletName: "",
        GetArgumentsKey.bindFailed: false,
        GetArgumentsKey.bindFailedText: bindFailedTextStr,
      },
    );
    if (result == "result") {
      Get.toScanner();
    }
  }

  Future<void> _bindSuccess(WalletTableCompanion walletModel) async {
    Get.dismissLoadingDialog();
    await Get.offNamedUntil(
      AppRoutes.connectSuccessfullyPage, // 新页面的路由名称
      ModalRoute.withName(AppRoutes.mainPage), // 直到遇到的路由名称
      arguments: {
        GetArgumentsKey.coldWalletName: walletModel.deviceType.value!,
        GetArgumentsKey.bindFailed: true,
      }, // 传递给新页面的参数
    );

    AppController.refreshHome(
        isLoading: true, isScrollToTop: true, isConnectWallet: true);
  }

  @override
  void loadData() {}
}
