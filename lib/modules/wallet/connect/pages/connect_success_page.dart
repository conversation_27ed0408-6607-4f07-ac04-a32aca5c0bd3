/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-10 15:48:14
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class ConnectSuccessfullyPage extends BaseStatelessWidget {
  const ConnectSuccessfullyPage({super.key});

  @override
  Widget build(BuildContext context) {
    bool isBuildSuccess = Get.arguments?[GetArgumentsKey.bindFailed] ?? false;
    String bindFailedText =
        Get.arguments?[GetArgumentsKey.bindFailedText] ?? "";

    return Scaffold(
        appBar: baseAppBar(),
        body: Scaffold(
            body: SingleChildScrollView(
          child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: Get.setHeight(40),
                    ),
                    ImageWidget(
                      width: Get.setImageSize(84),
                      height: Get.setImageSize(84),
                      assetUrl: isBuildSuccess ? 'icon_success' : 'icon_failed',
                    ),
                    SizedBox(
                      height: Get.setHeight(24),
                    ),
                    Text(
                      isBuildSuccess ? ID.bindSucess.tr : ID.bindFailed.tr,
                      style: TextStyle(
                          color: isBuildSuccess
                              ? Get.theme.color02B58A
                              : Get.theme.colorF44D4D,
                          fontWeight: FontWeightX.medium,
                          fontSize: Get.setFontSize(20)),
                    ),
                    Visibility(
                      visible: !Get.isEmptyString(bindFailedText),
                      child: Padding(
                        padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
                        child: Text(
                          bindFailedText,
                          style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontWeight: FontWeightX.medium,
                              fontSize: Get.setFontSize(14)),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: isBuildSuccess,
                      child: SizedBox(
                        height: Get.setHeight(8),
                      ),
                    ),
                    Visibility(
                      visible: isBuildSuccess,
                      child: Text(
                        ID.bindSucessInfo.trParams({
                          'wallet':
                              Get.arguments?[GetArgumentsKey.coldWalletName] ??
                                  '',
                        }),
                        style: TextStyle(
                            color: Get.theme.textSecondary,
                            fontWeight: FontWeightX.regular,
                            fontSize: Get.setFontSize(14)),
                      ),
                    ),
                    SizedBox(
                      height: Get.setHeight(36),
                    ),
                    Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: Get.setPaddingSize(Get.width * 68 / 375)),
                      child: OutlinedButtonWidget(
                        text: isBuildSuccess ? ID.openWallet.tr : ID.rebind.tr,
                        buttonSize: ButtonSize.full,
                        onPressed: () {
                          if (isBuildSuccess) {
                            Get.until((route) =>
                                route.settings.name == AppRoutes.mainPage);
                          } else {
                            Get.back(result: "result");
                          }
                        },
                      ),
                    )
                  ])),
        )));
  }
}
