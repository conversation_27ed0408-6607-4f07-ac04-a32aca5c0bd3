/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-05 16:41:52
 */
import 'package:carousel_slider_plus/carousel_slider_plus.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/qr/qr_config.dart';
import 'package:coinbag/modules/wallet/connect/controllers/sync_balance_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/status_widget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class SyncBalancePage extends BaseStatelessWidget<SyncBalanceController> {
  const SyncBalancePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: ID.stringSyncAmountValue
            .trParams({'value': controller.coinModel?.symbol ?? ''}),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          children: [
            _addressWidget(),
            SizedBox(height: Get.setPaddingSize(16)),
            _balanceWidget(),
            DividerWidget(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(32)),
            ),
            _buildWidget(),
          ],
        ),
      ),
    );
  }

  GetBuilder<SyncBalanceController> _buildWidget() {
    return GetBuilder<SyncBalanceController>(
      id: GetKey.syncBalance,
      builder: (_) => controller.obx(
        (state) => _successWidget(),
        onLoading: const SyncBalanceSkeletonWidget(),
        onEmpty: const AppEmptyWidget(),
        onError: (error) =>
            AppErrorWidget(onRefresh: () => controller.loadData()),
      ),
    );
  }

  Column _successWidget() => Column(
        children: [
          _titleWidget(),
          SizedBox(height: Get.setPaddingSize(16)),
          _carouselSlider(),
          SizedBox(height: Get.setPaddingSize(24)),
          _buttonWidget(),
        ],
      );

  Column _carouselSlider() {
    return Column(
      children: [
        SizedBox(
          width: QRConfig.qrSize + Get.setPaddingSize(10),
          child: CarouselSlider(
            items: controller.qrImageList,
            controller: controller.carouselCtr,
            options: CarouselOptions(
                enableInfiniteScroll: controller.qrImageList.length != 1,
                autoPlay: false,
                autoPlayInterval: const Duration(milliseconds: 1500),
                autoPlayAnimationDuration: const Duration(milliseconds: 300),
                animateToClosest: false,
                height: QRConfig.qrSize + Get.setPaddingSize(10),
                aspectRatio: 1,
                viewportFraction: 1,
                onPageChanged: (index, _) {
                  controller.selectedPage.value = index;
                }),
          ),
        ),
        Visibility(
          visible: controller.qrImageList.length > 1,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(16)),
            child: _indicatorWidget(),
          ),
        ),
      ],
    );
  }

  Obx _indicatorWidget() {
    double maxWidth = QRConfig.qrSize;
    double itemWidth = 18 + Get.setPaddingSize(4);
    int maxVisibleDots = maxWidth ~/ itemWidth;
    if ((maxVisibleDots % 2) == 0) {
      maxVisibleDots++;
    }

    if (maxVisibleDots < 5) {
      maxVisibleDots = 5;
    }

    return Obx(() => SizedBox(
          width: maxWidth,
          child: Center(
            child: AnimatedSmoothIndicator(
                activeIndex: controller.selectedPage.value,
                count: controller.qrImageList.length,
                effect: ScrollingDotsEffect(
                    spacing: Get.setPaddingSize(4),
                    radius: 2,
                    dotWidth: 16,
                    dotHeight: 4,
                    paintStyle: PaintingStyle.fill,
                    strokeWidth: 1,
                    activeStrokeWidth: 1,
                    activeDotScale: 1,
                    maxVisibleDots: maxVisibleDots,
                    dotColor: Get.theme.colorECECEC,
                    activeDotColor: Get.theme.primary), // your preferred effect
                onDotClicked: (index) {}),
          ),
        ));
  }

  Obx _buttonWidget() => Obx(() => ButtonWidget(
        width: QRConfig.qrSize + Get.setPaddingSize(24),
        text: ID.stringScanCompleteTitle.tr,
        buttonStatus:
            controller.selectedPage.value == controller.qrImageList.length - 1
                ? ButtonStatus.enable
                : ButtonStatus.disable,
        onPressed: () => Get.back(),
      ));

  Row _balanceWidget() {
    return Row(
      children: [
        Text(
          ID.stringCurrentSyncAmount.tr,
          style: styleSecond_14,
        ),
        SizedBox(width: Get.setPaddingSize(16)),
        Expanded(
          child: Obx(() => Text(
                Get.isEmptyString(controller.balance.value)
                    ? '--'
                    : '${controller.balance.value} ${controller.coinModel?.symbol}',
                textAlign: TextAlign.end,
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(16),
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              )),
        ),
      ],
    );
  }

  Container _addressWidget() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Get.setPaddingSize(12),
        vertical: Get.setPaddingSize(12),
      ),
      decoration: BoxDecoration(
          color: Get.theme.colorF9F9F9,
          borderRadius: BorderRadius.all(Radius.circular(Get.setRadius(12)))),
      child: Text(
        controller.getAddress(),
        style: TextStyle(
          color: Get.theme.textPrimary,
          fontSize: Get.setFontSize(16),
          fontFamily: Get.setNumberFontFamily(),
        ),
      ),
    );
  }

  Text _titleWidget() {
    return !controller.coinType.isBitcoinSeries
        ? Text(
            ID.stringSyncAmountTip1.tr,
            style: styleSecond_14,
          )
        : Text.rich(
            textAlign: TextAlign.center,
            TextSpan(
                text: ID.qrSettingQrsizeTitle1.tr,
                style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(14),
                    fontWeight: FontWeightX.regular),
                children: [
                  TextSpan(
                      text: ID.qrSettingQrsizeTitle2.tr,
                      style: TextStyle(
                          color: Get.theme.colorFF6A16,
                          fontSize: Get.setFontSize(14),
                          fontWeight: FontWeightX.regular),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () => Get.toNamed(AppRoutes.preferencesPage))
                ]));
  }
}
