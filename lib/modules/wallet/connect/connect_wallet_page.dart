/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-10-22 15:04:58
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class ConnectWalletPage extends BaseStatelessWidget {
  const ConnectWalletPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Get.theme.colorECECEC,
        appBar: baseAppBar(
          backgroundColor: Get.theme.colorECECEC,
        ),
        body: SingleChildScrollView(
            padding: EdgeInsets.only(
              top: Get.setPaddingSize(16),
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16),
              bottom: Get.getSafetyBottomPadding(),
            ),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                ID.bindHardwareWallet.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(30),
                    fontWeight: FontWeightX.semibold),
              ),
              SizedBox(height: Get.setPaddingSize(36)),
              _itemWidget(
                  key: const Key('connect_wallet_action_item_qr'),
                  title: ID.stringScanQrBind.tr,
                  des: ID.stringScanQrBindDes.tr,
                  imageName: 'bind_qr',
                  callback: () async {
                    if (AppConfig.instance.isTestMode) {
                      String testData =
                          await AppController.loadTestWalletData();
                      Get.connectWallet.handleConnectWallet(testData);
                    } else {
                      Get.toScanner();
                    }
                  }),
              SizedBox(height: Get.setPaddingSize(24)),
              _itemWidget(
                  key: const Key('connect_wallet_action_item_touch'),
                  title: ID.stringNFCBind.tr,
                  des: ID.stringNFCBindDes.tr,
                  imageName: 'bind_nfc',
                  callback: () async {
                    if (AppConfig.instance.isTestMode) {
                      String testData =
                          await AppController.loadTestWalletData();
                      Get.connectWallet.handleConnectWallet(testData);
                    } else {
                      Get.toNamed(AppRoutes.touchBindPage);
                    }
                  }),
            ])));
  }

  GestureDetector _itemWidget(
          {required Key? key,
          required String title,
          required String des,
          required String imageName,
          Function()? callback}) =>
      GestureDetector(
        key: key,
        onTap: callback,
        child: Container(
          width: Get.width,
          color: Get.theme.bgColor,
          padding: EdgeInsets.all(Get.setPaddingSize(24)),
          child: Column(
            children: [
              ImageWidget(
                assetUrl: imageName,
                width: Get.setImageSize(84),
                height: Get.setImageSize(84),
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(24),
                    fontWeight: FontWeightX.semibold),
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              Text(
                des,
                textAlign: TextAlign.center,
                style: styleSecond_14,
              ),
            ],
          ),
        ),
      );
}
