/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-17 14:33:30
 * @LastEditTime: 2024-09-09 16:04:08
 */
enum ActivityAction {
  all,

  receive,

  send,

  own,

  resources,
}

enum TokenActivityStatus { success, fail, confirming, packing }

enum TokenActivityType {
  unknow,

  /// 发送
  send,

  /// 接收
  receive,

  /// 转给自己
  own,

  /// Trx质押资产
  stake,

  ///  Trx解锁资产
  unstake,

  /// Trx 投票
  vote,

  /// Trx 质押资产2.0
  stakeV2,

  /// Trx 解锁资产2.0
  unstakeV2,

  /// Trx 提取
  withdraw,

  ///  Trx代理资源 delegateresource
  delegate,

  ///  Trx回收资源 undelegateresource
  reclaim,

  /// EOS 抵押
  delegatebw,

  /// EOS 赎回

  undelegatebw,

  ///买内存 EOS单位
  buyram,

  ///卖内存 EOS单位
  sellram,

  /// EOS投票
  voteproducer,

//创建EOS账户
  newaccount,

//EOS买内存 KB单位
  buyrambytes,

//EOS领取赎回的资源
  refund,

  //EOS修改权限
  updateauth,

  //竞拍
  auction,
}
