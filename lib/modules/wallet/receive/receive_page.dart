/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-11 14:06:50
 * @LastEditTime: 2024-10-23 17:44:55
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/receive/receive_cotroller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/bitcoin/bch.dart';

class ReceivePage extends BaseStatelessWidget<ReceiveController> {
  const ReceivePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        isCusomTitle: true,
        customTitleWidget: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SymbolWidget(
                  coinModel: controller.coinModel!,
                  size: Get.setImageSize(20),
                  chainImageSzie: Get.setImageSize(12)),
              SizedBox(width: Get.setPaddingSize(6)),
              Text(
                controller.isNft
                    ? ID.stringNftReceive.tr
                    : "${controller.getSymbol()} ${ID.stringCollection.tr}",
                style: TextStyle(
                  fontWeight: FontWeightX.medium,
                  fontSize: Get.setFontSize(16),
                  color: Get.theme.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
      body: Obx(() => SizedBox(
            width: Get.width,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: Get.width,
                    padding: EdgeInsets.symmetric(
                        horizontal: Get.setPaddingSize(12),
                        vertical: Get.setPaddingSize(8)),
                    decoration: BoxDecoration(
                      color: Get.theme.color1AFF6A16,
                      borderRadius: BorderRadius.circular(Get.setRadius(6)),
                    ),
                    child: Text(controller.getAddressReceiveInfo(),
                        style: TextStyle(
                          color: Get.theme.colorFF6A16,
                          fontWeight: FontWeightX.regular,
                          fontSize: Get.setFontSize(12),
                          fontFamily: Get.setFontFamily(),
                        )),
                  ),
                  SizedBox(height: Get.setHeight(45)),
                  QRWidget(
                    data: controller.getAddress(),
                  ),
                  SizedBox(height: Get.setHeight(16)),
                  Text(ID.stringReceiveAddress.tr,
                      style: TextStyle(
                        color: Get.theme.textSecondary,
                        fontWeight: FontWeightX.regular,
                        fontSize: Get.setFontSize(14),
                        fontFamily: Get.setFontFamily(),
                      )),
                  SizedBox(height: Get.setHeight(2)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: Get.setPaddingSize(16)),
                    child: Text(controller.getAddress(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Get.theme.textPrimary,
                          fontWeight: FontWeightX.semibold,
                          fontSize: Get.setFontSize(16),
                          fontFamily: Get.setNumberFontFamily(),
                        )),
                  ),
                  SizedBox(height: Get.setHeight(40)),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: Get.setWidth(68)),
                    child: OutlinedButtonWidget(
                        buttonSize: ButtonSize.full,
                        text: ID.stringCopyAddress.tr,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ImageWidget(
                              assetUrl: 'copy_black',
                              width: Get.setWidth(20),
                              height: Get.setHeight(20),
                            ),
                            SizedBox(width: Get.setWidth(8)),
                            Text(ID.stringCopyAddress.tr,
                                style: TextStyle(
                                    fontSize: Get.setFontSize(16),
                                    color: Get.theme.textPrimary,
                                    fontFamily: Get.setFontFamily(),
                                    fontWeight: FontWeightX.medium))
                          ],
                        ),
                        onPressed: () => Get.copy(controller.getAddress())),
                  ),
                  Visibility(
                    visible: controller.coinType! is BitcoinCashChain,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: Get.setPaddingSize(20),
                          horizontal: Get.setPaddingSize(68)),
                      child: OutlinedButtonWidget(
                          buttonSize: ButtonSize.full,
                          text: ID.stringSwitchAddress.tr,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              ImageWidget(
                                assetUrl: 'icon_turn',
                                width: Get.setWidth(20),
                                height: Get.setHeight(20),
                              ),
                              SizedBox(width: Get.setWidth(8)),
                              Text(ID.stringSwitchAddress.tr,
                                  style: TextStyle(
                                      fontSize: Get.setFontSize(16),
                                      color: Get.theme.textPrimary,
                                      fontFamily: Get.setFontFamily(),
                                      fontWeight: FontWeightX.medium))
                            ],
                          ),
                          onPressed: () =>
                              controller.doConvertBitcoinCashAddress()),
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
