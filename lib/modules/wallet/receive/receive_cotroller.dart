/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:47:52
 * @LastEditTime: 2024-10-23 17:40:51
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/bitcoin/bch.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';

class ReceiveController extends BaseController {
  CoinModel? coinModel;
  String? address;
  CoinType? coinType;
  bool isNft = false;
  var convering = false.obs;
  String convertBitcoinCashAddress = "";

  @override
  void onInit() async {
    coinModel = Get.arguments?[GetArgumentsKey.coinModel] as CoinModel;
    address = Get.arguments?[GetArgumentsKey.address];
    coinType = CoinBase.getCoinTypeByChain(coinModel!.chain);
    if (Get.arguments.containsKey(GetArgumentsKey.isNft)) {
      isNft = Get.arguments[GetArgumentsKey.isNft];
    }
    if (coinType is BitcoinCashChain) {
      convertBitcoinCashAddress =
          await Get.walletCore.convertBitcoinCashAddress(address!) ?? '';
    }
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();

    AppController.setBrightness();
  }

  @override
  void loadData() {}

  String getSymbol() {
    if (coinModel!.symbol!.isEmpty) return "";
    return coinModel!.symbol!;
  }

  String getSymbolIcon() {
    return CoinBase.getSymbolIcon(coinModel!.chain!);
  }

  String getAddress() {
    if (address!.isEmpty) return "";
    return convering.value ? convertBitcoinCashAddress : address!;
  }

  String getAddressReceiveInfo() {
    if (coinType == null) return "";
    return ID.stringSupportsReceivingNetwork
        .trParams({'receiveInfo': coinType!.receiveInfo});
  }

  void doConvertBitcoinCashAddress() {
    convering.value = !convering.value;
  }

  @override
  void onClose() {
    super.onClose();
    AppController.resetBrightness();
  }
}

class ReceiveBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ReceiveController());
  }
}
