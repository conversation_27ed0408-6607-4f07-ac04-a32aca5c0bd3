/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-27 10:52:36
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/models/token_activity_model.dart';
import 'package:coinbag/modules/wallet/assets/models/token_transaction_model.dart';
import 'package:coinbag/res/resource.dart';

class TokenTransactionDeatilsController
    extends BaseController<BlockChainService> {
  late TokenActivityModel activityModel;
  late CoinModel coinModel;
  late CoinModel mainCoinModel;
  late String address;
  TokenTransactionModel? model;

  @override
  void onInit() async {
    final arg = Get.arguments;
    activityModel = arg[GetArgumentsKey.activityModel];
    coinModel = arg[GetArgumentsKey.coinModel];
    address = arg[GetArgumentsKey.address];
    if (coinModel.isToken == true) {
      CoinModel? result =
          await Get.database.coinDao.getMainCoinModel(chain: coinModel.chain);
      mainCoinModel = result!;
    } else {
      mainCoinModel = coinModel;
    }

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadCache();
    loadData();
  }

  Future<void> loadCache() async {
    model =
        await Get.database.transactionsActivityDao.getTransactionDetailsCache(
      activityModel: activityModel,
      address: address,
    );
    if (model != null) {
      update([GetKey.tokenTransactionId]);
    }
  }

  @override
  void loadData() {
    httpRequest(
        api.getTransactionDetails(BlockChainParamsManager.createParams(
            method: BlockChainAPI.transactionDetails,
            requestParams: RequestParams()
                .put(APIConstant.chain, activityModel.coinType?.chain)
                .put(APIConstant.txIds, [activityModel.txId]).put(
                    APIConstant.extra,
                    {APIConstant.solUserAddress: address}).getRequestBody())),
        (value) {
      if (value.data == null || value.data is! List) return;
      List data = value.data ?? [];
      if (data.isNotEmpty) {
        final json = data.first;
        if (json != null) {
          model = TokenTransactionModel.fromJson(json);
          update([GetKey.tokenTransactionId]);
          if (model != null) {
            Get.database.transactionsActivityDao.insertOrUpdateDetails(
                activityModel: activityModel,
                address: address,
                detailsJson: jsonEncode(json));
          }
        }
      }
    });
  }
}

class TokenTransactionDetailsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TokenTransactionDeatilsController());
  }
}
