/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-27 09:46:19
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/assets/details/token_transaction_details_controller.dart';
import 'package:coinbag/modules/wallet/assets/widgets/token_transaction_address_widget.dart';
import 'package:coinbag/modules/wallet/assets/widgets/token_transaction_txtype_widget.dart';
import 'package:coinbag/modules/wallet/common/activity_action.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/outlined_button_Widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_type.dart';

class TokenTransactionDetailsPage
    extends BaseStatelessWidget<TokenTransactionDeatilsController> {
  const TokenTransactionDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(),
      bottomNavigationBar: _bottomWidget(),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(20)),
        child: GetBuilder<TokenTransactionDeatilsController>(
            id: GetKey.tokenTransactionId,
            builder: (context) {
              return context.obx(
                (state) => _builderWidget(),
                onEmpty: _builderWidget(),
                onLoading: _builderWidget(),
                onError: (_) => _builderWidget(),
              );
            }),
      ),
    );
  }

  Column _builderWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _headerWidget(),
          TokenTransctionTxTypeWidget(
            controller: controller,
          ),
          TokenTransactionAddressWidget(
            controller: controller,
          ),
        ],
      );

  SizedBox _headerWidget() {
    String symbol =
        controller.activityModel.getActivitySymbol(controller.address);
    String? amount = BalanceManager.getBalance(
        controller.activityModel.amount, controller.coinModel);
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ImageWidget(
            assetUrl: _imageName,
            width: Get.setImageSize(60),
            height: Get.setImageSize(60),
          ),
          SizedBox(
            height: Get.setPaddingSize(10),
          ),
          Text(
            _statusText,
            style: stylePrimary_16_m,
          ),
          Visibility(
            visible: _headerVisible,
            child: SizedBox(
              height: Get.setPaddingSize(10),
            ),
          ),
          Visibility(
            visible: _headerVisible,
            child: Text(
              "$symbol $amount ${controller.coinModel.symbol}",
              style: TextStyle(
                  fontSize: Get.setFontSize(24),
                  fontFamily: Get.setNumberFontFamily(),
                  color: controller.activityModel.getActivityColor(),
                  fontWeight: FontWeightX.semibold),
            ),
          ),
          SizedBox(
            height: Get.setPaddingSize(28),
          ),
          const DividerWidget()
        ],
      ),
    );
  }

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        bottom: Get.getSafetyBottomPadding(),
        top: Get.setPaddingSize(16),
        left: Get.setPaddingSize(40),
        right: Get.setPaddingSize(40),
      ),
      child: OutlinedButtonWidget(
        onPressed: () {
          String? txId = controller.model?.txId;
          if (Get.isEmptyString(txId)) {
            txId = controller.activityModel.txId ?? '';
          }
          CoinType coinType = controller.activityModel.coinType!;

          String url = '${coinType.blockBrowserUrlByTxHash}$txId';

          Get.toWeb(url: url);
        },
        text: ID.stringBlockchain.tr,
        textStyle: TextStyle(
            fontSize: Get.setFontSize(14), color: Get.theme.textPrimary),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(40)),
          child: Text(
            ID.stringBlockchain.tr,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              color: Get.theme.textPrimary,
            ),
          ),
        ),
      ),
    );
  }

  bool get _headerVisible {
    if (controller.activityModel.activityType == TokenActivityType.send ||
        controller.activityModel.activityType == TokenActivityType.receive ||
        controller.activityModel.activityType == TokenActivityType.own) {
      return true;
    }
    return false;
  }

  String get _statusText {
    if (controller.activityModel.status == TokenActivityStatus.success) {
      return ID.stringComplte.tr;
    } else if (controller.activityModel.status == TokenActivityStatus.fail) {
      return ID.stringTxFail.tr;
    } else if (controller.activityModel.status ==
        TokenActivityStatus.confirming) {
      return ID.stringConfirming.tr;
    } else if (controller.activityModel.status == TokenActivityStatus.packing) {
      return ID.stringPacking.tr;
    }
    return '';
  }

  String get _imageName {
    if (controller.activityModel.status == TokenActivityStatus.success) {
      return 'icon_success';
    } else if (controller.activityModel.status == TokenActivityStatus.fail) {
      return 'icon_failed';
    }
    return 'icon_sending';
  }
}
