/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-12 08:59:55
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/assets/eos_export/eos_show_edit_remark_widget.dart';
import 'package:coinbag/modules/wallet/assets/eos_export/eos_show_qr_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';

enum EOSEditType { detail, qr, copy, remark }

class EOSShowAccountEditWidget extends StatelessWidget {
  final AddressModel addressModel;
  const EOSShowAccountEditWidget({super.key, required this.addressModel});

  static void show({required AddressModel addressModel}) {
    Get.showBottomSheet(
      hideHeader: true,
      paddingBottom: 0,
      bodyWidget: EOSShowAccountEditWidget(addressModel: addressModel),
      bottomWidget: BottomCancelWidget(onTap: Get.back),
    );
  }

  void _itemAction(EOSEditType type) {
    if (type == EOSEditType.copy) {
      Get.back();
      Get.copy(addressModel.address);
    } else if (type == EOSEditType.qr) {
      Get.back();
      EOSShowQrWidget.show(
          title: addressModel.addressLabel ?? '',
          qrMessgae: addressModel.address!,
          coinType: CoinBase.getCoinTypeByChain(addressModel.chain!)!);
    } else if (type == EOSEditType.remark) {
      Get.back();
      EOSShowEditRemarkWidget.show(addressModel);
    } else if (type == EOSEditType.detail) {
      Get.offAndToNamed(
        AppRoutes.eosAccountDetailPage,
        arguments: {GetArgumentsKey.addressModel: addressModel},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _itemWidget(ID.stringCheckDetailTitle.tr, EOSEditType.detail),
          _itemWidget(ID.stringShowQRTitle.tr, EOSEditType.qr),
          _itemWidget(ID.stringCopyAccmountTitle.tr, EOSEditType.copy),
          _itemWidget(ID.stringEditRemarkTitle.tr, EOSEditType.remark),
        ],
      ),
    );
  }

  HighLightInkWell _itemWidget(String title, EOSEditType type) {
    return HighLightInkWell(
      onTap: () => _itemAction(type),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(14)),
        child: Row(
          children: [
            _textWidget(title),
            SizedBox(
              width: Get.setPaddingSize(8),
            ),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(12),
              height: Get.setImageSize(12),
            )
          ],
        ),
      ),
    );
  }

  Expanded _textWidget(String title) {
    return Expanded(
      child: Text(
        title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: stylePrimary_16_m,
      ),
    );
  }
}
