/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-11 13:19:54
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/assets/controllers/eos_export_account_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';

class EOSShowEditRemarkWidget extends StatelessWidget {
  final AddressModel addressModel;
  const EOSShowEditRemarkWidget({super.key, required this.addressModel});

  static void show(AddressModel addressModel) {
    Get.showBottomSheet(
      title: ID.chainEditRemark.tr,
      paddingBottom: 0,
      bodyWidget: EOSShowEditRemarkWidget(addressModel: addressModel),
    );
  }

  @override
  Widget build(BuildContext context) {
    String? text = addressModel.addressLabel;
    return Padding(
      padding: EdgeInsets.only(
          top: Get.setPaddingSize(14),
          right: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding()),
      child: Column(
        children: [
          TextFieldWidget(
            autofocus: true,
            maxLength: 16,
            controller: TextEditingController(text: addressModel.addressLabel),
            onValueChanged: (value) => text = value,
          ),
          SizedBox(
            height: Get.setPaddingSize(12),
          ),
          ButtonWidget(
            text: ID.stringConfirm.tr,
            width: Get.width,
            onPressed: () async {
              if (Get.isEmptyString(text)) {
                Get.showToast(ID.addressLabelEmpty.tr,
                    toastMode: ToastMode.waring);
                return;
              }
              await Get.database.addressDao
                  .updateAddressLabel(addressModel, text!);

              try {
                EOSExportAccountController ctr =
                    Get.find<EOSExportAccountController>();
                ctr.updateAddressList();
              } catch (_) {}

              Get.back();

              Get.showToast(ID.stringNameChangeSuccess.tr,
                  toastMode: ToastMode.success);
            },
          )
        ],
      ),
    );
  }
}
