/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-30 16:19:29
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/chain.dart';

class EOSShowQrWidget extends StatelessWidget {
  final String qrMessgae;
  final CoinType coinType;

  const EOSShowQrWidget(
      {super.key, required this.qrMessgae, required this.coinType});

  static void show({
    required String title,
    required String qrMessgae,
    required CoinType coinType,
  }) {
    Get.showBottomSheet(
      title: title,
      paddingBottom: 0,
      bodyWidget: EOSShowQrWidget(
        qrMessgae: qrMessgae,
        coinType: coinType,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: Get.getSafetyBottomPadding(),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
            child: QRWidget(
              data: qrMessgae,
              assetImage: AssetImage('assets/images/${coinType.chainIcon}.png'),
            ),
          ),
          Text(
            qrMessgae,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily()),
          ),
          SizedBox(
            height: Get.setPaddingSize(24),
            width: Get.width,
          ),
          OutlinedButtonWidget(
            text: ID.copy.tr,
            width: Get.setWidth(240),
            onPressed: () => Get.copy(qrMessgae),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ImageWidget(
                  assetUrl: 'copy_black',
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(20),
                ),
                SizedBox(
                  width: Get.setPaddingSize(6),
                ),
                Text(
                  ID.copy.tr,
                  style: TextStyle(
                    fontSize: Get.setFontSize(16),
                    color: Get.theme.textPrimary,
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.medium,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
