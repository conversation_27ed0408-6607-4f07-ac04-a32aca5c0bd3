// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'extra.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Extra _$ExtraFromJson(Map<String, dynamic> json) => Extra(
      nodeHeight: (json['node_height'] as num?)?.toInt(),
      stakeCpuQuantity: (json['stake_cpu_quantity'] as num?)?.toInt(),
      stakeNetQuantity: (json['stake_net_quantity'] as num?)?.toInt(),
      unstakeCpuQuantity: (json['unstake_cpu_quantity'] as num?)?.toInt(),
      unstakeNetQuantity: (json['unstake_net_quantity'] as num?)?.toInt(),
    )
      ..contractExecute = json['contract_execute'] as bool?
      ..contractAddress = json['contract_address'] as String?;

Map<String, dynamic> _$ExtraToJson(Extra instance) => <String, dynamic>{
      'node_height': instance.nodeHeight,
      'stake_cpu_quantity': instance.stakeCpuQuantity,
      'stake_net_quantity': instance.stakeNetQuantity,
      'unstake_net_quantity': instance.unstakeNetQuantity,
      'unstake_cpu_quantity': instance.unstakeCpuQuantity,
      'contract_execute': instance.contractExecute,
      'contract_address': instance.contractAddress,
    };
