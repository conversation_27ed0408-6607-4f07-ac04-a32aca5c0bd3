/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 14:39:04
 * @LastEditTime: 2025-04-27 10:48:13
 */

import 'dart:convert';

import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/common/activity_action.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/extension/string_decimal.dart';

import 'extra.dart';

class TokenActivityModel {
  String? action;
  String? amount;
  int? blockNumber;
  Extra? extra;
  String? from;
  String? to;
  String? txId;
  int? txTime;
  String? type;
  bool? valid;
  TokenActivityType? _activityType;
  TokenActivityStatus? _status;
  CoinType? coinType;
  bool? isPacking;

  TokenActivityModel({
    this.action,
    this.amount,
    this.blockNumber,
    this.extra,
    this.from,
    this.to,
    this.txId,
    this.txTime,
    this.type,
    this.valid,
    this.isPacking,
  });

  factory TokenActivityModel.fromJson(Map<String, dynamic> json) {
    dynamic blockNumber = json['block_number'];
    dynamic txTime = json['tx_time'];
    if (blockNumber is String) {
      blockNumber = 0;
    }
    if (txTime is String) {
      txTime = 0;
    }

    return TokenActivityModel(
      action: json['action'] as String?,
      amount: json['amount'] != null ? json['amount'].toString() : "",
      blockNumber: blockNumber as int?,
      extra: json['extra'] == null
          ? null
          : Extra.fromJson(json['extra'] as Map<String, dynamic>),
      from: json['from'] as String?,
      to: json['to'] as String?,
      txId: json['tx_id'] as String?,
      txTime: txTime as int?,
      type: json['type'] as String?,
      valid: json['valid'] as bool?,
    );
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'action': action,
        'amount': amount,
        'block_number': blockNumber,
        'extra': extra?.toJson(),
        'from': from,
        'to': to,
        'tx_id': txId,
        'tx_time': txTime,
        'type': type,
        'valid': valid,
      };

  String? get extraText {
    Map? json = extra?.toJson();
    return json == null ? null : jsonEncode(json);
  }

//数据库model转换为当前model
  factory TokenActivityModel.fromTransactionsActivityModel(
      TransactionsActivityModel model) {
    String? extra = model.extra;
    Map<String, dynamic>? json;
    if (extra != null) {
      json = jsonDecode(extra);
    }

    return TokenActivityModel(
      action: model.action,
      amount: model.amount,
      blockNumber: model.blockNumber,
      from: model.fromAddress,
      to: model.receiveAddress,
      txId: model.txId,
      txTime: model.txTime,
      type: model.type,
      valid: model.valid,
      extra: json != null ? Extra.fromJson(json) : null,
      isPacking: model.isPacking,
    );
  }

  String getActivityIcon(String? address) {
    if (status == TokenActivityStatus.fail) {
      return 'icon_tx_fail';
    }
    switch (activityType) {
      case TokenActivityType.newaccount:
        return "icon_add_acount";
      case TokenActivityType.receive:
      case TokenActivityType.withdraw:
      case TokenActivityType.unstake:
      case TokenActivityType.unstakeV2:
      case TokenActivityType.undelegatebw:
      case TokenActivityType.sellram:
        return "icon_tx_receive";
      case TokenActivityType.own:
      case TokenActivityType.send:
      case TokenActivityType.stake:
      case TokenActivityType.stakeV2:
      case TokenActivityType.buyram:
      case TokenActivityType.buyrambytes:
      case TokenActivityType.delegatebw:
        return "icon_tx_send";
      case TokenActivityType.vote:
      case TokenActivityType.voteproducer:
        return "icon_tx_vote";
      case TokenActivityType.reclaim:
        {
          if (address == from) {
            return "icon_tx_receive";
          } else {
            return "icon_tx_send";
          }
        }
      case TokenActivityType.delegate:
        {
          if (address == from) {
            return "icon_tx_send";
          } else {
            return "icon_tx_receive";
          }
        }
      default:
        return "icon_tx_send";
    }
  }

  String getActivityText() {
    if (extra?.contractExecute == true) {
      return ID.stringContractInteraction.tr;
    }
    final activityTexts = {
      TokenActivityType.receive: ID.receive.tr,
      TokenActivityType.send: ID.send.tr,
      TokenActivityType.own: ID.stringSendOneself.tr,
      TokenActivityType.delegate: ID.stringDelegateResource.tr,
      TokenActivityType.vote: ID.stringVote.tr,
      TokenActivityType.stake: ID.stringStake.tr,
      TokenActivityType.stakeV2: ID.stringStake2.tr,
      TokenActivityType.unstake: ID.stringUnstake.tr,
      TokenActivityType.unstakeV2: ID.stringUnstake2.tr,
      TokenActivityType.reclaim: ID.stringReclaim.tr,
      TokenActivityType.withdraw: ID.stringTxWithdraw.tr,
      TokenActivityType.delegatebw: ID.stringEosStake.tr,
      TokenActivityType.undelegatebw: ID.stringEosRefoud.tr,
      TokenActivityType.buyram: ID.stringEosBuyRam.tr,
      TokenActivityType.sellram: ID.stringEosSellRam.tr,
      TokenActivityType.voteproducer: ID.stringVote.tr,
      TokenActivityType.newaccount: ID.stringEosNewAccount.tr,
      TokenActivityType.buyrambytes: ID.stringEosBuyRam.tr,
      TokenActivityType.refund: ID.stringEosReceiveTheRefunded.tr,
      TokenActivityType.updateauth: "updateauth",
      TokenActivityType.auction: ID.stringEosBidname.tr,
    };

    return activityTexts[activityType] ?? "";
  }

  String getActivityAddressText() {
    if (extra?.contractExecute == true) {
      return ID.stringContractAddress.tr;
    }
    switch (activityType) {
      case TokenActivityType.receive:
        return ID.sendAddress.tr;
      case TokenActivityType.send:
      case TokenActivityType.own:
      case TokenActivityType.withdraw:
      case TokenActivityType.delegate:
        return ID.receiveAddress.tr;
      case TokenActivityType.reclaim:
        return ID.stringReclaimAddress.tr;
      default:
        return '';
    }
  }

  String getActivitySymbol(String? address) {
    switch (activityType) {
      case TokenActivityType.receive:
      case TokenActivityType.withdraw:
      case TokenActivityType.unstake:
      case TokenActivityType.undelegatebw:
      case TokenActivityType.sellram:
      case TokenActivityType.refund:
        return "+";
      case TokenActivityType.send:
      case TokenActivityType.stake:
      case TokenActivityType.stakeV2:
      case TokenActivityType.delegatebw:
      case TokenActivityType.buyram:
      case TokenActivityType.buyrambytes:
      case TokenActivityType.newaccount:
        return "-";
      case TokenActivityType.reclaim:
        {
          if (address == from) {
            return "+";
          } else {
            return "-";
          }
        }
      case TokenActivityType.delegate:
        {
          if (address == from) {
            /// 代理给别人
            return "-";
          } else {
            return "+";
          }
        }
      default:
        return '';
    }
  }

  Color getActivityColor() {
    switch (activityType) {
      case TokenActivityType.receive:
      case TokenActivityType.unstake:
      case TokenActivityType.withdraw:
      case TokenActivityType.undelegatebw:
      case TokenActivityType.sellram:
      case TokenActivityType.refund:
        return Get.theme.color02B58A;
      case TokenActivityType.send:
      case TokenActivityType.stake:
      case TokenActivityType.stakeV2:
      case TokenActivityType.delegatebw:
      case TokenActivityType.buyram:
      case TokenActivityType.buyrambytes:
      case TokenActivityType.newaccount:
        return Get.theme.colorF44D4D;
      case TokenActivityType.own:
      case TokenActivityType.reclaim:
      case TokenActivityType.delegate:
      case TokenActivityType.unstakeV2:
        return Get.theme.textPrimary;
      default:
        return Get.theme.textPrimary;
    }
  }

  String getActivityAddressValue() {
    if (extra?.contractExecute == true) {
      return AddressUtils.omitAddress(
          CoinBase.getFormatAddress(coinType!, extra!.contractAddress)!,
          len: 5);
    }

    switch (activityType) {
      case TokenActivityType.receive:
      case TokenActivityType.unstake:
        return AddressUtils.omitAddress(
            CoinBase.getFormatAddress(coinType!, from!)!,
            len: 5);
      case TokenActivityType.own:
      case TokenActivityType.vote:
      case TokenActivityType.send:
      case TokenActivityType.stake:
      case TokenActivityType.stakeV2:
      case TokenActivityType.unstakeV2:
      case TokenActivityType.withdraw:
      case TokenActivityType.delegate:
      case TokenActivityType.reclaim:
      case TokenActivityType.delegatebw:
      case TokenActivityType.undelegatebw:
      case TokenActivityType.buyram:
      case TokenActivityType.sellram:
      case TokenActivityType.voteproducer:
      case TokenActivityType.newaccount:
      case TokenActivityType.buyrambytes:
      case TokenActivityType.refund:
      case TokenActivityType.updateauth:
      case TokenActivityType.auction:
        return AddressUtils.omitAddress(
            CoinBase.getFormatAddress(coinType!, to ?? '')!,
            len: 5);
      default:
        return "";
    }
  }

  String getActivityAmount(
      CoinModel? coinModel, CoinType? mCointype, String? address) {
    String value = "";
    try {
      switch (activityType) {
        case TokenActivityType.vote:
        case TokenActivityType.voteproducer:
          value = "";
          break;

        // // 抵押
        case TokenActivityType.delegatebw:
          if (mCointype is EosChain) {
            String net =
                CoinBase.getEosBalacne(extra!.stakeNetQuantity!.toString());
            String cpu =
                CoinBase.getEosBalacne(extra!.stakeCpuQuantity!.toString());
            String valueTotal = DecimalUtils.add(net, cpu);

            value =
                "${getActivitySymbol(address)} ${BalanceManager.getBalance(valueTotal, coinModel)}";
          } else {
            value =
                "${getActivitySymbol(address)} ${BalanceManager.getBalance(amount, coinModel)}";
          }
          break;
        // // 赎回
        case TokenActivityType.undelegatebw:
          String net =
              CoinBase.getEosBalacne(extra!.unstakeNetQuantity!.toString());
          String cpu =
              CoinBase.getEosBalacne(extra!.unstakeCpuQuantity!.toString());
          String valueTotal = DecimalUtils.add(net, cpu);
          if (mCointype is EosChain) {
            value =
                "${getActivitySymbol(address)} ${BalanceManager.getBalance(valueTotal, coinModel)}";
          } else {
            value =
                "${getActivitySymbol(address)} ${BalanceManager.getBalance(amount, coinModel)}";
          }
          break;

        default:
          value =
              "${getActivitySymbol(address)} ${BalanceManager.getBalance(amount, coinModel)}";
          break;
      }
    } catch (e) {
      Log.e(e.toString());
    }
    return value;
  }

  String getTime() {
    if (status == TokenActivityStatus.fail) return ID.stringSendFail.tr;
    if (status == TokenActivityStatus.packing) return ID.stringPacking.tr;
    if (status == TokenActivityStatus.confirming) return ID.stringConfirming.tr;
    if (txTime == null) return '';
    return DateHeleper.formatTimestamp(txTime!);
  }

  TokenActivityStatus get status {
    if (_status != null) return _status!;
    if (blockNumber == null || isPacking == true) {
      return TokenActivityStatus.packing;
    }

    String txBlockNumber = blockNumber.toString();
    String txCount =
        (extra?.nodeHeight ?? 0).toString().sub(txBlockNumber.toString());

    if (txBlockNumber.moreThan('0')) {
      if (valid != true) {
        _status = TokenActivityStatus.fail;
      } else {
        if (txCount.moreThan('0')) {
          if (txCount.moreThan(coinType!.confirmations.toString())) {
            _status = TokenActivityStatus.success;
          } else {
            _status = TokenActivityStatus.confirming;
          }
        } else {
          _status = TokenActivityStatus.success;
        }
      }
    } else {
      _status = TokenActivityStatus.packing;
    }

    return _status ??= TokenActivityStatus.packing;
  }

  /// 交易类型
  TokenActivityType get activityType {
    if (_activityType != null) return _activityType!;

    switch (action) {
      case 'transfer':
      case 'TRANSFER':
      case 'Payment':
      case '':
        {
          if (type == 'in') {
            _activityType = TokenActivityType.receive;
          } else if (type == 'out') {
            _activityType = TokenActivityType.send;
          }

          if (from == to) {
            _activityType = TokenActivityType.own;
          }
        }
        break;
      case 'freeze':
        _activityType = TokenActivityType.stake;
        break;
      case 'unfreeze':
        _activityType = TokenActivityType.unstake;
        break;
      case 'vote':
        _activityType = TokenActivityType.vote;
        break;
      case 'freezeV2':
        _activityType = TokenActivityType.stakeV2;
        break;
      case 'unfreezeV2':
        _activityType = TokenActivityType.unstakeV2;
        break;
      case 'withdraw':
        _activityType = TokenActivityType.withdraw;
        break;
      case 'delegateresource':
        _activityType = TokenActivityType.delegate;
        break;
      case 'undelegateresource':
        _activityType = TokenActivityType.reclaim;
        break;
      case 'delegatebw':
        _activityType = TokenActivityType.delegatebw;
        break;
      case 'undelegatebw':
        _activityType = TokenActivityType.undelegatebw;
        break;

      case 'buyram':
        _activityType = TokenActivityType.buyram;
        break;
      case 'sellram':
        _activityType = TokenActivityType.sellram;
        break;
      case 'buyrambytes':
        _activityType = TokenActivityType.buyrambytes;
        break;
      case 'newaccount':
        _activityType = TokenActivityType.newaccount;
        break;
      case 'voteproducer':
        _activityType = TokenActivityType.voteproducer;
        break;
      case 'refund':
        _activityType = TokenActivityType.refund;
        break;
      case 'updateauth':
        _activityType = TokenActivityType.updateauth;
        break;
      case 'auction':
        _activityType = TokenActivityType.auction;
        break;
      default:
        _activityType = TokenActivityType.unknow;
        break;
    }
    return _activityType!;
  }
}
