/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2025-04-27 10:38:11
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class TokenTransactionModel {
  String? txId;
  String? txTime;
  String? blockNumber;
  AddressInfo? fromInfo;
  AddressInfo? toInfo;
  Extra? extra;

  TokenTransactionModel({
    this.txId,
    this.txTime,
    this.fromInfo,
    this.toInfo,
    this.extra,
    this.blockNumber,
  });

  factory TokenTransactionModel.fromJson(Map<String, dynamic> json) {
    final txTime = json['tx_time'];
    String? txTimeText;
    if (txTime != null) {
      if (txTime is int) {
        txTimeText = DateHeleper.formatTimestamp(txTime);
      } else if (txTime is String) {
        if (!Get.isEmptyString(txTime)) {
          txTimeText =
              DateHeleper.formatTimestamp(DecimalUtils.toIntSafe(txTime));
        }
      }
    }

    AddressInfo? fromInfo;
    List? fromList = json['from_info'] as List?;
    if (fromList != null) {
      if (fromList.isNotEmpty) {
        fromInfo = AddressInfo.fromJson(fromList.first);
      }
    }

    AddressInfo? toInfo;
    List? toList = json['to_info'] as List?;
    if (toList != null) {
      if (toList.isNotEmpty) {
        toInfo = AddressInfo.fromJson(toList.first);
      }
    }

    final extraJson = json['extra'] as Map<String, dynamic>;
    Extra extra = Extra.fromJson(extraJson);

    return TokenTransactionModel(
      txTime: txTimeText,
      txId: json['tx_id'] as String?,
      fromInfo: fromInfo,
      toInfo: toInfo,
      blockNumber: json['block_number']?.toString(),
      extra: extra,
    );
  }
}

class AddressInfo {
  String? address;
  String? amount;
  String? contract;
  String? memo;

  AddressInfo({this.address, this.amount, this.contract, this.memo});

  factory AddressInfo.fromJson(Map<String, dynamic> json) => AddressInfo(
        address: json['address'] as String?,
        amount: json['amount']?.toString(),
        contract: json['contract'] as String?,
        memo: json['memo'] as String?,
      );
}

class Extra {
  String? accountCreateFee;
  String? energyFee;
  String? energyUsage;
  String? fee;
  String? memoFee;
  String? netFee;
  String? netUsage;
  bool? contractExecute;

  Extra({
    this.accountCreateFee,
    this.energyFee,
    this.energyUsage,
    this.fee,
    this.memoFee,
    this.netFee,
    this.netUsage,
    this.contractExecute,
  });

  bool get isMore {
    if ((accountCreateFee ?? '0').moreThan('0')) return true;
    if ((energyFee ?? '0').moreThan('0')) return true;
    if ((netFee ?? '0').moreThan('0')) return true;
    if ((memoFee ?? '0').moreThan('0')) return true;
    return false;
  }

  factory Extra.fromJson(Map<String, dynamic> json) => Extra(
        accountCreateFee: json['account_create_fee']?.toString(),
        energyFee: json['energy_fee']?.toString(),
        energyUsage: json['energy_usage']?.toString(),
        fee: json['fee']?.toString(),
        memoFee: json['memoFee']?.toString(),
        netFee: json['net_fee']?.toString(),
        netUsage: json['net_usage']?.toString(),
        contractExecute: json['contract_execute'] as bool?,
      );
}
