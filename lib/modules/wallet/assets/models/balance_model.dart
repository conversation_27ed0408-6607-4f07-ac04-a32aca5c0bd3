import 'extra.dart';

class BalanceModel {
  String? address;
  String? balance;
  String? contract;
  int? decimal;
  Extra? extra;
  String? name;
  String? nonce;
  int? supply;
  String? symbol;

  BalanceModel({
    this.address,
    this.balance,
    this.contract,
    this.decimal,
    this.extra,
    this.name,
    this.nonce,
    this.supply,
    this.symbol,
  });

  factory BalanceModel.fromJson(Map<String, dynamic> json) => BalanceModel(
        address: json['address'] as String?,
        balance: json['balance'] as String?,
        contract: json['contract'] as String?,
        decimal: json['decimal'] as int?,
        extra: json['extra'] == null
            ? null
            : Extra.fromJson(json['extra'] as Map<String, dynamic>),
        name: json['name'] as String?,
        nonce: json['nonce'] as String?,
        supply: json['supply'] as int?,
        symbol: json['symbol'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'address': address,
        'balance': balance,
        'contract': contract,
        'decimal': decimal,
        'extra': extra?.toJson(),
        'name': name,
        'nonce': nonce,
        'supply': supply,
        'symbol': symbol,
      };
}
