/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2025-04-27 10:20:51
 */

import 'package:json_annotation/json_annotation.dart';

part 'extra.g.dart';

@JsonSerializable()
class Extra {
  @Json<PERSON>ey(name: 'node_height')
  int? nodeHeight;
  @Json<PERSON>ey(name: 'stake_cpu_quantity')
  int? stakeCpuQuantity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'stake_net_quantity')
  int? stakeNetQuantity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'unstake_net_quantity')
  int? unstakeNetQuantity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'unstake_cpu_quantity')
  int? unstakeCpuQuantity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'contract_execute')
  bool? contractExecute;
  @J<PERSON><PERSON><PERSON>(name: 'contract_address')
  String? contractAddress;

  Extra(
      {this.nodeHeight,
      this.stakeCpuQuantity,
      this.stakeNetQuantity,
      this.unstakeCpuQuantity,
      this.unstakeNetQuantity});

  factory Extra.fromJson(Map<String, dynamic> json) => _$ExtraFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$ExtraToJson(this);
}
