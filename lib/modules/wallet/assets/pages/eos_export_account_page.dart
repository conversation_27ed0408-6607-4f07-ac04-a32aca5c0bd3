/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-28 15:33:22
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/assets/controllers/eos_export_account_controller.dart';
import 'package:coinbag/modules/wallet/assets/eos_export/eos_show_account_edit_widget.dart';
import 'package:coinbag/modules/wallet/assets/eos_export/eos_show_qr_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';

class EosExportAccountPage
    extends BaseStatelessWidget<EOSExportAccountController> {
  const EosExportAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringExportAccountTitle.tr),
      bottomNavigationBar: _bottomButtonWidget(),
      body: Padding(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
        ),
        child: GetBuilder<EOSExportAccountController>(
          builder: (_) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _publickeyWidget(),
                SizedBox(height: Get.setPaddingSize(24)),
                _accountWidget()
              ],
            );
          },
        ),
      ),
    );
  }

  Expanded _accountWidget() {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: Get.width,
              padding: EdgeInsets.all(Get.setPaddingSize(16)),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(color: Get.theme.colorECECEC, width: 1)),
              child: Obx(() => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _accountListWidget(),
                  )),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _accountListWidget() {
    List<Widget> widgets = [];
    widgets.add(Text(
      ID.stringEosAccountTitle.tr,
      style: styleSecond_14,
    ));
    widgets.add(DividerWidget(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
    ));

    for (var e in controller.addressList) {
      Widget widget = GestureDetector(
        onTap: () => EOSShowAccountEditWidget.show(addressModel: e),
        behavior: HitTestBehavior.translucent,
        child: Container(
          width: Get.width,
          padding: EdgeInsets.all(Get.setPaddingSize(16)),
          decoration: BoxDecoration(
            color: Get.theme.colorF3F3F5,
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      e.addressLabel ?? ID.stringNORemarkTitle.tr,
                      style: styleSecond_14,
                    ),
                    SizedBox(height: Get.setPaddingSize(8)),
                    Text(
                      e.address ?? '',
                      style: TextStyle(
                        color: Get.theme.textPrimary,
                        fontSize: Get.setFontSize(16),
                        fontFamily: Get.setNumberFontFamily(),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              ImageWidget(
                assetUrl: 'icon_new_arrow01',
                width: Get.setImageSize(12),
                height: Get.setImageSize(12),
              ),
            ],
          ),
        ),
      );
      widgets.add(widget);
      widgets.add(SizedBox(height: Get.setPaddingSize(8)));
    }

    return widgets;
  }

  HighLightInkWell _publickeyWidget() {
    return HighLightInkWell(
      borderRadius: BorderRadius.circular(12.0),
      onTap: () => EOSShowQrWidget.show(
        title: ID.stringEosPublicKeyTitle.tr,
        qrMessgae: controller.addressModel!.publickey ?? '',
        coinType: CoinBase.getCoinTypeByChain(controller.coinModel.chain!)!,
      ),
      child: Container(
        width: Get.width,
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Get.theme.colorECECEC, width: 1)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              ID.stringEosPublicKeyTitle.tr,
              style: styleSecond_14,
            ),
            DividerWidget(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
            ),
            Row(
              children: [
                Expanded(
                  child: Text(
                    controller.addressModel?.publickey ?? '',
                    style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(16),
                      fontWeight: FontWeightX.medium,
                      fontFamily: Get.setNumberFontFamily(),
                    ),
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(8)),
                ImageWidget(
                  assetUrl: 'icon_new_arrow01',
                  width: Get.setImageSize(16),
                  height: Get.setImageSize(16),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Padding _bottomButtonWidget() {
    return Padding(
      padding: EdgeInsets.only(
        top: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        left: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: Obx(() => ButtonWidget(
            buttonStatus: controller.buttonStatus.value,
            text: ID.stringExportAccountTitle.tr,
            onPressed: () => controller.exportAction(),
          )),
    );
  }
}
