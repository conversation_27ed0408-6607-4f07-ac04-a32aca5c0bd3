/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-11 14:18:58
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/assets/controllers/eos_export_account_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_account_info.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:flutter/material.dart';

class EOSAccountDetailPage extends StatefulWidget {
  const EOSAccountDetailPage({super.key});

  @override
  State<EOSAccountDetailPage> createState() => _EOSAccountDetailPageState();
}

class _EOSAccountDetailPageState extends State<EOSAccountDetailPage> {
  AddressModel? addressModel;
  EosAccountInfo? eosInfo;

  bool isLoad = false;

  @override
  void initState() {
    super.initState();
    addressModel = Get.arguments[GetArgumentsKey.addressModel];

    if (!Get.isEmptyString(addressModel?.eosAccountInfoCache)) {
      // eosInfo = EosAccountInfo.fromJson(
      //     json.decode(addressModel!.eosAccountInfoCache!));
    }

    EOSExportAccountController ctr = Get.find<EOSExportAccountController>();
    ctr.loadAccountInfo(
      addressModel: addressModel!,
      callback: ({
        info,
      }) {
        eosInfo = info;
        isLoad = true;
        setState(() {});
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringEosAccountDetialTitle.tr),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          children: [
            _accountWidget(),
            SizedBox(height: Get.setPaddingSize(24)),
            Visibility(
              visible: isLoad == true || eosInfo != null,
              child: Column(
                children: [
                  _permissionWidget(
                    permName: 'Owner',
                    key: eosInfo?.ownerPermission?.requiredAuth?.key?.key,
                    threshold:
                        eosInfo?.ownerPermission?.requiredAuth?.threshold,
                    weight: eosInfo?.ownerPermission?.requiredAuth?.key?.weight,
                  ),
                  SizedBox(height: Get.setPaddingSize(8)),
                  _permissionWidget(
                    permName: 'Active',
                    key: eosInfo?.activePermission?.requiredAuth?.key?.key,
                    threshold:
                        eosInfo?.activePermission?.requiredAuth?.threshold,
                    weight:
                        eosInfo?.activePermission?.requiredAuth?.key?.weight,
                  ),
                ],
              ),
            ),
            Visibility(
              visible: !(isLoad == true || eosInfo != null),
              child: Padding(
                padding: EdgeInsets.only(top: Get.setPaddingSize(24)),
                child: SizedBox(
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(20),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Get.theme.primary),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container _permissionWidget({
    String? permName,
    String? key,
    int? threshold,
    int? weight,
  }) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(Get.setPaddingSize(16)),
      decoration: BoxDecoration(
          color: Get.theme.colorF9F9F9,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: Get.theme.colorECECEC, width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      permName ?? '',
                      style: TextStyle(
                        color: Get.theme.textSecondary,
                        fontSize: Get.setFontSize(16),
                        fontFamily: Get.setNumberFontFamily(),
                      ),
                    ),
                    SizedBox(width: Get.setPaddingSize(4)),
                    Visibility(
                      visible: key == addressModel?.publickey,
                      child: Flexible(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: Get.setPaddingSize(6)),
                          decoration: BoxDecoration(
                            color: Get.theme.textPrimary,
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          child: Text(
                            ID.myTitle.tr,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Get.theme.white,
                              fontSize: Get.setFontSize(16),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: Get.setPaddingSize(6)),
              Text(
                ID.stringThresholdTitle
                    .trParams({'value': '${threshold ?? 0}'}),
                style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(16),
                ),
              ),
              SizedBox(width: Get.setPaddingSize(6)),
              Text(
                ID.stringWeightTitle.trParams({'value': '${weight ?? 0}'}),
                style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(16),
                ),
              ),
            ],
          ),
          DividerWidget(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
          ),
          Text(
            key ?? '',
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.regular,
              fontFamily: Get.setNumberFontFamily(),
            ),
          ),
        ],
      ),
    );
  }

  Container _accountWidget() {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(Get.setPaddingSize(16)),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: Get.theme.colorECECEC, width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            addressModel?.address ?? '',
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(16),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setNumberFontFamily(),
            ),
          ),
          DividerWidget(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
          ),
          Text(
            ID.stringCreateDateTitle.trParams({
              'date': eosInfo?.createDateTime == null
                  ? ''
                  : DateHeleper.formatDateTime(
                      eosInfo!.createDateTime!,
                      format: DateHeleper.format,
                    )
            }),
            style: styleSecond_14,
          ),
          SizedBox(height: Get.setPaddingSize(8)),
          Text(
            ID.stringAccountRemarkTitle
                .trParams({'tag': addressModel?.addressLabel ?? ''}),
            style: styleSecond_14,
          ),
        ],
      ),
    );
  }
}
