/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-04 17:28:27
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/modules/wallet/assets/token_assets_controller.dart';
import 'package:coinbag/modules/wallet/assets/widgets/token_assets_top_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/behavior/over_scroll_behavior.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:coinbag/widgets/sliver/auto_sliver_appbar_delegate.dart';
import 'package:coinbag/widgets/tab_bar/base_tab_bar.dart';
import 'package:flutter/material.dart';

class TokenAssetsPage extends BaseStatefulWidget<TokenAssetsController> {
  const TokenAssetsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(
          isCusomTitle: true,
          customTitleWidget: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SymbolWidget(
                    coinModel: controller.coinModel!,
                    size: Get.setImageSize(20),
                    chainImageSzie: Get.setImageSize(12)),
                SizedBox(width: Get.setPaddingSize(6)),
                Text(controller.getSymbol(),
                    style: TextStyle(
                      fontFamily: Get.setNumberFontFamily(),
                      fontWeight: FontWeightX.semibold,
                      fontSize: Get.setFontSize(18),
                      color: Get.theme.textPrimary,
                    )),
              ],
            ),
          ),
        ),
        body: ScrollConfiguration(
          behavior: OverScrollBehavior(),
          child: NestedScrollView(
              controller: controller.scrollController,
              headerSliverBuilder:
                  (BuildContext context, bool innerBoxIsScrolled) {
                return <Widget>[
                  const TokenAssetsTopWidget(),
                  SliverPersistentHeader(
                    pinned: true,
                    floating: true,
                    delegate: AutoSliverPersistentHeaderDelegate(baseTabBar(
                        onTab: (index) =>
                            controller.pagerController.jumpToPage(index),
                        controller: controller.tabController,
                        tabs: controller.tabList
                            .map((element) => Tab(
                                  text: element,
                                ))
                            .toList())),
                  ),
                ];
              },
              body: PageView.builder(
                  itemCount: controller.tabList.length,
                  onPageChanged: (index) {
                    controller.tabController.animateTo(index);
                  },
                  controller: controller.pagerController,
                  itemBuilder: (_, int index) => controller.pagerList[index])),
        ));
  }
}
