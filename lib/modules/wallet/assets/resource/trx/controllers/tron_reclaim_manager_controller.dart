/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-19 16:10:53
 */
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_delegate_model/tron_delegate_model.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/tron_resource.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TronReclaimManagerControlelr
    extends BaseRefreshController<BlockChainService> {
  late TronResourceManagerController ctr;
  TronResourceType type = TronResourceType.energy;
  late TronResource tronResource;
  String canDelegateValue = '';
  String delegatedValue = '';
  List<TronDelegateModel> dataSource = [];

  @override
  void onInit() {
    ctr = Get.arguments[GetArgumentsKey.controller];
    type = Get.arguments[GetArgumentsKey.tronResourceType];
    tronResource = ctr.tronResource.value;

    delegatedValue = type == TronResourceType.energy
        ? ctr.tronResource.value.resourceDetail?.delegatedToOthersEnergyValue ??
            ''
        : ctr.tronResource.value.resourceDetail
                ?.delegatedToOthersBandwidthValue ??
            '';
    canDelegateValue = type == TronResourceType.energy
        ? ctr.energyDelegate.value
        : ctr.bandwidthDelegate.value;

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();

    loadData();
  }

  void onRefresh() {
    showLoading();
    loadData();
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    loadData();
  }

  @override
  void loadData() {
    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
            method: BlockChainAPI.getTronResourceV2,
            requestParams: RequestParams()
                .put(APIConstant.chain, ctr.coinModel.chain!)
                .put(APIConstant.type, 2)
                .put(APIConstant.address, ctr.addressModel.address)
                .getRequestBody())), (value) {
      if (value.data != null) {
        hideRefresh(refreshController);
        List<dynamic> data = value.data['data'];

        List<TronDelegateModel> list =
            data.map((json) => TronDelegateModel.fromJson(json)).toList();
        dataSource = list.where((model) {
          if (type == TronResourceType.energy) {
            return model.resource == 1;
          } else {
            return model.resource == 0;
          }
        }).toList();
        if (dataSource.isEmpty) {
          showEmpty();
        }
      }
    });
  }
}

class TronReclaimManagerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TronReclaimManagerControlelr());
  }
}
