/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-20 17:26:25
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/resource_detail.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/tron_resource.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/pages/tron_resource_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/segment/segment_control_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

enum TronResourceStatus { reclaim, delegate }

class TronResourceManagerController extends BaseController<BlockChainService> {
  late SegmentedController segmentedController;
  late PageController pagerController;
  ScrollController scrollController = ScrollController();
  final List<Widget> pagerList = [
    const TronResourcePage(type: TronResourceType.energy),
    const TronResourcePage(type: TronResourceType.bandwidth),
  ];

  final List<String> tabList = [
    ID.stringEnergy.tr,
    ID.stringBandwidth.tr,
  ];

  TronResourceType type = TronResourceType.energy;
  late Rx<TronResource> tronResource;
  late CoinModel coinModel;
  late AddressModel addressModel;
  TronAccountInfo? accountInfo;
  late WalletModel walletModel;
  late Wallet wallet;

  Rx<ButtonStatus> delegateButtonStatus = ButtonStatus.loading.obs;
  RxString energyDelegate = ''.obs;
  String energyDelegateTrx = '0';
  RxString bandwidthDelegate = ''.obs;
  String bandwidthDelegateTrx = '0';

  @override
  void onInit() {
    final arg = Get.arguments;
    type = arg[GetArgumentsKey.tronResourceType];
    TronResource tronResource = arg[GetArgumentsKey.tronResource];
    this.tronResource = tronResource.obs;
    coinModel = arg[GetArgumentsKey.coinModel];
    addressModel = arg[GetArgumentsKey.addressModel];
    accountInfo = arg[GetArgumentsKey.tronAccountInfo];

    int selectedIndex = type == TronResourceType.energy ? 0 : 1;
    segmentedController = SegmentedController(selectedIndex: selectedIndex.obs);
    pagerController = PageController(initialPage: selectedIndex);
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    loadData();
    walletModel = (await Get.database.walletDao.getCheckedWallets())!;
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);
  }

  void getResourceAction() {
    if (wallet.isUltraSeries || wallet is TouchWallet) {
      Get.toNamed(AppRoutes.stakePage, arguments: {
        GetArgumentsKey.coinModel: coinModel,
        GetArgumentsKey.tronAccountInfo: accountInfo,
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.tronResourceType: type,
        GetArgumentsKey.walletModel: walletModel,
        GetArgumentsKey.wallet: wallet,
      });
    } else {
      TronResourceManagerController.showTip(ID.stringTronNoSupportedTip.tr);
    }
  }

  void delegateAction(TronResourceType type) {
    if (wallet.isUltraSeries || wallet is TouchWallet) {
      Get.toNamed(AppRoutes.tronDelegatePage, arguments: {
        GetArgumentsKey.tronResourceType: type,
        GetArgumentsKey.controller: this,
      });
    } else {
      TronResourceManagerController.showTip(ID.stringTronNoSupportedTip.tr);
    }
  }

  Future<void> reclaimAction(TronResourceType type) async {
    if (wallet.isUltraSeries || wallet is TouchWallet) {
      Get.toNamed(
        AppRoutes.tronReclaimManagerPage,
        arguments: {
          GetArgumentsKey.controller: this,
          GetArgumentsKey.tronResourceType: type,
        },
      );
    } else {
      TronResourceManagerController.showTip(ID.stringTronNoSupportedTip.tr);
    }
  }

  static void showTip(String message) {
    Get.showBottomSheet(
      title: ID.stringTips.tr,
      paddingBottom: 0,
      bodyWidget: Padding(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          children: [
            Text(
              message,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(16),
                fontFamily: Get.setFontFamily(),
              ),
            ),
            SizedBox(height: Get.setPaddingSize(24)),
            ButtonWidget(
              width: Get.width,
              text: ID.stringOk.tr,
              onPressed: () => Get.back(),
            )
          ],
        ),
      ),
    );
  }

  @override
  void loadData() {
    _loadResource();
    _availableDelegate();
  }

  void _availableDelegate() {
    List<Future> futures = [];

    futures.add(api.getAccountInfo(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getTronAccountInfo,
        requestParams: RequestParams()
            .put(APIConstant.chain, coinModel.chain!)
            .put(APIConstant.address, addressModel.address ?? "")
            .getRequestBody())));
    futures.add(api.tronResource(BlockChainParamsManager.createParams(
        method: BlockChainAPI.availableDelegate,
        requestParams:
            RequestParams().put(APIConstant.chain, coinModel.chain!).put(
          APIConstant.params,
          {
            APIConstant.type: 1,
            APIConstant.ownerAddress: addressModel.address,
          },
        ).getRequestBody())));
    futures.add(api.tronResource(BlockChainParamsManager.createParams(
        method: BlockChainAPI.availableDelegate,
        requestParams:
            RequestParams().put(APIConstant.chain, coinModel.chain!).put(
          APIConstant.params,
          {
            APIConstant.type: 0,
            APIConstant.ownerAddress: addressModel.address,
          },
        ).getRequestBody())));

    multiHttpRequest(
      futures,
      (data) {
        delegateButtonStatus.value = ButtonStatus.enable;
        if (data != null) {
          BaseResponseV1 response1 = data[0];
          if (response1.data != null) {
            accountInfo = TronAccountInfo.fromJson(response1.data);
          }
          BaseResponseV1 response2 = data[1];
          if (response2.data != null) {
            String? cost = accountInfo?.energyCost?.toString();
            if (Get.isEmptyString(cost)) {
              energyDelegate.value = '0';
            } else {
              String maxSize = response2.data['max_size'] ?? '0';
              maxSize = maxSize.div(pow(10, coinModel.chainDecimal!).toString(),
                  scale: 0, roundMode: RoundMode.round);
              energyDelegateTrx = maxSize;
              maxSize = maxSize
                  .mul(cost!)
                  .decimal(scale: 0, roundMode: RoundMode.down);
              energyDelegate.value = maxSize;
            }
          }
          BaseResponseV1 response3 = data[2];
          if (response3.data != null) {
            String? cost = accountInfo?.netCost?.toString();
            if (Get.isEmptyString(cost)) {
              bandwidthDelegate.value = '0';
            } else {
              String maxSize = response3.data['max_size'] ?? '0';
              maxSize = maxSize.div(pow(10, coinModel.chainDecimal!).toString(),
                  scale: 0, roundMode: RoundMode.round);
              bandwidthDelegateTrx = maxSize;
              maxSize = maxSize
                  .mul(cost!)
                  .decimal(scale: 0, roundMode: RoundMode.down);
              bandwidthDelegate.value = maxSize;
            }
          }
        }
      },
    );
  }

  void _loadResource() {
    httpRequest(
      api.tronResource(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getTronResourceV2,
        requestParams: RequestParams()
            .put(APIConstant.chain, coinModel.chain!)
            .put(APIConstant.allType, true)
            .put(APIConstant.address, addressModel.address!)
            .getRequestBody(),
      )),
      (value) async {
        if (value.data != null) {
          ResourceDetail detail = ResourceDetail.fromJson(value.data);
          tronResource.value.resourceDetail = detail;
          tronResource.value = tronResource.value;
          Log.logPrint(json.encode(tronResource.toJson()));
          await Get.database.addressDao.updateTronResource(
              addressModel, json.encode(tronResource.toJson()));
        }
      },
    );
  }

  @override
  void onClose() {
    super.onClose();
    segmentedController.onClose();
    scrollController.dispose();
    pagerController.dispose();
  }
}

class TronResourceManagerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TronResourceManagerController());
  }
}
