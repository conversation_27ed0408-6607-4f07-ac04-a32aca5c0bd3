/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-01 10:54:17
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_amount_widget.dart';
import 'package:coinbag/modules/wallet/send/dialog/transfer_info_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

class TronDelegateController extends BaseController<BlockChainService> {
  InputResourceType inputType = InputResourceType.resource;
  bool addressResult = false;
  bool amountResult = false;

  late final TronResourceType type;
  late TronResourceManagerController managerController;
  Rx<ButtonStatus> buttonStatus = ButtonStatus.disable.obs;
  TextEditingController addressController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  RxString amountErrorTips = ''.obs;

  RxString addressTip = ''.obs;
  RxString amountTip = ''.obs;
  RxString delegateTitle = ''.obs;
  RxString delegateValue = ''.obs;
  RxString delegateTips = ''.obs;
  RxString delegateAmountHintText = ''.obs;

  late TransferModel tsModel;

  @override
  void onInit() {
    final arg = Get.arguments;
    type = arg[GetArgumentsKey.tronResourceType];
    managerController = arg[GetArgumentsKey.controller];

    tsModel = TransferModel(
      chain: managerController.coinModel.chain!,
      type: TransferType.delegate,
      fromAddress: managerController.addressModel.address,
      wallet: managerController.wallet,
      addressPath: managerController.addressModel.path,
      coinSymbol: managerController.coinModel.symbol,
      coinDecimal: managerController.coinModel.chainDecimal,
      walletId: managerController.walletModel.walletId,
    )..resourceType = type;

    updateDelegateValue(inputType: InputResourceType.resource);
    super.onInit();
  }

  Future<void> delegateAction() async {
    bool result = await Get.walletCore.verifyAddress(
      chain: managerController.addressModel.chain!,
      address: addressController.text,
    );
    if (result == false) {
      Get.showToast(ID.inputOkToAddressTip.tr, toastMode: ToastMode.waring);
      return;
    }

    loadData();
  }

  Future<void> updateAddress() async {
    String address = addressController.text;
    if (!Get.isEmptyString(address)) {
      addressResult = true;
    }

    if (address == managerController.addressModel.address) {
      addressTip.value = ID.stringResourceOwnAddressTips.tr;
      addressResult = false;
    } else {
      addressTip.value = '';
    }

    tsModel.toAddress = address;

    updateStatus();
  }

  void updateAmount() {
    String amount = amountController.text;
    if (Get.isEmptyString(amount)) {
      amount = '0';
    }

    String? txValue = '';

    if (type == TronResourceType.energy) {
      String maxAmount = '0';
      String cost = (managerController.accountInfo?.energyCost ?? 1).toString();

      if (inputType == InputResourceType.resource) {
        maxAmount = managerController.energyDelegate.value;
        txValue = amount.div(cost, scale: 0, roundMode: RoundMode.up);
        tsModel.amount = txValue;
        tsModel.resourceAmount = amount;
      } else if (inputType == InputResourceType.trx) {
        maxAmount = managerController.energyDelegateTrx;
        txValue =
            amount.mul(cost).decimal(scale: 0, roundMode: RoundMode.round);
        tsModel.amount = amount;
        tsModel.resourceAmount = txValue;
      }
      if (amount.moreThan(maxAmount)) {
        amountResult = false;
        amountErrorTips.value = ID.stringResourceMaxTip.tr;
      } else {
        amountResult = true;
        amountErrorTips.value = '';
      }
    } else if (type == TronResourceType.bandwidth) {
      String maxAmount = '0';
      String cost = (managerController.accountInfo?.netCost ?? 1).toString();

      if (inputType == InputResourceType.resource) {
        maxAmount = managerController.bandwidthDelegate.value;
        txValue = amount.div(cost, scale: 0, roundMode: RoundMode.up);
        tsModel.amount = txValue;
        tsModel.resourceAmount = amount;
      } else if (inputType == InputResourceType.trx) {
        maxAmount = managerController.bandwidthDelegateTrx;
        txValue =
            amount.mul(cost).decimal(scale: 0, roundMode: RoundMode.round);
        tsModel.amount = amount;
        tsModel.resourceAmount = txValue;
      }
      if (amount.moreThan(maxAmount)) {
        amountResult = false;
        amountErrorTips.value = ID.stringResourceMaxTip.tr;
      } else {
        amountResult = true;
        amountErrorTips.value = '';
      }
    }

    if (amount.equal('0')) {
      amountResult = false;
      txValue = null;
    }

    updateDelegateValue(inputType: inputType, txValue: txValue);

    updateStatus();
  }

  void updateDelegateValue({
    required InputResourceType inputType,
    String? txValue,
  }) {
    this.inputType = inputType;
    if (inputType == InputResourceType.trx) {
      delegateTitle.value = ID.stringUseStakeAmount.tr;
      delegateAmountHintText.value = ID.stringInputHintTrx.tr;

      if (type == TronResourceType.energy) {
        delegateValue.value = ID.stringDelegateUseTrx
            .trParams({'value': managerController.energyDelegateTrx});
        delegateTips.value = ID.stringOtherGetResource.trParams({
          'value': txValue ?? '--',
          'resource': ID.stringEnergy.tr,
        });
      } else if (type == TronResourceType.bandwidth) {
        delegateValue.value = ID.stringDelegateUseTrx
            .trParams({'value': managerController.energyDelegateTrx});
        delegateTips.value = ID.stringOtherGetResource.trParams({
          'value': txValue ?? '--',
          'resource': ID.stringBandwidth.tr,
        });
      }
    } else if (inputType == InputResourceType.resource) {
      delegateTitle.value = ID.stringDelegateAmount.tr;
      delegateTips.value =
          ID.stringUseStakeTrx.trParams({'value': txValue ?? '--'});
      delegateAmountHintText.value = ID.stringDelegateAmountTip.tr;

      if (type == TronResourceType.energy) {
        delegateValue.value = ID.stringCanDelegateEnergy
            .trParams({'value': managerController.energyDelegate.value});
      } else if (type == TronResourceType.bandwidth) {
        delegateValue.value = ID.stringCanDelegateBandwidth
            .trParams({'value': managerController.bandwidthDelegate.value});
      }
    }
  }

  void updateStatus() {
    buttonStatus.value = (addressResult == true && amountResult == true)
        ? ButtonStatus.enable
        : ButtonStatus.disable;
  }

  void _showPop() {
    TransferInfoDialog(
      tsModel: tsModel,
      coinModel: managerController.coinModel,
      addressModel: managerController.addressModel,
      mainCoinModel: managerController.coinModel,
      onTap: () {
        if (managerController.wallet is TouchWallet) {
          Get.toNamed(AppRoutes.touchSignPage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.walletModel: managerController.walletModel,
          });
        } else {
          Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.qrType: QRCodeType.transfer
          });
        }
      },
    ).showBottomSheet();
  }

  @override
  void loadData() {
    buttonStatus.value = ButtonStatus.loading;

    String chain = managerController.coinModel.chain!;
    String resource = type == TronResourceType.energy
        ? APIConstant.energy
        : APIConstant.bandWidth;

    String amount = tsModel.amount!;
    amount = amount
        .mul(pow(10, managerController.coinModel.chainDecimal!).toString());
    int balance = DecimalUtils.toIntSafe(amount);

    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.createDelegateResource,
          requestParams: RequestParams()
              .put(APIConstant.chain, chain)
              .put(APIConstant.params, {
            APIConstant.resource: resource,
            APIConstant.balance: balance,
            APIConstant.lock: false,
            APIConstant.lockPeriod: 0,
            APIConstant.ownerAddress: managerController.addressModel.address!,
            APIConstant.receiverAddress: addressController.text,
          }).getRequestBody(),
        )),
        handleSuccess: false,
        handleError: false, (value) async {
      dynamic data = value.data;
      if (data is String) {
        Get.showToast(data, toastMode: ToastMode.failed);
      } else if (data is Map) {
        String? rawDataHex = data[GetArgumentsKey.rawDataHex] as String?;
        Map<String, dynamic>? rawData =
            data[GetArgumentsKey.rawData] as Map<String, dynamic>?;
        String? txID = data[GetArgumentsKey.txID] as String?;
        if (Get.isEmptyString(txID)) {
          Get.isTronResoueceResponseDataValid(data, isShowToast: true);
        } else {
          bool result = await Get.walletCore.verityTronRawDataHex(
                rawDataHex: rawDataHex ?? '',
                fromAddress: managerController.addressModel.address!,
                toAddress: addressController.text,
                amount: tsModel.amount!,
                decimal: managerController.coinModel.chainDecimal!,
              ) ??
              false;
          if (result == false) {
            Get.showToast(ID.stringTronRawDataHexError.tr,
                toastMode: ToastMode.failed);
          } else {
            tsModel.rawDataHex = rawDataHex;
            tsModel.rawData = jsonEncode(rawData!);
            tsModel.txID = txID;
            _showPop();
          }
        }
      }
      buttonStatus.value = ButtonStatus.enable;
    }, error: (_) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      buttonStatus.value = ButtonStatus.enable;
    });
  }
}

class TronDelegateBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TronDelegateController());
  }
}
