/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-01 11:23:28
 */
import 'dart:convert';
import 'dart:math';

import 'package:big_decimal/big_decimal.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_reclaim_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_delegate_model/tron_delegate_model.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_amount_widget.dart';
import 'package:coinbag/modules/wallet/send/dialog/transfer_info_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

class TronReclaimControlelr extends BaseController<BlockChainService> {
  late TronDelegateModel model;
  late TronReclaimManagerControlelr ctr;
  late TronResourceType type;
  Rx<ButtonStatus> buttonStatus = ButtonStatus.disable.obs;

  TextEditingController amountController = TextEditingController();
  InputResourceType inputType = InputResourceType.resource;

  RxString hintText = ''.obs;
  RxString inputTitle = ''.obs;
  RxString inputValue = ''.obs;
  RxString inputTips = ''.obs;
  RxString amountErrorTips = ''.obs;

  late TransferModel tsModel;

  @override
  void onInit() {
    model = Get.arguments[GetArgumentsKey.model];
    ctr = Get.arguments[GetArgumentsKey.controller];
    type = ctr.type;

    CoinModel coinModel = ctr.ctr.coinModel;
    AddressModel addressModel = ctr.ctr.addressModel;

    tsModel = TransferModel(
      chain: coinModel.chain!,
      type: TransferType.reclaim,
      fromAddress: addressModel.address,
      toAddress: model.receiverAddress,
      wallet: ctr.ctr.wallet,
      addressPath: addressModel.path,
      coinSymbol: coinModel.symbol,
      coinDecimal: coinModel.chainDecimal,
      walletId: ctr.ctr.walletModel.walletId,
    )..resourceType = type;

    updateAmount(InputResourceType.resource);
    super.onInit();
  }

  void updateAmount(InputResourceType type) {
    inputType = type;

    String amount = amountController.text;
    if (Get.isEmptyString(amount)) {
      amount = '0';
    }

    String cost = '';
    String resourceTitle = '';
    if (this.type == TronResourceType.energy) {
      cost = (ctr.ctr.accountInfo?.energyCost ?? 1).toString();
      resourceTitle = ID.stringTxEnergy.tr;
    } else {
      cost = (ctr.ctr.accountInfo?.netCost ?? 1).toString();
      resourceTitle = ID.stringTxBandwidth.tr;
    }

    if (type == InputResourceType.resource) {
      inputTitle.value = ID.stringReclaimAmount1.tr;
      hintText.value = ID.stringInputReclaimHint1.tr;
      inputValue.value = ID.stringCanReclaimValue1.trParams({
        'value': model.canReclaimValue,
        'resource': resourceTitle,
      });

      if (amount.moreThan(model.canReclaimValue)) {
        amountErrorTips.value = ID.stringResourceMaxTip.tr;
      } else {
        amountErrorTips.value = '';
      }

      String trxValue = DecimalUtils.divide(amount, cost,
          roundingMode: RoundingMode.UP, scale: 0);
      if (Get.isEmptyString(trxValue)) {
        trxValue = amount;
      }
      if (amount.equal('0')) {
        trxValue = '--';
      }

      inputTips.value = ID.stringInputReclaimTip1.trParams({
        'value': trxValue,
      });

      tsModel.amount = trxValue;
      tsModel.resourceAmount = amount;
    } else {
      inputTitle.value = ID.stringReclaimAmount2.tr;
      hintText.value = ID.stringInputReclaimHint2.tr;
      inputValue.value = ID.stringCanReclaimValue2.trParams({
        'value': model.balanceValue,
      });

      if (amount.moreThan(model.balanceValue)) {
        amountErrorTips.value = ID.stringResourceMaxTip.tr;
      } else {
        amountErrorTips.value = '';
      }

      String resourceValue =
          amount.mul(cost).decimal(scale: 0, roundMode: RoundMode.down);
      if (amount.equal('0')) {
        resourceValue = '--';
      }

      inputTips.value = ID.stringInputReclaimTip2.trParams({
        'value': resourceValue,
        'resource': resourceTitle,
      });

      tsModel.amount = amount;
      tsModel.resourceAmount = resourceValue;
    }

    if (Get.isEmptyString(amountErrorTips.value) && amount.moreThan('0')) {
      buttonStatus.value = ButtonStatus.enable;
    } else {
      buttonStatus.value = ButtonStatus.disable;
    }
  }

  void _showPop() {
    CoinModel coinModel = ctr.ctr.coinModel;
    AddressModel addressModel = ctr.ctr.addressModel;

    TransferInfoDialog(
      tsModel: tsModel,
      coinModel: coinModel,
      addressModel: addressModel,
      mainCoinModel: coinModel,
      onTap: () {
        if (ctr.ctr.wallet is TouchWallet) {
          Get.toNamed(AppRoutes.touchSignPage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.walletModel: ctr.ctr.walletModel,
          });
        } else {
          Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.qrType: QRCodeType.transfer
          });
        }
      },
    ).showBottomSheet();
  }

  @override
  void loadData() {
    buttonStatus.value = ButtonStatus.loading;

    CoinModel coinModel = ctr.ctr.coinModel;
    AddressModel addressModel = ctr.ctr.addressModel;

    String resource = type == TronResourceType.energy
        ? APIConstant.energy
        : APIConstant.bandWidth;

    String amount = tsModel.amount!;
    amount = amount.mul(pow(10, coinModel.chainDecimal!).toString());
    int balance = DecimalUtils.toIntSafe(amount);

    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.reclaimTxResource,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel.chain)
              .put(APIConstant.params, {
            APIConstant.resource: resource,
            APIConstant.balance: balance,
            APIConstant.ownerAddress: addressModel.address!,
            APIConstant.receiverAddress: model.receiverAddress,
          }).getRequestBody(),
        )),
        handleSuccess: false,
        handleError: false, (value) async {
      dynamic data = value.data;
      if (data is String) {
        Get.showToast(data, toastMode: ToastMode.failed);
      } else if (data is Map) {
        String? rawDataHex = data[GetArgumentsKey.rawDataHex] as String?;
        Map<String, dynamic>? rawData =
            data[GetArgumentsKey.rawData] as Map<String, dynamic>?;
        String? txID = data[GetArgumentsKey.txID] as String?;
        if (Get.isEmptyString(txID)) {
          Get.isTronResoueceResponseDataValid(data, isShowToast: true);
        } else {
          bool result = await Get.walletCore.verityTronRawDataHex(
                rawDataHex: rawDataHex ?? '',
                fromAddress: addressModel.address!,
                toAddress: tsModel.toAddress!,
                amount: tsModel.amount!,
                decimal: coinModel.chainDecimal!,
              ) ??
              false;
          if (result == false) {
            Get.showToast(ID.stringTronRawDataHexError.tr,
                toastMode: ToastMode.failed);
          } else {
            tsModel.rawDataHex = rawDataHex;
            tsModel.rawData = jsonEncode(rawData!);
            tsModel.txID = txID;
            _showPop();
          }
        }
      }
      buttonStatus.value = ButtonStatus.enable;
    }, error: (_) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      buttonStatus.value = ButtonStatus.enable;
    });
  }
}

class TronReclaimBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TronReclaimControlelr());
  }
}
