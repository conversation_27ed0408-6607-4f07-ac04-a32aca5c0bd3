// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resource_detail.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResourceDetail _$ResourceDetailFromJson(Map<String, dynamic> json) =>
    ResourceDetail(
      delegatedByOthersBandwidth:
          json['delegated_by_others_bandwidth'] as String?,
      delegatedByOthersEnergy: json['delegated_by_others_energy'] as String?,
      delegatedToOthersBandwidth:
          json['delegated_to_others_bandwidth'] as String?,
      delegatedToOthersEnergy: json['delegated_to_others_energy'] as String?,
      fromStakingBandwidth: json['from_staking_bandwidth'] as String?,
      fromStakingEnergy: json['from_staking_energy'] as String?,
    );

Map<String, dynamic> _$ResourceDetailToJson(ResourceDetail instance) =>
    <String, dynamic>{
      'delegated_by_others_bandwidth': instance.delegatedByOthersBandwidth,
      'delegated_by_others_energy': instance.delegatedByOthersEnergy,
      'delegated_to_others_bandwidth': instance.delegatedToOthersBandwidth,
      'delegated_to_others_energy': instance.delegatedToOthersEnergy,
      'from_staking_bandwidth': instance.fromStakingBandwidth,
      'from_staking_energy': instance.fromStakingEnergy,
    };
