/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-13 13:53:24
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/resource_detail.dart';
import 'package:coinbag/res/resource.dart';
import 'package:json_annotation/json_annotation.dart';

part 'tron_resource.g.dart';

@JsonSerializable()
class TronResource {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnergyLimit')
  int? energyLimit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'EnergyUsed')
  int? energyUsed;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'NetLimit')
  int? netLimit;
  @J<PERSON><PERSON><PERSON>(name: 'NetUsed')
  int? netUsed;
  int? freeNetUsed;
  @Json<PERSON><PERSON>(name: 'TotalEnergyLimit')
  int? totalEnergyLimit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TotalEnergyWeight')
  int? totalEnergyWeight;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'TotalNetLimit')
  int? totalNetLimit;
  @J<PERSON><PERSON><PERSON>(name: 'TotalNetWeight')
  int? totalNetWeight;
  // List<AssetNetLimit>? assetNetLimit;
  // List<AssetNetUsed>? assetNetUsed;
  int? freeNetLimit;
  int? tronPowerLimit;

  @JsonKey(name: 'resource_detail', toJson: _toJson)
  ResourceDetail? resourceDetail;

  TronResource({
    this.energyLimit,
    this.netLimit,
    this.totalEnergyLimit,
    this.totalEnergyWeight,
    this.totalNetLimit,
    this.totalNetWeight,
    this.freeNetLimit,
    this.tronPowerLimit,
    this.resourceDetail,
  });

  factory TronResource.fromJson(Map<String, dynamic> json) {
    return _$TronResourceFromJson(json);
  }

  static Map<String, dynamic>? _toJson(ResourceDetail? resourceDetail) =>
      resourceDetail?.toJson();
  Map<String, dynamic> toJson() => _$TronResourceToJson(this);

  /// 总带宽
  int get totalNet => (netLimit ?? 0) + (freeNetLimit ?? 0);

  /// 不可用带宽
  int get unAvailableNet => (freeNetUsed ?? 0) + (netUsed ?? 0);

  /// 可用带宽
  int get availableNet => totalNet - unAvailableNet;

  /// 可用带宽百分比
  double get netRate => totalNet == 0 ? 0 : availableNet / totalNet;

  /// 总能量
  int get totalEnergy => (energyLimit ?? 0);

  /// 不可用能量能量
  int get unAvailableEnergy => energyUsed ?? 0;

  /// 可用能量
  int get availableEnergy => totalEnergy - unAvailableEnergy;

  /// 可用能量百分比
  double get energyRate => totalEnergy == 0 ? 0 : availableEnergy / totalEnergy;

  /// 质押获得的能量
  String get fromStakingEnergyValue {
    if (Get.isEmptyString(resourceDetail?.fromStakingEnergyValue)) {
      return '-';
    }
    return '+${resourceDetail!.fromStakingEnergyValue}';
  }

  /// 质押获得的带宽
  String get fromStakingBandwidthValue {
    if (Get.isEmptyString(resourceDetail?.fromStakingBandwidthValue)) {
      return '-';
    }
    return '+${resourceDetail!.fromStakingBandwidthValue}';
  }

  /// 他人代理给自己的能量
  String get delegatedByOthersEnergyValue {
    if (Get.isEmptyString(resourceDetail?.delegatedByOthersEnergyValue)) {
      return '-';
    }
    return '+${resourceDetail!.delegatedByOthersEnergyValue}';
  }

  /// 他人代理给自己的带宽
  String get delegatedByOthersBandwidthValue {
    if (Get.isEmptyString(resourceDetail?.delegatedByOthersBandwidthValue)) {
      return '-';
    }
    return '+${resourceDetail!.delegatedByOthersBandwidthValue}';
  }

  /// 代理给他人的能量
  String get delegatedToOthersEnergyValue {
    if (Get.isEmptyString(resourceDetail?.delegatedToOthersEnergyValue)) {
      return '-';
    }
    return '-${resourceDetail!.delegatedToOthersEnergyValue}';
  }

  /// 代理给他人的带宽
  String get delegatedToOthersBandwidthValue {
    if (Get.isEmptyString(resourceDetail?.delegatedToOthersBandwidthValue)) {
      return '-';
    }
    return '-${resourceDetail!.delegatedToOthersBandwidthValue}';
  }
}
