import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:json_annotation/json_annotation.dart';

part 'resource_detail.g.dart';

@JsonSerializable()
class ResourceDetail {
  @Json<PERSON>ey(name: 'delegated_by_others_bandwidth')
  String? delegatedByOthersBandwidth;
  @JsonKey(name: 'delegated_by_others_energy')
  String? delegatedByOthersEnergy;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'delegated_to_others_bandwidth')
  String? delegatedToOthersBandwidth;
  @J<PERSON><PERSON>ey(name: 'delegated_to_others_energy')
  String? delegatedToOthersEnergy;
  @JsonKey(name: 'from_staking_bandwidth')
  String? fromStakingBandwidth;
  @JsonKey(name: 'from_staking_energy')
  String? fromStakingEnergy;

  ResourceDetail({
    this.delegatedByOthersBandwidth,
    this.delegatedByOthersEnergy,
    this.delegatedToOthersBandwidth,
    this.delegatedToOthersEnergy,
    this.fromStakingBandwidth,
    this.fromStakingEnergy,
  });

  factory ResourceDetail.fromJson(Map<String, dynamic> json) {
    return _$ResourceDetailFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ResourceDetailToJson(this);

  /// 质押获得的能量
  String get fromStakingEnergyValue {
    if (Get.isEmptyString(fromStakingEnergy)) {
      return '0';
    }
    return fromStakingEnergy!;
  }

  /// 质押获得的带宽
  String get fromStakingBandwidthValue {
    if (Get.isEmptyString(fromStakingBandwidth)) {
      return '0';
    }
    return fromStakingBandwidth!;
  }

  /// 他人代理给自己的能量
  String get delegatedByOthersEnergyValue {
    if (Get.isEmptyString(delegatedByOthersEnergy)) {
      return '0';
    }
    return delegatedByOthersEnergy!;
  }

  /// 他人代理给自己的带宽
  String get delegatedByOthersBandwidthValue {
    if (Get.isEmptyString(delegatedByOthersBandwidth)) {
      return '0';
    }
    return delegatedByOthersBandwidth!;
  }

  /// 代理给他人的能量
  String get delegatedToOthersEnergyValue {
    if (Get.isEmptyString(delegatedToOthersEnergy)) {
      return '0';
    }
    return delegatedToOthersEnergy!;
  }

  /// 代理给他人的带宽
  String get delegatedToOthersBandwidthValue {
    if (Get.isEmptyString(delegatedToOthersBandwidth)) {
      return '0';
    }
    return delegatedToOthersBandwidth!;
  }
}
