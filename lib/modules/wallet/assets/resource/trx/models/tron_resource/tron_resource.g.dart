// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tron_resource.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TronResource _$TronResourceFromJson(Map<String, dynamic> json) => TronResource(
      energyLimit: (json['EnergyLimit'] as num?)?.toInt(),
      netLimit: (json['NetLimit'] as num?)?.toInt(),
      totalEnergyLimit: (json['TotalEnergyLimit'] as num?)?.toInt(),
      totalEnergyWeight: (json['TotalEnergyWeight'] as num?)?.toInt(),
      totalNetLimit: (json['TotalNetLimit'] as num?)?.toInt(),
      totalNetWeight: (json['TotalNetWeight'] as num?)?.toInt(),
      freeNetLimit: (json['freeNetLimit'] as num?)?.toInt(),
      tronPowerLimit: (json['tronPowerLimit'] as num?)?.toInt(),
      resourceDetail: json['resource_detail'] == null
          ? null
          : ResourceDetail.fromJson(
              json['resource_detail'] as Map<String, dynamic>),
    )
      ..energyUsed = (json['EnergyUsed'] as num?)?.toInt()
      ..netUsed = (json['NetUsed'] as num?)?.toInt()
      ..freeNetUsed = (json['freeNetUsed'] as num?)?.toInt();

Map<String, dynamic> _$TronResourceToJson(TronResource instance) =>
    <String, dynamic>{
      'EnergyLimit': instance.energyLimit,
      'EnergyUsed': instance.energyUsed,
      'NetLimit': instance.netLimit,
      'NetUsed': instance.netUsed,
      'freeNetUsed': instance.freeNetUsed,
      'TotalEnergyLimit': instance.totalEnergyLimit,
      'TotalEnergyWeight': instance.totalEnergyWeight,
      'TotalNetLimit': instance.totalNetLimit,
      'TotalNetWeight': instance.totalNetWeight,
      'freeNetLimit': instance.freeNetLimit,
      'tronPowerLimit': instance.tronPowerLimit,
      'resource_detail': TronResource._toJson(instance.resourceDetail),
    };
