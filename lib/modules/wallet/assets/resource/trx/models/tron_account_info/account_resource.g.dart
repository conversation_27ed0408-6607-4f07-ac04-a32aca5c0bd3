// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_resource.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AccountResource _$AccountResourceFromJson(Map<String, dynamic> json) =>
    AccountResource(
      acquiredDelegatedFrozenBalanceForEnergy:
          (json['acquired_delegated_frozen_balance_for_energy'] as num?)
              ?.toInt(),
      delegatedFrozenV2BalanceForEnergy:
          (json['delegated_frozenV2_balance_for_energy'] as num?)?.toInt(),
      energyWindowOptimized: json['energy_window_optimized'] as bool?,
      energyWindowSize: (json['energy_window_size'] as num?)?.toInt(),
      latestConsumeTimeForEnergy:
          (json['latest_consume_time_for_energy'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AccountResourceToJson(AccountResource instance) =>
    <String, dynamic>{
      'acquired_delegated_frozen_balance_for_energy':
          instance.acquiredDelegatedFrozenBalanceForEnergy,
      'delegated_frozenV2_balance_for_energy':
          instance.delegatedFrozenV2BalanceForEnergy,
      'energy_window_optimized': instance.energyWindowOptimized,
      'energy_window_size': instance.energyWindowSize,
      'latest_consume_time_for_energy': instance.latestConsumeTimeForEnergy,
    };
