// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tron_account_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TronAccountInfo _$TronAccountInfoFromJson(Map<String, dynamic> json) =>
    TronAccountInfo(
      accountName: json['account_name'] as String?,
      accountResource: json['account_resource'] == null
          ? null
          : AccountResource.fromJson(
              json['account_resource'] as Map<String, dynamic>),
      activated: json['activated'] as bool?,
      address: json['address'] as String?,
      assetOptimized: json['asset_optimized'] as bool?,
      balance: (json['balance'] as num?)?.toInt(),
      createTime: (json['create_time'] as num?)?.toInt(),
      delegated: json['delegated'],
      energyCost: (json['energyCost'] as num?)?.toDouble(),
      frozenV1: (json['frozenV1'] as List<dynamic>?)
          ?.map((e) => Frozen.fromJson(e as Map<String, dynamic>))
          .toList(),
      frozenV2: (json['frozenV2'] as List<dynamic>?)
          ?.map((e) => Frozen.fromJson(e as Map<String, dynamic>))
          .toList(),
      latestConsumeFreeTime:
          (json['latest_consume_free_time'] as num?)?.toInt(),
      latestOprationTime: (json['latest_opration_time'] as num?)?.toInt(),
      netCost: (json['netCost'] as num?)?.toDouble(),
      netWindowOptimized: json['net_window_optimized'] as bool?,
      netWindowSize: (json['net_window_size'] as num?)?.toInt(),
      withdraw: (json['withdraw'] as num?)?.toInt(),
      unfrozenV2: (json['unfrozenV2'] as List<dynamic>?)
          ?.map((e) => Frozen.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TronAccountInfoToJson(TronAccountInfo instance) =>
    <String, dynamic>{
      'account_name': instance.accountName,
      'account_resource': instance.accountResource,
      'activated': instance.activated,
      'address': instance.address,
      'asset_optimized': instance.assetOptimized,
      'balance': instance.balance,
      'create_time': instance.createTime,
      'delegated': instance.delegated,
      'energyCost': instance.energyCost,
      'frozenV1': instance.frozenV1,
      'frozenV2': instance.frozenV2,
      'latest_consume_free_time': instance.latestConsumeFreeTime,
      'latest_opration_time': instance.latestOprationTime,
      'netCost': instance.netCost,
      'net_window_optimized': instance.netWindowOptimized,
      'net_window_size': instance.netWindowSize,
      'withdraw': instance.withdraw,
      'unfrozenV2': instance.unfrozenV2,
    };
