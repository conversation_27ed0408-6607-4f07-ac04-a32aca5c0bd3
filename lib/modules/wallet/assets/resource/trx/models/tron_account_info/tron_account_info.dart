/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-20 16:20:32
 */
import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

import 'account_resource.dart';
import 'frozen.dart';

part 'tron_account_info.g.dart';

@JsonSerializable()
class TronAccountInfo {
  @Json<PERSON>ey(name: 'account_name')
  String? accountName;
  @Json<PERSON>ey(name: 'account_resource')
  AccountResource? accountResource;
  bool? activated;
  String? address;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'asset_optimized')
  bool? assetOptimized;
  int? balance;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'create_time')
  int? createTime;
  dynamic delegated;
  double? energyCost;
  // List<Frozen>? frozen;
  List<Frozen>? frozenV1;
  List<Frozen>? frozenV2;
  @Json<PERSON>ey(name: 'latest_consume_free_time')
  int? latestConsumeFreeTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_opration_time')
  int? latestOprationTime;
  double? netCost;
  @J<PERSON><PERSON><PERSON>(name: 'net_window_optimized')
  bool? netWindowOptimized;
  @J<PERSON><PERSON><PERSON>(name: 'net_window_size')
  int? netWindowSize;
  int? withdraw;
  List<Frozen>? unfrozenV2;

  TronAccountInfo({
    this.accountName,
    this.accountResource,
    this.activated,
    this.address,
    this.assetOptimized,
    this.balance,
    this.createTime,
    this.delegated,
    this.energyCost,
    this.frozenV1,
    this.frozenV2,
    this.latestConsumeFreeTime,
    this.latestOprationTime,
    this.netCost,
    this.netWindowOptimized,
    this.netWindowSize,
    this.withdraw,
    this.unfrozenV2,
  });

  factory TronAccountInfo.fromJson(Map<String, dynamic> json) {
    return _$TronAccountInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TronAccountInfoToJson(this);

  /// V1 冻结总数量
  String get frozenV1Amount => frozenV1Energy.add(frozenV1Bandwidth);

  /// V1 能量冻结数量
  String get frozenV1Energy => _frozenAmount(frozenV1 ?? <Frozen>[], 'ENERGY');

  /// V1 带宽冻结数量
  String get frozenV1Bandwidth =>
      _frozenAmount(frozenV1 ?? <Frozen>[], 'BandWidth');

  /// V2 冻结总数量
  String get frozenV2Amount =>
      frozenV2Energy.add(frozenV2Bandwidth).add(unfrozenV2Amount);

  /// V2 能量冻结数量
  String get frozenV2Energy => _frozenAmount(frozenV2 ?? <Frozen>[], 'ENERGY');

  /// V2 带宽冻结数量
  String get frozenV2Bandwidth =>
      _frozenAmount(frozenV2 ?? <Frozen>[], 'BandWidth');

  /// 可提取
  String get withdrawValue {
    if (withdraw == null) return '0';
    return withdraw!
        .toString()
        .div(_decimal, scale: 6, roundMode: RoundMode.down);
  }

  String get balanceValue {
    if (balance == null) return '0';
    return balance!
        .toString()
        .div(_decimal, scale: 6, roundMode: RoundMode.down);
  }

  /// V2 解锁中数量
  String get unfrozenV2Amount {
    String amount = '0';
    for (var e in unfrozenV2 ?? <Frozen>[]) {
      amount = amount.add(e.unfreezeValue);
    }
    return amount.div(_decimal, scale: 6, roundMode: RoundMode.down);
  }

  String _frozenAmount(List<Frozen> data, String type) {
    String amount = '0';
    for (var e in data) {
      if (e.type == type) {
        amount = amount.add(e.amountValue);
      }
    }
    return amount.div(_decimal, scale: 6, roundMode: RoundMode.down);
  }

  String get _decimal => '1000000';
}
