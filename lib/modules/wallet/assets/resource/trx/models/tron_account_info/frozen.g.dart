// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'frozen.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Frozen _$FrozenFromJson(Map<String, dynamic> json) => Frozen(
      amount: (json['amount'] as num?)?.toInt(),
      type: json['type'] as String?,
      unfreezeAmount: (json['unfreeze_amount'] as num?)?.toInt(),
      unfreezeExpireDay: (json['unfreeze_expire_day'] as num?)?.toInt(),
    );

Map<String, dynamic> _$Frozen<PERSON>o<PERSON>son(Frozen instance) => <String, dynamic>{
      'amount': instance.amount,
      'type': instance.type,
      'unfreeze_amount': instance.unfreezeAmount,
      'unfreeze_expire_day': instance.unfreezeExpireDay,
    };
