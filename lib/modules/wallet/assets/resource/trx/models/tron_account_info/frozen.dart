/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 09:58:24
 */
import 'package:json_annotation/json_annotation.dart';

part 'frozen.g.dart';

@JsonSerializable()
class Frozen {
  int? amount;
  String? type;

  @Json<PERSON>ey(name: 'unfreeze_amount')
  int? unfreezeAmount;
  @Json<PERSON>ey(name: 'unfreeze_expire_day')
  int? unfreezeExpireDay;

  Frozen({this.amount, this.type, this.unfreezeAmount, this.unfreezeExpireDay});

  factory Frozen.fromJson(Map<String, dynamic> json) {
    return _$FrozenFromJson(json);
  }

  Map<String, dynamic> toJson() => _$FrozenToJson(this);

  String get amountValue {
    if (amount == null) return '0';
    return amount!.toString();
  }

  String get unfreezeValue {
    if (unfreezeAmount == null) return '0';
    return unfreezeAmount!.toString();
  }

  int get unfreezeDay => unfreezeExpireDay ?? 0;
}
