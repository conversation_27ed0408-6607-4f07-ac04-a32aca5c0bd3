import 'package:json_annotation/json_annotation.dart';

part 'account_resource.g.dart';

@JsonSerializable()
class AccountResource {
  @JsonKey(name: 'acquired_delegated_frozen_balance_for_energy')
  int? acquiredDelegatedFrozenBalanceForEnergy;
  @Json<PERSON>ey(name: 'delegated_frozenV2_balance_for_energy')
  int? delegatedFrozenV2BalanceForEnergy;
  @JsonKey(name: 'energy_window_optimized')
  bool? energyWindowOptimized;
  @JsonKey(name: 'energy_window_size')
  int? energyWindowSize;
  @JsonKey(name: 'latest_consume_time_for_energy')
  int? latestConsumeTimeForEnergy;

  AccountResource({
    this.acquiredDelegatedFrozenBalanceForEnergy,
    this.delegatedFrozenV2BalanceForEnergy,
    this.energyWindowOptimized,
    this.energyWindowSize,
    this.latestConsumeTimeForEnergy,
  });

  factory AccountResource.fromJson(Map<String, dynamic> json) {
    return _$AccountResourceFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AccountResourceToJson(this);
}
