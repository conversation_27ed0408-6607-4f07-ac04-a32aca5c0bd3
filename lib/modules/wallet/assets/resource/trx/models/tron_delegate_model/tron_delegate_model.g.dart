// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tron_delegate_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TronDelegateModel _$TronDelegateModelFromJson(Map<String, dynamic> json) =>
    TronDelegateModel(
      balance: (json['balance'] as num?)?.toInt(),
      expireTime: (json['expireTime'] as num?)?.toInt(),
      lockBalance: (json['lockBalance'] as num?)?.toInt(),
      lockResourceValue: (json['lockResourceValue'] as num?)?.toInt(),
      operationTime: (json['operationTime'] as num?)?.toInt(),
      ownerAddress: json['ownerAddress'] as String?,
      receiverAddress: json['receiverAddress'] as String?,
      resource: (json['resource'] as num?)?.toInt(),
      resourceValue: (json['resourceValue'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$TronDelegateModelToJson(TronDelegateModel instance) =>
    <String, dynamic>{
      'balance': instance.balance,
      'expireTime': instance.expireTime,
      'lockBalance': instance.lockBalance,
      'lockResourceValue': instance.lockResourceValue,
      'operationTime': instance.operationTime,
      'ownerAddress': instance.ownerAddress,
      'receiverAddress': instance.receiverAddress,
      'resource': instance.resource,
      'resourceValue': instance.resourceValue,
    };
