/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-18 16:55:54
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

part 'tron_delegate_model.g.dart';

@JsonSerializable()
class TronDelegateModel {
  int? balance;
  int? expireTime;
  int? lockBalance;
  int? lockResourceValue;
  int? operationTime;
  String? ownerAddress;
  String? receiverAddress;
  int? resource;
  double? resourceValue;

  TronDelegateModel({
    this.balance,
    this.expireTime,
    this.lockBalance,
    this.lockResourceValue,
    this.operationTime,
    this.ownerAddress,
    this.receiverAddress,
    this.resource,
    this.resourceValue,
  });

  factory TronDelegateModel.fromJson(Map<String, dynamic> json) {
    return _$TronDelegateModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TronDelegateModelToJson(this);

  /// 可回收
  String get canReclaimValue {
    return '${resourceValue ?? 0}'.decimal(scale: 0, roundMode: RoundMode.down);
  }

  String get balanceValue {
    if (Get.isEmptyString('$balance')) {
      return '0';
    } else {
      return balance!.toString().div('1000000');
    }
  }
}
