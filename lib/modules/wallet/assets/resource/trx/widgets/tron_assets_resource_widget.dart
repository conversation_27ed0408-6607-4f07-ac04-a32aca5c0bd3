/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-14 15:24:20
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/tron_resource.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TronAssetsResourceWidget extends StatelessWidget {
  final TronResource? tronResource;
  final CoinModel coinModel;
  final AddressModel addressModel;
  final TronAccountInfo accountInfo;
  const TronAssetsResourceWidget({
    super.key,
    required this.coinModel,
    required this.addressModel,
    required this.tronResource,
    required this.accountInfo,
  });

  void _action(TronResourceType type) {
    Get.toNamed(
      AppRoutes.tronResourceManagerPage,
      arguments: {
        GetArgumentsKey.tronResourceType: type,
        GetArgumentsKey.tronResource: tronResource,
        GetArgumentsKey.coinModel: coinModel,
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.tronAccountInfo: accountInfo,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Get.setPaddingSize(10),
        horizontal: Get.setPaddingSize(16),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(12),
          horizontal: Get.setPaddingSize(16),
        ),
        decoration: BoxDecoration(
          color: Get.theme.colorF9F9F9,
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _tronSourceWidget(
              title: ID.stringEnergy.tr,
              color: Get.theme.color02B58A,
              available: tronResource?.availableEnergy ?? 0,
              total: tronResource?.totalEnergy ?? 0,
              callback: () => _action(TronResourceType.energy),
            ),
            SizedBox(width: Get.setPaddingSize(16)),
            _tronSourceWidget(
              title: ID.stringBandwidth.tr,
              color: Get.theme.colorFF6A16,
              available: tronResource?.availableNet ?? 0,
              total: tronResource?.totalNet ?? 0,
              callback: () => _action(TronResourceType.bandwidth),
            ),
          ],
        ),
      ),
    );
  }

  Expanded _tronSourceWidget({
    required String title,
    required Color color,
    required int available,
    required int total,
    required Function() callback,
  }) {
    return Expanded(
      flex: 1,
      child: GestureDetector(
        onTap: callback,
        behavior: HitTestBehavior.translucent,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(12),
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(2)),
                Expanded(
                  child: Text(
                    '$available / $total',
                    maxLines: 2,
                    textAlign: TextAlign.end,
                    style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(12),
                      fontFamily: Get.setNumberFontFamily(),
                    ),
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(2)),
                ImageWidget(
                  assetUrl: 'icon_new_arrow01',
                  width: Get.setImageSize(10),
                  height: Get.setImageSize(10),
                ),
              ],
            ),
            SizedBox(height: Get.setPaddingSize(4)),
            LinearProgressIndicator(
                minHeight: Get.setHeight(3),
                value: _rate(available, total),
                backgroundColor: Get.theme.colorECECEC,
                valueColor: AlwaysStoppedAnimation(color),
                borderRadius:
                    BorderRadius.all(Radius.circular(Get.setRadius(1.5))))
          ],
        ),
      ),
    );
  }

  double _rate(int available, int total) {
    if (total <= 0) return 0;
    String str = available.toString().div(total.toString(), scale: 2);
    return double.parse(str);
  }
}
