import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';

enum InputResourceType { trx, resource }

class ResourceAmountWidget extends StatefulWidget {
  final TronResourceType resourceType;
  final TransferType tsType;
  final Function(InputResourceType type) action;
  const ResourceAmountWidget({
    super.key,
    required this.resourceType,
    required this.action,
    this.tsType = TransferType.delegate,
  });

  @override
  State<ResourceAmountWidget> createState() => _DelegateAmountWidgetState();
}

class _DelegateAmountWidgetState extends State<ResourceAmountWidget> {
  InputResourceType type = InputResourceType.resource;

  @override
  void initState() {
    if (widget.tsType == TransferType.stake) {
      type = InputResourceType.trx;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Get.setPaddingSize(2)),
      decoration: BoxDecoration(
        color: Get.theme.colorF3F3F5,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              type = InputResourceType.trx;
              setState(() {});
              widget.action(type);
            },
            behavior: HitTestBehavior.translucent,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(6),
                vertical: Get.setPaddingSize(2),
              ),
              decoration: BoxDecoration(
                color: _color(InputResourceType.trx),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'TRX',
                style: TextStyle(
                  color: _titleColor(InputResourceType.trx),
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              type = InputResourceType.resource;
              setState(() {});
              widget.action(type);
            },
            behavior: HitTestBehavior.translucent,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(6),
                vertical: Get.setPaddingSize(2),
              ),
              decoration: BoxDecoration(
                color: _color(InputResourceType.resource),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                widget.resourceType == TronResourceType.energy
                    ? ID.stringEnergy.tr
                    : ID.stringBandwidth.tr,
                style: TextStyle(
                  color: _titleColor(InputResourceType.resource),
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _titleColor(InputResourceType type) {
    if (type == this.type) {
      return Get.theme.textPrimary;
    } else {
      return Get.theme.textSecondary;
    }
  }

  Color _color(InputResourceType type) {
    if (type == this.type) {
      return Get.theme.white;
    } else {
      return Colors.transparent;
    }
  }
}
