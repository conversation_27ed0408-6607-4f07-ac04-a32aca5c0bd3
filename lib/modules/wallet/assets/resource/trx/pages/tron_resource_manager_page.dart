/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-20 13:58:58
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/behavior/over_scroll_behavior.dart';
import 'package:coinbag/widgets/segment/segment_control_widget.dart';
import 'package:coinbag/widgets/sliver/sliver_app_bar_delegate.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TronResourceManagerPage
    extends BaseStatelessWidget<TronResourceManagerController> {
  const TronResourceManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringResourceManager.tr),
      body: ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: NestedScrollView(
            controller: controller.scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
              return <Widget>[
                SliverPersistentHeader(
                  pinned: true,
                  floating: false,
                  delegate: SliverAppBarDelegate(
                    minHeight: Get.setHeight(44),
                    maxHeight: Get.setHeight(44),
                    child: Container(
                      color: Get.theme.bgColor,
                      child: Center(
                        child: SegmentControlWidget(
                          values: controller.tabList,
                          controller: controller.segmentedController,
                          onChanged: (index) {
                            controller.pagerController.jumpToPage(index);
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ];
            },
            body: PageView.builder(
                itemCount: controller.tabList.length,
                onPageChanged: (index) {
                  controller.segmentedController.selectedIndex.value = index;
                },
                controller: controller.pagerController,
                itemBuilder: (_, int index) {
                  if (index == 0) {
                    controller.type = TronResourceType.energy;
                  } else {
                    controller.type = TronResourceType.bandwidth;
                  }
                  return controller.pagerList[index];
                })),
      ),
    );
  }
}
