/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-18 16:33:34
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_delegate_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_amount_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class TronDelegatePage extends BaseStatelessWidget<TronDelegateController> {
  const TronDelegatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => KeyboardDismissWidget(
          child: Scaffold(
            appBar: baseAppBar(title: ID.stringDelegateResource.tr),
            bottomNavigationBar: _bottomWidget(),
            body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _titleWidget(ID.stringResourceToAddress.tr),
                  SizedBox(height: Get.setPaddingSize(8)),
                  _addressWidget(),
                  SizedBox(height: Get.setPaddingSize(36)),
                  _delegateAmountWidget(),
                ],
              ),
            ),
          ),
        ));
  }

  Column _delegateAmountWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _delegateAmountTitleWidget(),
        SizedBox(height: Get.setPaddingSize(8)),
        TextFieldWidget(
          hintText: controller.delegateAmountHintText.value,
          maxLines: 1,
          controller: controller.amountController,
          keyboardType: const TextInputType.numberWithOptions(decimal: false),
          scale: controller.managerController.coinModel.chainDecimal,
          suffixIcon: ResourceAmountWidget(
            resourceType: controller.type,
            action: (delegateType) {
              controller.amountController.text = '';
              controller.updateDelegateValue(inputType: delegateType);
              controller.updateAmount();
            },
          ),
          onValueChanged: (value) => controller.updateAmount(),
        ),
        SizedBox(height: Get.setPaddingSize(4)),
        Text(
          controller.delegateTips.value,
          style: TextStyle(
            color: Get.theme.textSecondary,
            fontSize: Get.setFontSize(12),
          ),
        ),
        Visibility(
          visible: !Get.isEmptyString(controller.amountErrorTips.value),
          child: Padding(
            padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
            child: Text(
              controller.amountErrorTips.value,
              style: TextStyle(
                color: Get.theme.colorF44D4D,
                fontSize: Get.setFontSize(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Row _delegateAmountTitleWidget() {
    return Row(
      children: [
        _titleWidget(controller.delegateTitle.value),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          child: Text(
            controller.delegateValue.value,
            textAlign: TextAlign.end,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
            ),
          ),
        )
      ],
    );
  }

  Column _addressWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldWidget(
          hintText: ID.stringResourceToAddressTip.tr,
          maxLines: 3,
          controller: controller.addressController,
          suffixIcon: _addressSuffixIcon(),
          onValueChanged: (value) => controller.updateAddress(),
        ),
        _addressTipWidget(),
      ],
    );
  }

  Visibility _addressTipWidget() {
    return Visibility(
      visible: !Get.isEmptyString(controller.addressTip.value),
      child: Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
        child: Text(
          controller.addressTip.value,
          style: TextStyle(
            color: Get.theme.colorF44D4D,
            fontSize: Get.setFontSize(12),
          ),
        ),
      ),
    );
  }

  Text _titleWidget(String title) {
    return Text(
      title,
      style: styleSecond_14,
    );
  }

  Row _addressSuffixIcon() {
    return Row(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            KeyboardUtils.hideKeyboardNoContext();
            final result = await Get.toScanner(arguments: {
              GetArgumentsKey.scanAction: ScanAction.resultAction
            });

            if (!Get.isEmptyString(result)) {
              controller.addressController.text = result;
              controller.updateAddress();
            }
          },
          child: ImageWidget(
            assetUrl: 'icon_scan_black',
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
          ),
        ),
        Container(
          color: Get.theme.colorD3D3D3,
          margin: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
          height: Get.setHeight(14),
          width: Get.setWidth(1),
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            KeyboardUtils.hideKeyboardNoContext();
            final result =
                await Get.toNamed(AppRoutes.addressBookPage, arguments: {
              GetArgumentsKey.chain:
                  controller.managerController.coinModel.chain,
              GetArgumentsKey.walletModel:
                  controller.managerController.walletModel,
            });

            if (result != null) {
              controller.addressController.text = result;
              controller.updateAddress();
            }
          },
          child: ImageWidget(
            assetUrl: 'icon_contact',
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
          ),
        ),
      ],
    );
  }

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(ID.stringDelegateTip.tr, style: styleSecond_14),
          SizedBox(height: Get.setPaddingSize(8)),
          ButtonWidget(
            width: Get.width,
            text: ID.stringConfirm.tr,
            buttonStatus: controller.buttonStatus.value,
            onPressed: () => controller.delegateAction(),
          ),
        ],
      ),
    );
  }
}
