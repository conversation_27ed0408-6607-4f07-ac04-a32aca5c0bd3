/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-18 15:40:04
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_reclaim_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_delegate_model/tron_delegate_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TronReclaimManagerPage
    extends BaseStatelessWidget<TronReclaimManagerControlelr> {
  const TronReclaimManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: controller.type == TronResourceType.energy
            ? ID.stringEnergyManager.tr
            : ID.stringNetManager.tr,
      ),
      body: Padding(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _headerWidget(),
            SizedBox(height: Get.setPaddingSize(12)),
            Text(
              ID.stringDelegateToOther.tr,
              style: stylePrimary_16_m,
            ),
            Expanded(
              child: controller.obx(
                (state) => _bodyWidget(),
                onLoading: const TronReclaimListSkeletonWidget(),
                onError: (error) =>
                    AppErrorWidget(onRefresh: () => controller.onRefresh()),
                onEmpty: const AppEmptyWidget(),
              ),
            )
          ],
        ),
      ),
    );
  }

  RefreshWidget<TronReclaimManagerControlelr> _bodyWidget() {
    return RefreshWidget<TronReclaimManagerControlelr>(
      enablePullDown: true,
      enablePullUp: false,
      refreshController: controller.refreshController,
      child: ListView.builder(
          padding: EdgeInsets.only(
            top: Get.setPaddingSize(16),
            bottom: Get.getSafetyBottomPadding(),
          ),
          itemCount: controller.dataSource.length,
          itemBuilder: (_, index) {
            TronDelegateModel model = controller.dataSource[index];
            return Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _resourceValue(model.resourceValue),
                        style: TextStyle(
                          color: Get.theme.textPrimary,
                          fontSize: Get.setFontSize(18),
                          fontWeight: FontWeightX.medium,
                        ),
                      ),
                    ),
                    SizedBox(width: Get.setPaddingSize(8)),
                    _actionWidget(model),
                  ],
                ),
                SizedBox(height: Get.setPaddingSize(12)),
                _itemWidget(
                  title: ID.stringLockingResource.tr,
                  value: _resourceValue(model.lockResourceValue),
                ),
                SizedBox(height: Get.setPaddingSize(12)),
                _itemWidget(
                  title: ID.stringUseStakeAmount.tr,
                  value:
                      '${model.balanceValue} ${controller.ctr.coinModel.symbol}',
                ),
                SizedBox(height: Get.setPaddingSize(12)),
                _itemWidget(
                  title: ID.stringCanReclaimTitle.tr,
                  value: _resourceValue(
                    model.resourceValue,
                    roundMode: RoundMode.down,
                  ),
                ),
                SizedBox(height: Get.setPaddingSize(12)),
                _itemWidget(
                  title: ID.stringResourceToAddress.tr,
                  value: AddressUtils.omitAddress(model.receiverAddress),
                ),
                SizedBox(height: Get.setPaddingSize(12)),
                _itemWidget(
                  title: ID.stringDateTitle.tr,
                  value: DateHeleper.formatTimestamp(model.operationTime),
                ),
                DividerWidget(
                  padding:
                      EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
                ),
              ],
            );
          }),
    );
  }

  /// 资源
  String _resourceValue(dynamic resource, {RoundMode? roundMode}) {
    String value = '0';
    if (resource == null) {
      value = '0';
    } else {
      if (resource is! String) {
        value = '$resource';
      }
    }

    if (Get.isEmptyString(value)) {
      value = '0';
    }

    if (value.equal('0')) return '0';

    if (roundMode != null) {
      value = value.decimal(scale: 0, roundMode: roundMode);
    }

    if (controller.type == TronResourceType.energy) {
      return ID.valueEnergy.trParams({'energy': value});
    } else {
      return ID.valueBandwidth.trParams({'bandwidth': value});
    }
  }

  Row _itemWidget({
    required String title,
    required String value,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: styleSecond_14,
          ),
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Text(
          value,
          style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setNumberFontFamily()),
        ),
      ],
    );
  }

  GestureDetector _actionWidget(TronDelegateModel model) {
    return GestureDetector(
      onTap: () => Get.toNamed(AppRoutes.tronReclaimPage, arguments: {
        GetArgumentsKey.model: model,
        GetArgumentsKey.controller: controller,
      }),
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: Get.setHeight(26),
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(12),
        ),
        decoration: BoxDecoration(
          color: Get.theme.black,
          borderRadius: BorderRadius.circular(Get.setRadius(13)),
        ),
        child: Row(
          children: [
            Text(
              ID.stringReclaimTitle.tr,
              style: TextStyle(
                color: Get.theme.white,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setFontFamily(),
              ),
            )
          ],
        ),
      ),
    );
  }

  Container _headerWidget() {
    return Container(
      padding: EdgeInsets.all(Get.setPaddingSize(16)),
      decoration: BoxDecoration(
        color: Get.theme.colorF9F9F9,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            flex: 1,
            child: Column(
              children: [
                Text(
                  ID.stringDelegated.tr,
                  style: styleSecond_14,
                ),
                SizedBox(height: Get.setPaddingSize(8)),
                Text(
                  controller.delegatedValue,
                  style: stylePrimary_16_m,
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                Text(
                  ID.stringCanDelegate.tr,
                  style: styleSecond_14,
                ),
                SizedBox(height: Get.setPaddingSize(8)),
                Text(
                  controller.canDelegateValue,
                  style: stylePrimary_16_m,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
