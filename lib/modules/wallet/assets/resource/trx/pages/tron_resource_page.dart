import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_border_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/status/status_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TronResourcePage
    extends BaseStatelessWidget<TronResourceManagerController> {
  final TronResourceType type;
  const TronResourcePage({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          bottomNavigationBar: _bottomWidget(),
          body: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(16),
              vertical: Get.setPaddingSize(24),
            ),
            child: Column(
              children: [
                _resourceWidget(),
                <PERSON><PERSON><PERSON><PERSON>(height: Get.setPaddingSize(36)),
                _resourceDetailWidget(),
                SizedBox(height: Get.setPaddingSize(36)),
                _resourceManagerWidget(),
              ],
            ),
          ),
        ));
  }

  ResourceBorderWidget _resourceManagerWidget() {
    return ResourceBorderWidget(
      headerWidget: Text(
        type == TronResourceType.energy
            ? ID.stringEnergyManager.tr
            : ID.stringNetManager.tr,
        style: stylePrimary_14_m,
      ),
      contentWidget: Column(
        children: [
          _resourceDetailItem(
              title: ID.stringDelegated.tr,
              status: TronResourceStatus.reclaim,
              value: type == TronResourceType.energy
                  ? controller.tronResource.value.resourceDetail
                          ?.delegatedToOthersEnergyValue ??
                      '-'
                  : controller.tronResource.value.resourceDetail
                          ?.delegatedToOthersBandwidth ??
                      '-',
              callabck: () => controller.reclaimAction(type)),
          SizedBox(height: Get.setPaddingSize(12)),
          _resourceDetailItem(
              title: ID.stringCanDelegate.tr,
              status: TronResourceStatus.delegate,
              value: type == TronResourceType.energy
                  ? controller.energyDelegate.value
                  : controller.bandwidthDelegate.value,
              callabck: () => controller.delegateAction(type)),
        ],
      ),
    );
  }

  ResourceBorderWidget _resourceDetailWidget() {
    return ResourceBorderWidget(
      headerWidget: Text(
        type == TronResourceType.energy
            ? ID.stringEnergyDetail.tr
            : ID.stringNetDetail.tr,
        style: stylePrimary_14_m,
      ),
      contentWidget: Column(
        children: [
          _resourceDetailItem(
            title: ID.stringStakeGet.tr,
            value: type == TronResourceType.energy
                ? controller.tronResource.value.fromStakingEnergyValue
                : controller.tronResource.value.fromStakingBandwidthValue,
          ),
          SizedBox(height: Get.setPaddingSize(12)),
          _resourceDetailItem(
            title: ID.stringOtherDelegateOwner.tr,
            value: type == TronResourceType.energy
                ? controller.tronResource.value.delegatedByOthersEnergyValue
                : controller.tronResource.value.delegatedByOthersBandwidthValue,
          ),
          SizedBox(height: Get.setPaddingSize(12)),
          _resourceDetailItem(
            title: ID.stringDelegateToOther.tr,
            value: type == TronResourceType.energy
                ? controller.tronResource.value.delegatedToOthersEnergyValue
                : controller.tronResource.value.delegatedToOthersBandwidthValue,
          ),
          Visibility(
            visible: type == TronResourceType.bandwidth,
            child: SizedBox(height: Get.setPaddingSize(12)),
          ),
          Visibility(
            visible: type == TronResourceType.bandwidth,
            child: _resourceDetailItem(
              title: ID.stringFreeGet.tr,
              value: '+${controller.tronResource.value.freeNetLimit ?? 0}',
            ),
          ),
        ],
      ),
    );
  }

  Row _resourceDetailItem({
    required String title,
    required String value,
    TronResourceStatus? status,
    Function()? callabck,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: styleSecond_14,
          ),
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Visibility(
          visible: !(status == TronResourceStatus.delegate &&
              controller.delegateButtonStatus.value == ButtonStatus.loading),
          child: Text(
            value,
            overflow: TextOverflow.ellipsis,
            style: stylePrimary_14_m,
          ),
        ),
        Visibility(
          visible: status != null,
          child: SizedBox(width: Get.setPaddingSize(8)),
        ),
        Visibility(
          visible: status != null &&
              !(status == TronResourceStatus.delegate &&
                  controller.delegateButtonStatus.value ==
                      ButtonStatus.loading),
          child: _actionWidget(status, callabck),
        ),
        Visibility(
          visible: status == TronResourceStatus.delegate &&
              controller.delegateButtonStatus.value == ButtonStatus.loading,
          child: _loadingWidget(),
        ),
      ],
    );
  }

  SizedBox _loadingWidget() {
    return SizedBox(
      height: Get.setHeight(26),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
        child: const AppLoadingWidget(),
      ),
    );
  }

  GestureDetector _actionWidget(
    TronResourceStatus? status,
    Function()? callabck,
  ) {
    return GestureDetector(
      onTap: callabck,
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: Get.setHeight(26),
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(12),
        ),
        decoration: BoxDecoration(
          color: Get.theme.black,
          borderRadius: BorderRadius.circular(Get.setRadius(13)),
        ),
        child: Row(
          children: [
            Text(
              status == TronResourceStatus.reclaim
                  ? ID.stringReclaimTitle.tr
                  : ID.stringDelegate.tr,
              style: TextStyle(
                color: Get.theme.white,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setFontFamily(),
              ),
            )
          ],
        ),
      ),
    );
  }

  ResourceBorderWidget _resourceWidget() {
    return ResourceBorderWidget(
      contentWidget: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  type == TronResourceType.energy
                      ? ID.stringEnergy.tr
                      : ID.stringBandwidth.tr,
                  style: stylePrimary_14_m,
                ),
              ),
              Text(
                type == TronResourceType.energy
                    ? '${controller.tronResource.value.availableEnergy} / ${controller.tronResource.value.availableEnergy}'
                    : '${controller.tronResource.value.availableNet} / ${controller.tronResource.value.totalNet}',
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              )
            ],
          ),
          SizedBox(height: Get.setPaddingSize(8)),
          LinearProgressIndicator(
              minHeight: Get.setHeight(3),
              value: type == TronResourceType.energy
                  ? controller.tronResource.value.energyRate
                  : controller.tronResource.value.netRate,
              backgroundColor: Get.theme.colorECECEC,
              valueColor: AlwaysStoppedAnimation(type == TronResourceType.energy
                  ? Get.theme.color02B58A
                  : Get.theme.colorFF6A16),
              borderRadius:
                  BorderRadius.all(Radius.circular(Get.setRadius(1.5))))
        ],
      ),
    );
  }

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: ButtonWidget(
        text: type == TronResourceType.energy
            ? ID.stringGetEnergy.tr
            : ID.stringGetBandwidth.tr,
        onPressed: () => controller.getResourceAction(),
      ),
    );
  }
}
