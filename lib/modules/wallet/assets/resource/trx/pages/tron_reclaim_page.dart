/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-19 09:11:37
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_reclaim_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_amount_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';

class TronReclaimPage extends BaseStatelessWidget<TronReclaimControlelr> {
  const TronReclaimPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: baseAppBar(title: ID.stringReclaim.tr),
          bottomNavigationBar: _bottomWidget(),
          body: KeyboardDismissWidget(
            child: Padding(
              padding: EdgeInsets.all(Get.setPaddingSize(16)),
              child: Column(
                children: [
                  _titleWidget(
                    title: ID.stringReclaimAddress.tr,
                    value: AddressUtils.omitAddress(
                        controller.model.receiverAddress),
                  ),
                  SizedBox(height: Get.setPaddingSize(16)),
                  _titleWidget(
                    title: ID.stringReclaim.tr,
                    value: controller.type == TronResourceType.energy
                        ? ID.stringTxEnergy.tr
                        : ID.stringTxBandwidth.tr,
                  ),
                  DividerWidget(
                    padding:
                        EdgeInsets.symmetric(vertical: Get.setPaddingSize(16)),
                  ),
                  _reclaimAmountWidget(),
                ],
              ),
            ),
          ),
        ));
  }

  Column _reclaimAmountWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _inputTitleWidget(),
        SizedBox(height: Get.setPaddingSize(8)),
        TextFieldWidget(
          hintText: controller.hintText.value,
          maxLines: 1,
          controller: controller.amountController,
          keyboardType: const TextInputType.numberWithOptions(decimal: false),
          scale: controller.ctr.ctr.coinModel.chainDecimal,
          suffixIcon: ResourceAmountWidget(
            resourceType: controller.type,
            action: (type) {
              controller.amountController.text = '';
              controller.updateAmount(type);
            },
          ),
          onValueChanged: (value) =>
              controller.updateAmount(controller.inputType),
        ),
        SizedBox(height: Get.setPaddingSize(4)),
        Text(
          controller.inputTips.value,
          style: TextStyle(
            color: Get.theme.textSecondary,
            fontSize: Get.setFontSize(12),
          ),
        ),
        Visibility(
          visible: !Get.isEmptyString(controller.amountErrorTips.value),
          child: Padding(
            padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
            child: Text(
              controller.amountErrorTips.value,
              style: TextStyle(
                color: Get.theme.colorF44D4D,
                fontSize: Get.setFontSize(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Row _inputTitleWidget() {
    return Row(
      children: [
        Text(
          controller.inputTitle.value,
          style: styleSecond_14,
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          child: Text(
            controller.inputValue.value,
            textAlign: TextAlign.end,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.regular,
              fontFamily: Get.setFontFamily(),
            ),
          ),
        ),
      ],
    );
  }

  Row _titleWidget({required String title, required String value}) {
    return Row(
      children: [
        Text(
          title,
          style: styleSecond_14,
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          child: Text(
            value,
            textAlign: TextAlign.end,
            style: stylePrimary_14_m,
          ),
        ),
      ],
    );
  }

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: ButtonWidget(
        width: Get.width,
        text: ID.stringConfirm.tr,
        buttonStatus: controller.buttonStatus.value,
        onPressed: () => controller.loadData(),
      ),
    );
  }
}
