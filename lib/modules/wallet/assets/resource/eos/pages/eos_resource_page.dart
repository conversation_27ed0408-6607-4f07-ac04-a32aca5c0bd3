/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-25 10:10:33
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/controllers/eos_resource_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_border_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class EOSResourcePage extends BaseStatelessWidget<EOSResourceController> {
  const EOSResourcePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringResourceManager.tr),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(24),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Obx(() => Column(
              children: [
                _balanceWidget(),
                SizedBox(height: Get.setPaddingSize(36)),
                _resourceWidget('RAM'),
                SizedBox(height: Get.setPaddingSize(36)),
                _resourceWidget('CPU'),
              ],
            )),
      ),
    );
  }

  Container _resourceWidget(String type) {
    String title = '';
    String value = '';
    String tips = '';
    double rate = 1;
    Color color = Get.theme.colorFF6A16;
    if (type == 'RAM') {
      title = type;
      tips = ID.stringEosResourceTip1.tr;
      value =
          '${controller.eosInfo.value.resourceToKB(controller.eosInfo.value.availableRAM)} / ${controller.eosInfo.value.resourceToKB(controller.eosInfo.value.totalRAM)}';
      rate = controller.eosInfo.value.ramRate;
      color = Get.theme.colorFF6A16;
    } else if (type == 'CPU') {
      title = type;
      tips = ID.stringEosResourceTip2.tr;
      value =
          '${controller.eosInfo.value.availableCPU} ms / ${controller.eosInfo.value.totalCPU} ms';
      rate = controller.eosInfo.value.cpuRate;
      color = Get.theme.color02B58A;
    }
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Get.theme.colorECECEC, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16),
              top: Get.setPaddingSize(16),
              bottom: Get.setPaddingSize(24),
            ),
            child: Column(
              children: [
                _resourceItemWidget(title, value, rate, color),
                Visibility(
                  visible: type == 'CPU',
                  child: Padding(
                    padding: EdgeInsets.only(top: Get.setPaddingSize(24)),
                    child: _resourceItemWidget(
                      'NET',
                      '${controller.eosInfo.value.resourceToKB(controller.eosInfo.value.availableNET)} / ${controller.eosInfo.value.resourceToKB(controller.eosInfo.value.totalNET)}',
                      controller.eosInfo.value.netRate,
                      Get.theme.color6C60FF,
                    ),
                  ),
                ),
              ],
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(Get.setRadius(10)),
              bottomLeft: Radius.circular(Get.setRadius(10)),
            ),
            child: Container(
              color: Get.theme.colorECECEC,
              width: Get.width,
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(8),
              ),
              child: Text(
                tips,
                style: styleSecond_14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Column _resourceItemWidget(
      String title, String value, double rate, Color color) {
    return Column(
      children: [
        Row(
          children: [
            Text(
              title,
              style: stylePrimary_14_m,
            ),
            SizedBox(width: Get.setPaddingSize(8)),
            Expanded(
              child: Text(
                value,
                textAlign: TextAlign.end,
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: Get.setPaddingSize(8)),
        LinearProgressIndicator(
            minHeight: Get.setHeight(3),
            value: rate,
            backgroundColor: Get.theme.colorECECEC,
            valueColor: AlwaysStoppedAnimation(color),
            borderRadius:
                BorderRadius.all(Radius.circular(Get.setRadius(1.5)))),
      ],
    );
  }

  ResourceBorderWidget _balanceWidget() {
    return ResourceBorderWidget(
      headerWidget: _titleValueWidget(
        title: ID.stringTotalAmount.tr,
        value:
            '${(controller.addressModel.balance ?? '0').add(controller.eosInfo.value.ransomValue)} EOS',
      ),
      contentWidget: Column(
        children: [
          _titleValueWidget(
              title: ID.stringAvailableBalance.tr,
              value: controller.addressModel.balance ?? '0'),
          SizedBox(height: Get.setPaddingSize(12)),
          _titleValueWidget(
              title: ID.stringPledgedTitle.tr,
              value: controller.eosInfo.value.delegateBandwidthValue),
          SizedBox(height: Get.setPaddingSize(12)),
          _titleValueWidget(
              title: ID.stringRedemptionTitle.tr,
              value: controller.eosInfo.value.ransomValue),
        ],
      ),
    );
  }

  Row _titleValueWidget({
    required String title,
    required String value,
  }) {
    return Row(
      children: [
        Text(
          title,
          style: styleSecond_14,
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          child: Text(
            value,
            textAlign: TextAlign.end,
            style: stylePrimary_14_m,
          ),
        ),
      ],
    );
  }
}
