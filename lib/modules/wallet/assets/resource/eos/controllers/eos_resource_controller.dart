import 'dart:convert';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_resource_price.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/chain/coin_base.dart';

class EOSResourceController extends BaseController<BlockChainService> {
  late Rx<EosAccountInfo> eosInfo = EosAccountInfo().obs;
  late final CoinModel coinModel;
  late final AddressModel addressModel;

  @override
  void onInit() {
    eosInfo.value = Get.arguments[GetArgumentsKey.eosResourceInfo];
    coinModel = Get.arguments[GetArgumentsKey.coinModel];
    addressModel = Get.arguments[GetArgumentsKey.addressModel];
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {
    List<Future> futures = [];

    futures.add(api.getTokenBalance(BlockChainParamsManager.createParams(
      method: BlockChainAPI.getEosAccountInfo,
      requestParams: RequestParams()
          .put(APIConstant.chain, coinModel.chain!)
          .put(APIConstant.address, addressModel.address)
          .getRequestBody(),
    )));

    ApiService getApi = ApiService();
    futures.add(getApi.getEosResourcePrice());

    multiHttpRequest(futures, (value) async {
      if (value != null) {
        BaseResponseV1 responseV1 = value[0];
        EosAccountInfo info = EosAccountInfo();
        dynamic data = responseV1.data;
        if (data != null) {
          info = EosAccountInfo.fromJson(responseV1.data);
        }

        String sourceBalance = '0';
        if (info.coreLiquidBalance != null) {
          sourceBalance = CoinBase.getEosBalacne(info.coreLiquidBalance!);
        }

        BaseResponse response = value[1];
        if (response.code == APIConstant.responseCode) {
          info.price = EOSResourcePrice.fromJson(response.data);
          data['price'] = response.data;
        }

        await Get.database.addressDao.updateEOSAccountInfo(
            addressModel, sourceBalance, jsonEncode(data));

        eosInfo.value = info;
      }
    });
  }
}

class EosResourceBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => EOSResourceController());
  }
}
