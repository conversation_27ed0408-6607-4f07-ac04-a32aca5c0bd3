import 'package:json_annotation/json_annotation.dart';

part 'subjective_cpu_bill_limit.g.dart';

@JsonSerializable()
class SubjectiveCpuBillLimit {
  int? available;
  @JsonKey(name: 'current_used')
  int? currentUsed;
  @Json<PERSON>ey(name: 'last_usage_update_time')
  DateTime? lastUsageUpdateTime;
  int? max;
  int? used;

  SubjectiveCpuBillLimit({
    this.available,
    this.currentUsed,
    this.lastUsageUpdateTime,
    this.max,
    this.used,
  });

  factory SubjectiveCpuBillLimit.fromJson(Map<String, dynamic> json) {
    return _$SubjectiveCpuBillLimitFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SubjectiveCpuBillLimitToJson(this);
}
