// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voter_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VoterInfo _$VoterInfoFromJson(Map<String, dynamic> json) => VoterInfo(
      flags1: (json['flags1'] as num?)?.toInt(),
      isProxy: (json['is_proxy'] as num?)?.toInt(),
      lastVoteWeight: json['last_vote_weight'] as String?,
      owner: json['owner'] as String?,
      producers: (json['producers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      proxiedVoteWeight: json['proxied_vote_weight'] as String?,
      proxy: json['proxy'] as String?,
      reserved2: (json['reserved2'] as num?)?.toInt(),
      reserved3: json['reserved3'] as String?,
      staked: (json['staked'] as num?)?.toInt(),
    );

Map<String, dynamic> _$VoterInfoToJson(VoterInfo instance) => <String, dynamic>{
      'flags1': instance.flags1,
      'is_proxy': instance.isProxy,
      'last_vote_weight': instance.lastVoteWeight,
      'owner': instance.owner,
      'producers': instance.producers,
      'proxied_vote_weight': instance.proxiedVoteWeight,
      'proxy': instance.proxy,
      'reserved2': instance.reserved2,
      'reserved3': instance.reserved3,
      'staked': instance.staked,
    };
