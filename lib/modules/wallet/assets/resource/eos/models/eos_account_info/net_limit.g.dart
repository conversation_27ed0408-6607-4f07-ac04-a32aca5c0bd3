// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'net_limit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NetLimit _$NetLimitFromJson(Map<String, dynamic> json) => NetLimit(
      available: (json['available'] as num?)?.toInt(),
      currentUsed: (json['current_used'] as num?)?.toInt(),
      lastUsageUpdateTime: json['last_usage_update_time'] == null
          ? null
          : DateTime.parse(json['last_usage_update_time'] as String),
      max: (json['max'] as num?)?.toInt(),
      used: (json['used'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NetLimitToJson(NetLimit instance) => <String, dynamic>{
      'available': instance.available,
      'current_used': instance.currentUsed,
      'last_usage_update_time': instance.lastUsageUpdateTime?.toIso8601String(),
      'max': instance.max,
      'used': instance.used,
    };
