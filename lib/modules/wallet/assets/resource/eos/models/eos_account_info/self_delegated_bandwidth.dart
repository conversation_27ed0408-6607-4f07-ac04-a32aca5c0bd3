/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2024-09-24 16:05:58
 */
import 'package:json_annotation/json_annotation.dart';

part 'self_delegated_bandwidth.g.dart';

@JsonSerializable()
class SelfDelegatedBandwidth {
  @Json<PERSON>ey(name: 'cpu_weight')
  String? cpuWeight;
  String? from;
  @JsonKey(name: 'net_weight')
  String? netWeight;
  String? to;

  SelfDelegatedBandwidth({
    this.cpuWeight,
    this.from,
    this.netWeight,
    this.to,
  });

  factory SelfDelegatedBandwidth.fromJson(Map<String, dynamic> json) {
    return _$SelfDelegatedBandwidthFromJson(json);
  }

  Map<String, dynamic> toJson() => _$SelfDelegatedBandwidthToJson(this);

  String get cpuWeightValue {
    if (cpuWeight == null) return '0';
    return cpuWeight!.replaceAll(' EOS', '');
  }

  String get netWeightValue {
    if (netWeight == null) return '0';
    return netWeight!.replaceAll(' EOS', '');
  }
}
