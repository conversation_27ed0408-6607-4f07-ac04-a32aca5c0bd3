import 'package:json_annotation/json_annotation.dart';

part 'net_limit.g.dart';

@JsonSerializable()
class NetLimit {
  int? available;
  @JsonKey(name: 'current_used')
  int? currentUsed;
  @Json<PERSON>ey(name: 'last_usage_update_time')
  DateTime? lastUsageUpdateTime;
  int? max;
  int? used;

  NetLimit({
    this.available,
    this.currentUsed,
    this.lastUsageUpdateTime,
    this.max,
    this.used,
  });

  factory NetLimit.fromJson(Map<String, dynamic> json) {
    return _$NetLimitFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NetLimitToJson(this);

  String get availableValue {
    if (available == null) return '0';
    return available!.toString();
  }

  String get maxValue {
    if (max == null) return '0';
    return max!.toString();
  }
}
