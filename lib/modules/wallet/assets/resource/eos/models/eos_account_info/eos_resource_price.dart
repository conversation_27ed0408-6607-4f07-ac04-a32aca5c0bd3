class EOSResourcePrice {
  String? cpu;
  String? net;
  String? ram;

  EOSResourcePrice({this.cpu, this.net, this.ram});

  factory EOSResourcePrice.fromJson(Map<String, dynamic> json) =>
      EOSResourcePrice(
        cpu: json['cpu'] as String?,
        net: json['net'] as String?,
        ram: json['ram'] as String?,
      );

  static Map<String, dynamic> toJson(EOSResourcePrice? price) => {
        'cpu': price?.cpu,
        'net': price?.net,
        'ram': price?.ram,
      };
}
