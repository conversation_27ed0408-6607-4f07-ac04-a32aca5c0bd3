/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-11 13:01:28
 */
import 'package:json_annotation/json_annotation.dart';

import 'required_auth.dart';

part 'permission.g.dart';

@JsonSerializable()
class Permission {
  @Json<PERSON>ey(name: 'linked_actions')
  List<dynamic>? linkedActions;
  String? parent;
  @JsonKey(name: 'perm_name')
  String? permName;
  @JsonKey(name: 'required_auth')
  RequiredAuth? requiredAuth;

  Permission({
    this.linkedActions,
    this.parent,
    this.permName,
    this.requiredAuth,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    return _$PermissionFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PermissionToJson(this);
}
