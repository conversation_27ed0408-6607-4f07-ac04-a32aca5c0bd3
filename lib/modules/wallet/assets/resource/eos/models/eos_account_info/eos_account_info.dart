/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-09-25 09:57:40
 */
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_resource_price.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/refund_request.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/timezone.dart';
import 'package:wallet_core/extension/string_decimal.dart';

import 'cpu_limit.dart';
import 'net_limit.dart';
import 'permission.dart';
import 'self_delegated_bandwidth.dart';
import 'subjective_cpu_bill_limit.dart';
import 'total_resources.dart';
import 'voter_info.dart';

part 'eos_account_info.g.dart';

@JsonSerializable()
class EosAccountInfo {
  @JsonKey(name: 'account_name')
  String? accountName;
  @JsonKey(name: 'core_liquid_balance')
  String? coreLiquidBalance;
  @JsonKey(name: 'cpu_limit')
  CpuLimit? cpuLimit;
  @Json<PERSON>ey(name: 'cpu_weight')
  int? cpuWeight;
  DateTime? created;
  @JsonKey(name: 'eosio_any_linked_actions')
  List<dynamic>? eosioAnyLinkedActions;
  @JsonKey(name: 'head_block_num')
  int? headBlockNum;
  @JsonKey(name: 'head_block_time')
  DateTime? headBlockTime;
  @JsonKey(name: 'last_code_update')
  DateTime? lastCodeUpdate;
  @JsonKey(name: 'net_limit')
  NetLimit? netLimit;
  @JsonKey(name: 'net_weight')
  int? netWeight;
  List<Permission>? permissions;
  bool? privileged;
  @JsonKey(name: 'ram_quota')
  int? ramQuota;
  @JsonKey(name: 'ram_usage')
  int? ramUsage;
  @JsonKey(name: 'refund_request')
  RefundRequest? refundRequest;
  @JsonKey(name: 'rex_info')
  dynamic rexInfo;
  @JsonKey(name: 'self_delegated_bandwidth')
  SelfDelegatedBandwidth? selfDelegatedBandwidth;
  @JsonKey(name: 'subjective_cpu_bill_limit')
  SubjectiveCpuBillLimit? subjectiveCpuBillLimit;
  @JsonKey(name: 'total_resources')
  TotalResources? totalResources;
  @JsonKey(name: 'voter_info')
  VoterInfo? voterInfo;
  @JsonKey(toJson: EOSResourcePrice.toJson)
  EOSResourcePrice? price;

  EosAccountInfo({
    this.accountName,
    this.coreLiquidBalance,
    this.cpuLimit,
    this.cpuWeight,
    this.created,
    this.eosioAnyLinkedActions,
    this.headBlockNum,
    this.headBlockTime,
    this.lastCodeUpdate,
    this.netLimit,
    this.netWeight,
    this.permissions,
    this.privileged,
    this.ramQuota,
    this.ramUsage,
    this.refundRequest,
    this.rexInfo,
    this.selfDelegatedBandwidth,
    this.subjectiveCpuBillLimit,
    this.totalResources,
    this.voterInfo,
    this.price,
  });

  factory EosAccountInfo.fromJson(Map<String, dynamic> json) {
    return _$EosAccountInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EosAccountInfoToJson(this);

  /// 转成 Asia/Shanghai 时区时间
  DateTime? get createDateTime {
    if (created != null) {
      tz.initializeTimeZones();

      DateTime time = DateTime.utc(
        created!.year,
        created!.month,
        created!.day,
        created!.hour,
        created!.minute,
        created!.second,
      );

      Location location = tz.getLocation('Asia/Shanghai');
      DateTime dateTime = tz.TZDateTime.from(time, location);

      return dateTime;
    }

    return created;
  }

  Permission? get ownerPermission => _permission('owner');
  Permission? get activePermission => _permission('active');

  Permission? _permission(String permName) {
    for (var e in permissions!) {
      if (e.permName == permName) {
        return e;
      }
    }
    return null;
  }

  /// RAM
  double get ramRate {
    int ramQuota = _formatNumber(this.ramQuota);
    int ramUsage = _formatNumber(this.ramUsage);
    if (ramQuota == 0) return 0;

    int over = ramQuota - ramUsage;

    return double.parse(over.toString().div(
          ramQuota.toString(),
          scale: 2,
          roundMode: RoundMode.down,
        ));
  }

  /// CPU
  double get cpuRate {
    int available = _formatNumber(cpuLimit?.available);
    int max = _formatNumber(cpuLimit?.max);
    if (max == 0) return 0;
    String availableStr = available.toString().div('1000', scale: 4);
    String maxStr = max.toString().div('1000', scale: 4);

    return double.parse(availableStr.div(
      maxStr,
      scale: 2,
      roundMode: RoundMode.down,
    ));
  }

  /// NET
  double get netRate {
    int available = _formatNumber(netLimit?.available);
    int max = _formatNumber(netLimit?.max);
    if (max == 0) return 0;
    return double.parse(available.toString().div(
          max.toString(),
          scale: 2,
          roundMode: RoundMode.down,
        ));
  }

  String get availableRAM => totalRAM.sub((ramUsage ?? 0).toString());
  String get totalRAM => (ramQuota ?? 0).toString();

  String get availableCPU => cpuLimit?.availableValue ?? '0';
  String get totalCPU => cpuLimit?.maxValue ?? '0';

  String get availableNET => netLimit?.availableValue ?? '0';
  String get totalNET => netLimit?.maxValue ?? '0';

  String resourceToKB(String value) {
    String kbStr = "1024";
    String mbStr = "1048576";
    String gbStr = "1073741824";

    String reStr = '0';
    reStr = value.div(mbStr);
    if (reStr.moreThan('1') && reStr.lessThan('1024')) {
      return '${reStr.decimal(scale: 3, roundMode: RoundMode.down)} MB';
    } else {
      if (reStr.moreThan('1024')) {
        return '${value.div(gbStr, scale: 3, roundMode: RoundMode.down)} GB';
      } else {
        return '${value.div(kbStr, scale: 3, roundMode: RoundMode.down)} KB';
      }
    }
  }

  /// 已抵押
  String get delegateBandwidthValue {
    if (selfDelegatedBandwidth == null && voterInfo == null) return '0';

    if ((voterInfo?.voteBalance ?? '0').equal('0')) {
      return (selfDelegatedBandwidth?.cpuWeightValue ?? '0')
          .add((selfDelegatedBandwidth?.netWeightValue ?? '0'));
    } else {
      return voterInfo!.voteBalance;
    }
  }

  /// 赎回中
  String get ransomValue {
    return (refundRequest?.cpuAmountValue ?? '0')
        .add(refundRequest?.netAmountValue ?? '0');
  }

  int _formatNumber(int? number) {
    if (number == null || number < 0) return 0;
    return number;
  }
}
