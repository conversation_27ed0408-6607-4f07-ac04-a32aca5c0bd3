/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-25 10:16:23
 */

import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

part 'cpu_limit.g.dart';

@JsonSerializable()
class CpuLimit {
  int? available;
  @JsonKey(name: 'current_used')
  int? currentUsed;
  @JsonKey(name: 'last_usage_update_time')
  DateTime? lastUsageUpdateTime;
  int? max;
  int? used;

  CpuLimit({
    this.available,
    this.currentUsed,
    this.lastUsageUpdateTime,
    this.max,
    this.used,
  });

  factory CpuLimit.fromJson(Map<String, dynamic> json) {
    return _$CpuLimitFromJson(json);
  }

  Map<String, dynamic> toJson() => _$CpuLimitToJson(this);

  String get availableValue {
    if (available == null) return '0';
    return available!
        .toString()
        .div('1000', scale: 3, roundMode: RoundMode.down);
  }

  String get maxValue {
    if (max == null) return '0';
    return max!.toString().div('1000', scale: 3, roundMode: RoundMode.down);
  }
}
