/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-09-24 16:15:06
 */
import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

part 'voter_info.g.dart';

@JsonSerializable()
class VoterInfo {
  int? flags1;
  @Json<PERSON>ey(name: 'is_proxy')
  int? isProxy;
  @Json<PERSON>ey(name: 'last_vote_weight')
  String? lastVoteWeight;
  String? owner;
  List<String>? producers;
  @JsonKey(name: 'proxied_vote_weight')
  String? proxiedVoteWeight;
  String? proxy;
  int? reserved2;
  String? reserved3;
  int? staked;

  VoterInfo({
    this.flags1,
    this.isProxy,
    this.lastVoteWeight,
    this.owner,
    this.producers,
    this.proxiedVoteWeight,
    this.proxy,
    this.reserved2,
    this.reserved3,
    this.staked,
  });

  factory VoterInfo.fromJson(Map<String, dynamic> json) {
    return _$VoterInfoFromJson(json);
  }

  Map<String, dynamic> toJson() => _$VoterInfoToJson(this);

  String get voteBalance {
    if (staked == null) return '0';
    return staked!.toString().div('10000', scale: 4, roundMode: RoundMode.down);
  }
}
