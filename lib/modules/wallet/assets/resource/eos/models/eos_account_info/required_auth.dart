/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-11 12:59:37
 */
import 'package:json_annotation/json_annotation.dart';

import 'key.dart';

part 'required_auth.g.dart';

@JsonSerializable()
class RequiredAuth {
  List<dynamic>? accounts;
  List<Key>? keys;
  int? threshold;
  List<dynamic>? waits;

  RequiredAuth({this.accounts, this.keys, this.threshold, this.waits});

  factory RequiredAuth.fromJson(Map<String, dynamic> json) {
    return _$RequiredAuthFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RequiredAuthToJson(this);

  Key? get key => keys?.first;
}
