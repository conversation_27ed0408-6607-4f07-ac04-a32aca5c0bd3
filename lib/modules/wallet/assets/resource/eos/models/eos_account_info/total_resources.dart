import 'package:json_annotation/json_annotation.dart';

part 'total_resources.g.dart';

@JsonSerializable()
class TotalResources {
  @JsonKey(name: 'cpu_weight')
  String? cpuWeight;
  @<PERSON><PERSON><PERSON>ey(name: 'net_weight')
  String? netWeight;
  String? owner;
  @JsonKey(name: 'ram_bytes')
  int? ramBytes;

  TotalResources({
    this.cpuWeight,
    this.netWeight,
    this.owner,
    this.ramBytes,
  });

  factory TotalResources.fromJson(Map<String, dynamic> json) {
    return _$TotalResourcesFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TotalResourcesToJson(this);
}
