/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-09-24 17:22:15
 */

import 'package:json_annotation/json_annotation.dart';

part 'refund_request.g.dart';

@JsonSerializable()
class RefundRequest {
  @Json<PERSON>ey(name: 'cpu_amount')
  dynamic cpuAmount;
  @J<PERSON><PERSON>ey(name: 'net_amount')
  dynamic netAmount;

  RefundRequest({this.cpuAmount, this.netAmount});

  factory RefundRequest.fromJson(Map<String, dynamic> json) {
    return _$RefundRequestFromJson(json);
  }

  Map<String, dynamic> toJson() => _$RefundRequestToJson(this);

  String get cpuAmountValue {
    if (cpuAmount == null) return '0';
    String value = '$cpuAmount';
    return value.replaceAll(' EOS', '');
  }

  String get netAmountValue {
    if (netAmount == null) return '0';
    String value = '$netAmount';
    return value.replaceAll(' EOS', '');
  }
}
