// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'eos_account_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EosAccountInfo _$EosAccountInfoFromJson(Map<String, dynamic> json) =>
    EosAccountInfo(
      accountName: json['account_name'] as String?,
      coreLiquidBalance: json['core_liquid_balance'] as String?,
      cpuLimit: json['cpu_limit'] == null
          ? null
          : CpuLimit.fromJson(json['cpu_limit'] as Map<String, dynamic>),
      cpuWeight: (json['cpu_weight'] as num?)?.toInt(),
      created: json['created'] == null
          ? null
          : DateTime.parse(json['created'] as String),
      eosioAnyLinkedActions: json['eosio_any_linked_actions'] as List<dynamic>?,
      headBlockNum: (json['head_block_num'] as num?)?.toInt(),
      headBlockTime: json['head_block_time'] == null
          ? null
          : DateTime.parse(json['head_block_time'] as String),
      lastCodeUpdate: json['last_code_update'] == null
          ? null
          : DateTime.parse(json['last_code_update'] as String),
      netLimit: json['net_limit'] == null
          ? null
          : NetLimit.fromJson(json['net_limit'] as Map<String, dynamic>),
      netWeight: (json['net_weight'] as num?)?.toInt(),
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
      privileged: json['privileged'] as bool?,
      ramQuota: (json['ram_quota'] as num?)?.toInt(),
      ramUsage: (json['ram_usage'] as num?)?.toInt(),
      refundRequest: json['refund_request'] == null
          ? null
          : RefundRequest.fromJson(
              json['refund_request'] as Map<String, dynamic>),
      rexInfo: json['rex_info'],
      selfDelegatedBandwidth: json['self_delegated_bandwidth'] == null
          ? null
          : SelfDelegatedBandwidth.fromJson(
              json['self_delegated_bandwidth'] as Map<String, dynamic>),
      subjectiveCpuBillLimit: json['subjective_cpu_bill_limit'] == null
          ? null
          : SubjectiveCpuBillLimit.fromJson(
              json['subjective_cpu_bill_limit'] as Map<String, dynamic>),
      totalResources: json['total_resources'] == null
          ? null
          : TotalResources.fromJson(
              json['total_resources'] as Map<String, dynamic>),
      voterInfo: json['voter_info'] == null
          ? null
          : VoterInfo.fromJson(json['voter_info'] as Map<String, dynamic>),
      price: json['price'] == null
          ? null
          : EOSResourcePrice.fromJson(json['price'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EosAccountInfoToJson(EosAccountInfo instance) =>
    <String, dynamic>{
      'account_name': instance.accountName,
      'core_liquid_balance': instance.coreLiquidBalance,
      'cpu_limit': instance.cpuLimit,
      'cpu_weight': instance.cpuWeight,
      'created': instance.created?.toIso8601String(),
      'eosio_any_linked_actions': instance.eosioAnyLinkedActions,
      'head_block_num': instance.headBlockNum,
      'head_block_time': instance.headBlockTime?.toIso8601String(),
      'last_code_update': instance.lastCodeUpdate?.toIso8601String(),
      'net_limit': instance.netLimit,
      'net_weight': instance.netWeight,
      'permissions': instance.permissions,
      'privileged': instance.privileged,
      'ram_quota': instance.ramQuota,
      'ram_usage': instance.ramUsage,
      'refund_request': instance.refundRequest,
      'rex_info': instance.rexInfo,
      'self_delegated_bandwidth': instance.selfDelegatedBandwidth,
      'subjective_cpu_bill_limit': instance.subjectiveCpuBillLimit,
      'total_resources': instance.totalResources,
      'voter_info': instance.voterInfo,
      'price': EOSResourcePrice.toJson(instance.price),
    };
