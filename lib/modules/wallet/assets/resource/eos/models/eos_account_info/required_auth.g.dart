// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'required_auth.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RequiredAuth _$RequiredAuthFromJson(Map<String, dynamic> json) => RequiredAuth(
      accounts: json['accounts'] as List<dynamic>?,
      keys: (json['keys'] as List<dynamic>?)
          ?.map((e) => Key.fromJson(e as Map<String, dynamic>))
          .toList(),
      threshold: (json['threshold'] as num?)?.toInt(),
      waits: json['waits'] as List<dynamic>?,
    );

Map<String, dynamic> _$RequiredAuthToJson(RequiredAuth instance) =>
    <String, dynamic>{
      'accounts': instance.accounts,
      'keys': instance.keys,
      'threshold': instance.threshold,
      'waits': instance.waits,
    };
