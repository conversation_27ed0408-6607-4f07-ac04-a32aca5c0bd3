/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-24 14:41:17
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_account_info.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class EOSAssetsResourceWidget extends StatelessWidget {
  final EosAccountInfo eosInfo;
  final CoinModel coinModel;
  final AddressModel addressModel;
  const EOSAssetsResourceWidget({
    super.key,
    required this.eosInfo,
    required this.coinModel,
    required this.addressModel,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(
        AppRoutes.eosResourcePage,
        arguments: {
          GetArgumentsKey.eosResourceInfo: eosInfo,
          GetArgumentsKey.coinModel: coinModel,
          GetArgumentsKey.addressModel: addressModel,
        },
      ),
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(10),
          horizontal: Get.setPaddingSize(16),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(12),
            horizontal: Get.setPaddingSize(16),
          ),
          decoration: BoxDecoration(
            color: Get.theme.colorF9F9F9,
            borderRadius: BorderRadius.circular(Get.setRadius(12.0)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    _eosSourceWidget(
                      title: 'RAM',
                      color: Get.theme.colorFF6A16,
                      rate: eosInfo.ramRate,
                    ),
                    _eosSourceWidget(
                      title: 'CPU',
                      color: Get.theme.color02B58A,
                      rate: eosInfo.cpuRate,
                    ),
                    _eosSourceWidget(
                      title: 'NET',
                      color: Get.theme.color6C60FF,
                      rate: eosInfo.netRate,
                    ),
                  ],
                ),
              ),
              ImageWidget(
                assetUrl: 'icon_new_arrow01',
                width: Get.setImageSize(10),
                height: Get.setImageSize(10),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _eosSourceWidget({
    required String title,
    required Color color,
    double? rate,
  }) {
    return Expanded(
      flex: 1,
      child: Padding(
        padding: EdgeInsets.only(
          right: Get.setPaddingSize(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Get.theme.textSecondary,
                fontSize: Get.setFontSize(12),
                fontFamily: Get.setNumberFontFamily(),
              ),
              TextSpan(
                text: title,
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(12),
                  fontFamily: Get.setNumberFontFamily(),
                ),
                children: [
                  TextSpan(text: _rate(rate)),
                ],
              ),
            ),
            SizedBox(height: Get.setPaddingSize(4)),
            LinearProgressIndicator(
                minHeight: Get.setHeight(3),
                value: rate ?? 0,
                backgroundColor: Get.theme.colorECECEC,
                valueColor: AlwaysStoppedAnimation(color),
                borderRadius:
                    BorderRadius.all(Radius.circular(Get.setRadius(1.5))))
          ],
        ),
      ),
    );
  }

  String _rate(double? rate) {
    if (rate == null) return '';
    String result = rate.toString().mul('100');
    return ' $result%';
  }
}
