import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/assets/details/token_transaction_details_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/ethereum/evm/bsc.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/tron/trx.dart';

class TokenTransactionAddressWidget extends StatelessWidget {
  final TokenTransactionDeatilsController controller;
  const TokenTransactionAddressWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(10)),
      child: Column(
        children: [
          _itemWidget(ID.sendAddress.tr, _fromAddress),
          _itemWidget(receiveAddress, _toAddress),
          SizedBox(height: Get.setPaddingSize(10)),
          const DividerWidget(),
          SizedBox(height: Get.setPaddingSize(10)),
          _itemWidget(ID.stringTxId.tr, _txId),
          _itemWidget(ID.stringTxBlock.tr, _blockNumber, isCopy: false),
          Visibility(
            visible: _isShowRemark,
            child: _itemWidget(
                _remarkTitle, controller.model?.fromInfo?.memo ?? '',
                isCopy: false),
          ),
        ],
      ),
    );
  }

  String get receiveAddress {
    if (controller.model?.extra?.contractExecute == true) {
      return ID.stringContractAddress.tr;
    }
    return ID.receiveAddress.tr;
  }

  GestureDetector _itemWidget(String title, String value,
          {bool isCopy = true}) =>
      GestureDetector(
        onTap: () => isCopy
            ? Get.copy(CoinBase.getFormatAddress(
                controller.activityModel.coinType, value)!)
            : {},
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child: Row(
            children: [
              Text(
                title,
                style: styleSecond_14,
              ),
              SizedBox(
                width: Get.setPaddingSize(8),
              ),
              Expanded(
                child: Text(
                  AddressUtils.omitAddress(
                      CoinBase.getFormatAddress(
                          controller.activityModel.coinType, value)!,
                      len: 10),
                  maxLines: 1,
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(14),
                      fontWeight: FontWeightX.semibold,
                      fontFamily: Get.setNumberFontFamily()),
                ),
              ),
              Visibility(
                visible: isCopy,
                child: Padding(
                  padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                  child: ImageWidget(
                    assetUrl: 'copy',
                    width: Get.setImageSize(16),
                    height: Get.setImageSize(16),
                  ),
                ),
              )
            ],
          ),
        ),
      );

  String get _fromAddress {
    String? address = controller.model?.fromInfo?.address;
    if (Get.isEmptyString(address)) {
      address = controller.activityModel.from;
    }

    return address ?? CommonConstant.emptyAsstes;
  }

  String get _toAddress {
    String? address = controller.model?.toInfo?.address;
    if (Get.isEmptyString(address)) {
      address = controller.activityModel.to;
    }

    return address ?? CommonConstant.emptyAsstes;
  }

  String get _txId {
    String? txId = controller.model?.txId;
    if (Get.isEmptyString(txId)) {
      txId = controller.activityModel.txId;
    }

    return txId ?? CommonConstant.emptyAsstes;
  }

  String get _blockNumber {
    String? blockNumber = controller.model?.blockNumber;
    if (Get.isEmptyString(blockNumber)) {
      blockNumber = controller.activityModel.blockNumber?.toString();
    }

    return blockNumber ?? CommonConstant.emptyAsstes;
  }

  String get _remarkTitle {
    if (controller.activityModel.coinType is RippleChain) {
      return ID.remarkTag.tr;
    } else if (controller.activityModel.coinType is TronChain) {
      return ID.remark.tr;
    }
    return ID.remarkMemo.tr;
  }

  bool get _isShowRemark {
    if (controller.activityModel.coinType is RippleChain ||
        controller.activityModel.coinType is TronChain ||
        controller.activityModel.coinType is BNBChain ||
        controller.activityModel.coinType is NemChain ||
        controller.activityModel.coinType!.isBabyCoinNetWork) {
      return true;
    }
    return false;
  }
}
