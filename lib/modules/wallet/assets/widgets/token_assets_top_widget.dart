/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2025-03-11 10:44:09
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/widgets/eos_assets_resource_widget.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/tron_assets_resource_widget.dart';
import 'package:coinbag/modules/wallet/assets/token_assets_controller.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/binance/bnb.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/wallet/cold/pro1_pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro1_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_plus_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';

class TokenAssetsTopWidget extends BaseStatelessWidget<TokenAssetsController> {
  const TokenAssetsTopWidget({super.key});

  @override
  build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          _amountWidget(),
          _addressWidget(),
          _actionWallet(controller),
          _eosResourceWidget(),
          _tronRsourceWidget(),
          DividerBoldWidget(
            padding: EdgeInsets.symmetric(vertical: Get.setHeight(8)),
          ),
        ],
      ),
    );
  }

  Widget _tronRsourceWidget() {
    if (controller.coinModel?.isToken == false &&
        controller.coinType is TronChain) {
      return Obx(() => TronAssetsResourceWidget(
            coinModel: controller.coinModel!,
            addressModel: controller.addressModel!,
            tronResource: controller.tronResource,
            accountInfo: controller.tronAccountInfo.value,
          ));
    }
    return const SizedBox.shrink();
  }

  Widget _eosResourceWidget() {
    if (controller.coinModel?.isToken == false &&
        controller.coinType is EosChain) {
      return Obx(() => EOSAssetsResourceWidget(
            eosInfo: controller.eosAccountInfo.value,
            coinModel: controller.coinModel!,
            addressModel: controller.addressModel!,
          ));
    }
    return const SizedBox.shrink();
  }

  Padding _amountWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      child: Obx(() => Column(
            children: [
              Text(controller.balance.value,
                  style: TextStyle(
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.semibold,
                    fontSize: Get.setFontSize(40),
                    color: Get.theme.textPrimary,
                  )),
              Text(
                  BalanceManager.calculateFiatValue(
                      controller.balance.value, controller.coinModel),
                  style: TextStyle(
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.regular,
                    fontSize: Get.setFontSize(12),
                    color: Get.theme.textSecondary,
                  )),
            ],
          )),
    );
  }

  HighLightInkWell _addressWidget() {
    return HighLightInkWell(
      onTap: () => Get.copy(controller.getAddress()),
      child: Container(
        margin: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(16),
            horizontal: Get.setPaddingSize(16)),
        padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(4),
          horizontal: Get.setPaddingSize(12),
        ),
        decoration: ShapeDecoration(
          color: Get.theme.colorF3F3F5,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(Get.setRadius(17)),
          ),
        ),
        child: Text.rich(
            TextSpan(children: <InlineSpan>[
              TextSpan(
                text: AddressUtils.omitAddress(controller.getAddress()),
                style: TextStyle(
                    color: Get.theme.primary,
                    fontSize: Get.setFontSize(14),
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.regular,
                    overflow: TextOverflow.ellipsis),
              ),
              WidgetSpan(
                child: Padding(
                  padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                  child: ImageWidget(
                      assetUrl: "copy",
                      width: Get.setImageSize(16),
                      height: Get.setImageSize(16)),
                ),
              ),
            ]),
            maxLines: 1,
            overflow: TextOverflow.ellipsis),
      ),
    );
  }

  bool get _isFee =>
      controller.coinType!.isBitcoinSeries &&
      (controller.wallet is Pro3Wallet ||
          controller.wallet is Pro1Pro2Wallet ||
          controller.wallet is Pro2Wallet ||
          controller.wallet is Pro2PlusWallet ||
          controller.wallet is Pro1Wallet);

  double _actonWidget() {
    if (controller.coinType is EosChain && !controller.coinModel!.isToken!) {
      return Get.width;
    } else if (controller.coinType is TronChain &&
        !controller.coinModel!.isToken!) {
      return Get.width * 0.9;
    } else if (_isFee) {
      return Get.width * 0.9;
    } else {
      return Get.width * 0.7;
    }
  }

  SizedBox _actionWallet(TokenAssetsController controller) => SizedBox(
        width: _actonWidget(),
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setHeight(12), horizontal: Get.setHeight(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _actionItem('icon_receive', ID.receive.tr, onTap: () {
                if (controller.coinType is BinanceChain) {
                  Get.showToast(ID.stringBnbRecieveTip.tr,
                      toastMode: ToastMode.waring);
                  return;
                }
                Get.navigateWallet(
                  WalletAction.receive,
                  coinModel: controller.coinModel,
                  tokenModel: controller.tokenModel,
                  addressModel: controller.addressModel,
                );
              }),
              _sendActionItem(),
              Visibility(
                visible: controller.coinType is TronChain &&
                    !controller.coinModel!.isToken!,
                child: _actionItem('icon_cpu_lease', ID.stringStakeTitle.tr,
                    onTap: () {
                  Get.toNamed(
                    AppRoutes.stakeManagerPage,
                    arguments: {
                      GetArgumentsKey.tronAccountInfo:
                          controller.tronAccountInfo.value,
                      GetArgumentsKey.coinModel: controller.coinModel,
                      GetArgumentsKey.addressModel: controller.addressModel,
                      GetArgumentsKey.tronResource: controller.tronResource,
                    },
                  );
                }),
              ),
              Visibility(
                visible: controller.coinType is EosChain &&
                    !controller.coinModel!.isToken!,
                child: _actionItem(
                    'icon_export', ID.stringExportAccountTitle.tr, onTap: () {
                  Get.toNamed(
                    AppRoutes.eosExportAccountPage,
                    arguments: {
                      GetArgumentsKey.coinModel: controller.coinModel,
                      GetArgumentsKey.addressModel: controller.addressModel,
                    },
                  );
                }),
              ),
              Visibility(
                visible: controller.coinType is EosChain &&
                    !controller.coinModel!.isToken!,
                child: _actionItem('icon_cpu_lease', ID.stringCPULease.tr,
                    onTap: () {
                  Get.toWeb(url: AgreementUrl.eosCPULeaseUrl);
                }),
              ),
              Visibility(
                visible: _isFee,
                child: _actionItem('icon_best_fee', ID.feeTitle.tr, onTap: () {
                  Get.navigateWallet(
                    WalletAction.fee,
                    coinModel: controller.coinModel,
                    tokenModel: controller.tokenModel,
                    addressModel: controller.addressModel,
                    walletModel: controller.walletModel,
                  );
                }),
              ),
            ],
          ),
        ),
      );

  dynamic _sendActionItem() {
    return _actionItem(
        (controller.wallet.isP1P2P3Wallet && controller.coinType is! EosChain)
            ? 'icon_cpu_lease'
            : 'icon_send',
        (controller.wallet.isP1P2P3Wallet && controller.coinType is! EosChain)
            ? ID.stringSyncAmount.tr
            : ID.send.tr, onTap: () {
      Get.navigateWallet(
        (controller.wallet.isP1P2P3Wallet && controller.coinType is! EosChain)
            ? WalletAction.syncBalance
            : WalletAction.send,
        coinModel: controller.coinModel,
        tokenModel: controller.tokenModel,
        addressModel: controller.addressModel,
        walletModel: controller.walletModel,
      );
    });
  }

  Expanded _actionItem(String? assetUrl, String? title,
          {GestureTapCallback? onTap}) =>
      Expanded(
        flex: 1,
        child: GestureDetector(
          key: Key('token_assets_action_item_${title ?? ""}'),
          onTap: onTap,
          child: Column(
            children: [
              ImageWidget(
                assetUrl: assetUrl,
                width: Get.setImageSize(48),
                height: Get.setImageSize(48),
                fit: BoxFit.contain,
              ),
              SizedBox(
                height: Get.setHeight(8),
              ),
              Text(
                title ?? '',
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.medium,
                    color: Get.theme.textPrimary,
                    overflow: TextOverflow.ellipsis),
              )
            ],
          ),
        ),
      );
}
