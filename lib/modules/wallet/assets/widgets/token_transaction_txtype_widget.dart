/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-07-19 14:22:52
 */

import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/assets/details/token_transaction_details_controller.dart';
import 'package:coinbag/modules/wallet/assets/models/token_activity_model.dart';
import 'package:coinbag/modules/wallet/common/activity_action.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class TokenTransctionTxTypeWidget extends StatefulWidget {
  final TokenTransactionDeatilsController controller;
  const TokenTransctionTxTypeWidget({super.key, required this.controller});

  @override
  State<TokenTransctionTxTypeWidget> createState() =>
      _TokenTransctionTxTypeWidgetState();
}

class _TokenTransctionTxTypeWidgetState
    extends State<TokenTransctionTxTypeWidget> {
  CoinModel get coinModel => widget.controller.coinModel;
  TokenActivityModel get activityModel => widget.controller.activityModel;
  bool isShow = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(10)),
      child: Column(
        children: [
          _itemWidget(ID.coinTsTypeTitle.tr, activityModel.getActivityText()),
          Visibility(
            visible: !Get.isEmptyString(_fee),
            child: _itemWidget(
              activityModel.coinType! is TronChain
                  ? ID.stringTronNetworkFees.tr
                  : ID.feeTitle.tr,
              _fee,
              isFeeItem: true,
              callback: () {
                if (widget.controller.model?.extra?.isMore == true) {
                  isShow = !isShow;
                  setState(() {});
                }
              },
            ),
          ),
          _resourceWidget(),
          Visibility(
            visible: !Get.isEmptyString(_resource),
            child: _itemWidget(ID.stringTxSource.tr, _resource ?? ''),
          ),
          _itemWidget(ID.stringTxTime.tr, _txTime),
          SizedBox(
            height: Get.setPaddingSize(10),
          ),
          const DividerWidget()
        ],
      ),
    );
  }

  String? get _resource {
    CoinType coinType = activityModel.coinType!;
    if (coinType is! TronChain) return null;

    String energy = widget.controller.model?.extra?.energyUsage ?? '0';
    String bandwidth = widget.controller.model?.extra?.netUsage ?? '0';
    String? resource = '';
    if (bandwidth.moreThan('0')) {
      resource = '$bandwidth ${ID.stringTxBandwidth.tr}';
    }

    if (energy.moreThan('0')) {
      resource = '$resource $energy ${ID.stringTxEnergy.tr}';
    }

    return resource;
  }

  String get _txTime {
    if (Get.isEmptyString(widget.controller.model?.txTime)) {
      return CommonConstant.emptyAsstes;
    }
    return widget.controller.model!.txTime!;
  }

  String get _fee {
    String? fee = widget.controller.model?.extra?.fee;
    if (!Get.isEmptyString(fee)) {
      fee = fee!.div(
          pow(10, widget.controller.mainCoinModel.chainDecimal!).toString(),
          scale: widget.controller.mainCoinModel.chainDecimal!);
    }

    CoinType coinType = activityModel.coinType!;
    TokenActivityType activityType = activityModel.activityType;

    if (coinType is TronChain) {
      if (Get.isEmptyString(fee)) {
        return CommonConstant.emptyAsstes;
      } else {
        return '$fee ${widget.controller.mainCoinModel.symbol}';
      }
    } else {
      if (activityType == TokenActivityType.send ||
          activityType == TokenActivityType.own ||
          activityType == TokenActivityType.receive) {
        if (activityType == TokenActivityType.receive) return '';
        if (Get.isEmptyString(fee)) {
          return CommonConstant.emptyAsstes;
        } else {
          return '$fee ${widget.controller.mainCoinModel.symbol}';
        }
      }
    }

    return '';
  }

  GestureDetector _itemWidget(String title, String value,
          {Function? callback, bool isFeeItem = false}) =>
      GestureDetector(
        onTap: () {
          if (callback != null) {
            callback();
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child: Row(
            children: [
              Text(
                title,
                style: styleSecond_14,
              ),
              SizedBox(
                width: Get.setPaddingSize(8),
              ),
              Expanded(
                child: Text(
                  value,
                  maxLines: 1,
                  textAlign: TextAlign.end,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(14),
                      fontWeight: FontWeightX.semibold,
                      fontFamily: Get.setNumberFontFamily()),
                ),
              ),
              Visibility(
                visible:
                    isFeeItem && widget.controller.model?.extra?.isMore == true,
                child: Padding(
                  padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                  child: ImageWidget(
                    assetUrl:
                        isShow == false ? 'arrow_up_gray' : 'arrow_down_gray',
                    width: Get.setImageSize(10),
                    height: Get.setImageSize(10),
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  Visibility _resourceWidget() => Visibility(
        visible: isShow == true,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(12),
            vertical: Get.setPaddingSize(6),
          ),
          decoration: BoxDecoration(
            color: Get.theme.colorF9F9F9,
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Column(
            children: [
              _resourceItemWidget(
                ID.activedAccount.tr,
                widget.controller.model?.extra?.accountCreateFee,
              ),
              _resourceItemWidget(
                ID.remark.tr,
                widget.controller.model?.extra?.memoFee,
              ),
              _resourceItemWidget(
                ID.stringBurnedEnergy.tr,
                widget.controller.model?.extra?.energyFee,
              ),
              _resourceItemWidget(
                ID.stringBurnedBandwidth.tr,
                widget.controller.model?.extra?.netFee,
              ),
            ],
          ),
        ),
      );

  SingleChildRenderObjectWidget _resourceItemWidget(
      String title, String? value) {
    if (Get.isEmptyString(value)) return const SizedBox.shrink();
    if (!value!.moreThan('0')) return const SizedBox.shrink();
    int decimals = widget.controller.activityModel.coinType!.decimals;
    value = value.div(pow(10, decimals).toString(), scale: decimals);
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Get.setPaddingSize(6),
      ),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: Get.setFontSize(12),
              color: Get.theme.textSecondary,
            ),
          ),
          SizedBox(
            width: Get.setPaddingSize(4),
          ),
          Expanded(
            child: Text(
              '$value TRX',
              maxLines: 1,
              textAlign: TextAlign.end,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: Get.setFontSize(12),
                color: Get.theme.textSecondary,
                fontFamily: Get.setNumberFontFamily(),
              ),
            ),
          )
        ],
      ),
    );
  }
}
