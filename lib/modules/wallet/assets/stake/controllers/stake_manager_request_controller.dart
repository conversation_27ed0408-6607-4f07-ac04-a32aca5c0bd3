/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 15:19:07
 */

import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/tron_resource.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/models/stake_v1_model/stake_v1_model.dart';
import 'package:coinbag/modules/wallet/send/dialog/transfer_info_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

extension StakeManagerRequestController on StakeManagerController {
  /// v1 解锁交易
  void unstakeV1Request({
    required bool isOther,
    required String to,
    required String amount,
    required String resource,
  }) {
    TransferModel tsModel = TransferModel(
      chain: coinModel.chain!,
      type: TransferType.unstake,
      fromAddress: addressModel.address,
      toAddress: to,
      wallet: wallet,
      addressPath: addressModel.path,
      coinSymbol: coinModel.symbol,
      coinDecimal: coinModel.chainDecimal,
      walletId: walletModel.walletId,
    );
    tsModel.amount = amount;
    tsModel.isV1 = true;
    if (resource == APIConstant.energy) {
      tsModel.resourceType = TronResourceType.energy;
    } else {
      tsModel.resourceType = TronResourceType.bandwidth;
    }
    Get.showLoadingDialog();

    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.unstakeV1Transaction,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel.chain)
              .put(APIConstant.params, {
            APIConstant.ownerAddress: tsModel.fromAddress,
            APIConstant.receiverAddress: to,
            APIConstant.resource: resource,
            APIConstant.type: 'unfreeze',
          }).getRequestBody(),
        )),
        handleSuccess: false,
        handleError: false, (value) async {
      Get.dismissLoadingDialog();
      dynamic data = value.data;
      if (data is String) {
        Get.showToast(data, toastMode: ToastMode.failed);
      } else if (data is Map) {
        String? rawDataHex = data[GetArgumentsKey.rawDataHex] as String?;
        Map<String, dynamic>? rawData =
            data[GetArgumentsKey.rawData] as Map<String, dynamic>?;
        String? txID = data[GetArgumentsKey.txID] as String?;
        if (Get.isEmptyString(txID)) {
          Get.isTronResoueceResponseDataValid(data, isShowToast: true);
        } else {
          bool result = await Get.walletCore.verityTronRawDataHex(
                rawDataHex: rawDataHex ?? '',
                fromAddress: tsModel.fromAddress!,
                toAddress: tsModel.toAddress!,
                amount: tsModel.amount!,
                decimal: coinModel.chainDecimal!,
              ) ??
              false;
          if (result == false) {
            Get.showToast(ID.stringTronRawDataHexError.tr,
                toastMode: ToastMode.failed);
          } else {
            tsModel.rawDataHex = rawDataHex;
            tsModel.rawData = jsonEncode(rawData!);
            tsModel.txID = txID;
            _showPop(tsModel);
          }
        }
      }
    }, error: (_) {
      Get.dismissLoadingDialog();
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
    });
  }

  // v1 为他人质押列表
  void stakeV1ToOther() {
    stakeV1Data = [];
    showLoading();
    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.stakeV1ToOtherList,
          requestParams: RequestParams()
              .put(APIConstant.chain, walletModel.chain)
              .put(APIConstant.value, addressModel.address!)
              .getRequestBody(),
        )), (value) {
      final data = value.data;
      if (data != null) {
        if (data.containsKey('toAccount_list')) {
          List list = data['toAccount_list'];
          stakeV1Data = list.map((e) => StakeV1Model.fromJson(e)).toList();
        }
      }
    });
  }

  void _showPop(TransferModel tsModel) {
    TransferInfoDialog(
      tsModel: tsModel,
      coinModel: coinModel,
      addressModel: addressModel,
      mainCoinModel: coinModel,
      onTap: () {
        if (wallet is TouchWallet) {
          Get.toNamed(AppRoutes.touchSignPage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.walletModel: walletModel,
          });
        } else {
          Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.qrType: QRCodeType.transfer
          });
        }
      },
    ).showBottomSheet();
  }

  void withdrawRequest() {
    if (wallet.isUltraSeries || wallet is TouchWallet) {
      String chain = coinModel.chain!;

      TransferModel tsModel = TransferModel(
        chain: chain,
        type: TransferType.withdraw,
        fromAddress: addressModel.address,
        toAddress: addressModel.address,
        wallet: wallet,
        addressPath: addressModel.path,
        coinSymbol: coinModel.symbol,
        coinDecimal: coinModel.chainDecimal,
        walletId: walletModel.walletId,
      );
      tsModel.amount = accountInfo.withdrawValue;

      Get.showLoadingDialog();

      httpRequest(
          api.tronResource(BlockChainParamsManager.createParams(
            method: BlockChainAPI.withdrawTransaction,
            requestParams: RequestParams()
                .put(APIConstant.chain, chain)
                .put(APIConstant.params, {
              APIConstant.ownerAddress: addressModel.address!,
            }).getRequestBody(),
          )),
          handleSuccess: false,
          handleError: false, (value) async {
        Get.dismissLoadingDialog();
        dynamic data = value.data;
        if (data is String) {
          Get.showToast(data, toastMode: ToastMode.failed);
        } else if (data is Map) {
          String? rawDataHex = data[GetArgumentsKey.rawDataHex] as String?;
          Map<String, dynamic>? rawData =
              data[GetArgumentsKey.rawData] as Map<String, dynamic>?;
          String? txID = data[GetArgumentsKey.txID] as String?;
          if (Get.isEmptyString(txID)) {
            Get.isTronResoueceResponseDataValid(data, isShowToast: true);
          } else {
            /// 暂时无法验证 测试
            // bool result = await Get.walletCore.verityTronRawDataHex(
            //       rawDataHex: rawDataHex ?? '',
            //       fromAddress: addressModel.address!,
            //       toAddress: addressModel.address!,
            //       amount: tsModel.amount!,
            //       decimal: coinModel.chainDecimal!,
            //     ) ??
            //     false;
            // if (result == false) {
            //   Get.showToast(ID.stringTronRawDataHexError.tr,
            //       toastMode: ToastMode.failed);
            // } else {

            // }
            tsModel.rawDataHex = rawDataHex;
            tsModel.rawData = jsonEncode(rawData!);
            tsModel.txID = txID;
            _showPop(tsModel);
          }
        }
      }, error: (_) {
        Get.dismissLoadingDialog();
        Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      });
    } else {
      TronResourceManagerController.showTip(ID.stringTronNoSupportedTip.tr);
    }
  }

  void loadAccountInfoAndResource() {
    List<Future<dynamic>> futures = [];

    futures.add(
      api.getAccountInfo(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getTronAccountInfo,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel.chain!)
              .put(APIConstant.address, addressModel.address ?? "")
              .getRequestBody())),
    );

    futures.add(
      api.getAccountResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getAccountResource,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel.chain!)
              .put(APIConstant.address, addressModel.address ?? "")
              .getRequestBody())),
    );

    multiHttpRequest(futures, (value) async {
      if (value != null) {
        BaseResponseV1 accountResponse = value[0];
        BaseResponseV1 resourceResponse = value[1];

        if (accountResponse.data != null) {
          accountInfo = TronAccountInfo.fromJson(accountResponse.data);
          await Get.database.addressDao.updateTronAccountInfo(
              addressModel, jsonEncode(accountResponse.data));
        }

        if (resourceResponse.data != null) {
          TronResource resource = TronResource.fromJson(resourceResponse.data);
          // 资源详情赋值
          resource.resourceDetail = tronResource.resourceDetail;
          tronResource = resource;
          await Get.database.addressDao.updateTronResource(
              addressModel, jsonEncode(resourceResponse.data));
        }
      }
    });
  }
}
