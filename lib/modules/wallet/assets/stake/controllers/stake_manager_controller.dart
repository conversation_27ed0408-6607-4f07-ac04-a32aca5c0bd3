import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/tron_resource.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_manager_request_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/models/stake_v1_model/stake_v1_model.dart';
import 'package:coinbag/modules/wallet/assets/stake/widgets/stake_v1_detail_widget.dart';
import 'package:coinbag/modules/wallet/assets/stake/widgets/unstaking_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class StakeManagerController extends BaseController<BlockChainService> {
  late TronAccountInfo accountInfo;
  late TronResource tronResource;
  late CoinModel coinModel;
  late AddressModel addressModel;
  late WalletModel walletModel;
  late Wallet wallet;

  String totalAmount = '';
  String totalStakeAmount = '';

  /// v1 为他人质押列表
  List<StakeV1Model> stakeV1Data = [];

  @override
  void onInit() {
    final arg = Get.arguments;
    accountInfo = arg[GetArgumentsKey.tronAccountInfo];
    tronResource = arg[GetArgumentsKey.tronResource];
    coinModel = arg[GetArgumentsKey.coinModel];
    addressModel = arg[GetArgumentsKey.addressModel];

    totalStakeAmount =
        accountInfo.frozenV1Amount.add(accountInfo.frozenV2Amount);

    totalAmount = totalStakeAmount.add(accountInfo.balanceValue);

    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    loadAccountInfoAndResource();
    walletModel = (await Get.database.walletDao.getCheckedWallets())!;
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);
  }

  /// 质押
  void stakeAction() {
    if (wallet.isUltraSeries || wallet is TouchWallet) {
      Get.toNamed(AppRoutes.stakePage, arguments: {
        GetArgumentsKey.coinModel: coinModel,
        GetArgumentsKey.tronAccountInfo: accountInfo,
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.tronResourceType: TronResourceType.energy,
        GetArgumentsKey.walletModel: walletModel,
        GetArgumentsKey.wallet: wallet,
      });
    } else {
      TronResourceManagerController.showTip(ID.stringTronNoSupportedStake2.tr);
    }
  }

  /// 解锁中
  void unstakingAction() {
    if (accountInfo.unfrozenV2 == null || accountInfo.unfrozenV2!.isEmpty) {
      Get.showToast(ID.emptyData.tr, toastMode: ToastMode.waring);
      return;
    }
    UnstakingWidget.show(accountInfo.unfrozenV2!);
  }

  /// 提取
  dynamic withdrawAction() => withdrawRequest();

  void unstakeAction() {
    Get.showBottomSheet(
        hideHeader: true,
        paddingBottom: 0,
        bodyWidget: Column(
          children: [
            SizedBox(height: Get.setPaddingSize(8)),
            HighLightInkWell(
              onTap: () => _unstake2(),
              child: SizedBox(
                width: Get.width,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Get.setPaddingSize(16),
                    vertical: Get.setPaddingSize(12),
                  ),
                  child: Text(
                    ID.stringUnstake2.tr,
                    style: stylePrimary_16_m,
                  ),
                ),
              ),
            ),
            HighLightInkWell(
              onTap: () {
                Get.back();
                StakeV1DetialWidget.show(this);
              },
              child: SizedBox(
                width: Get.width,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Get.setPaddingSize(16),
                    vertical: Get.setPaddingSize(12),
                  ),
                  child: Text(
                    ID.stringUnstake1.tr,
                    style: stylePrimary_16_m,
                  ),
                ),
              ),
            ),
            SizedBox(height: Get.setPaddingSize(12)),
          ],
        ),
        bottomWidget: const BottomCancelWidget());
  }

  void _unstake2() {
    if (wallet.isUltraSeries || wallet is TouchWallet) {
      Get.offAndToNamed(AppRoutes.unstakePage, arguments: {
        GetArgumentsKey.coinModel: coinModel,
        GetArgumentsKey.tronAccountInfo: accountInfo,
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.tronResourceType: TronResourceType.energy,
        GetArgumentsKey.walletModel: walletModel,
        GetArgumentsKey.wallet: wallet,
      });
    } else {
      Get.back();
      TronResourceManagerController.showTip(
          ID.stringTronNoSupportedUnstake2.tr);
    }
  }

  @override
  void loadData() {}
}

class StakeManagerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => StakeManagerController());
  }
}
