/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-20 14:21:24
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_amount_widget.dart';
import 'package:coinbag/modules/wallet/assets/stake/widgets/stake_widget.dart';
import 'package:coinbag/modules/wallet/send/dialog/transfer_info_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/segment/segment_control_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class StakeController extends BaseController<BlockChainService> {
  TronResourceType type = TronResourceType.energy;
  late SegmentedController segmentedController;
  late PageController pagerController;
  ScrollController scrollController = ScrollController();
  final List<Widget> pagerList = [
    const StakeWidget<StakeEnergyController>(type: TronResourceType.energy),
    const StakeWidget<StakeBandwidthController>(
        type: TronResourceType.bandwidth),
  ];
  final List<String> tabList = [
    ID.stringEnergy.tr,
    ID.stringBandwidth.tr,
  ];

  late CoinModel coinModel;
  late TronAccountInfo accountInfo;
  late AddressModel addressModel;
  late WalletModel walletModel;

  String stakeTitle = '';
  String stakeValue = '';
  String hintText = '';
  String tipText = '';
  String errorText = '';
  ButtonStatus buttonStatus = ButtonStatus.disable;
  InputResourceType inputType = InputResourceType.trx;
  TextEditingController amountController = TextEditingController();
  String updateId = GetKey.stakeEnergyId;

  String get cost => '1';

  late TransferModel tsModel;

  @override
  void onInit() {
    coinModel = Get.arguments[GetArgumentsKey.coinModel];
    accountInfo = Get.arguments[GetArgumentsKey.tronAccountInfo];
    addressModel = Get.arguments[GetArgumentsKey.addressModel];
    walletModel = Get.arguments[GetArgumentsKey.walletModel];
    Wallet wallet = Get.arguments[GetArgumentsKey.wallet];

    TronResourceType currentType =
        Get.arguments[GetArgumentsKey.tronResourceType];
    int selectedIndex = 0;
    if (currentType == TronResourceType.energy) {
      selectedIndex = 0;
    } else {
      selectedIndex = 1;
    }
    segmentedController = SegmentedController(selectedIndex: selectedIndex.obs);
    pagerController = PageController(initialPage: selectedIndex);

    tsModel = TransferModel(
      chain: coinModel.chain!,
      type: TransferType.stake,
      fromAddress: addressModel.address,
      toAddress: addressModel.address,
      wallet: wallet,
      addressPath: addressModel.path,
      coinSymbol: coinModel.symbol,
      coinDecimal: coinModel.chainDecimal,
      walletId: addressModel.walletId,
    )..resourceType = type;

    updateAmountAction();
    super.onInit();
  }

  void updateAmountAction() {
    errorText = '';
    String balance =
        accountInfo.balanceValue.decimal(scale: 0, roundMode: RoundMode.down);
    String amount = amountController.text;
    if (Get.isEmptyString(amount)) {
      amount = '0';
    }

    if (inputType == InputResourceType.trx) {
      stakeTitle = ID.stringStakeAmount.tr;
      stakeValue = ID.stringTrxCanUse.trParams({
        'value': balance,
      });
      hintText = ID.stringStakeHint2.tr;

      String resource =
          amount.mul(cost).decimal(scale: 0, roundMode: RoundMode.down);
      if (resource.equal('0')) {
        resource = '--';
      }

      tipText = ID.stringStakeTip2.trParams({
        'value': resource,
        'vote': amount.equal('0') ? '--' : amount,
        'resource': type == TronResourceType.energy
            ? ID.stringTxEnergy.tr
            : ID.stringTxBandwidth.tr,
      });

      errorText = amount.moreThan(balance) ? ID.stringResourceMaxTip.tr : '';

      tsModel.amount = amount;
    } else {
      stakeTitle = ID.stringGetAmount.tr;
      String resourceValue =
          balance.mul(cost).decimal(scale: 0, roundMode: RoundMode.down);
      stakeValue = ID.stringStakeGetResource.trParams({
        'value': resourceValue,
        'resource': type == TronResourceType.energy
            ? ID.stringTxEnergy.tr
            : ID.stringTxBandwidth.tr,
      });
      hintText = ID.stringStakeHint1.tr;

      String trx = amount.div(cost, scale: 0, roundMode: RoundMode.up);
      tsModel.amount = amount;
      if (trx.equal('0')) {
        trx = '--';
      }

      tipText = ID.stringStakeTip1.trParams({
        'trx': trx,
        'vote': trx,
      });
      errorText =
          amount.moreThan(resourceValue) ? ID.stringResourceMaxTip.tr : '';
    }

    if (!Get.isEmptyString(errorText) || amount.equal('0')) {
      buttonStatus = ButtonStatus.disable;
    } else {
      buttonStatus = ButtonStatus.enable;
    }

    update([updateId]);
  }

  void _showPop() {
    TransferInfoDialog(
      tsModel: tsModel,
      coinModel: coinModel,
      addressModel: addressModel,
      mainCoinModel: coinModel,
      onTap: () {
        if (tsModel.wallet is TouchWallet) {
          Get.toNamed(AppRoutes.touchSignPage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.walletModel: walletModel,
          });
        } else {
          Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
            GetArgumentsKey.transferModel: tsModel,
            GetArgumentsKey.qrType: QRCodeType.transfer
          });
        }
      },
    ).showBottomSheet();
  }

  @override
  void loadData() {
    buttonStatus = ButtonStatus.loading;
    update([updateId]);

    int amount = DecimalUtils.toIntSafe(
        tsModel.amount!.mul(pow(10, tsModel.coinDecimal!).toString()));

    httpRequest(
        api.tronResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.trxTransactionInfo,
          requestParams: RequestParams()
              .put(APIConstant.chain, tsModel.chain)
              .put(APIConstant.params, {
            APIConstant.resource: type == TronResourceType.energy
                ? APIConstant.energy
                : APIConstant.bandWidth,
            APIConstant.frozenDuration: 3,
            APIConstant.receiverAddress: tsModel.toAddress,
            APIConstant.frozenBalance: amount,
            APIConstant.ownerAddress: tsModel.fromAddress,
            APIConstant.type: 'freeze',
          }).getRequestBody(),
        )),
        handleSuccess: false,
        handleError: false, (value) async {
      dynamic data = value.data;
      if (data is String) {
        Get.showToast(data, toastMode: ToastMode.failed);
      } else if (data is Map) {
        String? rawDataHex = data[GetArgumentsKey.rawDataHex] as String?;
        Map<String, dynamic>? rawData =
            data[GetArgumentsKey.rawData] as Map<String, dynamic>?;
        String? txID = data[GetArgumentsKey.txID] as String?;
        if (Get.isEmptyString(txID)) {
          Get.isTronResoueceResponseDataValid(data, isShowToast: true);
        } else {
          bool result = await Get.walletCore.verityTronRawDataHex(
                rawDataHex: rawDataHex ?? '',
                fromAddress: tsModel.fromAddress!,
                toAddress: tsModel.toAddress!,
                amount: tsModel.amount!,
                decimal: coinModel.chainDecimal!,
              ) ??
              false;
          if (result == false) {
            Get.showToast(ID.stringTronRawDataHexError.tr,
                toastMode: ToastMode.failed);
          } else {
            tsModel.rawDataHex = rawDataHex;
            tsModel.rawData = jsonEncode(rawData!);
            tsModel.txID = txID;
            _showPop();
          }
        }
      }
      buttonStatus = ButtonStatus.enable;
      update([updateId]);
    }, error: (_) {
      Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
      buttonStatus = ButtonStatus.enable;
      update([updateId]);
    });
  }

  @override
  void onClose() {
    super.onClose();
    segmentedController.onClose();
    scrollController.dispose();
    pagerController.dispose();
  }
}

class StakeEnergyController extends StakeController {
  @override
  void onInit() {
    type = TronResourceType.energy;
    updateId = GetKey.stakeEnergyId;
    super.onInit();
  }

  @override
  String get cost {
    if (accountInfo.energyCost == null) return '1';
    return accountInfo.energyCost!.toString();
  }
}

class StakeBandwidthController extends StakeController {
  @override
  void onInit() {
    type = TronResourceType.bandwidth;
    updateId = GetKey.stakeBandwidthId;
    super.onInit();
  }

  @override
  String get cost {
    if (accountInfo.netCost == null) return '1';
    return accountInfo.netCost!.toString();
  }
}

class StakeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => StakeController());
    Get.lazyPut(() => StakeEnergyController());
    Get.lazyPut(() => StakeBandwidthController());
  }
}
