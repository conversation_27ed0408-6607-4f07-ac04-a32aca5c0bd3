/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-07-01 09:31:51
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_border_widget.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/widgets/stake_v1_detail_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

class StakeManagerPage extends BaseStatelessWidget<StakeManagerController> {
  const StakeManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringStakeTitle.tr),
      bottomNavigationBar: _bottomWidget(),
      body: controller.obx(
        (value) => _bodyWidget(),
        onLoading: _bodyWidget(),
        onEmpty: _bodyWidget(),
        onError: (error) => _bodyWidget(),
      ),
    );
  }

  SingleChildScrollView _bodyWidget() => SingleChildScrollView(
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        child: Column(
          children: [
            _headerWidget(),
            SizedBox(height: Get.setPaddingSize(36)),
            _stake2Widget(),
            SizedBox(height: Get.setPaddingSize(24)),
            _stakeV1Widget(),
            SizedBox(height: Get.setPaddingSize(24)),
            _resourceWidget(TronResourceType.bandwidth),
            SizedBox(height: Get.setPaddingSize(24)),
            _resourceWidget(TronResourceType.energy),
          ],
        ),
      );

  double _rate(int available, int total) {
    if (total <= 0) return 0;
    String str = available.toString().div(total.toString(), scale: 2);
    return double.parse(str);
  }

  Container _resourceWidget(TronResourceType type) {
    String title = '';
    String value = '';
    String tips = '';
    double rate = 1;
    Color color = Get.theme.color02B58A;
    if (type == TronResourceType.energy) {
      title = ID.stringTxEnergy.tr;
      tips = ID.stringStakeEnergyTips.tr;
      value =
          '${controller.tronResource.availableEnergy} / ${controller.tronResource.totalEnergy}';
      rate = _rate(controller.tronResource.availableEnergy,
          controller.tronResource.totalEnergy);
      color = Get.theme.color02B58A;
    } else {
      title = ID.stringTxBandwidth.tr;
      tips = ID.stringStakeBandwidthTips.tr;
      value =
          '${controller.tronResource.availableNet} / ${controller.tronResource.totalNet}';
      rate = _rate(controller.tronResource.availableNet,
          controller.tronResource.totalNet);
      color = Get.theme.colorFF6A16;
    }
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Get.theme.colorECECEC, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16),
              top: Get.setPaddingSize(16),
              bottom: Get.setPaddingSize(18),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: stylePrimary_14_m,
                    ),
                    SizedBox(width: Get.setPaddingSize(8)),
                    Expanded(
                      child: Text(
                        value,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          color: Get.theme.textPrimary,
                          fontSize: Get.setFontSize(14),
                          fontWeight: FontWeightX.medium,
                          fontFamily: Get.setNumberFontFamily(),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: Get.setPaddingSize(8)),
                LinearProgressIndicator(
                    minHeight: Get.setHeight(3),
                    value: rate,
                    backgroundColor: Get.theme.colorECECEC,
                    valueColor: AlwaysStoppedAnimation(color),
                    borderRadius:
                        BorderRadius.all(Radius.circular(Get.setRadius(1.5)))),
              ],
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(Get.setRadius(10)),
              bottomLeft: Radius.circular(Get.setRadius(10)),
            ),
            child: Container(
              color: Get.theme.colorECECEC,
              width: Get.width,
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(8),
              ),
              child: Text(
                tips,
                style: styleSecond_14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  ResourceBorderWidget _stakeV1Widget() {
    return ResourceBorderWidget(
      headerWidget: _stakeTotalWidget(
          type: '1.0',
          value: controller.accountInfo.frozenV1Amount,
          callabck: () => StakeV1DetialWidget.show(controller)),
    );
  }

  ResourceBorderWidget _stake2Widget() {
    return ResourceBorderWidget(
      headerWidget: _stake2HeaderWidget(),
      contentWidget: Column(
        children: [
          _unStakeWidget(),
          _withdrawWidget(),
          DividerWidget(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(16)),
          ),
          _stake2WebWidget(),
        ],
      ),
    );
  }

  GestureDetector _stake2WebWidget() {
    return GestureDetector(
      onTap: () => Get.toWeb(url: TronUrl.tronStakeV2),
      child: SizedBox(
        width: Get.width,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              ID.stringUnderstandingStaking.tr,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(12),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily(),
              ),
            ),
            SizedBox(height: Get.setPaddingSize(2)),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(10),
              height: Get.setImageSize(10),
            ),
          ],
        ),
      ),
    );
  }

  Visibility _withdrawWidget() {
    return Visibility(
      visible: controller.accountInfo.withdrawValue.moreThan('0'),
      child: Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(12)),
        child: Row(
          children: [
            Text(
              ID.stringCanExtract.tr,
              style: stylePrimary_14_m,
            ),
            SizedBox(width: Get.setPaddingSize(8)),
            Expanded(
              child: Text(
                '${controller.accountInfo.withdrawValue} TRX',
                textAlign: TextAlign.end,
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              ),
            ),
            SizedBox(width: Get.setPaddingSize(8)),
            _stakeV2WithdrawWidget(),
          ],
        ),
      ),
    );
  }

  GestureDetector _stakeV2WithdrawWidget() {
    return GestureDetector(
      onTap: () => controller.withdrawAction(),
      child: Container(
        height: Get.setHeight(24),
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(8),
        ),
        decoration: BoxDecoration(
          color: Get.theme.textPrimary,
          borderRadius: BorderRadius.circular(Get.setRadius(12)),
        ),
        child: Center(
          child: Text(
            ID.stringWithraw.tr,
            style: TextStyle(
              color: Get.theme.white,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setFontFamily(),
            ),
          ),
        ),
      ),
    );
  }

  GestureDetector _unStakeWidget() {
    return GestureDetector(
      onTap: () => controller.unstakingAction(),
      behavior: HitTestBehavior.translucent,
      child: Row(
        children: [
          Text(
            ID.stringUnlocking.tr,
            style: stylePrimary_14_m,
          ),
          SizedBox(width: Get.setPaddingSize(8)),
          Expanded(
            child: Text(
              '${controller.accountInfo.unfrozenV2Amount} TRX',
              textAlign: TextAlign.end,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily(),
              ),
            ),
          ),
          SizedBox(width: Get.setPaddingSize(2)),
          ImageWidget(
            assetUrl: 'icon_new_arrow01',
            width: Get.setImageSize(10),
            height: Get.setImageSize(10),
          ),
        ],
      ),
    );
  }

  Column _stake2HeaderWidget() {
    return Column(
      children: [
        _stakeTotalWidget(type: '2.0', value: controller.totalStakeAmount),
        SizedBox(height: Get.setPaddingSize(8)),
        _stake2ProgressWidget(),
        SizedBox(height: Get.setPaddingSize(8)),
        _stake2StakeWidget(),
      ],
    );
  }

  Row _stake2StakeWidget() {
    return Row(
      children: [
        Text(
          '${controller.accountInfo.frozenV2Energy} TRX',
          style: TextStyle(
            color: Get.theme.textSecondary,
            fontSize: Get.setFontSize(14),
            fontWeight: FontWeightX.medium,
            fontFamily: Get.setNumberFontFamily(),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          child: Text(
            '${controller.accountInfo.frozenV2Bandwidth} TRX',
            textAlign: TextAlign.end,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setNumberFontFamily(),
            ),
          ),
        ),
      ],
    );
  }

  Row _stake2ProgressWidget() {
    bool isZero = false;
    if (controller.accountInfo.frozenV2Energy.equal('0') &&
        controller.accountInfo.frozenV2Bandwidth.equal('0')) {
      isZero = true;
    }

    return Row(
      children: [
        Expanded(
          flex: isZero
              ? 1
              : DecimalUtils.toIntSafe(controller.accountInfo.frozenV2Energy,
                  defaultValue: 1),
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(Get.setRadius(2)),
              bottomLeft: Radius.circular(Get.setRadius(2)),
            ),
            child: Container(
              color: isZero ? Get.theme.colorECECEC : Get.theme.color02B58A,
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(4),
                vertical: Get.setPaddingSize(1),
              ),
              child: Text(
                ID.stringTxEnergy.tr,
                maxLines: 1,
                style: TextStyle(
                  color: isZero ? Get.theme.textSecondary : Get.theme.white,
                  fontSize: Get.setFontSize(12),
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Expanded(
          flex: isZero
              ? 1
              : DecimalUtils.toIntSafe(controller.accountInfo.frozenV2Bandwidth,
                  defaultValue: 1),
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(Get.setRadius(2)),
              bottomRight: Radius.circular(Get.setRadius(2)),
            ),
            child: Container(
              color: isZero ? Get.theme.colorECECEC : Get.theme.colorFF6A16,
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(4),
                vertical: Get.setPaddingSize(1),
              ),
              child: Text(
                ID.stringTxBandwidth.tr,
                maxLines: 1,
                textAlign: TextAlign.end,
                style: TextStyle(
                  color: isZero ? Get.theme.textSecondary : Get.theme.white,
                  fontSize: Get.setFontSize(12),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  GestureDetector _stakeTotalWidget(
      {required String type, required String value, Function()? callabck}) {
    return GestureDetector(
      onTap: callabck,
      behavior: HitTestBehavior.translucent,
      child: Row(
        children: [
          Text(
            ID.stringStakeTitle.tr,
            style: stylePrimary_14_m,
          ),
          SizedBox(width: Get.setPaddingSize(4)),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(6),
            ),
            decoration: BoxDecoration(
              color: Get.theme.colorFF6A16.withAlpha(20),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              type,
              style: TextStyle(
                fontSize: Get.setFontSize(14),
                color: Get.theme.colorFF6A16,
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily(),
              ),
            ),
          ),
          Expanded(
            child: Text(
              '$value TRX',
              textAlign: TextAlign.end,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily(),
              ),
            ),
          ),
          Visibility(
            visible: callabck != null,
            child: Padding(
              padding: EdgeInsets.only(left: Get.setPaddingSize(2)),
              child: ImageWidget(
                assetUrl: 'icon_new_arrow01',
                width: Get.setImageSize(10),
                height: Get.setImageSize(10),
              ),
            ),
          )
        ],
      ),
    );
  }

  Row _headerWidget() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Column(
            children: [
              Text(
                ID.stringTotalAmount.tr,
                style: styleSecond_14,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              Text(
                '${controller.totalAmount} TRX',
                style: stylePrimary_16_m,
              ),
            ],
          ),
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          flex: 1,
          child: Column(
            children: [
              Text(
                ID.stringTotalStakeAmount.tr,
                style: styleSecond_14,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              Text(
                '${controller.totalStakeAmount} TRX',
                style: stylePrimary_16_m,
              ),
            ],
          ),
        )
      ],
    );
  }

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        top: Get.setPaddingSize(16),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: OutlinedButtonWidget(
              text: ID.stringUnstakeTitle.tr,
              onPressed: () => controller.unstakeAction(),
            ),
          ),
          SizedBox(width: Get.setPaddingSize(16)),
          Expanded(
            flex: 1,
            child: ButtonWidget(
              text: ID.stringStakeTitle.tr,
              onPressed: () => controller.stakeAction(),
            ),
          ),
        ],
      ),
    );
  }
}
