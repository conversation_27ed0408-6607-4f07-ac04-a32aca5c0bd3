/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-20 10:30:57
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/behavior/over_scroll_behavior.dart';
import 'package:coinbag/widgets/segment/segment_control_widget.dart';
import 'package:coinbag/widgets/sliver/sliver_app_bar_delegate.dart';
import 'package:flutter/material.dart';

class StakePage extends BaseStatelessWidget<StakeController> {
  const StakePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: baseAppBar(title: '${ID.stringStakeTitle.tr} TRX 2.0'),
      body: ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: NestedScrollView(
            controller: controller.scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
              return <Widget>[
                SliverPersistentHeader(
                  pinned: true,
                  floating: false,
                  delegate: SliverAppBarDelegate(
                    minHeight: Get.setHeight(44),
                    maxHeight: Get.setHeight(44),
                    child: Container(
                      color: Get.theme.bgColor,
                      child: Center(
                        child: SegmentControlWidget(
                          values: controller.tabList,
                          controller: controller.segmentedController,
                          onChanged: (index) {
                            controller.pagerController.jumpToPage(index);
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ];
            },
            body: PageView.builder(
                itemCount: controller.tabList.length,
                onPageChanged: (index) {
                  controller.segmentedController.selectedIndex.value = index;
                },
                controller: controller.pagerController,
                itemBuilder: (_, int index) => controller.pagerList[index])),
      ),
    );
  }
}
