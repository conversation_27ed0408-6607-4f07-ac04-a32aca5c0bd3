/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 15:25:10
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/widgets/resource_amount_widget.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/model/transfer_model.dart';

class StakeWidget<T extends StakeController> extends BaseStatelessWidget<T> {
  final TronResourceType type;
  const StakeWidget({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<T>(
      id: controller.updateId,
      builder: (_) {
        return KeepAliveWrapper(
          child: KeyboardDismissWidget(
            child: Scaffold(
              bottomNavigationBar: _bottomWidget(),
              resizeToAvoidBottomInset: false,
              body: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Get.setPaddingSize(16),
                  vertical: Get.setPaddingSize(36),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _titleWidget(),
                    SizedBox(height: Get.setPaddingSize(8)),
                    _inputWidget(),
                    SizedBox(height: Get.setPaddingSize(4)),
                    _tipWidget(),
                    _errorWidget(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Visibility _errorWidget() {
    return Visibility(
      visible: !Get.isEmptyString(controller.errorText),
      child: Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
        child: Text(
          controller.errorText,
          style: TextStyle(
            fontSize: Get.setFontSize(12),
            color: Get.theme.colorF44D4D,
          ),
        ),
      ),
    );
  }

  Text _tipWidget() {
    return Text(
      controller.tipText,
      style: TextStyle(
        fontSize: Get.setFontSize(12),
        color: Get.theme.textSecondary,
      ),
    );
  }

  TextFieldWidget _inputWidget() {
    return TextFieldWidget(
      hintText: controller.hintText,
      controller: controller.amountController,
      maxLines: 1,
      keyboardType: const TextInputType.numberWithOptions(decimal: false),
      onValueChanged: (value) => controller.updateAmountAction(),
      suffixIcon: ResourceAmountWidget(
        resourceType: controller.type,
        tsType: TransferType.stake,
        action: (type) {
          controller.inputType = type;
          controller.amountController.text = '';
          controller.updateAmountAction();
        },
      ),
    );
  }

  Row _titleWidget() {
    return Row(
      children: [
        Text(
          controller.stakeTitle,
          style: styleSecond_14,
        ),
        SizedBox(width: Get.setPaddingSize(8)),
        Expanded(
          child: Text(
            controller.stakeValue,
            textAlign: TextAlign.end,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.regular,
            ),
          ),
        ),
      ],
    );
  }

  Padding _bottomWidget() => Padding(
        padding: EdgeInsets.only(
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(ID.stringStakeTip3.tr, style: styleSecond_14),
            SizedBox(height: Get.setPaddingSize(4)),
            Text(ID.stringStakeTip4.tr, style: styleSecond_14),
            SizedBox(height: Get.setPaddingSize(18)),
            ButtonWidget(
              text: ID.stringStakeTitle.tr,
              width: Get.width,
              buttonStatus: controller.buttonStatus,
              onPressed: () => controller.loadData(),
            ),
          ],
        ),
      );
}
