/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 10:26:13
 */
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/frozen.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class UnstakingWidget extends StatelessWidget {
  final List<Frozen> data;
  const UnstakingWidget({
    super.key,
    required this.data,
  });

  static void show(List<Frozen> data) {
    Get.showBottomSheet(
      title: ID.stringUnlockingTrx.tr,
      paddingBottom: 0,
      bodyWidget: UnstakingWidget(data: data),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: Column(
        children: _widgets(),
      ),
    );
  }

  List<Padding> _widgets() => data
      .map((model) => Padding(
            padding: EdgeInsets.only(top: Get.setPaddingSize(16)),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        model.type == 'ENERGY'
                            ? ID.stringTxEnergy.tr
                            : ID.stringTxBandwidth.tr,
                        style: stylePrimary_14_m,
                      ),
                      SizedBox(height: Get.setPaddingSize(4)),
                      Text(
                        '${model.unfreezeValue.div(pow(10, TronChain.get.decimals).toString())} TRX',
                        style: styleSecond_14,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(8)),
                Text(
                  ID.stringDayToWithdraw
                      .trParams({'day': model.unfreezeDay.toString()}),
                  style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(14),
                  ),
                )
              ],
            ),
          ))
      .toList();
}
