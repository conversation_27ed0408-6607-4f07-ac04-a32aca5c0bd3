/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 16:31:58
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_manager_request_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/status/status_widget.dart';
import 'package:flutter/material.dart';

class StakeV1DetialWidget extends StatelessWidget {
  final StakeManagerController controller;
  const StakeV1DetialWidget({super.key, required this.controller});

  static void show(StakeManagerController controller) {
    Get.showBottomSheet(
      title: ID.stringStake1DetialTitle.tr,
      bodyWidget: StakeV1DetialWidget(controller: controller),
    );
    controller.stakeV1ToOther();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: Get.setPaddingSize(16),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ownWidget(),
          DividerWidget(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(24))),
          _otherWidget(),
        ],
      ),
    );
  }

  Column _otherWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          ID.stringStakeToOtherTitle.tr,
          style: TextStyle(
            color: Get.theme.textPrimary,
            fontSize: Get.setFontSize(14),
          ),
        ),
        controller.obx(
          (state) => controller.stakeV1Data.isEmpty
              ? _emptyWidget()
              : _otherStakeWidget(),
          onLoading: _loadingWidget(),
          onEmpty: _emptyWidget(),
          onError: (error) =>
              AppErrorWidget(onRefresh: () => controller.stakeV1ToOther()),
        )
      ],
    );
  }

  Column _otherStakeWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: controller.stakeV1Data
          .map((e) => Padding(
                padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            e.type == 'ENERGY'
                                ? ID.stringTxEnergy.tr
                                : ID.stringTxBandwidth.tr,
                            style: stylePrimary_14_m,
                          ),
                          SizedBox(height: Get.setPaddingSize(4)),
                          Text(
                            '${e.unfreezeAmountValue} TRX',
                            style: TextStyle(
                              color: Get.theme.textSecondary,
                              fontSize: Get.setFontSize(14),
                              fontFamily: Get.setNumberFontFamily(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: Get.setPaddingSize(8)),
                    _actionWidget(ID.stringUnstakeTitle.tr, () {
                      Get.back();
                      controller.unstakeV1Request(
                        isOther: true,
                        to: e.to ?? '',
                        amount: e.unfreezeAmountValue,
                        resource: e.type ?? '',
                      );
                    }),
                  ],
                ),
              ))
          .toList(),
    );
  }

  Padding _emptyWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(50)),
      child: const AppEmptyWidget(),
    );
  }

  Padding _loadingWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(50)),
      child: const AppLoadingWidget(),
    );
  }

  Column _ownWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          ID.stringStakeToOwnTitle.tr,
          style: TextStyle(
            color: Get.theme.textPrimary,
            fontSize: Get.setFontSize(14),
          ),
        ),
        SizedBox(height: Get.setPaddingSize(12)),
        SizedBox(
          width: Get.width * 0.6,
          child: Row(
            children: [
              Flexible(
                  child: _actionWidget(
                ID.stringUnstakeEnergy.tr,
                () {
                  Get.back();
                  controller.unstakeV1Request(
                    isOther: false,
                    to: controller.addressModel.address!,
                    amount: '0',
                    resource: APIConstant.energy,
                  );
                },
              )),
              SizedBox(width: Get.setPaddingSize(16)),
              Flexible(
                  child: _actionWidget(
                ID.stringUnstakeBandwidth.tr,
                () {
                  Get.back();
                  controller.unstakeV1Request(
                    isOther: false,
                    to: controller.addressModel.address!,
                    amount: '0',
                    resource: APIConstant.bandWidth,
                  );
                },
              )),
            ],
          ),
        )
      ],
    );
  }

  HighLightInkWell _actionWidget(String title, Function() callback) {
    return HighLightInkWell(
      onTap: callback,
      child: Container(
        height: Get.setHeight(30),
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(12),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Get.setRadius(15)),
          color: Get.theme.black,
        ),
        child: Center(
          child: Text(
            title,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.white,
              fontSize: Get.setFontSize(14),
            ),
          ),
        ),
      ),
    );
  }
}
