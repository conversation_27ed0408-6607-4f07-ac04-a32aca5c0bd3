import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/unstake_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class UnstakeWidget<T extends UnstakeController>
    extends BaseStatelessWidget<T> {
  const UnstakeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<T>(
      id: controller.updateId,
      builder: (_) {
        return KeepAliveWrapper(
          child: KeyboardDismissWidget(
            child: Scaffold(
              bottomNavigationBar: _bottomWidget(),
              body: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Get.setPaddingSize(16),
                  vertical: Get.setPaddingSize(24),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _titleWidget(),
                    SizedBox(height: Get.setPaddingSize(8)),
                    TextFieldWidget(
                      hintText: ID.stringUnstakeHint.tr,
                      controller: controller.amountController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: false),
                      onValueChanged: (value) {
                        controller.updateAmount();
                      },
                    ),
                    SizedBox(height: Get.setPaddingSize(4)),
                    Visibility(
                      visible: !Get.isEmptyString(controller.errorText),
                      child: Text(
                        controller.errorText,
                        style: TextStyle(
                          fontSize: Get.setFontSize(12),
                          color: Get.theme.colorF44D4D,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Text _titleWidget() {
    return Text(
      ID.stringCanUnstakeTitle.trParams({'value': controller.balance}),
      style: styleSecond_14,
    );
  }

  Padding _bottomWidget() => Padding(
        padding: EdgeInsets.only(
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(ID.stringUnstakeTip.tr, style: styleSecond_14),
            SizedBox(height: Get.setPaddingSize(18)),
            ButtonWidget(
              text: ID.stringUnstakeTitle.tr,
              width: Get.width,
              buttonStatus: controller.buttonStatus,
              onPressed: () => controller.loadData(),
            ),
          ],
        ),
      );
}
