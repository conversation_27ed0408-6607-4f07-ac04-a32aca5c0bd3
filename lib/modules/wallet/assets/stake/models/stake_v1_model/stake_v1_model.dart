/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-23 14:36:32
 */
import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

part 'stake_v1_model.g.dart';

@JsonSerializable()
class StakeV1Model {
  String? type;
  String? to;
  @JsonKey(name: 'unfreeze_amount')
  int? unfreezeAmount;

  StakeV1Model({this.type, this.to, this.unfreezeAmount});

  factory StakeV1Model.fromJson(Map<String, dynamic> json) {
    return _$StakeV1ModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StakeV1ModelToJson(this);

  String get unfreezeAmountValue {
    if (unfreezeAmount == null) return '0';

    return unfreezeAmount!.toString().div('1000000', scale: 0);
  }
}
