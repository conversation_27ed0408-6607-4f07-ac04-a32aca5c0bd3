/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-10 16:15:34
 */

import 'dart:convert';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/extra.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/http/response/error_bean.dart';
import 'package:coinbag/modules/wallet/assets/activity/token_activity_controller.dart';
import 'package:coinbag/modules/wallet/assets/activity/token_activity_page.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_account_info/tron_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/models/tron_resource/tron_resource.dart';
import 'package:coinbag/modules/wallet/common/activity_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/wallet/wallet.dart';

class TokenAssetsController extends BaseController<BlockChainService>
    with GetTickerProviderStateMixin {
  final List<String> tabList = [
    ID.stringAll.tr,
    ID.stringCollection.tr,
    ID.stringTransferSend.tr,
  ];
  late TabController tabController =
      TabController(length: tabList.length, vsync: this);
  ScrollController scrollController = ScrollController();
  final PageController pagerController = PageController();

  Rx<String> balance = "".obs;
  CoinModel? coinModel;
  CoinType? coinType;
  BalanceModel? balanceModel;
  WalletModel? walletModel;
  late Wallet wallet;
  Rx<EosAccountInfo> eosAccountInfo = EosAccountInfo().obs;
  Rx<TronAccountInfo> tronAccountInfo = TronAccountInfo().obs;
  TronResource tronResource = TronResource();
  AppDatabase? appDatabase;

  AddressModel? addressModel;
  TokenModel? tokenModel;
  String? address;
  String? contract;
  String? sourceBalance;
  String? walletId;
  final List<Widget> pagerList = [
    const TokenActivityPage(
      action: ActivityAction.all,
    ),
    const TokenActivityPage(
      action: ActivityAction.receive,
    ),
    const TokenActivityPage(
      action: ActivityAction.send,
    ),
  ];

  @override
  void onInit() {
    final args = Get.arguments;
    if (args == null) {
      Log.e('TokenAssetsController.onInit: Get.arguments is null');
      super.onInit();
      return;
    }

    if (args.containsKey(GetArgumentsKey.coinModel)) {
      coinModel = args[GetArgumentsKey.coinModel] as CoinModel?;
      if (coinModel != null && coinModel!.chain != null) {
        coinType = CoinBase.getCoinTypeByChain(coinModel!.chain!);
      } else {
        Log.e(
            'TokenAssetsController.onInit: coinModel or coinModel.chain is null');
      }
    }
    if (args.containsKey(GetArgumentsKey.addressModel)) {
      addressModel = args[GetArgumentsKey.addressModel] as AddressModel?;
    }
    if (args.containsKey(GetArgumentsKey.tokenModel)) {
      tokenModel = args[GetArgumentsKey.tokenModel] as TokenModel?;
    }
    if (args.containsKey(GetArgumentsKey.walletModel)) {
      walletModel = args[GetArgumentsKey.walletModel] as WalletModel?;
      if (walletModel != null && walletModel!.batchId != null) {
        wallet = Wallet.getWalletByBatch(walletModel!.batchId!);
      } else {
        Log.e('TokenAssetsController.onInit: walletModel or batchId is null');
      }
    }

    appDatabase = Get.database;

    // 业务参数判空，防止后续空指针
    if (coinModel == null) {
      Log.e('TokenAssetsController.onInit: coinModel is null');
      super.onInit();
      return;
    }
    if (coinModel!.isToken == true) {
      if (tokenModel != null) {
        contract = tokenModel!.contract;
        sourceBalance = tokenModel!.balance;
        walletId = tokenModel!.walletId;
        address = tokenModel!.address;
      } else {
        Log.e(
            'TokenAssetsController.onInit: tokenModel is null for token asset');
      }
    } else {
      if (addressModel != null) {
        sourceBalance = addressModel!.balance;
        walletId = addressModel!.walletId;
        address = addressModel!.address;
      } else {
        Log.e(
            'TokenAssetsController.onInit: addressModel is null for main asset');
      }
      if (coinModel?.isToken != true) {
        if (coinType is TronChain || coinType is EosChain) {
          // 资源
          tabList.add(ID.stringResource.tr);
          pagerList.add(const TokenActivityPage(
            action: ActivityAction.resources,
          ));
        }
      }
    }

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {
    if (!Get.isEmptyString(sourceBalance)) {
      balance.value = sourceBalance!;
    }
    _loadResourceCache();
    fetchTokenBalanceOrResource();
  }

  void _loadResourceCache() {
    if (coinType is EosChain && coinModel?.isToken == false) {
      if (!Get.isEmptyString(addressModel!.eosAccountInfoCache) &&
          coinModel?.isToken == false) {
        Map<String, dynamic> jsonResult =
            json.decode(addressModel!.eosAccountInfoCache!);
        eosAccountInfo.value = EosAccountInfo.fromJson(jsonResult);
      }
    } else if (coinType is TronChain && coinModel?.isToken == false) {
      if (!Get.isEmptyString(addressModel!.tronAccountInfoCache)) {
        Map<String, dynamic> jsonResult =
            json.decode(addressModel!.tronAccountInfoCache!);
        tronAccountInfo.value = TronAccountInfo.fromJson(jsonResult);
      }
      if (!Get.isEmptyString(addressModel!.tronResourceCache)) {
        Map<String, dynamic> jsonResult =
            json.decode(addressModel!.tronResourceCache!);
        tronResource = TronResource.fromJson(jsonResult);
      }
    }
  }

  void fetchTokenBalanceOrResource() {
    Extra extra = Extra();
    Map<String, dynamic> extraMap = _getExtraMap(extra);

    // 请求资源管理Tron EOS
    if (coinModel != null && !coinModel!.isToken!) {
      _handleResourceChain(extraMap);
    } else {
      _loadOtherBalance(extraMap);
    }
  }

  void _handleResourceChain(Map<String, dynamic> extraMap) {
    if (coinType is EosChain) {
      _loadEosAccountInfo(extraMap);
    } else if (coinType is TronChain) {
      _loadTronAccountInfoOrResource(extraMap);
    } else {
      _loadOtherBalance(extraMap);
    }
  }

  Map<String, dynamic> _getExtraMap(Extra extra) {
    if (coinModel!.isToken!) {
      if (coinType is EosChain || coinType is TronChain) {
        return extra.toEosOrTronTokenContractBalanceJson(
            contract!, coinModel!.symbol!);
      } else if (coinType is SolanaChain) {
        return extra.toSolContractBalanceJson(
          contract: contract!,
          derivedAddresses: tokenModel?.derivedAddresses ?? '',
        );
      } else {
        return extra.toContractBalanceJson(contract!);
      }
    } else if (coinType!.isBitcoinSeries) {
      return extra.toBitcoinSeriesUnconfirmed();
    } else {
      return extra.toJson();
    }
  }

  void _loadTronAccountInfoOrResource(Map<String, dynamic> extraMap) {
    List<Future<dynamic>> futures = [];

    futures.add(
      api.getAccountInfo(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getTronAccountInfo,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel!.chain!)
              .put(APIConstant.address, address ?? "")
              .getRequestBody())),
    );

    futures.add(
      api.getAccountResource(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getAccountResource,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel!.chain!)
              .put(APIConstant.address, address ?? "")
              .getRequestBody())),
    );

    multiHttpRequest(futures, (value) async {
      if (value != null) {
        BaseResponseV1 accountResponse = value[0];
        BaseResponseV1 resourceResponse = value[1];

        if (accountResponse.data != null) {
          tronAccountInfo.value =
              TronAccountInfo.fromJson(accountResponse.data);
          await Get.database.addressDao.updateTronAccountInfo(
              addressModel!, jsonEncode(accountResponse.data));
        }

        if (resourceResponse.data != null) {
          TronResource resource = TronResource.fromJson(resourceResponse.data);
          // 资源详情赋值
          resource.resourceDetail = tronResource.resourceDetail;
          tronResource = resource;
          await Get.database.addressDao.updateTronResource(
              addressModel!, jsonEncode(resourceResponse.data));
          addressModel = await Get.database.addressDao.getAddressModel(
            walletId: walletModel!.walletId!,
            deviceId: walletModel!.deviceId!,
            address: addressModel!.address!,
            chain: coinType!.chain,
          );
        }
      }
    });
  }

  void _loadEosAccountInfo(Map<String, dynamic> extraMap) {
    httpRequest<BaseResponseV1<dynamic>>(
      api.getTokenBalance(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getEosAccountInfo,
        requestParams: RequestParams()
            .put(APIConstant.chain, coinModel!.chain!)
            .put(APIConstant.address, address ?? "")
            .getRequestBody(),
      )),
      (accountData) {
        if (accountData.data != null) {
          _handleEosAccountData(accountData.data!);
        } else {
          _handleError(accountData.error);
        }
      },
    );
  }

  void _handleEosAccountData(Map<String, dynamic> data) {
    eosAccountInfo.value = EosAccountInfo.fromJson(data);
    if (eosAccountInfo.value.coreLiquidBalance != null) {
      sourceBalance =
          CoinBase.getEosBalacne(eosAccountInfo.value.coreLiquidBalance!);
    } else {
      sourceBalance = "0";
    }

    Log.s("EOS Balance=$sourceBalance");

    if (!Get.isEmptyString(sourceBalance)) {
      _updateBalance(sourceBalance!);
      appDatabase!.addressDao.updateEOSAccountInfo(
          addressModel!, sourceBalance!, jsonEncode(data));
    }
  }

  void _loadOtherBalance(Map<String, dynamic> extraMap) {
    httpRequest<BaseResponseV1<dynamic>>(
      api.getTokenBalance(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getTokenBalance,
        requestParams: RequestParams()
            .put(APIConstant.chain, coinModel!.chain!)
            .put(APIConstant.address, address ?? "")
            .put(APIConstant.extra, extraMap)
            .put(
                APIConstant.type,
                (coinType is TronChain && coinModel!.isToken!)
                    ? TronChain.get.getTokenType(coinModel!.tokenType)
                    : "")
            .getRequestBody(),
      )),
      (balanceData) {
        if (balanceData.data != null) {
          _handleBalanceData(balanceData.data!);
        } else {
          _handleError(balanceData.error);
        }
      },
    );
  }

  Future<void> _handleBalanceData(Map<String, dynamic> data) async {
    balanceModel = BalanceModel.fromJson(data);
    if (balanceModel != null) {
      sourceBalance =
          BalanceManager.getBalance(balanceModel!.balance!, coinModel);

      if (!Get.isEmptyString(sourceBalance)) {
        _updateBalance(sourceBalance!);
        if (coinModel!.isToken!) {
          appDatabase!.tokenDao
              .updateAddressBalances(tokenModel!, sourceBalance!);
          TokenModel? newTokenModel = await appDatabase!.tokenDao.getTokenModel(
            walletId: walletModel!.walletId!,
            address: tokenModel!.address!,
            contract: tokenModel!.contract!,
            chain: tokenModel!.chain!,
          );
          if (newTokenModel == null) {
            await appDatabase!.tokenDao.intertToken(
              balanceModel!,
              coinType!,
              walletModel!.walletId!,
              tokenModel!.addressLabel ?? '',
            );
            newTokenModel = await appDatabase!.tokenDao.getTokenModel(
              walletId: walletModel!.walletId!,
              address: tokenModel!.address!,
              contract: tokenModel!.contract!,
              chain: tokenModel!.chain!,
            );
          }
          if (newTokenModel != null) tokenModel = newTokenModel;
        } else {
          appDatabase!.addressDao
              .updateAddressBalances(addressModel!, sourceBalance!);
          AddressModel? newAddressModel =
              await Get.database.addressDao.getAddressModel(
            walletId: walletModel!.walletId!,
            deviceId: walletModel!.deviceId!,
            address: addressModel!.address!,
            chain: coinType!.chain,
          );
          addressModel = newAddressModel;
        }
      }
    }
  }

  void _updateBalance(String balance) {
    try {
      updateBalance(balance);
    } catch (_) {
      // 处理更新余额时的异常（如果需要）
    }
  }

  void _handleError(ErrorBean? error) {
    if (error != null && !Get.isEmptyString(error.message!)) {
      Get.showToast(error.message);
    }
  }

  void updateBalance(String newBalance) {
    balance.value = newBalance;
  }

  String getSymbol() {
    if (coinModel!.symbol!.isEmpty) return "";
    return coinModel!.symbol!;
  }

  String getSymbolIcon() {
    return CoinBase.getSymbolIcon(coinModel!.chain!);
  }

  String getAddress() {
    return address ?? "";
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
    tabController.dispose();
  }
}

class TokenAssetsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TokenAssetsController());
    Get.lazyPut(() => TokenActivityController(), tag: "all");
    Get.lazyPut(() => TokenActivityController(), tag: "send");
    Get.lazyPut(() => TokenActivityController(), tag: "receive");
    Get.lazyPut(() => TokenActivityController(), tag: "resources");
  }
}
