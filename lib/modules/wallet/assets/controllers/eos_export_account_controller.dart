/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-11 14:44:20
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/eos_account_info.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/key.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/models/eos_account_info/permission.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/pro3_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class EOSExportAccountController extends BaseController<BlockChainService> {
  @override
  void loadData() {}

  late CoinModel coinModel;
  Wallet? wallet;
  late WalletModel walletModel;
  AddressModel? addressModel;
  RxList<AddressModel> addressList = <AddressModel>[].obs;

  Rx<ButtonStatus> buttonStatus = ButtonStatus.enable.obs;
  List<Map<String, String>> pro3dataList = [];

  @override
  void onInit() async {
    super.onInit();
    coinModel = Get.arguments?[GetArgumentsKey.coinModel];
    addressModel = Get.arguments?[GetArgumentsKey.addressModel];
    wallet = Get.arguments?[GetArgumentsKey.wallet];
    await updateAddressList();
  }

  Future<void> updateAddressList() async {
    walletModel = (await Get.database.walletDao.getCheckedWallets())!;

    addressList.value = await Get.database.addressDao.getAddressModelList(
      walletId: walletModel.walletId!,
      deviceId: walletModel.deviceId!,
      chain: coinModel.chain!,
    );
  }

  Future<void> _setupTxModel(List<Map<String, dynamic>> accounts) async {
    WalletModel? walletModel = await Get.database.walletDao.getCheckedWallets();
    Wallet wallet = Wallet.getWalletByBatch(walletModel!.batchId!);

    TransferModel tsModel = TransferModel(
      chain: coinModel.chain!,
      type: TransferType.export,
      walletId: addressModel!.walletId,
      wallet: wallet,
    );
    if (wallet is Pro3Wallet) {
      tsModel.pro3ExportData = accounts;
      tsModel.addressPublickey = addressModel!.publickey;
    } else {
      tsModel.exportData = {
        'accounts': accounts,
        'pub': addressModel?.publickey ?? '',
        'chain_symbol': coinModel.symbol ?? '',
      };
    }

    Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
      GetArgumentsKey.transferModel: tsModel,
      GetArgumentsKey.qrType: QRCodeType.export,
    });
  }

  Future<void> exportAction() async {
    buttonStatus.value = ButtonStatus.loading;
    List<Future> futures = [];
    for (var e in addressList) {
      Future request = api.getAccountInfo(
        BlockChainParamsManager.createParams(
          method: BlockChainAPI.getEosAccountInfo,
          requestParams: RequestParams()
              .put(APIConstant.chain, coinModel.chain!)
              .put(APIConstant.address, e.address ?? "")
              .getRequestBody(),
        ),
      );
      futures.add(request);
    }

    multiHttpRequest(
      futures,
      handleError: false,
      handleSuccess: false,
      (value) async {
        if (value == null) {
          Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
          buttonStatus.value = ButtonStatus.enable;
          return;
        }

        List<Map<String, dynamic>> dataList = [];
        for (BaseResponseV1 response in value) {
          Map<String, dynamic>? data = response.data;
          if (data == null) continue;
          EosAccountInfo info = EosAccountInfo.fromJson(data);

          AddressModel? addressModel =
              await Get.database.addressDao.getAddressModel(
            walletId: walletModel.walletId!,
            deviceId: walletModel.deviceId!,
            chain: coinModel.chain!,
            address: this.addressModel!.address!,
          );

          String? lable = addressModel?.addressLabel;

          int createTimeEpoch =
              (info.createDateTime ?? DateTime.now()).microsecondsSinceEpoch ~/
                  1000000;
          Map<String, dynamic> map = {};
          if (wallet != null && wallet! is Pro3Wallet) {
            map = {
              'a': info.accountName,
              't': createTimeEpoch.toString(),
            };
          } else {
            map = {
              'account': info.accountName,
              'create_time': createTimeEpoch.toString(),
            };
            if (!Get.isEmptyString(lable)) {
              map['label'] = lable;
            }

            for (var e in info.permissions ?? <Permission>[]) {
              if (e.permName == 'active') {
                List<Key> keys = e.requiredAuth?.keys ?? [];
                if (keys.isNotEmpty) {
                  String? activeKey = keys.first.key;
                  if (activeKey != addressModel?.publickey) {
                    map['active'] = activeKey;
                  }
                }
              } else if (e.permName == 'owner') {
                List<Key> keys = e.requiredAuth?.keys ?? [];
                if (keys.isNotEmpty) {
                  String? ownerKey = keys.first.key;
                  if (ownerKey != addressModel?.publickey) {
                    map['owner'] = ownerKey;
                  }
                }
              }
            }
          }

          dataList.add(map);

          _setupTxModel(dataList);
        }

        buttonStatus.value = ButtonStatus.enable;
      },
      error: (e) {
        Get.showToast(ID.stringNodeTimeOut.tr, toastMode: ToastMode.failed);
        buttonStatus.value = ButtonStatus.enable;
      },
    );
  }

  void loadAccountInfo({
    required AddressModel addressModel,
    required Function({EosAccountInfo? info}) callback,
  }) {
    httpRequest<BaseResponseV1<dynamic>>(
        api.getAccountInfo(BlockChainParamsManager.createParams(
          method: BlockChainAPI.getEosAccountInfo,
          requestParams: RequestParams()
              .put(APIConstant.chain, addressModel.chain!)
              .put(APIConstant.address, addressModel.address ?? "")
              .getRequestBody(),
        )),
        handleError: false,
        handleSuccess: false, (value) {
      if (value.data != null) {
        EosAccountInfo info = EosAccountInfo.fromJson(value.data);
        callback(info: info);
      } else {
        callback();
      }
    }, error: (_) {
      callback();
    });
  }
}

class EOSExportAccountBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => EOSExportAccountController());
  }
}
