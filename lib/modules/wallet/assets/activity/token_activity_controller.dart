/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 10:24:24
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/extra.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/wallet/assets/models/token_activity_model.dart';
import 'package:coinbag/modules/wallet/assets/token_assets_controller.dart';
import 'package:coinbag/modules/wallet/common/activity_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/tron/trx.dart';

class TokenActivityController extends BaseRefreshController<BlockChainService> {
  final ActivityAction? action;

  TokenActivityController({this.action});

  List<TokenActivityModel> dataList = <TokenActivityModel>[];
  List<TokenActivityModel> sendDataList = <TokenActivityModel>[];
  List<TokenActivityModel> receiveDataList = <TokenActivityModel>[];
  List<TokenActivityModel> resourcesDataList = <TokenActivityModel>[];

  CoinModel? coinModel;
  AddressModel? addressModel;
  BalanceModel? balanceModel;
  TokenModel? tokenModel;
  String? address;
  String? contract;
  String? sourceBalance;
  String? walletId;
  CoinType? coinType;
  AppDatabase? appDatabase;
  bool hasCache = false;

  @override
  void onInit() {
    initData();
    super.onInit();
  }

  void initData() {
    appDatabase = Get.database;
    if (Get.arguments!.containsKey(GetArgumentsKey.coinModel)) {
      coinModel = Get.arguments![GetArgumentsKey.coinModel] as CoinModel?;
      coinType = CoinBase.getCoinTypeByChain(coinModel!.chain!);
    }
    if (Get.arguments!.containsKey(GetArgumentsKey.addressModel)) {
      addressModel =
          Get.arguments![GetArgumentsKey.addressModel] as AddressModel?;
    }
    if (Get.arguments!.containsKey(GetArgumentsKey.tokenModel)) {
      tokenModel = Get.arguments![GetArgumentsKey.tokenModel] as TokenModel?;
    }

    if (coinModel!.isToken!) {
      contract = tokenModel!.contract;
      sourceBalance = tokenModel!.balance;
      walletId = tokenModel!.walletId;
      address = tokenModel!.address;
    } else {
      sourceBalance = addressModel!.balance;
      walletId = addressModel!.walletId;
      address = addressModel!.address;
    }
  }

  @override
  void loadData() {
    requestPageData();
  }

  @override
  void onReady() {
    super.onReady();
    loadCache();
    loadData();
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    List<Future<dynamic>> futures = [];
    if (refresh == Refresh.pull) {
      Get.find<TokenAssetsController>().fetchTokenBalanceOrResource();
    }

    Extra extra = Extra();
    futures.add(api.getTokenActivity(BlockChainParamsManager.createParams(
        method: BlockChainAPI.getTokenActivity,
        requestParams: RequestParams()
            .put(APIConstant.chain, coinModel!.chain!)
            .put(APIConstant.address, address ?? "")
            .put(
                APIConstant.type,
                (coinType is TronChain && coinModel!.isToken!)
                    ? TronChain.get.getTokenType(coinModel!.tokenType)
                    : '')
            .put(APIConstant.contract, contract ?? "")
            .put(APIConstant.extra,
                extra.toTokenActivityJson(tokenModel?.derivedAddresses))
            .getRequestBody())));

    multiHttpRequest(futures, (value) async {
      if (value != null) {
        showSuccess();
      }

      var activityData = value[0];
      if (activityData != null) {
        if (activityData.error != null) {
          hideRefresh(refreshController);
          showError();
          return;
        }
        if (activityData.data == null && dataList.isEmpty) {
          hideRefresh(refreshController);
          showEmpty();
          return;
        }

        List<TokenActivityModel> dataSource = activityData.data
            .map<TokenActivityModel>(
                (item) => TokenActivityModel.fromJson(item))
            .toList();
        if (dataSource.isEmpty) {
          await loadCache();
        } else {
          dataList = dataSource;
          await loadPackingData();
        }

        setDataList();
        hideRefresh(refreshController);
        saveCache();
      } else {
        hideRefresh(refreshController);
      }
    });
  }

  Future<void> loadCache() async {
    List<TokenActivityModel> allList =
        await appDatabase!.transactionsActivityDao.getTokenActivityList(
            walletId: walletId!, address: address!, coinModel: coinModel!);
    dataList = allList;
    await loadPackingData();

    hasCache = dataList.isNotEmpty;
    if (hasCache) {
      setDataList();
    }
  }

  Future<void> loadPackingData() async {
    List<TokenActivityModel> packingList =
        await appDatabase!.transactionsActivityDao.getTokenActivityPackingList(
      walletId: walletId!,
      address: address!,
      coinModel: coinModel!,
      contract: contract,
    );

    // 使用 Set 进行去重
    final existingTxIds = dataList.map((e) => e.txId).toSet();

    packingList =
        packingList.where((p) => !existingTxIds.contains(p.txId)).toList();

    if (packingList.isNotEmpty) {
      dataList.insertAll(0, packingList);
    }
  }

  void saveCache() {
    appDatabase!.transactionsActivityDao.insertBatch(
        tokenActivityList: dataList,
        walletId: walletId!,
        address: address!,
        coinModel: coinModel!);
  }

  void setDataList() {
    if (dataList.isEmpty) {
      showEmpty();
    } else {
      sendDataList.clear();
      receiveDataList.clear();
      resourcesDataList.clear();
      for (var model in dataList) {
        model.coinType = coinType;
        if (model.activityType == TokenActivityType.send ||
            model.activityType == TokenActivityType.own) {
          sendDataList.add(model);
        } else if (model.activityType == TokenActivityType.receive ||
            model.activityType == TokenActivityType.own) {
          receiveDataList.add(model);
        } else {
          resourcesDataList.add(model);
        }
      }
    }

    update([GetKey.assetsActivityId]);
  }

  String getSymbol() {
    if (coinModel!.symbol!.isEmpty) return "";
    return coinModel!.symbol!;
  }

  String getSymbolIcon() {
    return CoinBase.getSymbolIcon(coinModel!.chain!);
  }

  String getAddress() {
    if (address!.isEmpty) return "";
    return address!;
  }

  void toBlockBrowser() {
    String url = '${coinType!.blockBrowserUrlByAddress}$address';
    Get.toWeb(url: url);
  }

  List<TokenActivityModel> getDataList(ActivityAction? action) {
    switch (action!) {
      case ActivityAction.all:
        return dataList;
      case ActivityAction.receive:
        return receiveDataList;
      case ActivityAction.send:
        return sendDataList;
      case ActivityAction.resources:
        return resourcesDataList;
      default:
        return <TokenActivityModel>[];
    }
  }
}
