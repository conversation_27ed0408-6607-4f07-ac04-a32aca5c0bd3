/*
 * @description: 交易记录
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-11-04 10:17:22
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/assets/models/token_activity_model.dart';
import 'package:coinbag/modules/wallet/common/activity_action.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:flutter/material.dart';

import 'token_activity_controller.dart';

class TokenActivityPage extends BaseStatefulWidget<TokenActivityController> {
  final ActivityAction? action;
  const TokenActivityPage({super.key, this.action});

  @override
  String get tag {
    return action!.name;
  }

  @override
  build(BuildContext context) {
    return GetBuilder<TokenActivityController>(
        id: GetKey.assetsActivityId,
        tag: tag,
        builder: (controller) {
          return controller.obx((state) => buildContent(action!),
              onLoading: !controller.hasCache
                  ? const HomeTokeListSkeletonWidget()
                  : buildContent(action!),
              onError: (error) => controller.hasCache
                  ? buildContent(action!)
                  : AppErrorWidget(onRefresh: () {
                      controller.showLoading();
                      controller.loadData();
                    }),
              onEmpty: const AppEmptyWidget());
        });
  }

  StatelessWidget buildContent(ActivityAction action) {
    if (action == ActivityAction.send && controller.sendDataList.isEmpty) {
      return const AppEmptyWidget();
    } else if (action == ActivityAction.receive &&
        controller.receiveDataList.isEmpty) {
      return const AppEmptyWidget();
    } else if (action == ActivityAction.resources &&
        controller.resourcesDataList.isEmpty) {
      return const AppEmptyWidget();
    }
    return RefreshWidget<TokenActivityController>(
        enablePullDown: true,
        enablePullUp: false,
        controllerTag: tag,
        refreshController: controller.refreshController,
        child:
            CustomScrollView(physics: const BouncingScrollPhysics(), slivers: [
          SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
            return (controller.dataList.isNotEmpty)
                ? _itemBuilder(controller.getDataList(action)[index])
                : const SizedBox.shrink();
          }, childCount: controller.getDataList(action).length)),
          footerWidget(),
        ]));
  }

  HighLightInkWell _itemBuilder(TokenActivityModel model) {
    return HighLightInkWell(
        onTap: () {
          Get.toNamed(AppRoutes.tokenTransactionDetailsPage, arguments: {
            GetArgumentsKey.coinModel: controller.coinModel,
            GetArgumentsKey.address: controller.address,
            GetArgumentsKey.activityModel: model,
          });
        },
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setHeight(14), horizontal: Get.setWidth(16)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: model.getActivityIcon(controller.address),
                width: Get.setWidth(32),
                height: Get.setHeight(32),
                fit: BoxFit.contain,
              ),
              SizedBox(
                width: Get.setWidth(10),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          model.getActivityText(),
                          style: TextStyle(
                            color: Get.theme.textPrimary,
                            fontSize: Get.setFontSize(16),
                            fontFamily: Get.setFontFamily(),
                            fontWeight: FontWeightX.medium,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            model.getActivityAmount(controller.coinModel,
                                controller.coinType, controller.address),
                            textAlign: TextAlign.end,
                            style: TextStyle(
                              color: model.getActivityColor(),
                              fontSize: Get.setFontSize(16),
                              fontFamily: Get.setNumberFontFamily(),
                              fontWeight: FontWeightX.semibold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text.rich(
                          TextSpan(children: [
                            TextSpan(
                              text: model.getActivityAddressText(),
                              style: TextStyle(
                                color: Get.theme.textSecondary,
                                fontSize: Get.setFontSize(12),
                                fontFamily: Get.setFontFamily(),
                                fontWeight: FontWeightX.regular,
                              ),
                            ),
                            WidgetSpan(
                                child: SizedBox(
                              width: Get.setWidth(4),
                            )),
                            TextSpan(
                              text: model.getActivityAddressValue(),
                              style: TextStyle(
                                fontFamily: Get.setNumberFontFamily(),
                                fontSize: Get.setFontSize(12),
                                color: Get.theme.textSecondary,
                              ),
                            )
                          ]),
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Expanded(
                          child: Text(
                            model.getTime(),
                            style: TextStyle(
                              fontFamily: Get.setNumberFontFamily(),
                              fontSize: Get.setFontSize(12),
                              color: _timeColor(model),
                            ),
                            textAlign: TextAlign.end,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  SliverToBoxAdapter footerWidget() => SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(40),
              vertical: Get.setPaddingSize(16)),
          child: OutlinedButtonWidget(
            onPressed: () => controller.toBlockBrowser(),
            text: ID.stringBlockchain.tr,
            textStyle: TextStyle(
                fontSize: Get.setFontSize(14), color: Get.theme.textPrimary),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(40)),
              child: Text(
                ID.stringBlockchain.tr,
                style: TextStyle(
                  fontSize: Get.setFontSize(14),
                  color: Get.theme.textPrimary,
                ),
              ),
            ),
          ),
        ),
      );

  Color _timeColor(TokenActivityModel model) {
    if (model.status == TokenActivityStatus.fail) {
      return Get.theme.colorF44D4D;
    } else if (model.status == TokenActivityStatus.packing ||
        model.status == TokenActivityStatus.confirming) {
      return Get.theme.colorFF6A16;
    }
    return Get.theme.textSecondary;
  }
}
