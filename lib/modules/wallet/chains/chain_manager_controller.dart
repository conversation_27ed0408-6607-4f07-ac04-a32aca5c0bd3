/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-19 11:30:27
 * @LastEditTime: 2025-03-24 17:43:47
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/dapp/browser/dapp_browser_controller.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/chain_address_edit_widget.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/bitcoin/testnet/sbtc.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/cosmos/baby.dart';
import 'package:wallet_core/chain/cosmos/tbaby.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class ChainManagerController extends BaseController<AppDatabase> {
  /// 地址是否可以编辑
  RxBool isAddressEdit = false.obs;

  /// 是否是链管理
  RxBool isChainEdit = false.obs;

  bool isFullPage = false;

  Rx<CoinModel>? coinModel;
  CoinType? coinType;

  String? chain;
  ChainManagerMode chainManagerMode = ChainManagerMode.wallet;
  RxList<AddressModel> addressList = <AddressModel>[].obs;
  RxList<CoinModel> dataSource = <CoinModel>[].obs;

  late WalletModel walletModel;
  late Wallet wallet;

  @override
  void onInit() async {
    final arg = Get.arguments;
    if (arg != null) {
      setData(walletModel: arg);
    }
    super.onInit();
  }

  Future<void> setData(
      {required WalletModel walletModel,
      String? chain,
      ChainManagerMode mode = ChainManagerMode.wallet}) async {
    this.chain = chain;
    this.walletModel = walletModel;
    chainManagerMode = mode;
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);
    // 获取支持的链
    List<CoinType> supportedChains =
        wallet.supportedChainsByBachId(walletModel.batchId!);

    switch (chainManagerMode) {
      case ChainManagerMode.wallet:
        break;
      case ChainManagerMode.nft:
        supportedChains =
            supportedChains.where((coinType) => coinType.isSupportNFT).toList();
        break;
      case ChainManagerMode.dapp:
      case ChainManagerMode.dappBaby:
        if (chain != null && !Get.isEmptyString(chain)) {
          supportedChains = supportedChains
              .where((coinType) => _isChainSupported(coinType, chain))
              .toList();
        } else {
          supportedChains = supportedChains
              .where((coinType) => _isGenericSupported(coinType))
              .toList();
        }

        // 检查 coinType 是否有效
        if (supportedChains.isEmpty) {
          Get.showToast(ID.stringNotCurrentlySupporte.tr,
              toastMode: ToastMode.waring);
          Get.back();

          return;
        }
        break;
    }

    /// 根据配置决定是否移除测试币
    if (!AppConfig.instance.enableTestNetwork) {
      supportedChains =
          supportedChains.where((chain) => !chain.isTestNet).toList();
    }
    List<CoinModel> coinModelList = await Get.database.coinDao.getAllData();

    // data
    CoinModel allCoinModel =
        CoinModel(id: -1, chain: AllChain.get.chain, isCoinSupported: true);
    List<CoinModel> dataList = [];
    if (chainManagerMode == ChainManagerMode.wallet) {
      dataList.insert(0, allCoinModel);
    }

    List<CoinModel> supportedList = coinModelList
        .where((element) => supportedChains.any((supportedChain) =>
            (supportedChain.chain == element.chain &&
                element.isToken == false &&
                element.sortedId != null)))
        .toList();

    /// wallet 需要过滤隐藏的链
    if (chainManagerMode == ChainManagerMode.wallet) {
      supportedList = supportedList
          .where((e) => (isFullPage ? true : e.isCoinSupported ?? false))
          .toList();
    }

    /// 排序
    supportedList.sort((a, b) => a.sortedId!.compareTo(b.sortedId!));

    if (isFullPage) {
      // 隐藏的放后面
      supportedList.sort((a, b) {
        DateTime dateTime = DateTime.now();
        DateTime aTime = a.hiddenDateTime ?? dateTime;
        DateTime bTime = b.hiddenDateTime ?? dateTime;
        return bTime.compareTo(aTime);
      });
    }

    dataList.addAll(supportedList);

    dataSource.value = dataList;

    String? selectedChain = this.selectedChain();

    List<CoinModel> coinModelResult =
        dataList.where((p0) => p0.chain == selectedChain).toList();

    if (coinModelResult.isEmpty) {
      coinModel = dataList.first.obs;
    } else {
      coinModel = coinModelResult.first.obs;
    }

    if (isFullPage && walletModel.chain == AllChain.get.chain) {
      coinModel = supportedList.first.obs;
    }

    /// 如果当前选中的链隐藏 则选中第一个
    if (coinModel?.value.isCoinSupported != true) {
      coinModel?.value = supportedList.first;
    }

    coinType = CoinBase.getCoinTypeByChain(coinModel?.value.chain);

    updateChain(coinModel!.value);
  }

  // 检查Dapp链方法
  bool _isChainSupported(CoinType coinType, String chain) {
    if (chain == AllChain.get.chain) {
      return _isGenericSupported(coinType);
    }
    if (CoinBase.isBitCoinNetWork(chain)) {
      CoinType? currentCoinType = CoinBase.getCoinTypeByChain(chain);
      if (currentCoinType != null) {
        return currentCoinType == coinType;
      } else {
        return coinType is BitcoinChain || coinType is BitcoinSignetChain;
      }
    }

    if (CoinBase.isEthereumSeriesNetWork(chain)) {
      return _isEthereumSupported(coinType);
    }

    if (CoinBase.isBabyNetWork(chain)) {
      CoinType? currentCoinType = CoinBase.getCoinTypeByChain(chain);
      if (currentCoinType != null) {
        return currentCoinType == coinType;
      } else {
        return coinType is BabylonChain || coinType is BabylonTestnetChain;
      }
    }

    return _isGenericSupported(coinType);
  }

// 检查以太坊系列链的支持
  bool _isEthereumSupported(CoinType coinType) {
    return coinType is EthereumChain || coinType.isEVM || coinType.isLayer2;
  }

// 检查其他链的支持
  bool _isGenericSupported(CoinType coinType) {
    return coinType is BitcoinChain ||
        coinType is BitcoinSignetChain ||
        coinType is EthereumChain ||
        coinType.isEVM ||
        coinType.isLayer2;
  }

  /// 切换链
  void updateChain(CoinModel model) {
    coinModel?.value = model;
    coinType = CoinBase.getCoinTypeByChain(model.chain);
    updateAddressList();
  }

  /// 更新地址
  Future<void> updateAddressList() async {
    if (coinType is! AllChain) {
      List<AddressModel> resultList =
          await Get.database.addressDao.getAddressModelList(
        chain: coinType!.chain,
        walletId: walletModel.walletId!,
        deviceId: walletModel.deviceId!,
      );
      resultList.sort(((a, b) {
        int aSoredId = a.sortedId ?? 0;
        int bSoredId = b.sortedId ?? 0;
        return aSoredId.compareTo(bSoredId);
      }));

      addressList.value = resultList;
    } else {
      addressList.value = [];
    }
  }

  void bindChainAction() {
    if (wallet.isP1P2P3Wallet) {
      Get.showOldWalletBindBottomSheet(wallet);
    } else {
      if (wallet is TouchWallet) {
        Get.toNamed(AppRoutes.touchBindPage);
      } else {
        Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
          GetArgumentsKey.walletId: walletModel.walletId,
          GetArgumentsKey.qrData:
              CoinBase.createWalletBindingQRCode(coinType!.chain),
          GetArgumentsKey.qrType: QRCodeType.bindChain,
          GetArgumentsKey.wallet: Wallet.getWalletByBatch(walletModel.batchId!),
        });
      }
    }
  }

  /// 选中的地址
  String? selectedAddress() {
    switch (chainManagerMode) {
      case ChainManagerMode.wallet:
        return walletModel.address;
      case ChainManagerMode.nft:
        return walletModel.nftAddress;
      case ChainManagerMode.dapp:
      case ChainManagerMode.dappBaby:
        break;
    }
    return null;
  }

  /// 选中的地址
  String? selectedChain() {
    switch (chainManagerMode) {
      case ChainManagerMode.wallet:
        return walletModel.chain;
      case ChainManagerMode.nft:
        return walletModel.nftChain;
      case ChainManagerMode.dapp:
      case ChainManagerMode.dappBaby:
        return chain;
    }
  }

  Future<void> addressItemAction({AddressModel? addressModel}) async {
    if (isFullPage) {
      // 可编辑状态
      showEidtItems(addressModel!);
    } else {
      switch (chainManagerMode) {
        case ChainManagerMode.wallet:
          String? address = addressModel?.address;
          String? chain = addressModel?.chain ?? AllChain.get.chain;
          String wallletId2 = walletModel.walletId!;

          ///  存储选择的链和地址
          await api.walletDao.updateWalletChainOrAddress(
              walletId: wallletId2, chain: chain, address: address);
          AppController.refreshHome();
          Get.back();
          break;
        case ChainManagerMode.nft:
          await api.walletDao.updateWalletNFTChainOrAddress(
            walletId: walletModel.walletId!,
            chain: addressModel!.chain!,
            address: addressModel.address!,
            addressLabel: addressModel.addressLabel!,
          );
          AppController.refreshNft();
          Get.back();
          break;
        case ChainManagerMode.dapp:
        case ChainManagerMode.dappBaby:
          Get.find<WebController>().loadChainData(
            isSwitchWallet: true,
            selectChain: addressModel!.chain!,
            selectAddressModel: addressModel,
            mode: chainManagerMode,
          );
          break;
      }
    }
  }

  void showEidtItems(AddressModel addressModel) {
    Get.showBottomSheet(
        hideHeader: true,
        paddingBottom: 0,
        padding: const EdgeInsets.symmetric(horizontal: 0),
        bodyWidget: ChainAddressEditWidget(
          controller: this,
          addressModel: addressModel,
          callback: (value) {
            Get.database.addressDao
                .updateAddressLabel(addressModel, value ?? '');
            updateAddressList();
          },
        ),
        bottomWidget: const BottomCancelWidget());
  }

  String assets(AddressModel? addressModel) {
    if (isHideAssets) return CommonConstant.hideAssetsStr;

    if (addressModel == null) {
      // 全部网络
      String? value = walletModel.totalAssets;
      if (Get.isEmptyString(value)) {
        return CommonConstant.emptyAsstes;
      }
      return CurrencyController.formatMoney(value,
          withThousandmat: true, hasUnit: true);
    } else {
      return BalanceManager.calculateFiatValue(
          addressModel.balance, coinModel?.value);
    }
  }

  bool get isHideAssets {
    return StorageManager.getValue(
          key: StorageKey.hideAssets,
        ) ??
        false;
  }

  @override
  void onClose() {
    if (isFullPage == true && chainManagerMode == ChainManagerMode.wallet) {
      AppController.refreshHome();
    }
    super.onClose();
  }

  @override
  void loadData() {}
}
