/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-18 14:47:44
 * @LastEditTime: 2024-05-27 14:43:01
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class ChainManagerHeaderWidget extends StatelessWidget {
  const ChainManagerHeaderWidget({
    super.key,
    required this.height,
    required this.controller,
  });
  final double height;
  final ChainManagerController controller;

  Future<void> editWalletAction() async {
    Get.back();
    await Future.delayed(const Duration(milliseconds: 300));
    Get.to(
        () => const ChainManagerPage(
              isFullPage: true,
            ),
        arguments: controller.walletModel);
  }

  @override
  build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.0)),
      child: MediaQuery.removePadding(
          removeTop: true,
          context: context,
          child: baseAppBar(
              title: ID.chainSelectWallet.tr,
              backgroundColor: Colors.transparent,
              leading: IconButton(
                  onPressed: () => Get.back(),
                  icon: ImageWidget(
                      assetUrl: 'icon_quite',
                      width: Get.setImageSize(Get.setImageSize(28)),
                      height: Get.setImageSize(Get.setImageSize(28)))),
              actionWidget: [
                Padding(
                  padding: EdgeInsets.only(right: Get.setPaddingSize(14)),
                  child: IconButton(
                      onPressed: () => editWalletAction(),
                      icon: Text(
                        ID.chainManager.tr,
                        textAlign: TextAlign.end,
                        style: stylePrimary_16_m,
                      )),
                )
              ])),
    );
  }
}
