/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-20 10:04:40
 * @LastEditTime: 2024-08-16 11:05:25
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/switch/switch_widget.dart';
import 'package:flutter/material.dart';

class ChainManagerWidget extends StatelessWidget {
  const ChainManagerWidget({super.key, required this.controller});

  final ChainManagerController controller;

  Future<void> updateCoinType(
      {required int oldIndex, required int newIndex}) async {
    if (oldIndex < newIndex) {
      newIndex = newIndex - 1;
    }
    if (oldIndex == newIndex) return;

    CoinModel newModel = _dataSource[newIndex];
    if (newModel.isCoinSupported == true) {
      CoinModel oldModel = _dataSource[oldIndex];

      double sortedId = 0;
      if (newIndex < oldIndex) {
        /// 往上移
        sortedId = newModel.sortedId! - 0.00001;
      } else {
        /// 往下移
        sortedId = newModel.sortedId! + 0.00001;
      }
      await Get.database.coinDao
          .updateCoinSortedId(coinModel: oldModel, sortedId: sortedId);
      controller.setData(walletModel: controller.walletModel);
    }
  }

  Future<void> switchAction({required bool value, required int index}) async {
    CoinModel coinModel = _dataSource[index];
    await Get.database.coinDao.updateCoinStatus(
        coinModel: coinModel,
        isCoinSupported: value,
        dataTime: value == false ? DateTime.now() : null);

    controller.setData(walletModel: controller.walletModel);
  }

  List<CoinModel> get _dataSource {
    List<CoinModel> dataSource = controller.dataSource
        .getRange(1, controller.dataSource.length)
        .toList();

    return dataSource;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Get.theme.bgColor,
      child: Obx(() => ReorderableListView.builder(
            padding: EdgeInsets.only(bottom: Get.getSafetyBottomPadding()),
            itemCount: controller.dataSource.length - 1,
            onReorder: (int oldIndex, int newIndex) =>
                updateCoinType(oldIndex: oldIndex, newIndex: newIndex),
            proxyDecorator:
                (Widget child, int index, Animation<double> animation) =>
                    _itemBuilder(index, true),
            itemBuilder: (_, int index) => _itemBuilder(index, false),
          )),
    );
  }

  HighLightInkWell _itemBuilder(int index, bool isEdit) {
    CoinModel coinModel = _dataSource[index];
    return HighLightInkWell(
      key: Key(index.toString()),
      onTap: () => {},
      child: Container(
        color: isEdit == true ? Get.theme.colorF3F3F5 : Colors.transparent,
        child: Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: coinModel.isCoinSupported == true
                    ? coinModel.chainIcon
                    : coinModel.disabledIcon,
                width: Get.setImageSize(20),
                height: Get.setImageSize(20),
              ),
              SizedBox(
                width: Get.setPaddingSize(6),
              ),
              _chainName(coinModel),
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
                child: ImageWidget(
                  assetUrl: 'chain_drag',
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(22),
                ),
              ),
              SwitchWidget(
                onTintColor: Get.theme.colorFD8114,
                isOn: coinModel.isCoinSupported == true,
                onChanged: (value) => switchAction(value: value, index: index),
              )
            ],
          ),
        ),
      ),
    );
  }

  Expanded _chainName(CoinModel coinModel) {
    return Expanded(
      child: Text.rich(
        TextSpan(children: [
          TextSpan(
            text: coinModel.symbol,
            style: TextStyle(
                color: Get.theme.textPrimary,
                fontFamily: Get.setNumberFontFamily(),
                fontWeight: FontWeightX.semibold,
                fontSize: Get.setFontSize(14)),
          ),
          TextSpan(
            text: ' - ${coinModel.chainName}',
            style: TextStyle(
                color: Get.theme.textSecondary,
                fontFamily: Get.setNumberFontFamily(),
                fontWeight: FontWeightX.regular,
                fontSize: Get.setFontSize(12)),
          )
        ]),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
