/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-05 16:11:44
 * @LastEditTime: 2024-09-26 10:05:17
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class ChainListWidget extends BaseStatelessWidget<ChainManagerController> {
  const ChainListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
        color: Get.theme.colorF9F9F9,
        child: ListView.builder(
            padding: EdgeInsets.only(
              bottom: Get.getSafetyBottomPadding(),
            ),
            itemCount: controller.dataSource.length,
            itemBuilder: (_, int index) {
              return _itemBuilder(index, context);
            })));
  }

  GestureDetector _itemBuilder(int index, BuildContext context) {
    CoinModel coinModel = controller.dataSource[index];
    return GestureDetector(
      onTap: () {
        if (controller.isFullPage == true && index == 0) {
          Scaffold.of(context).openDrawer();
          return;
        }
        controller.updateChain(coinModel);
      },
      behavior: HitTestBehavior.translucent,
      child: Obx(() => Container(
            color: coinModel.chain == controller.coinModel?.value.chain
                ? Get.theme.bgColor
                : Get.theme.colorF9F9F9,
            child: Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(13)),
                child: ImageWidget(
                  assetUrl: index == 0 &&
                          controller.chainManagerMode == ChainManagerMode.wallet
                      ? (controller.isFullPage == true
                          ? 'chain_sort'
                          : 'icon_chain_all')
                      : (controller.isFullPage == false
                          ? coinModel.chainIcon
                          : (coinModel.isCoinSupported == true
                              ? coinModel.chainIcon
                              : coinModel.disabledIcon)),
                  width: Get.setImageSize(22),
                  height: Get.setImageSize(22),
                ),
              ),
            ),
          )),
    );
  }
}
