/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-30 16:28:15
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';

class ChainAddressShowMessageWidget extends StatelessWidget {
  final String message;
  final String chainIcon;
  const ChainAddressShowMessageWidget(
      {super.key, required this.message, required this.chainIcon});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: Get.getSafetyBottomPadding(),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
            child: QRWidget(
              data: message,
              assetImage: AssetImage('assets/images/$chainIcon.png'),
            ),
          ),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(14),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setNumberFontFamily()),
          ),
          SizedBox(
            height: Get.setPaddingSize(24),
            width: Get.width,
          ),
          OutlinedButtonWidget(
            text: ID.copy.tr,
            width: Get.setWidth(240),
            onPressed: () => Get.copy(message),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ImageWidget(
                  assetUrl: 'copy_black',
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(20),
                ),
                SizedBox(
                  width: Get.setPaddingSize(6),
                ),
                Text(
                  ID.copy.tr,
                  style: TextStyle(
                    fontSize: Get.setFontSize(16),
                    color: Get.theme.textPrimary,
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.medium,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
