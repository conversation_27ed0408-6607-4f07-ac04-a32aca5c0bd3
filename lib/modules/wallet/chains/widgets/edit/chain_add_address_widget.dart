/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-18 10:13:05
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/bitcoin/ltc.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class ChainAddAddressWidget extends StatefulWidget {
  const ChainAddAddressWidget(
      {super.key, required this.controller, this.addAddressTap});

  final ChainManagerController controller;
  final Function(int? addressType, String? addressLable)? addAddressTap;

  @override
  State<ChainAddAddressWidget> createState() => _ChainAddAddressState();
}

class _ChainAddAddressState extends State<ChainAddAddressWidget> {
  bool isTypeAction = false;

  int selectedType = CoinAddressProtocol.legacy;
  String addressLabel = '';

  @override
  void initState() {
    addressLabel = _addressLabel;
    if ((widget.controller.coinType!.isBitCoinNetWork ||
            widget.controller.coinType is LitecoinChain) &&
        !widget.controller.wallet.isP1P2P3Wallet) {
      selectedType = CoinAddressProtocol.segWitP2sh;
    }
    super.initState();
  }

  String _title(int type) {
    if (type == CoinAddressProtocol.segWitP2sh) {
      return ID.chainSegWitP2shItem.tr;
    } else if (type == CoinAddressProtocol.segWit) {
      return ID.chainSegWitItem.tr;
    } else if (type == CoinAddressProtocol.taproot) {
      return 'Taproot';
    } else if (type == CoinAddressProtocol.legacy) {
      return ID.chainLegacyItem.tr;
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return isTypeAction == true
        ? ChainAddressTypeWidget(
            selectedType: selectedType,
            coinType: widget.controller.coinType!,
            wallet: widget.controller.wallet,
            callback: (type) {
              setState(() {
                isTypeAction = false;
                selectedType = type;
              });
            },
          )
        : Padding(
            padding: EdgeInsets.only(
                top: Get.setPaddingSize(16),
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                bottom: Get.getSafetyBottomPadding()),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _headerWidget(),
                _addressTypeWidget(),
                _titleWidget(ID.chainAddRemark.tr),
                _remarkWidget(),
                SizedBox(
                  height: Get.setPaddingSize(20),
                ),
                ButtonWidget(
                  text: ID.stringConfirm.tr,
                  width: Get.width,
                  onPressed: () {
                    if (Get.isEmptyString(addressLabel)) {
                      Get.showToast(ID.addressLabelEmpty.tr,
                          toastMode: ToastMode.waring);
                      return;
                    }

                    if (widget.addAddressTap != null) {
                      widget.addAddressTap!(selectedType, addressLabel);
                    }
                  },
                )
              ],
            ),
          );
  }

  RenderObjectWidget _addressTypeWidget() {
    if (!(widget.controller.coinType!.isBitCoinNetWork ||
            widget.controller.coinType is LitecoinChain) ||
        widget.controller.wallet.isP1P2P3Wallet) {
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleWidget(ID.chainAddType.tr),
        _addressType(),
        SizedBox(
          height: Get.setPaddingSize(20),
        )
      ],
    );
  }

  SizedBox _remarkWidget() {
    return SizedBox(
      height: Get.setHeight(44),
      child: TextFieldWidget(
        hintText: _addressLabel,
        controller: TextEditingController(text: _addressLabel),
        onValueChanged: (value) => addressLabel = value,
      ),
    );
  }

  String get _addressLabel {
    int length = widget.controller.addressList.length + 1;
    String index = length.toString();
    if (length < 10) {
      index = index.padLeft(2, '0');
    }

    return '${widget.controller.coinType?.symbol}-$index';
  }

  GestureDetector _addressType() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isTypeAction = true;
        });
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: Get.setHeight(44),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Get.theme.colorD3D3D3, width: 0.5)),
        child: Padding(
          padding: EdgeInsets.only(
              top: Get.setPaddingSize(10),
              bottom: Get.setPaddingSize(12),
              right: Get.setPaddingSize(12),
              left: Get.setPaddingSize(12)),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _title(selectedType),
                  style: stylePrimary_16_m,
                ),
              ),
              ImageWidget(
                assetUrl: 'down',
                width: Get.setImageSize(10),
                height: Get.setImageSize(10),
              )
            ],
          ),
        ),
      ),
    );
  }

  Padding _titleWidget(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.setPaddingSize(8)),
      child: Text(title,
          style: TextStyle(
              fontSize: Get.setFontSize(14), color: Get.theme.textSecondary)),
    );
  }

  Padding _headerWidget() {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.setPaddingSize(16)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            ID.chainAddAddress.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: Get.setFontSize(18),
                color: Get.theme.textPrimary,
                fontWeight: FontWeightX.medium),
          ),
          GestureDetector(
            onTap: () => Get.back(),
            child: ImageWidget(
              assetUrl: 'icon_exit',
              width: Get.setImageSize(24),
              height: Get.setImageSize(24),
            ),
          )
        ],
      ),
    );
  }
}

class ChainAddressTypeWidget extends StatelessWidget {
  const ChainAddressTypeWidget(
      {super.key,
      required this.selectedType,
      required this.coinType,
      required this.wallet,
      required this.callback});

  final int selectedType;
  final CoinType coinType;
  final Wallet wallet;
  final Function(int type) callback;

  String _title(int type) {
    if (type == CoinAddressProtocol.segWitP2sh) {
      return ID.chainSegWitP2shItem.tr;
    } else if (type == CoinAddressProtocol.segWit) {
      return ID.chainSegWitItem.tr;
    } else if (type == CoinAddressProtocol.taproot) {
      return 'Taproot';
    } else if (type == CoinAddressProtocol.legacy) {
      return ID.chainLegacyItem.tr;
    } else {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _itemWidget(CoinAddressProtocol.segWitP2sh),
          const DividerWidget(),
          Visibility(
            visible: coinType.isBitCoinNetWork && wallet is UltraPlusWallet,
            child: _itemWidget(CoinAddressProtocol.taproot),
          ),
          Visibility(
            visible: coinType.isBitCoinNetWork && wallet is UltraPlusWallet,
            child: const DividerWidget(),
          ),
          _itemWidget(CoinAddressProtocol.segWit),
          const DividerWidget(),
          _itemWidget(CoinAddressProtocol.legacy),
          const DividerWidget(),
          _cancelWidget(),
        ],
      ),
    );
  }

  GestureDetector _cancelWidget() {
    return GestureDetector(
      onTap: () => callback(selectedType),
      behavior: HitTestBehavior.translucent,
      child: SizedBox(
        width: Get.width,
        child: Padding(
          padding: EdgeInsets.only(
              top: Get.setPaddingSize(16),
              bottom: Get.getSafetyBottomPadding()),
          child: Text(
            ID.stringCancel.tr,
            textAlign: TextAlign.center,
            style: stylePrimary_16_m,
          ),
        ),
      ),
    );
  }

  HighLightInkWell _itemWidget(int type) {
    return HighLightInkWell(
      onTap: () => callback(type),
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(14),
            horizontal: Get.setPaddingSize(16)),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _title(type),
                    style: stylePrimary_16_m,
                  ),
                  SizedBox(height: Get.setPaddingSize(4)),
                  Text(
                    _method(type),
                    style: TextStyle(
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setNumberFontFamily(),
                    ),
                  ),
                  SizedBox(height: Get.setPaddingSize(4)),
                  Text(
                    _desStr(type),
                    style: TextStyle(
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setNumberFontFamily(),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: Get.setPaddingSize(8)),
            Visibility(
                visible: selectedType == type,
                child: ImageWidget(
                  assetUrl: 'icon_select',
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(20),
                ))
          ],
        ),
      ),
    );
  }

  String _method(int type) {
    if (type == CoinAddressProtocol.segWitP2sh) {
      if (coinType.isBitCoinNetWork) {
        return "P2SH-P2WPKH (m/49'/0'/0')";
      } else if (coinType is LitecoinChain) {
        return "P2SH-P2WPKH (m/49'/2'/0')";
      }
    } else if (type == CoinAddressProtocol.segWit) {
      if (coinType.isBitCoinNetWork) {
        return "P2WPKH (m/84'/0'/0')";
      } else if (coinType is LitecoinChain) {
        return "P2WPKH (m/84'/2'/0')";
      }
    } else if (type == CoinAddressProtocol.taproot) {
      return "P2TR (m/86'/0'/0')";
    } else if (type == CoinAddressProtocol.legacy) {
      if (coinType.isBitCoinNetWork) {
        return "P2PKH (m/44'/0'/0')";
      } else if (coinType is LitecoinChain) {
        return "P2PKH (m/44'/2'/0')";
      }
    }
    return '';
  }

  String _desStr(int type) {
    if (type == CoinAddressProtocol.segWitP2sh) {
      if (coinType.isBitCoinNetWork) {
        return ID.stringP2SHDes.tr;
      } else if (coinType is LitecoinChain) {
        return ID.stringP2SHDLTCes.tr;
      }
    } else if (type == CoinAddressProtocol.segWit) {
      if (coinType.isBitCoinNetWork) {
        return ID.stringP2WPKHDes.tr;
      } else if (coinType is LitecoinChain) {
        return ID.stringP2WPKHLTCDes.tr;
      }
    } else if (type == CoinAddressProtocol.taproot) {
      return ID.stringP2TRDes.tr;
    } else if (type == CoinAddressProtocol.legacy) {
      if (coinType.isBitCoinNetWork) {
        return ID.stringP2PKHDes.tr;
      } else if (coinType is LitecoinChain) {
        return ID.stringP2PKHLTCDes.tr;
      }
    }
    return '';
  }
}
