/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-21 14:10:26
 * @LastEditTime: 2024-11-22 09:37:04
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/chain_address_remark_widget.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/chain_address_show_message_widget.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/chain_show_address_detail_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';

enum AddressEditType { qr, remark, publickey, signiMessage }

class ChainAddressEditWidget extends StatelessWidget {
  const ChainAddressEditWidget(
      {super.key,
      required this.controller,
      required this.addressModel,
      this.callback});

  final ChainManagerController controller;
  final ValueChanged<String?>? callback;
  final AddressModel? addressModel;

  Future<void> _showEdit(AddressEditType type) async {
    Get.back();
    if (type == AddressEditType.signiMessage) {
      Get.toNamed(AppRoutes.messageSignPage, arguments: {
        GetArgumentsKey.addressModel: addressModel,
        GetArgumentsKey.coinType: controller.coinType,
        GetArgumentsKey.wallet: controller.wallet,
      });
    } else {
      await Future.delayed(const Duration(milliseconds: 300));
      Get.showBottomSheet(
        title: _title(type),
        paddingBottom: 0,
        bodyWidget: _bodyWidget(type),
      );
    }
  }

  String? _title(AddressEditType type) {
    if (type == AddressEditType.qr) {
      return addressModel?.addressLabel;
    } else if (type == AddressEditType.remark) {
      return ID.chainEditRemark.tr;
    } else if (type == AddressEditType.publickey) {
      return ID.stringAddressPublicTitle.tr;
    } else if (type == AddressEditType.signiMessage) {
      return '';
    }
    return null;
  }

  Widget _bodyWidget(AddressEditType type) {
    if (type == AddressEditType.qr) {
      return ChainShowAddressDetailWidget(
        controller: controller,
        addressModel: addressModel,
      );
    } else if (type == AddressEditType.remark) {
      return ChainAddressRemarkWidget(
        controller: controller,
        addressLabel: addressModel?.addressLabel,
        callback: callback,
      );
    } else if (type == AddressEditType.publickey) {
      return ChainAddressShowMessageWidget(
        message: addressModel!.publickey ?? '',
        chainIcon: controller.coinType!.chainIcon,
      );
    }
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _itemWidget(ID.chainAddressDetail.tr, AddressEditType.qr),
          _itemWidget(ID.chainEditRemark.tr, AddressEditType.remark),
          _itemWidget(
              ID.stringAddressPublicTitle.tr, AddressEditType.publickey),
          Visibility(
            visible: controller.coinType!.isSupportMessageSign &&
                controller.wallet is UltraPlusWallet,
            child: _itemWidget(
                ID.stringSignMessage.tr, AddressEditType.signiMessage),
          ),
        ],
      ),
    );
  }

  Widget _itemWidget(String title, AddressEditType type) {
    if (type == AddressEditType.publickey) {
      if (controller.wallet.isP1P2P3Wallet ||
          controller.coinType is SolanaChain) {
        return const SizedBox.shrink();
      }
    }
    return HighLightInkWell(
      onTap: () => _showEdit(type),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(14)),
        child: Row(
          children: [
            _textWidget(title),
            SizedBox(
              width: Get.setPaddingSize(8),
            ),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(12),
              height: Get.setImageSize(12),
            )
          ],
        ),
      ),
    );
  }

  Expanded _textWidget(String title) {
    return Expanded(
      child: Text(
        title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: stylePrimary_16_m,
      ),
    );
  }
}
