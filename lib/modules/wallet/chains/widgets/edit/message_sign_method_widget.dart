/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-19 09:33:13
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/model/transfer_model.dart';

class MessageSignMethodWidget extends StatelessWidget {
  const MessageSignMethodWidget({
    super.key,
    required this.type,
    required this.coinType,
    required this.addressModel,
    this.callback,
  });

  final TransferType type;
  final CoinType coinType;
  final AddressModel addressModel;
  final Function(TransferType type)? callback;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.setPaddingSize(16),
      ),
      child: Column(
        children: [
          Visibility(
            visible: coinType.isEthereumSeries,
            child: _signItemWidget(
              SignMessageType.signPersonalMessage,
              TransferType.signPersonalMessage,
            ),
          ),
          Visibility(
            visible: coinType is BitcoinChain,
            child: _signItemWidget(
              SignMessageType.ecdsa,
              TransferType.ecdsa,
            ),
          ),
          Visibility(
            visible: coinType is BitcoinChain &&
                (addressModel.segwitType == CoinAddressProtocol.taproot ||
                    addressModel.segwitType == CoinAddressProtocol.segWit),
            child: _signItemWidget(
              SignMessageType.bip322Simple,
              TransferType.bip322Simple,
            ),
          ),
        ],
      ),
    );
  }

  GestureDetector _signItemWidget(String title, TransferType type) {
    return GestureDetector(
      onTap: () {
        if (callback != null) {
          callback!(type);
          Get.back();
        }
      },
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: Get.setFontSize(16),
                  color: Get.theme.textPrimary,
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.medium,
                ),
              ),
            ),
            SizedBox(width: Get.setPaddingSize(8)),
            Visibility(
              visible: this.type == type,
              child: ImageWidget(
                assetUrl: 'icon_select',
                width: Get.setImageSize(20),
                height: Get.setImageSize(20),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
