/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-21 14:34:16
 * @LastEditTime: 2024-09-30 16:19:51
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/chain.dart';

class ChainShowAddressDetailWidget extends StatelessWidget {
  const ChainShowAddressDetailWidget(
      {super.key, required this.controller, required this.addressModel});

  final AddressModel? addressModel;
  final ChainManagerController controller;

  String get _addressTypeTitle {
    if (addressModel?.segwitType == CoinAddressProtocol.segWitP2sh) {
      return ID.chainSegWitP2shItem.tr;
    } else if (addressModel?.segwitType == CoinAddressProtocol.segWit) {
      return ID.chainSegWitItem.tr;
    } else if (addressModel?.segwitType == CoinAddressProtocol.legacy) {
      return ID.chainLegacyItem.tr;
    } else if (addressModel?.segwitType == CoinAddressProtocol.taproot) {
      return 'Taproot';
    } else {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Padding(
        padding: EdgeInsets.only(
          bottom: Get.getSafetyBottomPadding(),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Visibility(
              visible: controller.coinType!.isMultipleAddressType,
              child: Container(
                decoration: BoxDecoration(
                  color: Get.theme.colorF9F9F9,
                  borderRadius: BorderRadius.circular(5.0),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: Get.setPaddingSize(6),
                      vertical: Get.setPaddingSize(2)),
                  child: Text(
                    _addressTypeTitle,
                    style: TextStyle(
                        fontSize: Get.setFontSize(12),
                        color: Get.theme.textSecondary),
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
              child: QRWidget(
                data: addressModel?.address ?? '',
                assetImage: AssetImage(
                    'assets/images/${controller.coinType?.chainIcon}.png'),
              ),
            ),
            Text(
              addressModel?.address ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.regular,
                  fontFamily: Get.setFontFamily()),
            ),
            SizedBox(
              height: Get.setPaddingSize(12),
            ),
            Container(
              decoration: BoxDecoration(
                color: Get.theme.colorFF6A16.withOpacity(0.1),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: Get.setPaddingSize(12),
                    vertical: Get.setPaddingSize(6)),
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                      text: ID.chainAddressDetailTip1.tr,
                      style: TextStyle(
                          color: Get.theme.textPrimary,
                          fontSize: Get.setFontSize(16),
                          fontWeight: FontWeightX.regular,
                          fontFamily: Get.setFontFamily()),
                      children: [
                        TextSpan(
                            text: ' ${controller.coinType?.chainName} ',
                            style: TextStyle(
                                color: Get.theme.colorFF6A16,
                                fontSize: Get.setFontSize(16),
                                fontWeight: FontWeightX.semibold,
                                fontFamily: Get.setFontFamily())),
                        TextSpan(
                            text: ID.chainAddressDetailTip2.tr,
                            style: TextStyle(
                                color: Get.theme.textPrimary,
                                fontSize: Get.setFontSize(16),
                                fontWeight: FontWeightX.regular,
                                fontFamily: Get.setFontFamily()))
                      ]),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
