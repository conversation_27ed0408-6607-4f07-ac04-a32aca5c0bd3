/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-21 15:43:52
 * @LastEditTime: 2024-10-15 09:28:50
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';

class ChainAddressRemarkWidget extends StatelessWidget {
  const ChainAddressRemarkWidget(
      {super.key, required this.controller, this.callback, this.addressLabel});

  final ChainManagerController controller;
  final ValueChanged<String?>? callback;
  final String? addressLabel;

  @override
  Widget build(BuildContext context) {
    String? text = addressLabel;
    return Padding(
      padding: EdgeInsets.only(
          top: Get.setPaddingSize(14),
          right: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding()),
      child: Column(
        children: [
          TextFieldWidget(
            controller: TextEditingController(text: addressLabel),
            maxLength: 12,
            onValueChanged: (value) => text = value,
          ),
          SizedBox(
            height: Get.setPaddingSize(12),
          ),
          ButtonWidget(
            text: ID.stringConfirm.tr,
            width: Get.width,
            onPressed: () {
              if (Get.isEmptyString(text)) {
                Get.showToast(ID.addressLabelEmpty.tr,
                    toastMode: ToastMode.waring);
                return;
              }
              if (callback != null) {
                callback!(text);
              }
              Get.back();
            },
          )
        ],
      ),
    );
  }
}
