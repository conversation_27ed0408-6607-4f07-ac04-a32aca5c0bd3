/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-18 15:36:10
 * @LastEditTime: 2025-03-24 17:39:32
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/modules/wallet/chains/controllers/chain_manager_add_controller.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_eos_account_empty.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/nem/xem.dart';
import 'package:wallet_core/chain/ripple/xrp.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/wallet/cold/pro1_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

class ChainManagerAddressWidget
    extends BaseStatelessWidget<ChainManagerController> {
  const ChainManagerAddressWidget({super.key});

  int get itemCount {
    if (controller.coinType is AllChain) {
      return 1;
    }
    return controller.addressList.length;
  }

  Future<void> onReorder(int oldIndex, int newIndex) async {
    if (oldIndex == newIndex) return;
    if (oldIndex < newIndex) {
      newIndex = newIndex - 1;
    }
    AddressModel oldModel = controller.addressList[oldIndex];
    AddressModel newModel = controller.addressList[newIndex];
    int? oldSortedId = oldModel.sortedId;
    int? newSortedId = newModel.sortedId;
    await Get.database.addressDao.updateAddressSortedId(oldModel, newSortedId);
    await Get.database.addressDao.updateAddressSortedId(newModel, oldSortedId);
    controller.updateAddressList();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => ReorderableListView.builder(
        clipBehavior: Clip.antiAlias,
        padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            top: Get.setPaddingSize(12),
            bottom: Get.getSafetyBottomPadding()),
        itemCount: itemCount,
        onReorder: (int oldIndex, int newIndex) =>
            onReorder(oldIndex, newIndex),
        buildDefaultDragHandles: controller.isAddressEdit.value,
        header: _headerWidget(),
        footer: _footerWidget(),
        proxyDecorator:
            (Widget child, int index, Animation<double> animation) =>
                _itemBuilder(index),
        itemBuilder: (_, int index) => _itemBuilder(index)));
  }

  Padding _itemBuilder(int index) {
    AddressModel? addressModel;
    if (controller.coinType is! AllChain) {
      addressModel = controller.addressList[index];
    }
    return Padding(
      key: Key(index.toString()),
      padding: EdgeInsets.only(top: Get.setPaddingSize(12)),
      child: _bgWidget(addressModel),
    );
  }

  GestureDetector _bgWidget(AddressModel? addressModel) {
    return GestureDetector(
      onTap: () => controller.addressItemAction(addressModel: addressModel),
      behavior: HitTestBehavior.translucent,
      child: Container(
        decoration: BoxDecoration(
          color: getChainThemeColor(controller.coinType?.chain ?? ''),
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Padding(
          padding: EdgeInsets.only(left: Get.setPaddingSize(5)),
          child: _contentWidget(addressModel),
        ),
      ),
    );
  }

  Container _contentWidget(AddressModel? addressModel) {
    return Container(
      decoration: BoxDecoration(
          color: Get.theme.bgColor,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
              color: (controller.selectedAddress() == addressModel?.address &&
                      controller.selectedChain() == addressModel?.chain)
                  ? getChainThemeColor(controller.coinType?.chain ?? '')
                  : Get.theme.colorECECEC,
              width: 1)),
      child: Padding(
        padding: EdgeInsets.all(Get.setPaddingSize(14)),
        child: addressModel == null
            ? _allAmountContent()
            : Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: controller.isAddressEdit.value == false
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.center,
                children: [
                  _addressContent(addressModel),
                  SizedBox(
                    width: Get.setPaddingSize(4),
                  ),
                  controller.isAddressEdit.value == false
                      ? _amountWidget(addressModel)
                      : _addressEdit(),
                ],
              ),
      ),
    );
  }

  ImageWidget _addressEdit() {
    return ImageWidget(
      assetUrl: 'chain_drag',
      width: Get.setImageSize(21),
      height: Get.setImageSize(21),
    );
  }

  Column _amountWidget(AddressModel addressModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Visibility(
            visible: controller.isFullPage,
            child: ImageWidget(
                assetUrl: 'wallet_more_icon',
                width: Get.setImageSize(20),
                height: Get.setImageSize(20))),
        SizedBox(
          height: Get.setPaddingSize(5),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Visibility(
              visible: controller.chainManagerMode != ChainManagerMode.nft,
              child: Text(
                controller.assets(addressModel),
                maxLines: 1,
                style: TextStyle(
                    color: Get.theme.primary,
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.semibold,
                    overflow: TextOverflow.ellipsis),
              ),
            ),
            Visibility(
              visible: !controller.isFullPage,
              child: ImageWidget(
                  assetUrl: "icon_new_arrow01",
                  width: Get.setImageSize(12),
                  height: Get.setImageSize(12)),
            )
          ],
        ),
      ],
    );
  }

  Expanded _addressContent(AddressModel addressModel) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _addressName(addressModel),
          SizedBox(
            height: Get.setPaddingSize(5),
          ),
          _addressWidget(addressModel)
        ],
      ),
    );
  }

  Column _allAmountContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(ID.chainTotalAssets.tr,
            style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(16),
                fontWeight: FontWeightX.semibold,
                fontFamily: Get.setNumberFontFamily())),
        SizedBox(
          height: Get.setPaddingSize(5),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                controller.assets(null),
                maxLines: 1,
                style: TextStyle(
                    color: Get.theme.primary,
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.semibold,
                    overflow: TextOverflow.ellipsis),
              ),
            ),
            ImageWidget(
                assetUrl: "icon_new_arrow01",
                width: Get.setImageSize(12),
                height: Get.setImageSize(12))
          ],
        )
      ],
    );
  }

  HighLightInkWell _addressWidget(AddressModel addressModel) {
    return HighLightInkWell(
      onTap: () => Get.copy(addressModel.address),
      child: Text.rich(
          TextSpan(children: <InlineSpan>[
            TextSpan(
              text: AddressUtils.omitAddress(addressModel.address),
              style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(12),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.regular,
                  overflow: TextOverflow.ellipsis),
            ),
            WidgetSpan(
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: ImageWidget(
                    assetUrl: "copy",
                    width: Get.setImageSize(16),
                    height: Get.setImageSize(16)),
              ),
            ),
          ]),
          maxLines: 1,
          overflow: TextOverflow.ellipsis),
    );
  }

  Row _addressName(AddressModel addressModel) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Text(
            addressModel.addressLabel ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(16),
                fontWeight: FontWeightX.semibold,
                fontFamily: Get.setNumberFontFamily()),
          ),
        ),
        SizedBox(
          width: Get.setPaddingSize(8),
        ),
        Visibility(
          visible: !Get.isEmptyString(_addressType(addressModel)),
          child: Container(
            decoration: BoxDecoration(
              color: Get.theme.colorF9F9F9,
              borderRadius: BorderRadius.circular(5),
            ),
            padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(6),
                vertical: Get.setPaddingSize(2)),
            child: Text(
              _addressType(addressModel),
              style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(12),
                  fontWeight: FontWeightX.regular,
                  fontFamily: Get.setFontFamily()),
            ),
          ),
        ),
      ],
    );
  }

  dynamic _footerWidget() {
    if (controller.addressList.isEmpty && controller.coinType is! AllChain) {
      return _bindButton();
    }

    if ((controller.coinType is AllChain || controller.coinType is NemChain) ||
        controller.isFullPage == false) {
      return null;
    }

    if (controller.wallet is TouchWallet) return null;

    if (controller.wallet.isP1P2P3Wallet &&
        controller.coinType!.isEthereumSeries) {
      return null;
    }

    if (controller.wallet is Pro1Wallet || controller.wallet is Pro2Wallet) {
      return null;
    }

    if (controller.coinType is EosChain) {
      return null;
    }

    if (controller.walletModel.isPassphraseWallet == true &&
        controller.coinType != null &&
        controller.coinType! is RippleChain) {
      return null;
    }

    return _buttomButton(
      title: controller.coinType is SolanaChain
          ? ID.stringSynAddressTitle.tr
          : ID.chainAddAddress.tr,
      imageName: controller.coinType is SolanaChain ? 'scan' : 'add_address',
      onPressed: () => controller.addAddressAction(),
    );
  }

  dynamic _bindButton() => controller.coinType is EosChain
      ? _eosButtomWidget()
      : _addressEmptyWidget();

  HomeEOSAccountEmptyWidget _eosButtomWidget() =>
      const HomeEOSAccountEmptyWidget(isChainPage: true);

  Padding _addressEmptyWidget() => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(16)),
        child: Column(
          children: [
            Text(
              ID.stringNotBoundYet.tr,
              style: TextStyle(
                fontSize: Get.setFontSize(18),
                color: Get.theme.textPrimary,
                fontWeight: FontWeightX.medium,
              ),
            ),
            SizedBox(height: Get.setPaddingSize(16)),
            Text(
              ID.stringBindAddressInfo.tr,
              style: TextStyle(
                fontSize: Get.setFontSize(14),
                color: Get.theme.textPrimary,
              ),
            ),
            SizedBox(
              width: Get.width,
              child: _buttomButton(
                title: ID.stringBindAddressNow.tr,
                imageName: 'scan',
                onPressed: () => controller.bindChainAction(),
              ),
            ),
          ],
        ),
      );

  Padding _buttomButton(
      {required String title,
      required Function() onPressed,
      String? imageName}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(16)),
      child: ButtonWidget(
        text: title,
        onPressed: onPressed,
        buttonStatus: ButtonStatus.icon,
        iconWidget: Padding(
          padding: EdgeInsets.only(right: Get.setPaddingSize(8)),
          child: Get.isEmptyString(imageName)
              ? null
              : Visibility(
                  visible: controller.wallet is! TouchWallet,
                  child: ImageWidget(
                      assetUrl: imageName,
                      width: Get.setImageSize(18),
                      height: Get.setImageSize(18)),
                ),
        ),
      ),
    );
  }

  Row _headerWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          (controller.coinType is AllChain)
              ? ID.chainAllChains.tr
              : controller.coinType?.symbol ?? '',
          style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.semibold,
              fontFamily: Get.setNumberFontFamily()),
        ),
        Visibility(
          visible: (controller.coinType is AllChain) ? false : true,
          child: Text(
            ' - ${controller.coinType?.chainName}',
            style: TextStyle(
                color: Get.theme.textSecondary,
                fontSize: Get.setFontSize(12),
                fontWeight: FontWeightX.regular,
                fontFamily: Get.setNumberFontFamily()),
          ),
        )
      ],
    );
  }

  String _addressType(AddressModel addressModel) {
    if (controller.coinType!.isMultipleAddressType) {
      if (addressModel.segwitType == CoinAddressProtocol.legacy) {
        return 'Legacy';
      } else if (addressModel.segwitType == CoinAddressProtocol.segWitP2sh) {
        return 'Nested';
      } else if (addressModel.segwitType == CoinAddressProtocol.segWit) {
        return 'Native';
      } else if (addressModel.segwitType == CoinAddressProtocol.taproot) {
        return 'Taproot';
      }
    }
    return '';
  }
}
