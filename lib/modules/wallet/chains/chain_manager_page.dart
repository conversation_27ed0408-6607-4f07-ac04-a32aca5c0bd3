import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/modules/wallet/chains/widgets/chain_manager_address_widget.dart';
import 'package:coinbag/modules/wallet/chains/widgets/chain_manager_chains_widget.dart';
import 'package:coinbag/modules/wallet/chains/widgets/chain_manager_header_widget.dart';
import 'package:coinbag/modules/wallet/chains/widgets/chain_manager_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

class ChainManagerPage extends StatefulWidget {
  final bool isFullPage;
  final WalletModel? wallet;
  final String? chain;
  final ChainManagerMode chainManagerMode;
  const ChainManagerPage(
      {super.key,
      this.wallet,
      this.isFullPage = false,
      this.chain,
      this.chainManagerMode = ChainManagerMode.wallet});

  @override
  State<ChainManagerPage> createState() => _ChainManagerPageState();

  void showChainPage() {
    Get.showBottomSheet(
      hideHeader: true,
      fullScreenBodyWidget: this,
      paddingBottom: 0,
    );
  }
}

class _ChainManagerPageState extends State<ChainManagerPage> {
  final ChainManagerController controller = Get.find<ChainManagerController>();

  double get headerHeight {
    return AppBar().preferredSize.height;
  }

  double get contentHeight {
    return Get.height - headerHeight - Get.mediaQuery.padding.top;
  }

  double get chainsWidth {
    return Get.setWidth(54);
  }

  @override
  void initState() {
    super.initState();
    controller.isFullPage = widget.isFullPage;
    if (widget.wallet != null) {
      controller.setData(
          walletModel: widget.wallet!,
          chain: widget.chain,
          mode: widget.chainManagerMode);
    }
  }

  @override
  void dispose() {
    Get.delete<ChainManagerController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return controller.isFullPage == false
        ? Column(
            children: [
              ChainManagerHeaderWidget(
                height: headerHeight,
                controller: controller,
              ),
              _contentWidget()
            ],
          )
        : Scaffold(
            appBar: baseAppBar(
                title: ID.chainWalletManager.tr,
                leading: _leading(),
                actionWidget: _actionWidget()),
            body: Scaffold(
              drawerEnableOpenDragGesture: false,
              body: _contentWidget(),
              drawer: _drawer(),
              onDrawerChanged: (bool isOpened) => _onDrawerChanged(isOpened),
            ),
          );
  }

  Drawer _drawer() {
    return Drawer(
      width: Get.width * 0.8,
      clipBehavior: Clip.none,
      child: ChainManagerWidget(
        controller: controller,
      ),
    );
  }

  void _onDrawerChanged(bool isOpened) {
    if (isOpened == false) {
      controller.isChainEdit.value = false;
    } else {
      controller.isChainEdit.value = true;
      controller.isAddressEdit.value = false;
      controller.update();
    }
  }

  ConstrainedBox _contentWidget() {
    return ConstrainedBox(
      constraints: BoxConstraints(
          maxHeight: contentHeight,
          minWidth: Get.mediaQuery.size.width,
          maxWidth: Get.mediaQuery.size.width),
      child: SizedBox(
        width: Get.mediaQuery.size.width,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _chains(),
            _adresses(),
          ],
        ),
      ),
    );
  }

  Obx _leading() => Obx(() => IconButton(
      padding: EdgeInsets.only(left: Get.setPaddingSize(6)),
      onPressed: () => controller.isAddressEdit.value == false
          ? Get.back()
          : controller.isAddressEdit.value = false,
      icon: controller.isAddressEdit.value == false
          ? ImageWidget(
              assetUrl: 'icon_back_arrow',
              cacheRawData: true,
              shape: BoxShape.circle,
              width: Get.setImageSize(28),
              height: Get.setImageSize(28),
            )
          : Text(
              ID.stringCancel.tr,
              style: TextStyle(
                  color: Get.theme.colorFF6A16,
                  fontSize: Get.setFontSize(14),
                  fontWeight: FontWeightX.regular),
            )));

  List<Obx> _actionWidget() => [
        Obx(() => (controller.isChainEdit.value == true ||
                controller.wallet is TouchWallet)
            ? const SizedBox.shrink()
            : Padding(
                padding: EdgeInsets.only(right: Get.setPaddingSize(8)),
                child: IconButton(
                    padding:
                        EdgeInsets.symmetric(horizontal: Get.setPaddingSize(6)),
                    onPressed: () {
                      controller.isAddressEdit.value =
                          !controller.isAddressEdit.value;
                      controller.update();
                    },
                    icon: controller.isAddressEdit.value == false
                        ? ImageWidget(
                            assetUrl: 'icon_menu_filter',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          )
                        : Text(
                            ID.stringComplte.tr,
                            style: TextStyle(
                                color: Get.theme.colorFF6A16,
                                fontSize: Get.setFontSize(14),
                                fontWeight: FontWeightX.regular),
                          )),
              ))
      ];

  Expanded _adresses() => Expanded(
        child: SizedBox(
          height: contentHeight,
          child: const ChainManagerAddressWidget(),
        ),
      );

  SizedBox _chains() => SizedBox(
        width: chainsWidth,
        height: contentHeight,
        child: const ChainListWidget(),
      );
}
