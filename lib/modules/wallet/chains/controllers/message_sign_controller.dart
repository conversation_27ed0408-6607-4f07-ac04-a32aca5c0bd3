/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-28 15:45:40
 */

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/chain_address_show_message_widget.dart';
import 'package:coinbag/modules/wallet/chains/widgets/edit/message_sign_method_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/wallet.dart';

class MessageSignController extends BaseController<AppDatabase> {
  @override
  void loadData() {}

  late AddressModel addressModel;
  late CoinType coinType;

  late TransferModel tsModel;

  String? signMessage;

  RxString methodMessage = ''.obs;

  Rx<TextEditingController> textCtr = TextEditingController().obs;

  @override
  void onInit() {
    Map arg = Get.arguments;
    addressModel = arg[GetArgumentsKey.addressModel];
    coinType = arg[GetArgumentsKey.coinType];
    Wallet wallet = arg[GetArgumentsKey.wallet];
    tsModel = TransferModel(
      chain: coinType.chain,
      addressPath: addressModel.path,
      walletId: addressModel.walletId,
      wallet: wallet,
      type: coinType.isBitCoinNetWork
          ? TransferType.ecdsa
          : TransferType.signPersonalMessage,
    );
    methodMessage.value = tsModel.signMessageMethod(tsModel.type) ?? '';
    super.onInit();
  }

  Future<void> signAction() async {
    if (Get.isEmptyString(signMessage)) {
      Get.showToast(ID.stringSignTextTip.tr, toastMode: ToastMode.waring);
      return;
    }

    if (coinType.isEthereumSeries) {
      tsModel.signMessage =
          await Get.walletCore.getSignPersonalMessage(signMessage!.stringToHex);
    } else {
      tsModel.signMessage = signMessage;
    }
    if (Get.isAuthorizeTransaction()) {
      final result = await Get.toNamed(AppRoutes.securityPage,
          arguments: SecurityState.security);
      if (result == true) {
        navigateToQRCodePage();
      }
    } else {
      navigateToQRCodePage();
    }
  }

  void navigateToQRCodePage() {
    Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
      GetArgumentsKey.transferModel: tsModel,
      GetArgumentsKey.qrType: QRCodeType.signMessage,
    });
  }

  void showResultSignMessge(String? message) {
    if (!Get.isEmptyString(message)) {
      Get.showBottomSheet(
        title: ID.stringSignMessage.tr,
        paddingBottom: 0,
        bodyWidget: ChainAddressShowMessageWidget(
          message: message!,
          chainIcon: coinType.chainIcon,
        ),
      );
    }
  }

  void signMethodAction() {
    Get.showBottomSheet(
        hideHeader: true,
        paddingBottom: 0,
        padding: const EdgeInsets.symmetric(horizontal: 0),
        bodyWidget: MessageSignMethodWidget(
          type: tsModel.type,
          coinType: coinType,
          addressModel: addressModel,
          callback: (type) {
            tsModel.type = type;
            methodMessage.value = tsModel.signMessageMethod(tsModel.type) ?? '';
          },
        ),
        bottomWidget: const BottomCancelWidget());
  }
}

class MessageSignBinding extends Bindings {
  @override
  void dependencies() => Get.lazyPut(() => MessageSignController());
}
