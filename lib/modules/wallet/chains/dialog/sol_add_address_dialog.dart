/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-22 13:39:41
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SolanaAddAddressDialog extends StatelessWidget {
  final VoidCallback onPressed;
  const SolanaAddAddressDialog({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: Get.setPaddingSize(16),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: Column(
        children: [
          Text(
            ID.stringSynAddressTip.tr,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(16),
              height: 2,
            ),
          ),
          SizedBox(height: Get.setPaddingSize(24)),
          ButtonWidget(
            text: ID.stringSynAddressTitle.tr,
            width: Get.width,
            buttonStatus: ButtonStatus.icon,
            iconWidget: Padding(
              padding: EdgeInsets.only(right: Get.setPaddingSize(8)),
              child: ImageWidget(
                  assetUrl: 'scan',
                  width: Get.setImageSize(18),
                  height: Get.setImageSize(18)),
            ),
            onPressed: () => onPressed(),
          ),
        ],
      ),
    );
  }
}
