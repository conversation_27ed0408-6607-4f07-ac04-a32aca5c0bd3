/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-06 13:13:41
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/wallet/chains/controllers/message_sign_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class MessageSignPage extends BaseStatelessWidget<MessageSignController> {
  const MessageSignPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringMessgaeSign.tr),
      bottomNavigationBar: _bottomWidget(),
      body: KeyboardDismissWidget(
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            top: Get.setPaddingSize(20),
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            bottom: Get.getSafetyBottomPadding(),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _titleWidget(ID.stringAddress.tr),
              SizedBox(height: Get.setPaddingSize(8)),
              _addressWidget(),
              SizedBox(height: Get.setPaddingSize(24)),
              _titleWidget(ID.stringSignMethodTitle.tr),
              SizedBox(height: Get.setPaddingSize(8)),
              _signMethodWidget(),
              SizedBox(height: Get.setPaddingSize(24)),
              _messageTitleWidget(),
              SizedBox(height: Get.setPaddingSize(8)),
              _textFieldWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Container _textFieldWidget() {
    return Container(
      height: Get.setHeight(200),
      decoration: BoxDecoration(
          color: Get.theme.colorF9F9F9,
          borderRadius: BorderRadius.circular(Get.setRadius(12)),
          border: Border.all(
            color: Get.theme.colorD3D3D3,
            width: 1,
          )),
      child: TextFieldWidget(
        controller: controller.textCtr.value,
        fillColor: Colors.transparent,
        maxLines: 100,
        hintText: ID.stringSignTextHint.tr,
        enabledBorder: _border(),
        focusedBorder: _border(),
        onValueChanged: (value) => controller.signMessage = value,
      ),
    );
  }

  Text _titleWidget(String title) => Text(title, style: styleSecond_14);

  Text _addressWidget() => Text(controller.addressModel.address ?? '',
      style: TextStyle(
        fontSize: Get.setFontSize(16),
        color: Get.theme.textPrimary,
        fontFamily: Get.setNumberFontFamily(),
        fontWeight: FontWeightX.medium,
      ));

  GestureDetector _signMethodWidget() => GestureDetector(
        onTap: () => controller.signMethodAction(),
        behavior: HitTestBehavior.translucent,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(12),
            vertical: Get.setPaddingSize(12),
          ),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: Get.theme.colorD3D3D3,
                width: 1,
              )),
          child: Row(
            children: [
              Expanded(
                child: Obx(() => Text(
                      controller.methodMessage.value,
                      style: TextStyle(
                        fontSize: Get.setFontSize(16),
                        color: Get.theme.textPrimary,
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.medium,
                      ),
                    )),
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              ImageWidget(
                assetUrl: 'arrow_down_black',
                width: Get.setImageSize(15),
                height: Get.setImageSize(15),
              )
            ],
          ),
        ),
      );

  Row _messageTitleWidget() {
    return Row(
      children: [
        Expanded(child: _titleWidget(ID.stringMessageContentTitle.tr)),
        SizedBox(height: Get.setPaddingSize(8)),
        GestureDetector(
          onTap: () async {
            KeyboardUtils.hideKeyboardNoContext();
            final result = await Get.toScanner(arguments: {
              GetArgumentsKey.scanAction: ScanAction.resultAction
            });
            if (!Get.isEmptyString(result)) {
              controller.textCtr.value.text = result;
              controller.signMessage = result;
            }
          },
          child: ImageWidget(
            assetUrl: 'icon_scan_black',
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
          ),
        )
      ],
    );
  }

  OutlineInputBorder _border() => OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
          width: Get.setWidth(1),
        ),
        borderRadius: BorderRadius.circular(Get.setRadius(12)),
      );

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        top: Get.setPaddingSize(16),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: ButtonWidget(
        text: ID.stringSignMessage.tr,
        onPressed: () => controller.signAction(),
      ),
    );
  }
}
