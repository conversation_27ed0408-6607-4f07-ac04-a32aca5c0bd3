/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-18 10:53:54
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/tokens/token_manager_controlle.dart';
import 'package:coinbag/modules/wallet/tokens/widgets/token_search_item_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class TokenManagerPage extends BaseStatelessWidget<TokenManagerController> {
  const TokenManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: ID.manageCoin.tr,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
              child: _searchBarWidget(),
            ),
            SizedBox(height: Get.setPaddingSize(24)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
              child: Text(
                ID.supportToken.tr,
                style: TextStyle(
                  fontSize: Get.setFontSize(12),
                  color: Get.theme.textSecondary,
                  fontWeight: FontWeightX.regular,
                ),
              ),
            ),
            Obx(() => _buildTokenManagerList(true)),
            SizedBox(height: Get.setPaddingSize(24)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
              child: Text(
                ID.nosupportToken.tr,
                style: TextStyle(
                  fontSize: Get.setFontSize(12),
                  color: Get.theme.textSecondary,
                  fontWeight: FontWeightX.regular,
                ),
              ),
            ),
            Obx(() => _buildTokenManagerList(false)),
          ],
        ),
      ),
    );
  }

  ListView _buildTokenManagerList(bool isSupport) {
    List<CoinModel> dataSource =
        isSupport ? controller.supportList : controller.noSupportList;
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: dataSource.length,
      itemBuilder: (context, index) {
        return TokenSearchItemWidget(
          coinModel: dataSource[index],
          wallet: controller.wallet,
          coinType: controller.coinType,
          callback: (coinModel) => controller.itemAction(coinModel),
        );
      },
    );
  }

  GestureDetector _searchBarWidget() => GestureDetector(
        onTap: () => Get.toNamed(AppRoutes.tokenSearchPage, arguments: {
          GetArgumentsKey.coinType: controller.coinType,
          GetArgumentsKey.controller: controller,
        }),
        behavior: HitTestBehavior.translucent,
        child: Container(
          height: Get.setHeight(40),
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
          decoration: BoxDecoration(
            color: Get.theme.colorF3F3F5,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Row(
              children: [
                ImageWidget(
                  assetUrl: 'search_bar_search',
                  width: Get.setImageSize(16),
                  height: Get.setImageSize(16),
                ),
                SizedBox(
                  width: Get.setPaddingSize(6),
                ),
                Expanded(
                  child: Text(
                    ID.tokenSearch.tr,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: Get.setFontSize(14),
                      color: Get.theme.textTertiary,
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      );
}
