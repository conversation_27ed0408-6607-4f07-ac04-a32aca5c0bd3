/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-12 14:59:11
 */

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/tokens/models/token_search_model.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/wallet/wallet.dart';

class TokenManagerController extends BaseController<ApiService> {
  var filteredTokenList = <Map<String, String>>[].obs;
  final box = GetStorage();
  var searchQuery = ''.obs;
  var searchResults = <Map<String, String>>[].obs;
  var currentChain = ''.obs;
  late CoinType coinType;
  late WalletModel walletModel;
  late Wallet wallet;

  RxList<CoinModel> supportList = <CoinModel>[].obs;
  RxList<CoinModel> noSupportList = <CoinModel>[].obs;

  @override
  void onInit() {
    coinType = Get.arguments![GetArgumentsKey.coinType] as CoinType;
    walletModel = Get.arguments![GetArgumentsKey.walletModel] as WalletModel;
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);
    super.onInit();
    loadTokenLists();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  void loadTokenLists() async {
    List<CoinModel> allList = await Get.database.coinDao.getAllData();
    supportList.value = allList
        .where((element) => (element.chain == coinType.chain &&
            element.isCoinSupported == true))
        .toList();
    noSupportList.value = allList
        .where((element) => (element.chain == coinType.chain &&
            element.isCoinSupported != true))
        .toList();
  }

  Future<void> itemAction(CoinModel coinModel) async {
    if (coinModel.isToken != true) return;
    bool isCoinSupported = !(coinModel.isCoinSupported ?? false);
    await Get.database.coinDao.updateCoinisSupported(
      coinModel: coinModel,
      isCoinSupported: isCoinSupported,
      tokenType: coinModel.tokenType,
    );
    if (isCoinSupported == true) {
      await _updateToken(coinModel);
    }

    loadTokenLists();
  }

  @override
  void loadData() {
    httpRequest(
      api.getTokenList(50),
      handleError: false,
      handleSuccess: false,
      (value) {
        if (value.data != null && value.data is Map<String, dynamic>) {
          updateTokens(value.data[coinType.chain] ?? []);
        }
      },
    );
  }

  Future<void> _updateToken(CoinModel coinModel) async {
    BalanceModel balanceModel = BalanceModel(
      symbol: coinModel.symbol,
      name: coinModel.chainName,
      contract: coinModel.contract,
      decimal: coinModel.chainDecimal,
      tokenType: coinModel.tokenType ?? 0,
      address: '',
    );

    List<TokenModel> tokenList =
        await Get.database.tokenDao.getTokenListByContract(
      walletId: walletModel.walletId!,
      chain: coinType.chain,
      contract: coinModel.contract ?? '',
    );
    if (tokenList.isEmpty) {
      await Get.database.tokenDao
          .intertToken(balanceModel, coinType, walletModel.walletId!, "");
    }
  }

  Future<void> updateTokens(List data) async {
    List<TokenSearchModel> tokenList = data
        .map((json) => TokenSearchModel.fromJson(json, coinType: coinType))
        .toList();
    tokenList = tokenList
        .where((element) => (!Get.isEmptyString(element.symbol) &&
            !Get.isEmptyString(element.contractaddress) &&
            !Get.isEmptyString(element.symbol) &&
            !Get.isEmptyString(element.chain) &&
            !Get.isEmptyString(element.enname) &&
            element.symbol?.toLowerCase() != coinType.chain.toLowerCase()))
        .toList();

    for (var model in tokenList) {
      CoinModel? coinModel = await Get.database.coinDao.getTokenCoinModel(
          chain: coinType.chain, contract: model.contractaddress);
      if (coinModel == null) {
        BalanceModel balanceModel = BalanceModel(
          symbol: model.symbol,
          name: model.enname,
          contract: model.contractaddress,
          decimal: model.decimals,
          tokenType: model.tokenType ?? 0,
          logoUrl: model.imageurl,
          address: '',
        );
        await Get.database.coinDao.intertTokenCoinModel(balanceModel, coinType,
            isCoinSupported: false);
      }
    }

    loadTokenLists();
  }

  void searchList() {
    httpRequest(
      api.getSearchTokenList('H', 'eth'),
      handleError: false,
      handleSuccess: false,
      (value) {
        Log.logPrint(value.data);
      },
    );
  }

  @override
  void onClose() {
    AppController.refreshHome();
    super.onClose();
  }
}

class TokenManagerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TokenManagerController());
  }
}
