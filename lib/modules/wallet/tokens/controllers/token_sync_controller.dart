import 'dart:convert';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';

class TokenSyncController extends BaseController {
  late CoinModel coinModel;
  late String qrData;

  @override
  void onInit() {
    coinModel = Get.arguments[GetArgumentsKey.coinModel];
    qrData = jsonEncode({
      'cn': coinModel.cnName ?? '',
      'en': coinModel.chainName ?? '',
      'sb': coinModel.symbol ?? '',
      'a': (coinModel.contract ?? '').toLowerCase(),
      'p': coinModel.chainDecimal,
    });
    super.onInit();
  }

  @override
  void loadData() {}
}

class TokenSyncBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TokenSyncController());
  }
}
