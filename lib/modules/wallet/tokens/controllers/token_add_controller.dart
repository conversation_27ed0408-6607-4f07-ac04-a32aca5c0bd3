/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-18 13:15:37
 */
/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-06-06 17:06:24
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/wallet/wallet.dart';

class TokenAddController extends BaseController<ApiService> {
  late Rx<CoinType> coinType;
  late WalletModel walletModel;
  late Wallet wallet;

  String? symbol;
  String? contract;

  @override
  void onInit() {
    coinType = Rx(Get.arguments[GetArgumentsKey.coinType]);
    walletModel = Get.arguments[GetArgumentsKey.walletModel];
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);

    super.onInit();
  }

  void buttonAction() {
    if (Get.isEmptyString(symbol)) {
      Get.showToast(ID.importTokenSymbol.tr, toastMode: ToastMode.waring);
      return;
    }
    if (Get.isEmptyString(contract)) {
      Get.showToast(ID.enterContractsInfo.tr, toastMode: ToastMode.waring);
      return;
    }

    bool result = true;
    if (coinType.value is EosChain) {
      if (contract!.length < 13) {
        result = false;
      }
    } else if (coinType.value.isEthereumSeries) {
      RegExp regExp = RegExp(r'^(0x)?[0-9A-Fa-f]{40}$');
      result = regExp.hasMatch(contract!);
    }

    if (result == false) {
      Get.showToast(ID.stringInputOkContract.tr, toastMode: ToastMode.waring);
      return;
    }

    loadData();
  }

  @override
  void loadData() {
    Get.showLoadingDialog();
    httpRequest(
        api.addToken(RequestParams()
            .put(APIConstant.symbol, symbol)
            .put(APIConstant.type, coinType.value.chain)
            .put(APIConstant.account, contract)
            .getRequestBody()),
        handleError: false,
        handleSuccess: false, (value) {
      Get.dismissLoadingDialog();
      if (value.code == APIConstant.responseCode) {
        Get.showToast(ID.stringAddTokenSuccess.tr,
            toastMode: ToastMode.success);
        Future.delayed(const Duration(seconds: 1)).then((value) => Get.back());
      } else {
        Get.showToast(value.message, toastMode: ToastMode.waring);
      }
    }, error: (_) {
      Get.dismissLoadingDialog();
    });
  }
}

class TokenAddBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TokenAddController());
  }
}
