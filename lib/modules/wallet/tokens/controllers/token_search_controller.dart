import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/tokens/models/token_search_model.dart';
import 'package:coinbag/modules/wallet/tokens/token_manager_controlle.dart';
import 'package:coinbag/res/resource.dart';
import 'package:drift/drift.dart' as drift;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wallet_core/chain/coin_type.dart';

class TokenSearchController extends BaseController<ApiService> {
  late CoinType coinType;
  late TokenManagerController ctr;
  RxBool isFocus = true.obs;
  List<CoinModel> searchList = [];
  var textEditingController = TextEditingController();

  RxString searchText = ''.obs;

  @override
  void onInit() {
    coinType = Get.arguments[GetArgumentsKey.coinType];
    ctr = Get.arguments[GetArgumentsKey.controller];
    super.onInit();

    debounce(searchText, (callback) => loadData());
  }

  @override
  void onReady() {
    super.onReady();
    showSuccess();
  }

  @override
  void loadData() {
    if (Get.isEmptyString(searchText.value)) {
      searchList = [];
      showSuccess();
      update([GetKey.searchTokens]);
      return;
    }
    showLoading();
    httpRequest(
      api.getSearchTokenList(searchText.value, coinType.chain),
      (value) {
        searchList.clear();
        if (value.data != null) {
          showSuccess();
          List data = value.data;
          List<TokenSearchModel> dataSource = data
              .map((e) => TokenSearchModel.fromJson(e, coinType: coinType))
              .toList();
          dataSource = dataSource
              .where((element) => (!Get.isEmptyString(element.symbol) &&
                  !Get.isEmptyString(element.contractaddress) &&
                  !Get.isEmptyString(element.chain) &&
                  !Get.isEmptyString(element.enname)))
              .toList();
          updateSearchList(dataSource);
        } else {
          searchList.clear();
          showEmpty();
          update([GetKey.searchTokens]);
        }
      },
    );
  }

  Future<void> updateSearchList(List<TokenSearchModel> dataList) async {
    List<CoinModel> allCoinList = await Get.database.coinDao.getAllData();
    List<CoinModel> dataSource = [];
    for (var model in dataList) {
      List<CoinModel> reslutList = allCoinList
          .where((element) => (element.chain == model.chain &&
              element.contract == model.contractaddress))
          .toList();

      CoinModel? coinModel;
      int id = 0;
      if (reslutList.isNotEmpty) {
        coinModel = reslutList.first;
        id = coinModel.id;
      }

      dataSource.add(CoinModel(
          id: id,
          chain: coinType.chain,
          chainCode: coinType.id,
          chainId: coinType.chainId,
          chainName: model.enname,
          cnName: model.enname,
          symbol: model.symbol,
          contract: model.contractaddress,
          chainDecimal: model.decimals,
          balanceDecimal: coinType.balanceDecimals,
          tokenType: model.tokenType,
          isSupportToken: true,
          isCoinSupported: coinModel?.isCoinSupported,
          symbolIcon: model.imageurl,
          chainIcon: coinType.chainIcon,
          isToken: true));
    }
    searchList = dataSource;
    if (searchList.isEmpty) {
      showEmpty();
    }
    update([GetKey.searchTokens]);
  }

  Future<void> updateItemAction(CoinModel coinModel, int index) async {
    if (coinModel.isToken != true) return;

    coinModel = coinModel.copyWith(
        isCoinSupported: drift.Value(!(coinModel.isCoinSupported ?? false)));
    searchList.replaceRange(index, index + 1, [coinModel]);
    update([GetKey.searchTokens]);

    CoinModel? resultModel = await Get.database.coinDao
        .getCoinModel(chain: coinModel.chain, contract: coinModel.contract!);
    if (resultModel != null) {
      resultModel =
          resultModel.copyWith(symbolIcon: drift.Value(coinModel.symbolIcon));
      await Get.database.coinDao.updateCoinisSupported(
        coinModel: resultModel,
        isCoinSupported: coinModel.isCoinSupported!,
        tokenType: coinModel.tokenType,
      );
    } else {
      BalanceModel balanceModel = BalanceModel(
        symbol: coinModel.symbol,
        name: coinModel.chainName,
        contract: coinModel.contract,
        decimal: coinModel.chainDecimal,
        tokenType: coinModel.tokenType ?? 0,
        logoUrl: coinModel.symbolIcon,
      );
      await Get.database.coinDao.intertTokenCoinModel(
        balanceModel,
        coinType,
        isCoinSupported: coinModel.isCoinSupported ?? false,
      );
    }
    ctr.loadTokenLists();
  }
}

class TokenSearchBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TokenSearchController());
  }
}
