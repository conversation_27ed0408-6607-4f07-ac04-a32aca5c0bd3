/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2024-12-12 14:58:19
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:get/get.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/tron/trx.dart';

class TokenSearchModel {
  String? chain;
  String? symbol;
  String? contractaddress;
  String? enname;
  String? imageurl;
  int? decimals;
  int? tokenType;

  TokenSearchModel({
    this.chain,
    this.symbol,
    this.contractaddress,
    this.enname,
    this.imageurl,
    this.decimals,
    this.tokenType,
  });

  factory TokenSearchModel.fromJson(Map<String, dynamic> json,
      {required CoinType coinType}) {
    String? original = json['original'];
    String? chain;
    if (!Get.isEmptyString(original)) {
      final originalJson = jsonDecode(original!);
      chain = originalJson['chain'];
    } else {
      if (json['chain'] != null && json['chain'] is String) {
        chain = json['chain'];
      }
    }

    int? tokenType = json['tokenType'] as int?;
    if (tokenType != null && coinType is TronChain) {
      tokenType++;
    }

    return TokenSearchModel(
      symbol: json['symbol'] as String?,
      contractaddress: json['contractaddress'] as String?,
      enname: json['enname'] as String?,
      imageurl: json['imageurl'] as String?,
      decimals: json['decimals'] as int?,
      tokenType: tokenType,
      chain: chain,
    );
  }
}
