/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-18 13:25:34
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/wallet/wallet.dart';

class TokenSearchItemWidget extends BaseStatelessWidget {
  final CoinModel coinModel;
  final Wallet wallet;
  final CoinType coinType;
  final Function(CoinModel coinModel)? callback;
  const TokenSearchItemWidget(
      {super.key,
      required this.coinModel,
      required this.wallet,
      required this.coinType,
      this.callback});

  @override
  Widget build(BuildContext context) {
    return HighLightInkWell(
      onTap: () {
        if (callback != null) {
          callback!(coinModel);
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Get.setHeight(14),
          horizontal: Get.setPaddingSize(16),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SymbolWidget(
              coinModel: coinModel,
              size: Get.setPaddingSize(28),
            ),
            SizedBox(width: Get.setWidth(10)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    coinModel.symbol ?? '',
                    style: TextStyle(
                      fontSize: Get.setFontSize(16),
                      fontWeight: FontWeightX.semibold,
                      fontFamily: Get.setNumberFontFamily(),
                      color: Get.theme.textPrimary,
                    ),
                  ),
                  SizedBox(height: Get.setHeight(2)),
                  Text(
                    _name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: Get.setFontSize(12),
                      color: Get.theme.textSecondary,
                      fontFamily: Get.setNumberFontFamily(),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: Get.setPaddingSize(8),
            ),
            Column(
              children: [
                ImageWidget(
                  assetUrl: _actionImage(coinModel),
                  height: Get.setPaddingSize(18),
                  width: Get.setPaddingSize(18),
                ),
                Visibility(
                  visible: coinModel.isToken == true &&
                      wallet.isP1P2P3Wallet &&
                      coinType is EthereumChain,
                  child: GestureDetector(
                    onTap: () =>
                        Get.toNamed(AppRoutes.tokenSyncPage, arguments: {
                      GetArgumentsKey.coinModel: coinModel,
                    }),
                    behavior: HitTestBehavior.translucent,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        vertical: Get.setPaddingSize(8),
                      ),
                      child: ImageWidget(
                        assetUrl: 'wallet_more_icon',
                        width: Get.setImageSize(20),
                        height: Get.setImageSize(20),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String get _name {
    if (Get.isEmptyString(coinModel.contract)) {
      return coinModel.chainName ?? '';
    }
    return '${coinModel.chainName} - ${AddressUtils.omitAddress(coinModel.contract, len: 6)}';
  }

  String _actionImage(CoinModel coinModel) {
    if (coinModel.isToken == true) {
      if (coinModel.isCoinSupported == true) {
        return 'icon_delete';
      } else {
        return 'icon_add';
      }
    }
    return 'icon_delete_disabled';
  }
}
