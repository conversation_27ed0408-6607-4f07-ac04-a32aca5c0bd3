/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-06 13:32:37
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_search_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/solana/sol.dart';

class TokenSearchEmptyWidget
    extends BaseStatelessWidget<TokenSearchController> {
  const TokenSearchEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        top: Get.setPaddingSize(50),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: SizedBox(
        width: Get.width,
        child: Column(
          children: [
            SizedBox(
              width: Get.width,
              height: Get.setPaddingSize(50),
            ),
            ImageWidget(
              assetUrl: 'icon_Component',
              width: Get.setImageSize(88),
              height: Get.setImageSize(88),
            ),
            SizedBox(
              height: Get.setPaddingSize(12),
            ),
            Text(
              ID.stringSearchEmpty.tr,
              style: styleSecond_14,
            ),
            SizedBox(
              height: Get.setPaddingSize(20),
            ),
            OutlinedButtonWidget(
                width: Get.setWidth(240),
                text: ID.stringContactCustomerServiceToAdd.tr,
                onPressed: () =>
                    Get.offAndToNamed(AppRoutes.customerServicePage)),
            SizedBox(
              height: Get.setPaddingSize(20),
            ),
            Visibility(
                visible: controller.coinType is! SolanaChain,
                child: OutlinedButtonWidget(
                    width: Get.setWidth(240),
                    text: ID.stringSubmitToken.tr,
                    onPressed: () =>
                        Get.offAndToNamed(AppRoutes.addTokenPage, arguments: {
                          GetArgumentsKey.coinType: controller.coinType,
                          GetArgumentsKey.walletModel:
                              controller.ctr.walletModel,
                        }))),
          ],
        ),
      ),
    );
  }
}
