/*
 * @description: Do not edit
 * @Author: wangliming
 * @Date: 2024-02-23 13:47:13
 * @LastEditTime: 2024-12-05 17:30:26
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/home/<USER>/supprot_token_list_dialog.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_add_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class TokenAddPage extends BaseStatelessWidget<TokenAddController> {
  const TokenAddPage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
        appBar: baseAppBar(title: ID.submitToken.tr),
        bottomNavigationBar: _buttomWidget(),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              left: Get.setPaddingSize(16),
              right: Get.setPaddingSize(16),
              top: Get.setPaddingSize(8),
              bottom: Get.getSafetyBottomPadding(),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _typeWidget(),
                SizedBox(height: Get.setPaddingSize(24)),
                _symbolWidget(),
                SizedBox(height: Get.setPaddingSize(24)),
                _contractWidget(),
                SizedBox(height: Get.setPaddingSize(24)),
                _textWidget(ID.stringTips.tr, style: stylePrimary_14_m),
                SizedBox(height: Get.setPaddingSize(8)),
                _textWidget(ID.addContractRemind1.tr),
                SizedBox(height: Get.setPaddingSize(8)),
                _textWidget(ID.addContractRemind2.tr),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding _buttomWidget() {
    return Padding(
      padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding()),
      child: ButtonWidget(
        text: ID.texSubmit.tr,
        buttonSize: ButtonSize.full,
        onPressed: () => controller.buttonAction(),
      ),
    );
  }

  GestureDetector _typeWidget() => GestureDetector(
        onTap: () => SupportTokenListDialog(
          wallet: controller.wallet,
          coinType: controller.coinType.value,
          isAddToken: true,
          callback: (coinType) {
            Get.back();
            controller.coinType.value = coinType;
          },
        ).showBottomSheet(),
        child: Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _textWidget(ID.tokenLabel.tr),
                SizedBox(
                  height: Get.setPaddingSize(8),
                ),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
                  height: Get.setHeight(44),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border:
                        Border.all(color: Get.theme.colorD3D3D3, width: 0.5),
                  ),
                  child: Center(
                    child: Row(
                      children: [
                        ImageWidget(
                          assetUrl: controller.coinType.value.chainIcon,
                          width: Get.setImageSize(20),
                          height: Get.setImageSize(20),
                        ),
                        SizedBox(width: Get.setPaddingSize(8)),
                        Expanded(
                          child: Text(
                            controller.coinType.value.chainName,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(16),
                              fontWeight: FontWeightX.medium,
                              fontFamily: Get.setNumberFontFamily(),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: Get.setPaddingSize(8),
                        ),
                        ImageWidget(
                          assetUrl: 'arrow_down_black',
                          width: Get.setImageSize(10),
                          height: Get.setImageSize(10),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )),
      );

  Column _symbolWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _textWidget(ID.tokenSymbol.tr),
          SizedBox(
            height: Get.setPaddingSize(8),
          ),
          SizedBox(
            child: TextFieldWidget(
              hintText: ID.importTokenSymbol.tr,
              onValueChanged: (value) => controller.symbol = value,
            ),
          ),
        ],
      );

  Column _contractWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _textWidget(ID.tokenContracts.tr),
          SizedBox(
            height: Get.setPaddingSize(8),
          ),
          SizedBox(
            child: TextFieldWidget(
              hintText: ID.enterContractsInfo.tr,
              onValueChanged: (value) => controller.contract = value,
            ),
          ),
        ],
      );

  Text _textWidget(String text, {TextStyle? style}) => Text(
        text,
        style: style ?? styleSecond_14,
      );
}
