/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-18 14:27:54
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_sync_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';

class TokenSyncPage extends BaseStatelessWidget<TokenSyncController> {
  const TokenSyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringTokenAddTitle.tr),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          children: [
            _contractWidget(),
            SizedBox(height: Get.setPaddingSize(36)),
            _addTitleWidget(),
            SizedBox(height: Get.setPaddingSize(16)),
            _qrWidget(),
            SizedBox(height: Get.setPaddingSize(48)),
            _tipsWidget(),
          ],
        ),
      ),
    );
  }

  SizedBox _tipsWidget() => SizedBox(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _tipWidget(
              ID.stringTokenPro1Tip1.tr,
              ID.stringTokenPro1Tip2.tr,
            ),
            SizedBox(height: Get.setPaddingSize(16)),
            _tipWidget(
              ID.stringTokenPro2Tip1.tr,
              ID.stringTokenPro2Tip2.tr,
            ),
            SizedBox(height: Get.setPaddingSize(16)),
            _tipWidget(
              ID.stringTokenPro3Tip1.tr,
              ID.stringTokenPro3Tip2.tr,
            ),
          ],
        ),
      );

  Column _tipWidget(String title, String tipString) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(16),
              fontWeight: FontWeightX.medium,
            ),
          ),
          SizedBox(height: Get.setPaddingSize(8)),
          Text(
            tipString,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
            ),
          ),
        ],
      );

  QRWidget _qrWidget() => QRWidget(data: controller.qrData);

  SizedBox _addTitleWidget() {
    return SizedBox(
      width: Get.width,
      child: Text(
        ID.stringTokenAddTip1.tr,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Get.theme.textSecondary,
          fontSize: Get.setFontSize(14),
        ),
      ),
    );
  }

  Column _contractWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            ID.stringContractAddress.tr,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
            ),
          ),
          SizedBox(height: Get.setPaddingSize(4)),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(12),
              vertical: Get.setPaddingSize(12),
            ),
            decoration: BoxDecoration(
                color: Get.theme.colorF9F9F9,
                borderRadius:
                    BorderRadius.all(Radius.circular(Get.setRadius(12)))),
            child: Text(
              controller.coinModel.contract ?? '',
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(16),
                fontFamily: Get.setNumberFontFamily(),
              ),
            ),
          ),
        ],
      );
}
