import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_search_controller.dart';
import 'package:coinbag/modules/wallet/tokens/widgets/token_search_empty_widget.dart';
import 'package:coinbag/modules/wallet/tokens/widgets/token_search_item_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class TokenSearchPage extends BaseStatelessWidget<TokenSearchController> {
  const TokenSearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
          appBar: baseAppBar(
            hideLeading: true,
            actionWidget: [_searchBarWidget(), _cancelWidget()],
          ),
          body: GetBuilder<TokenSearchController>(
            id: GetKey.searchTokens,
            builder: (controller) => controller.obx((state) => _builderWidget(),
                onLoading: const HomeTokeListSkeletonWidget(),
                onEmpty: const TokenSearchEmptyWidget(),
                onError: (error) =>
                    AppErrorWidget(onRefresh: () => controller.loadData())),
          )),
    );
  }

  ListView _builderWidget() => ListView.builder(
      itemCount: controller.searchList.length,
      itemBuilder: (_, index) => TokenSearchItemWidget(
            coinModel: controller.searchList[index],
            wallet: controller.ctr.wallet,
            coinType: controller.ctr.coinType,
            callback: (coinModel) =>
                controller.updateItemAction(coinModel, index),
          ));

  Expanded _searchBarWidget() => Expanded(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
          child: Obx(() => Container(
                height: Get.setHeight(40),
                decoration: BoxDecoration(
                  color: Get.theme.colorF3F3F5,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                      color: controller.isFocus.value
                          ? Get.theme.textPrimary
                          : Colors.transparent,
                      width: 1),
                ),
                child: Center(
                  child: Row(
                    children: [
                      SizedBox(
                        width: Get.setPaddingSize(12),
                      ),
                      ImageWidget(
                        assetUrl: 'search_bar_search',
                        width: Get.setImageSize(16),
                        height: Get.setImageSize(16),
                      ),
                      SizedBox(
                        width: Get.setPaddingSize(6),
                      ),
                      Expanded(
                        child: Focus(
                          onFocusChange: (value) =>
                              controller.isFocus.value = value,
                          child: TextFieldWidget(
                            controller: controller.textEditingController,
                            hintText: ID.tokenSearch.tr,
                            fillColor: Colors.transparent,
                            textStyle: TextStyle(
                              fontSize: Get.setPaddingSize(14),
                              fontWeight: FontWeightX.medium,
                              fontFamily: Get.setFontFamily(),
                              color: Get.theme.textPrimary,
                            ),
                            hintStyle: TextStyle(
                              fontSize: Get.setPaddingSize(14),
                              fontWeight: FontWeightX.regular,
                              fontFamily: Get.setFontFamily(),
                              color: Get.theme.textTertiary,
                            ),
                            border: _textFieldBorder(),
                            focusedBorder: _textFieldBorder(),
                            enabledBorder: _textFieldBorder(),
                            textInputAction: TextInputAction.search,
                            contentPadding:
                                const EdgeInsets.symmetric(vertical: 0),
                            autofocus: true,
                            showClear: true,
                            suffixIconPadding:
                                const EdgeInsets.only(left: 12, right: 0),
                            onClear: () {
                              controller.textEditingController.clear();
                              controller.searchText.value = "";
                            },
                            onValueChanged: (value) =>
                                controller.searchText.value = value,
                            onFieldSubmitted: (value) => controller.loadData(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        ),
      );

  GestureDetector _cancelWidget() => GestureDetector(
        onTap: () => Get.back(),
        behavior: HitTestBehavior.translucent,
        child: Padding(
          padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
          child: Text(
            ID.stringCancel.tr,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              color: Get.theme.textPrimary,
            ),
          ),
        ),
      );

  OutlineInputBorder _textFieldBorder() => OutlineInputBorder(
        borderRadius: BorderRadius.circular(0),
        borderSide: const BorderSide(
          color: Colors.transparent,
          width: 0,
        ),
      );
}
