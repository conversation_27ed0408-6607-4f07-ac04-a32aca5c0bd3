/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-07-08 15:49:22
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

enum WalletManagerTouchAction { backup, password, reset }

class WalletManagerTouchWidget extends StatelessWidget {
  final Function(WalletManagerTouchAction action)? onTap;
  const WalletManagerTouchWidget({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DividerWidget(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
        ),
        _itemWidget(
            ID.stringBackupMnemonic.tr, WalletManagerTouchAction.backup),
        _itemWidget(
            ID.stringChangePswTitle.tr, WalletManagerTouchAction.password),
        _itemWidget(ID.stringResetCardTitle.tr, WalletManagerTouchAction.reset),
      ],
    );
  }

  HighLightInkWell _itemWidget(
    String title,
    WalletManagerTouchAction action,
  ) {
    return HighLightInkWell(
      onTap: () {
        if (onTap != null) {
          onTap!(action);
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(10),
          horizontal: Get.setPaddingSize(16),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: styleSecond_14,
              ),
            ),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(12),
              height: Get.setImageSize(12),
            ),
          ],
        ),
      ),
    );
  }
}
