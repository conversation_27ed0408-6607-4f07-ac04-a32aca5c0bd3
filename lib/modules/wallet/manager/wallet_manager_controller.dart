/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-11-15 16:26:30
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:get/get.dart';

class WalletManagerController extends BaseController<AppDatabase> {
  RxList<WalletModel> walletList = <WalletModel>[].obs;
  var canConncecClickable = true.obs; //可点击进入钱包列表

  @override
  void onReady() {
    super.onReady();
    loadData();

    Get.database.walletDao.watchAllData().listen((data) {
      loadData();
    });
  }

  @override
  void loadData() async {
    walletList.value = await api.walletDao.getSortAllData();
  }
}

class WalletManagerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WalletManagerController());
  }
}
