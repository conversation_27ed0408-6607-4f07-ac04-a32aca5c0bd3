/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-24 16:50:59
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/manager/controller/wallet_details_controller.dart';
import 'package:coinbag/modules/wallet/manager/widgets/wallet_manager_touch_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class WalletDetailsPage extends BaseStatelessWidget<WalletDetailsController> {
  const WalletDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
        title: ID.walletInfo.tr,
      ),
      bottomNavigationBar: _bottomBar(controller.walletModel),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(
              () => _itemWidget(
                  title: ID.walletName.tr,
                  value: controller.walletName.value,
                  isEdit: controller.wallet is TouchWallet,
                  onTap: controller.wallet is! TouchWallet
                      ? null
                      : () => controller.editWalletNameAction()),
            ),
            _itemWidget(
              title: ID.stringWalletIdTitle.tr,
              value: controller.walletModel.walletId ?? '',
              isHide: controller.hideItem(),
            ),
            _itemWidget(
              isPassphraseWallet: true,
              title: ID.p4seedIspharase.tr,
              value: controller.walletModel.isPassphraseWallet!
                  ? ID.p4seedIspharaseYes.tr
                  : ID.p4seedIspharaseNo.tr,
            ),
            _itemWidget(
              title: ID.bindingTime.tr,
              value: controller.walletModel.monitorTime!,
            ),
            DividerWidget(
              padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
            ),
            _itemWidget(
              title: ID.deviceModel.tr,
              value: controller.walletModel.deviceType ?? '',
            ),
            _itemWidget(
              title: ID.deviceId.tr,
              value: controller.walletModel.deviceId ?? '',
              isHide: controller.hideItem(),
            ),
            _itemWidget(
              title: ID.seVersion.tr,
              value: controller.walletModel.chipVersion ?? '',
            ),
            _itemWidget(
              title: ID.walletAppVersion.tr,
              value: controller.walletModel.appVersion ?? '',
            ),
            Visibility(
              visible: controller.wallet is TouchWallet,
              child: WalletManagerTouchWidget(
                onTap: (action) => controller.touchAction(action),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _itemWidget({
    String title = '',
    String value = '',
    bool isPassphraseWallet = false,
    Function()? onTap,
    bool isEdit = false,
    bool isHide = false,
  }) {
    if (isHide || Get.isEmptyString(value)) {
      return const SizedBox.shrink();
    }
    if (isPassphraseWallet) {
      Wallet wallet = Wallet.getWalletByBatch(controller.walletModel.batchId!);
      if (!wallet.isSupportedPassphrase) {
        return const SizedBox.shrink();
      }
    }
    return HighLightInkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(10),
          horizontal: Get.setPaddingSize(16),
        ),
        child: Row(
          children: [
            !isPassphraseWallet
                ? Text(
                    title,
                    style: styleSecond_14,
                  )
                : GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () => Get.showAlertDialog(
                        title: ID.p4seedIspharase.tr,
                        content: ID.p4seedRemind.tr,
                        onConfirmText: ID.stringConfirm.tr),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: styleSecond_14,
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        ImageWidget(
                          assetUrl: 'icon_question',
                          width: Get.setImageSize(12),
                          height: Get.setImageSize(12),
                        )
                      ],
                    ),
                  ),
            Expanded(
              child: Text(
                value,
                textAlign: TextAlign.end,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: Get.setFontSize(14),
                    color: Get.theme.textPrimary,
                    fontWeight: FontWeightX.semibold,
                    fontFamily: Get.setNumberFontFamily()),
              ),
            ),
            Visibility(
              visible: isEdit,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(8)),
                child: ImageWidget(
                  assetUrl: 'wallet_name_edit',
                  width: Get.setImageSize(18),
                  height: Get.setImageSize(18),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  GestureDetector _bottomBar(WalletModel walletModel) {
    return GestureDetector(
      onTap: () => Get.showAlertDialog(
          title: ID.stringTips.tr,
          content: ID.sureDeleteWallet.tr,
          onConfirmText: ID.stringConfirm.tr,
          onConfirm: () => controller.unBindAction()),
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.only(
            top: Get.setPaddingSize(16),
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            bottom: Get.getSafetyBottomPadding()),
        child: Container(
          height: Get.setHeight(44),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(22.0),
              border: Border.all(color: Get.theme.colorD3D3D3, width: 0.5)),
          child: Center(
            child: Text(
              ID.unbind.tr,
              textAlign: TextAlign.center,
              style: stylePrimary_16_m,
            ),
          ),
        ),
      ),
    );
  }
}
