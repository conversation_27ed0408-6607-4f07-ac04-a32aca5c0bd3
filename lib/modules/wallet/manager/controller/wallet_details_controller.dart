/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 16:46:19
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/manager/widgets/wallet_manager_touch_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_change_name_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:wallet_core/wallet/cold/pro1_pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/pro1_wallet.dart';
import 'package:wallet_core/wallet/cold/pro2_wallet.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';
import 'package:wallet_core/wallet/wallet.dart';

class WalletDetailsController extends BaseController {
  RxList<WalletModel> walletList = <WalletModel>[].obs;

  late WalletModel walletModel;
  RxString walletName = ''.obs;
  late Wallet wallet;

  @override
  void onInit() {
    super.onInit();

    walletModel = Get.arguments;
    walletName.value = walletModel.walletName ?? '';
    wallet = Wallet.getWalletByBatch(walletModel.batchId!);

    Get.database.walletDao.watchAllData().listen((data) {
      for (var e in data) {
        if (e.walletId == walletModel.walletId) {
          walletName.value = e.walletName ?? '';
          walletModel = e;
        }
      }
    });
  }

  @override
  void loadData() async {}

  void editWalletNameAction() {
    TouchChangeWalletNameWidget.show(walletModel.walletName ?? '', (value) {
      Get.toNamed(AppRoutes.touchReadCardPage, arguments: {
        GetArgumentsKey.targetCommand: TouchCommand.changeName,
        GetArgumentsKey.walletModel: walletModel,
        GetArgumentsKey.walletName: value,
      });
    });
  }

  Future<void> touchAction(WalletManagerTouchAction action) async {
    if (action == WalletManagerTouchAction.password) {
      Get.toNamed(AppRoutes.touchChangePasswordPage, arguments: {
        GetArgumentsKey.targetCommand: TouchCommand.changePsw,
        GetArgumentsKey.walletModel: walletModel,
      });
      return;
    }

    TouchCommand targetCommand = TouchCommand.backup;
    if (action == WalletManagerTouchAction.backup) {
      targetCommand = TouchCommand.backup;
    } else if (action == WalletManagerTouchAction.reset) {
      targetCommand = TouchCommand.reset;
    }

    final result = await Get.toNamed(AppRoutes.touchReadCardPage, arguments: {
      GetArgumentsKey.targetCommand: targetCommand,
      GetArgumentsKey.walletModel: walletModel,
    });

    if (result != null &&
        (result as Map).keys.contains(GetArgumentsKey.resetStatus)) {
      bool status = result[GetArgumentsKey.resetStatus];
      if (status) {
        unBindAction();
      }
    }
  }

  Future<void> unBindAction() async {
    await Get.find<AppDatabase>().walletDao.deleteWalletAndUpdateChecked(
        walletModel.walletId!, walletModel.deviceId!);

    Get.back();

    Get.until((route) => route.settings.name == AppRoutes.mainPage);
    Get.appController.unBindWallet(
        walletId: walletModel.walletId!, deviceId: walletModel.deviceId!);
  }

  bool hideItem() {
    return wallet is Pro1Pro2Wallet ||
        wallet is Pro1Wallet ||
        wallet is Pro2Wallet;
  }
}

class WalletDetailsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WalletDetailsController());
  }
}
