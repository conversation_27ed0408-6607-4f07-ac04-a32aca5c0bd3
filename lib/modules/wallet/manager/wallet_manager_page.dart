import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/manager/wallet_manager_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/wallet.dart';

class WalletManagerPage extends BaseStatelessWidget<WalletManagerController> {
  const WalletManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.allWallet.tr, actionWidget: [
        IconButton(
            onPressed: () async {
              // 导航到详情页面
              await Get.toNamed(AppRoutes.connectWalletPage);
            },
            icon: Padding(
              padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
              child: ImageWidget(
                assetUrl: 'add_scan',
                width: Get.setImageSize(28),
                height: Get.setImageSize(28),
                fit: BoxFit.contain,
              ),
            ))
      ]),
      body: Obx(() => ListView.builder(
          itemCount: controller.walletList.length,
          itemBuilder: (_, index) =>
              _builder(controller.walletList[index], index))),
    );
  }

  Container _builder(WalletModel walletModel, int index) {
    return Container(
      key: Key('wallet_item_$index'),
      margin: EdgeInsets.symmetric(
        vertical: Get.setPaddingSize(5),
        horizontal: Get.setPaddingSize(16),
      ),
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          side: BorderSide(
              width: Get.setWidth(0.5), color: Get.theme.colorD3D3D3),
          borderRadius: BorderRadius.circular(Get.setRadius(12)),
        ),
      ),
      child: HighLightInkWell(
        borderRadius: BorderRadius.circular(Get.setRadius(12)),
        onTap: () async {
          if (!walletModel.checked!) {
            await Get.database.walletDao
                .selectWallet(walletModel.walletId!, walletModel.deviceId!);

            AppController.refreshHome(
                isScrollToTop: true, isConnectWallet: true);
          }

          Get.back();

          // 等待动画结束（假设动画时间为500ms）
          await Future.delayed(const Duration(milliseconds: 500));
        },
        child: Stack(
          children: [
            DecoratedBox(
              decoration: _boxDecoration(walletModel),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: Get.setPaddingSize(16)),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: Get.setWidth(16),
                          ),
                          _walletName(walletModel),
                        ],
                      ),
                    ),
                  ),
                  _moreInfo(walletModel)
                ],
              ),
            ),
            _useStatus(walletModel)
          ],
        ),
      ),
    );
  }

  GestureDetector _moreInfo(WalletModel walletModel) {
    return GestureDetector(
      onTap: () =>
          Get.toNamed(AppRoutes.walletDetailsPage, arguments: walletModel),
      behavior: HitTestBehavior.translucent,
      child: Container(
        padding: EdgeInsets.only(
            top: Get.setPaddingSize(10),
            left: Get.setPaddingSize(20),
            right: Get.setPaddingSize(10),
            bottom: Get.setPaddingSize(10)),
        child: ImageWidget(
          assetUrl: 'wallet_more_icon',
          width: Get.setImageSize(20),
          height: Get.setImageSize(20),
        ),
      ),
    );
  }

  Visibility _useStatus(WalletModel walletModel) {
    return Visibility(
      visible: walletModel.checked!,
      child: Positioned(
          top: 0,
          right: 0,
          child: Container(
            decoration: BoxDecoration(
              color: Get.theme.textPrimary,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(Get.setRadius(12)),
                  topRight: Radius.circular(Get.setRadius(12))),
            ),
            child: Padding(
              padding: EdgeInsets.only(
                  left: Get.setPaddingSize(8),
                  right: Get.setPaddingSize(8),
                  bottom: Get.setPaddingSize(2)),
              child: Text(
                ID.using.tr,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    color: Get.theme.bgColor),
              ),
            ),
          )),
    );
  }

  Expanded _walletName(WalletModel walletModel) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            walletModel.walletName!,
            style: TextStyle(
                overflow: TextOverflow.ellipsis,
                fontSize: Get.setFontSize(16),
                fontFamily: Get.setNumberFontFamily(),
                fontWeight: FontWeightX.semibold,
                color: Get.theme.textPrimary),
          ),
          SizedBox(
            height: Get.setPaddingSize(4),
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              ImageWidget(
                assetUrl: 'wallet_type_icon',
                width: Get.setImageSize(51),
                height: Get.setImageSize(18),
              ),
              Text(
                Wallet.getWalletByBatch(walletModel.batchId!).name,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    color: Get.theme.textPrimary),
              )
            ],
          )
        ],
      ),
    );
  }

  BoxDecoration _boxDecoration(WalletModel walletModel) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(Get.setRadius(12)),
      border: Border.all(
        color: walletModel.checked!
            ? Get.theme.textPrimary
            : Get.theme.colorD3D3D3,
        width: 0.5,
      ),
    );
  }
}
