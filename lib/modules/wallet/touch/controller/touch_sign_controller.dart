/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2025-03-24 14:27:45
 */
import 'dart:typed_data';

import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_sign_finish_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_sign_wait_controller.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:crypto/crypto.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/extension/uint8_hex.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchSignController extends TouchReadCardController {
  late TransferModel tsModel;
  late CoinType coinType;
  List rawWaitData = [];

  /// 每笔签名最大个数
  int maxSignCount = 10;
  int maxPackageLength = 255; // 每页最大长度

  /// 每笔交易分页的所有数据
  List<int> packageData = [];

  /// 当前第几笔交易
  int currentTxCount = 1;

  List<List<List<int>>> waitSignData = [];

  /// 签名结果
  List<Uint8List> signRsultList = <Uint8List>[];

  int p1 = 0x00;
  int p2 = 0x00;
  List<int> lcData = <int>[];

  @override
  void onInit() async {
    targetCommand = TouchCommand.signature;
    title = ID.qrTitleTransfer.tr;
    tsModel = Get.arguments[GetArgumentsKey.transferModel];
    coinType = CoinBase.getCoinTypeByChain(tsModel.chain)!;
    if (coinType.isEVM || coinType.isLayer2) {
      // EVM 系列使用 ETH path
      tsModel.addressPath = "m/44'/60'/0'/0/0";
    }
    super.onInit();

    rawWaitData = await getRawWaitSignData();
  }

  @override
  startSession({
    required TouchCommand command,
    required TouchCommand nextCommand,
    String? alertMessage,
  }) async {
    p1 = 0x00;
    p2 = 0x00;
    lcData = <int>[];
    currentTxCount = 1;
    packageData = <int>[];
    signRsultList = <Uint8List>[];
    super.startSession(command: command, nextCommand: nextCommand);
  }

  @override
  int p1Parameter(TouchCommand command) => p1;
  @override
  int p2Parameter(TouchCommand command) => p2;
  @override
  List<int> signatureLc(TouchCommand command) => lcData;

  @override
  signatureCommand() async {
    nfcKitMessage(ID.stringSignLoading.tr);
    getWaitSignData();

    lcData = <int>[];
    int page = 1;
    p1 = 0x00;
    p2 = 0x00;

    if (currentTxCount > waitSignData.length) {
      nfcKitFinish();
      return;
    }

    List<List<int>> dataList = waitSignData[currentTxCount - 1];

    for (List<int> element in dataList) {
      TouchCommand command = TouchCommand.signature;
      TouchCommand nextCommand = TouchCommand.finish;
      if (dataList.length == 1) {
        nextCommand = TouchCommand.finish;
      } else {
        if (page == element.length) {
          nextCommand = TouchCommand.packageFinish;
        } else if (page < element.length) {
          nextCommand = TouchCommand.signature;
        }
      }

      lcData = element;
      p1 = page - 1;
      p2 = page < dataList.length ? 0x01 : 0x00;

      /// 1. 发送签名
      Uint8List? data =
          await sendCommand(command: command, nextCommand: nextCommand);
      if (data == null) return;

      page++;

      if (data.isNotEmpty) {
        packageData.addAll(data.toList());
      }

      /// 2. sw1 = 61 代表分包结束 发送0xC0 命令
      if (sw1 == CommonConstant.nfcSw1_61) {
        /// 2.1 发送
        bool isSuccess = await packageFinishCommand();

        /// 2.2 0xC0 发送是否成功
        if (isSuccess == true) {
          /// 2.3 保存当前这笔交易签名数据
          signRsultList.add(Uint8List.fromList(packageData));

          /// 2.4 判断是否还有下一笔
          if (currentTxCount < waitSignData.length) {
            if (_packageVerify()) {
              packageData = <int>[];
              currentTxCount++;
              signatureCommand();
            } else {
              await nfcKitFinish(errorMessage: ID.stringNfcScanError.tr);
            }
          } else {
            /// 2.5 签名结束
            packageData = <int>[];
            signatureFinish();
          }
        }
        return;
      }

      /// 3. 是否还有下一页
      if (page > dataList.length) {
        if (packageData.isNotEmpty) {
          /// 3.1 保存当前这笔交易签名数据
          signRsultList.add(Uint8List.fromList(packageData));
          packageData = <int>[];
        }
        signatureFinish();
        return;
      }
    }
  }

  /// 分包结束
  Future<bool> packageFinishCommand() async {
    p1 = 0x00;
    p2 = 0x00;
    TouchCommand command = TouchCommand.packageFinish;
    TouchCommand nextCommand = TouchCommand.finish;
    Uint8List? data = await sendCommand(
      command: command,
      nextCommand: nextCommand,
    );

    if (data != null && data.isNotEmpty) {
      packageData.addAll(data.toList());
    }

    if (sw1 == CommonConstant.nfcSw1_61) {
      await packageFinishCommand();
    }

    /// 是否成功
    return data != null ? true : false;
  }

  bool _packageVerify() {
    int length = packageData.length;
    if (length > 4) {
      List<int> data = packageData.getRange(0, length - 4).toList();
      List<int> sha256Data = sha256.convert(data).bytes;
      List<int> hashData = sha256Data.getRange(0, 4).toList();
      List<int> verifyData = packageData.getRange(length - 4, length).toList();
      String hashHex = Uint8List.fromList(hashData).toHexRawString;
      String verifyHex = Uint8List.fromList(verifyData).toHexRawString;
      return hashHex == verifyHex;
    }
    return false;
  }

  @override
  void onClose() {
    super.onClose();
    SendController.updateTimer();
  }
}

class TouchSignBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchSignController());
  }
}
