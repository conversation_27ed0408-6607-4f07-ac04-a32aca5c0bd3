/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-01-13 09:08:48
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchChangePasswordController extends BaseController {
  String navTitle = ID.stringChangePassword.tr;
  String oldPassword = '';
  String newPassword = '';
  String newPasswordTwo = '';

  @override
  onInit() async {
    super.onInit();
    if (GetPlatform.isIOS) {
      await Get.walletCore.isSecureKeyboard(isSecureKeyboard: true);
    }
  }

  @override
  onClose() async {
    super.onClose();

    if (GetPlatform.isIOS) {
      await Get.walletCore.isSecureKeyboard(isSecureKeyboard: false);
    }
  }

  bool _verifyPassword(String value) {
    bool result = RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value);
    if (result == false || value.length < 6) {
      Get.showToast(ID.stringTouchImInputCorrectPsw.tr,
          toastMode: ToastMode.failed);
      return false;
    }
    return true;
  }

  void buttonAction() {
    if (Get.isEmptyString(oldPassword)) {
      Get.showToast(ID.stringInputOldPswTip.tr, toastMode: ToastMode.waring);
      return;
    }

    if (!_verifyPassword(oldPassword)) return;

    if (Get.isEmptyString(newPassword)) {
      Get.showToast(ID.stringInputNewPswTip.tr, toastMode: ToastMode.waring);
      return;
    }
    if (!_verifyPassword(newPassword)) return;

    if (Get.isEmptyString(newPasswordTwo)) {
      Get.showToast(ID.stringInputNewPswTwoTip.tr, toastMode: ToastMode.waring);
      return;
    }

    if (newPassword != newPasswordTwo) {
      Get.showToast(ID.stringTouchTowPswFail.tr, toastMode: ToastMode.waring);
      return;
    }

    if (oldPassword == newPassword) {
      Get.showToast(ID.stringOldNewSameTip.tr, toastMode: ToastMode.waring);
      return;
    }

    Get.offNamedUntil(
      AppRoutes.touchReadCardPage,
      (route) => route.settings.name == AppRoutes.walletDetailsPage,
      arguments: {
        GetArgumentsKey.targetCommand: TouchCommand.changePsw,
        GetArgumentsKey.password: oldPassword,
        GetArgumentsKey.newPassword: newPassword,
        GetArgumentsKey.walletModel: Get.arguments[GetArgumentsKey.walletModel],
      },
    );
  }

  @override
  void loadData() {}
}

class TouchChangePasswordBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchChangePasswordController());
  }
}
