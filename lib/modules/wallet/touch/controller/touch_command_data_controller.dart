import 'dart:typed_data';

import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:enough_convert/enough_convert.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/uint8_hex.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';
import 'package:wallet_core/wallet/cold/touch/touch_inscode.dart';

extension TouchCommandDataController on TouchReadCardController {
  Future<Uint8List> commandData(TouchCommand command,
      {TouchXpubModel? xpubModel}) async {
    List<int> data = [];

    int ins = _insCode(command, xpubModel?.coinType);
    int p1 = p1Parameter(command);
    int p2 = p2Parameter(command);

    Log.logPrint('------------------------------');
    Log.logPrint('ins: $ins');
    Log.logPrint('p1: $p1');
    Log.logPrint('p2: $p2');

    data.addAll([0]);
    data.addAll([ins]);
    data.addAll([p1]);
    data.addAll([p2]);

    List<int> lcData = _lcData(command, xpubModel: xpubModel);
    if (lcData.isNotEmpty) {
      data.addAll([lcData.length]);
      data.addAll(lcData);
      Log.logPrint('lc_data_hex: ${Uint8List.fromList(lcData).toHexString}');
    }

    if (command != TouchCommand.packageFinish) {
      data.addAll([-1]);
    }

    Log.logPrint('------------------------------');

    Uint8List result = Uint8List.fromList(data);

    return result;
  }

  int _insCode(TouchCommand command, CoinType? coinType) =>
      TouchInsCode.insCode(command, coinType);

  List<int> _lcData(TouchCommand command, {TouchXpubModel? xpubModel}) {
    List<int> data = [];
    switch (command) {
      case TouchCommand.readCard:
        data = [];
        break;
      case TouchCommand.password:
        data = _passwordLc();
        break;
      case TouchCommand.xpub:
        data = _xpubDataLc(xpubModel!);
        break;
      case TouchCommand.randomEntropy:
        data = [0];
        break;
      case TouchCommand.create:
      case TouchCommand.import:
        data = _createLc();
        break;
      case TouchCommand.cardEntropy:
        data = _cardEntropyLc();
        break;
      case TouchCommand.entropyStatus:
        data = _entropyStatusLc();
        break;
      case TouchCommand.changeName:
        data = _changeNameLc();
        break;
      case TouchCommand.changePsw:
        data = _changePswLc();
        break;
      case TouchCommand.reset:
        data = _resetPswLc();
        break;
      case TouchCommand.signature:
        data = signatureLc(command);
        break;
      case TouchCommand.packageFinish:
        data = [];
        break;
      default:
    }

    return data;
  }

  List<int> _resetPswLc() {
    return baseInfo();
  }

  List<int> _changePswLc() {
    List<int> dataSource = [];
    // chipID
    dataSource.addAll(_containLengthCode(cardModel!.chipId!));
    // walletId
    String walletId = cardModel!.walletID!;
    List<int> walletIdList = walletId.codeUnits;
    dataSource.addAll([walletIdList.length]);
    dataSource.addAll(walletIdList);

    // old password
    List<int> oldPswList = password.codeUnits;
    dataSource.addAll([oldPswList.length]);
    dataSource.addAll(oldPswList);

    // new password
    List<int> newPswList = newPassword.codeUnits;
    dataSource.addAll([newPswList.length]);
    dataSource.addAll(newPswList);

    // mnemonic
    dataSource.addAll(_containLengthCode('mnemonic'));

    return dataSource;
  }

  List<int> _changeNameLc() {
    List<int> dataSource = baseInfo();

    final walletNameData =
        const GbkCodec(allowInvalid: false).encode(walletName!);
    dataSource.addAll([walletNameData.length]);
    dataSource.addAll(walletNameData.toList());

    return dataSource;
  }

  List<int> _entropyStatusLc() {
    return baseInfo();
  }

  List<int> _cardEntropyLc() {
    return baseInfo();
  }

  List<int> _createLc() {
    List<int> dataSource = [];
    // chipID
    dataSource.addAll(_containLengthCode(targetModel!.chipId!));
    // seedType
    dataSource.addAll([0]);
    // entropy
    dataSource.addAll(targetModel!.entropy!.toList());
    // password
    dataSource.addAll(_containLengthCode(targetModel!.cardPassword!));
    // sPass最大错误次数
    dataSource.addAll([10]);
    // mnemonic
    dataSource.addAll(_containLengthCode('mnemonic'));
    // mPass最大错误次数
    dataSource.addAll([10]);
    // isCreate
    dataSource.addAll([0]);
    // isShowEntropy
    dataSource.addAll([0]);
    // isVerityEntropy
    dataSource.addAll([0]);

    final walletNameData =
        const GbkCodec(allowInvalid: false).encode(targetModel!.newWalletName!);

    if (walletNameData.isNotEmpty) {
      dataSource.addAll([walletNameData.length]);
      dataSource.addAll(walletNameData.toList());
    }

    return dataSource;
  }

  // 验证密码
  List<int> _passwordLc() {
    List<int> data = [];
    String chipId = cardModel!.chipId!;
    List chipList = chipId.codeUnits;

    data.addAll([chipList.length]);
    data.addAll(chipId.codeUnits);

    String walletId = cardModel!.walletID!;
    List walletIdList = walletId.codeUnits;

    data.addAll([walletIdList.length]);
    data.addAll(walletId.codeUnits);

    List sPassList = password.codeUnits;

    data.addAll([sPassList.length]);
    data.addAll(password.codeUnits);

    return data;
  }

  List<int> _xpubDataLc(TouchXpubModel xpubModel) {
    List<int> data = baseInfo();

    data.addAll(xpubModel.pathIntList());
    if (xpubModel.coinType is SolanaChain) {
      data.addAll([4]);
      data.addAll([0]);
    } else {
      data.addAll([0]);
    }

    return data;
  }

  List<int> baseInfo() {
    // chipIDLen, chipID, walletIDLen, walletID, sPassLen, sPass, mPassLen, mPass
    List<int> dataSource = [];
    String chipId = cardModel!.chipId!;
    List<int> chipList = chipId.codeUnits;
    dataSource.addAll([chipList.length]);
    dataSource.addAll(chipList);

    String walletId = cardModel!.walletID!;
    List<int> walletIdList = walletId.codeUnits;
    dataSource.addAll([walletIdList.length]);
    dataSource.addAll(walletIdList);

    List<int> sPassList = password.codeUnits;
    dataSource.addAll([sPassList.length]);
    dataSource.addAll(sPassList);

    String mPass = 'mnemonic';
    List<int> mPassList = mPass.codeUnits;
    dataSource.addAll([mPassList.length]);
    dataSource.addAll(mPassList);

    return dataSource;
  }

  List<int> _containLengthCode(String string) {
    List<int> resultList = [];
    List<int> valueList = string.codeUnits;
    resultList.addAll([valueList.length]);
    resultList.addAll(valueList);
    return resultList;
  }
}
