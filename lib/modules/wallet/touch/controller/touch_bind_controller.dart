/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-24 14:18:53
 */

import 'dart:typed_data';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_android_window_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/extension/uint8_hex.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

class TouchBindController extends TouchReadCardController {
  @override
  void onInit() {
    targetCommand = TouchCommand.bind;
    super.onInit();
  }

  @override
  xpubDataCommand(
    TouchCommand command,
    TouchCommand nextCommand,
  ) async {
    TouchCommand targetNextCommand = TouchCommand.xpub;

    if (xpubIndex == TouchWallet.xpubDatas.length - 1) {
      targetNextCommand = TouchCommand.finish;
    }

    if (GetPlatform.isIOS) {
      await FlutterNfcKit.setIosAlertMessage(ID.stringTouchReading.tr);
    } else {
      AndroidNfcKit.setAlertMessage(ID.stringTouchReading.tr);
    }

    final xpubDataJson = TouchWallet.xpubDatas[xpubIndex];
    TouchXpubModel xpubModel = TouchXpubModel.fromJson(xpubDataJson);
    sendCommand(
      command: TouchCommand.xpub,
      nextCommand: targetNextCommand,
      xpubModel: xpubModel,
    );
    xpubIndex++;
  }

  @override
  xpubCommandResult(TouchXpubModel? xpubModel, Uint8List resultList) {
    String xpub = String.fromCharCodes(resultList);
    if (xpubModel?.coinType is SolanaChain) {
      xpub = resultList.toHexString;
      xpub = xpub.replaceFirst(RegExp(r'^0x01'), '');
    } else {
      xpub = String.fromCharCodes(resultList);
    }

    if (!Get.isEmptyString(xpub)) {
      xpubModel?.pubData = xpub;
      if (xpubModel != null) {
        xpubModels.add(xpubModel);
      }
    }
  }
}

class TouchBindBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchBindController());
  }
}
