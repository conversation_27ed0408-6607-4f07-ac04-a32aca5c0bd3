/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-08 11:22:23
 */
import 'package:coinbag/modules/wallet/touch/controller/touch_command_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

extension TouchCommandCreateController on TouchReadCardController {
  Future<void> createWalletCommand() async {
    /// 验证是否是同一张卡
    if (cardModel?.chipId != targetModel?.chipId) {
      String chipId = targetModel!.chipId ?? '';
      if (chipId.length < 4) {
        // Get.showToast('钱包不符');
        await nfcKitFinish(errorMessage: ID.stringCardWalletError.tr);
        return;
      }

      String number = chipId.substring(chipId.length - 4);
      // Get.showToast('请使用尾号为$number的卡片');
      await nfcKitFinish(
          errorMessage: ID.stringTouchNumber.trParams({'number': number}));
      return;
    }

    /// 判断,是否已经创建了钱包
    if (cardModel?.status == TouchStatus.wallet) {
      await nfcKitFinish(errorMessage: ID.stringCardHaveWallet.tr);
      return;
    }

    String iosAlertMessage = '';
    if (targetCommand == TouchCommand.create) {
      iosAlertMessage = ID.stringCreateLoading.tr;
    } else if (targetCommand == TouchCommand.import) {
      iosAlertMessage = ID.stringImportLoading.tr;
    }
    await nfcKitMessage(iosAlertMessage);

    // 创建钱包
    sendCommand(command: targetCommand, nextCommand: TouchCommand.finish);
  }
}
