/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-07-05 17:05:58
 */

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_mnemonics_pop_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:get/get.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

enum MnemonicsType { english, number, chinese }

class TouchMnemonicsController extends BaseController {
  @override
  void loadData() {}

  late TouchReadCardController readCardCtr;
  late TouchCommand targetCommand;
  List<String> mnemonicsList = [];
  List<String> phraseList = [];

  MnemonicsType type = MnemonicsType.english;

  @override
  void onInit() {
    super.onInit();

    readCardCtr = Get.arguments[GetArgumentsKey.controller];
    targetCommand = Get.arguments[GetArgumentsKey.targetCommand];
  }

  @override
  void onReady() {
    loadDataSource(type);
    super.onReady();

    TouchMnemonicsPopWidget.show();
  }

  Future<void> loadDataSource(MnemonicsType type) async {
    this.type = type;
    mnemonicsList = await Get.walletCore.getMnemonics(
          entropy: readCardCtr.cardModel!.entropy!,
          seedType: readCardCtr.cardModel!.seedType ?? 0,
          type: type.index,
        ) ??
        [];
    phraseList = await Get.walletCore.getPhrases(type: type.index) ?? [];

    update([GetKey.backupMnemonicsId]);
  }
}

class TouchBackupBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchMnemonicsController());
  }
}
