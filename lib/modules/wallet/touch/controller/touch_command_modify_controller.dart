/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-08 13:19:46
 */
import 'dart:typed_data';

import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

extension TouchCommandModifyController on TouchReadCardController {
  void resetCommand() {
    bool result = modifyVerify();
    if (result == false) return;

    sendCommand(
      command: TouchCommand.reset,
      nextCommand: TouchCommand.finish,
    );
  }

  void changePasswordCommand(TouchCommand command) {
    bool result = modifyVerify();
    if (result == false) return;

    sendCommand(
      command: TouchCommand.changePsw,
      nextCommand: TouchCommand.finish,
    );
  }

  void changeNameCommand() {
    bool result = modifyVerify();
    if (result == false) return;

    sendCommand(
      command: TouchCommand.changeName,
      nextCommand: TouchCommand.finish,
    );
  }

  void entropyStatusCommand() {
    bool result = modifyVerify();
    if (result == false) return;

    sendCommand(
      command: TouchCommand.entropyStatus,
      nextCommand: TouchCommand.finish,
    );
  }

  Future<void> cardEntropyCommand() async {
    bool result = modifyVerify();
    if (result == false) return;

    sendCommand(
      command: TouchCommand.cardEntropy,
      nextCommand: TouchCommand.cardEntropyNext,
    );
  }

  Future<void> cardEntropyNextCommand(Uint8List resultList) async {
    await nfcKitFinish(successMessage: ID.stringTouchNfcSuccess.tr);
    cardModel?.entropy = resultList;
    Get.toNamed(AppRoutes.touchMnemonicsPage, arguments: {
      GetArgumentsKey.controller: this,
      GetArgumentsKey.targetCommand: TouchCommand.backupEnd,
    });
  }

  bool modifyVerify() {
    if (cardModel!.status != TouchStatus.wallet) {
      nfcKitFinish(errorMessage: ID.stringCardEmpty.tr);
      return false;
    }

    String chipId = cardModel!.chipId!;
    if (chipId != walletModel?.deviceId) {
      String number = walletModel!.deviceId!.substring(chipId.length - 4);
      nfcKitFinish(
          errorMessage: ID.stringTouchNumber.trParams({'number': number}));
      return false;
    }

    if (walletModel?.walletId != cardModel!.walletID) {
      nfcKitFinish(errorMessage: ID.stringCardWalletError.tr);
      return false;
    }

    return true;
  }
}
