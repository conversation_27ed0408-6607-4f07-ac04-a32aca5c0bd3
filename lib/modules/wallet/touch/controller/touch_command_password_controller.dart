/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-08-20 16:53:22
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_create_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_modify_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

extension TouchCommandPasswordController on TouchReadCardController {
  Future<void> passwordCommand() async {
    if (targetCommand == TouchCommand.create ||
        targetCommand == TouchCommand.import) {
      createWalletCommand();
      return;
    }

    if (targetCommand == TouchCommand.bind) {
      if (cardModel?.status == TouchStatus.inactive) {
        /// 芯片未激活
        return;
      } else if (cardModel?.status == TouchStatus.noWallet) {
        /// 已激活 - 空钱包
        _createWallet();
        return;
      }
    } else {
      if (!modifyVerify()) return;
    }

    TouchCommand nextCommand = targetCommand;
    switch (targetCommand) {
      case TouchCommand.create:
      case TouchCommand.import:
      case TouchCommand.reset:
      case TouchCommand.changeName:
      case TouchCommand.changePsw:
      case TouchCommand.signature:
        nextCommand = targetCommand;
        break;
      case TouchCommand.bind:
        {
          nextCommand = TouchCommand.xpub;
          xpubIndex = 0;
          xpubModels = [];
          break;
        }
      case TouchCommand.backup:
        nextCommand = TouchCommand.cardEntropy;
        break;
      case TouchCommand.backupEnd:
        nextCommand = TouchCommand.entropyStatus;
        break;
      default:
    }

    /// 验证是否需要密码
    if (cardModel?.isUseSpass == true) {
      if (Get.isEmptyString(password)) {
        await nfcKitFinish(successMessage: ID.stringTouchPsw.tr);
        if (GetPlatform.isIOS) {
          await Future.delayed(const Duration(seconds: 3));
          showPasswordWidget(nextCommand: nextCommand);
        } else {
          showPasswordWidget(nextCommand: nextCommand);
        }
      } else {
        sendCommand(command: TouchCommand.password, nextCommand: nextCommand);
      }
    } else {
      sendCommand(command: nextCommand, nextCommand: targetCommand);
    }
  }

  void _createWallet() {
    // 激活未创建钱包 获取随机种子熵
    sendCommand(
      command: TouchCommand.randomEntropy,
      nextCommand: TouchCommand.finish,
    );
  }
}
