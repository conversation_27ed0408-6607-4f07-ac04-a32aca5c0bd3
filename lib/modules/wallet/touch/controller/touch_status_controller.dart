import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchStatusController extends BaseController {
  @override
  void loadData() {}

  TouchReadCardController? readCardCtr;
  late TouchCommand targetCommand;
  TouchCommand? command;

  String title = '';
  String desStr = '';
  String buttonTitle = '';

  @override
  void onInit() {
    super.onInit();

    Map arg = Get.arguments;
    if (arg.keys.contains(GetArgumentsKey.controller)) {
      readCardCtr = arg[GetArgumentsKey.controller];
    }

    if (arg.keys.contains(GetArgumentsKey.command)) {
      command = arg[GetArgumentsKey.command];
    }

    targetCommand = arg[GetArgumentsKey.targetCommand];

    if (targetCommand == TouchCommand.create) {
      title = ID.stringBackupMnemonic.tr;
      desStr = ID.stringBackupTip.tr;
      buttonTitle = ID.stringBackupButtonTitle.tr;
    } else if (targetCommand == TouchCommand.bind) {
      if (command == TouchCommand.create) {
        title = ID.stringCreateSuccess.tr;
        desStr = ID.stringCreateDesString.tr;
      } else if (command == TouchCommand.import) {
        title = ID.stringImportSuccess.tr;
        desStr = ID.stringImportDesString.tr;
      }
      buttonTitle = ID.stringBingdingTitle.tr;
    }
  }

  void nextAction() {
    if (targetCommand == TouchCommand.create) {
      Get.toNamed(
        AppRoutes.touchMnemonicsPage,
        arguments: Get.arguments,
      );
    } else if (targetCommand == TouchCommand.bind) {
      Get.offAndToNamed(AppRoutes.touchBindPage);
    }
  }
}

class TouchStatusBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchStatusController());
  }
}
