import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_mnemonics_model.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_new_wallet_page.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchImportController extends BaseController {
  @override
  void loadData() {}

  MnemonicsType type = MnemonicsType.english;
  List<TouchMnemonicsModel> mnemonicsList =
      List.filled(12, TouchMnemonicsModel(phrase: "", index: 0));
  List<String> phraseList = [];

  List<FocusNode> focusNodeList = List.filled(12, FocusNode());

  @override
  void onInit() {
    super.onInit();
    updateDataSource(type);
  }

  Future<void> nextAction() async {
    List<String> data = [];
    for (var e in mnemonicsList) {
      if (Get.isEmptyString(e.phrase) || e.isCorrect == false) {
        Get.showToast(ID.stringInputMnemonic.tr, toastMode: ToastMode.waring);
        return;
      }
      data.add(e.phrase);
    }

    TouchReadCardController readCardCtr = Get.arguments;
    readCardCtr.cardModel!.mnemonicsList = data;

    readCardCtr.cardModel!.entropy = await Get.walletCore.getEntropy(data);
    if (readCardCtr.cardModel!.entropy == null ||
        readCardCtr.cardModel!.entropy!.isEmpty) {
      Get.showToast(ID.stringInputMnemonic.tr, toastMode: ToastMode.waring);
      return;
    }

    Get.toNamed(AppRoutes.touchWalletNamePage, arguments: {
      GetArgumentsKey.controller: Get.arguments,
      GetArgumentsKey.newTouchWalletMethod: TouchNewWalletMethod.import,
      GetArgumentsKey.targetCommand: TouchCommand.import,
    });
  }

  Future<void> updateDataSource(MnemonicsType type) async {
    KeyboardUtils.hideKeyboardNoContext();

    if (type == MnemonicsType.chinese && type != this.type) {
      Get.showAlertDialog(
        title: ID.stringTips.tr,
        content: ID.stringTouchChineseTip.tr,
      );
    }

    this.type = type;

    phraseList = await Get.walletCore.getPhrases(type: type.index) ?? [];
    mnemonicsList = mnemonicsList.map((e) {
      e.phrase = '';
      e.isCorrect = true;
      return e;
    }).toList();

    update([GetKey.importWalletId]);
  }

  void updatePhraseStatus({
    required int index,
    required String value,
    required bool isEnd,
  }) {
    TouchMnemonicsModel model =
        TouchMnemonicsModel(phrase: value, index: index);
    model.isCorrect = _verifyPhrase(value, phraseList);
    mnemonicsList[index] = model;
    if (type == MnemonicsType.number) {
      if (isEnd == false) {
        model.isCorrect = true;
      }
      update([GetKey.importWalletId]);
    } else {
      update([GetKey.importWalletId]);
    }
  }

  bool _verifyPhrase(String value, List<String> list) {
    if (Get.isEmptyString(value)) return true;

    if (type == MnemonicsType.english && value.length >= 2) {
      for (var str in list) {
        if (str.startsWith(value)) {
          return true;
        }
      }
      return false;
    } else if (type == MnemonicsType.chinese) {
      return list.contains(value);
    } else if (type == MnemonicsType.number) {
      int intValue = int.tryParse(value) ?? 0;
      if (intValue > 999 && intValue < 3047) {
        return true;
      } else {
        return false;
      }
    }

    return true;
  }

  @override
  void onReady() async {
    super.onReady();
    if (GetPlatform.isIOS) {
      await Get.walletCore.isSecureKeyboard(isSecureKeyboard: true);
    }
  }

  @override
  void onClose() async {
    super.onClose();

    if (GetPlatform.isIOS) {
      await Get.walletCore.isSecureKeyboard(isSecureKeyboard: false);
    }
  }
}

class TouchImportBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchImportController());
  }
}
