/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-24 15:04:41
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_sign_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/extension/uint8_hex.dart';

extension TouchSignFinishController on TouchSignController {
  Future<void> signatureFinish() async {
    await nfcKitFinish(successMessage: ID.stringTouchNfcSuccess.tr);

    for (var obj in signRsultList) {
      Log.logPrint('签名消息：${obj.toHexString}');
    }

    if (coinType.isEthereumSeries) {
      String? rawtx = await Get.walletCore.getEthereumSeriesBuildRawtx(
        chain: tsModel.chain,
        chainId: coinType.chainId!,
        decimal: tsModel.coinDecimal!,
        slip44: coinType.bip44Id,
        gasPrice: tsModel.gasPrice!,
        gasLimit: tsModel.gasLimit!,
        txAmount: tsModel.amount!,
        toAddress: tsModel.toAddress!,
        nonce: tsModel.nonce!.decimalToHex,
        contract: tsModel.contract,
        signData: signRsultList.first,
        signList: signRsultList,
      );

      tsModel.rawtx = rawtx;
    } else if (coinType.isBitCoinNetWork) {
      String? rawtx = await Get.walletCore.getBtcBuildRawtx(
          slip44: coinType.bip44Id,
          utxoList: tsModel.utxoList ?? [],
          txAmount: tsModel.amount!,
          chain: coinType.chain,
          dust: coinType.dust,
          decimal: tsModel.coinDecimal!,
          fromAddress: tsModel.fromAddress!,
          toAddress: tsModel.toAddress!,
          xpub: tsModel.xpub ?? '',
          signList: signRsultList,
          btcSatB: tsModel.btcSatB!,
          availableBalance: tsModel.availableBalance!,
          addressPublicey: tsModel.addressPublickey!);
      tsModel.rawtx = rawtx;
      Log.logPrint('rawtx: $rawtx');
    } else if (coinType is TronChain) {
      String? rawtx = await Get.walletCore.getTronBuildRawtx(
        txId: tsModel.txID!,
        rawData: tsModel.rawData!,
        rawDataHex: tsModel.rawDataHex!,
        signData: signRsultList.first,
        signList: signRsultList,
      );
      tsModel.rawtx = rawtx;
    } else if (coinType is SolanaChain) {
      String signature = signRsultList.first.toHexRawString;
      signature = signature.replaceFirst(RegExp(r'^0140'), '');
      String? rawtx = await Get.walletCore.splitTransactionSignature(
        chain: tsModel.chain,
        waitSignature: tsModel.waitSignature!,
        signature: signature,
      );
      tsModel.rawtx = rawtx;
    }

    if (Get.isEmptyString(tsModel.rawtx)) {
      // 签名数据错误
      Get.showToast(ID.stringTouchSignErrorTip.tr, toastMode: ToastMode.failed);
      return;
    }

    if (GetPlatform.isAndroid) {
      Get.toNamed(AppRoutes.broadcastPage, arguments: tsModel);
    } else {
      Future.delayed(const Duration(seconds: 2)).then((value) {
        Get.toNamed(AppRoutes.broadcastPage, arguments: tsModel);
      });
    }
  }
}
