/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-24 16:35:54
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_data_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_sign_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:crypto/crypto.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/extension/string_to_int_list.dart';

extension TouchSignAwaitController on TouchSignController {
  void getWaitSignData() {
    List data = List.from(rawWaitData);
    waitSignData = [];

    if (coinType.isBitCoinNetWork) {
      int length = data.length;
      int count = length ~/ maxSignCount;
      if (length % maxSignCount > 0) {
        count++;
      }

      for (int i = 0; i < count; i++) {
        int start = i * maxSignCount;
        int? end = maxSignCount * (i + 1);
        if (end > length) {
          end = null;
        }
        List sublist = data.sublist(start, end);
        List<List<int>> pageList =
            _signPageList(signCount: count, signData: sublist);
        waitSignData.add(pageList);
      }
    } else {
      List<List<int>> pageList = _signPageList(signCount: 1, signData: data);
      waitSignData.add(pageList);
    }
  }

  /// 代签名数据分页
  List<List<int>> _signPageList({
    required int signCount, // 第几笔
    required List signData, // 待签名数据
  }) {
    List<int> signList = [];
    List<int> baseList = baseInfo();
    signList.addAll(baseList);

    // path
    signList.addAll(TouchXpubModel.pathList(tsModel.addressPath!));
    //分层确定性算法
    if (coinType is SolanaChain) {
      signList.add(4);
    } else {
      signList.add(0);
    }

    if (coinType.isBitCoinNetWork) {
      List<String> strList = List.from(signData);
      List<int> btcList = [];
      int length = strList.length;
      for (int i = 0; i < length; i++) {
        String hash = strList[i];

        List<int> hashList = hash.decimalHexToIntList;
        btcList.addAll(_pad4ToList(hashList.length));
        btcList.addAll(hashList);
      }
      signList.add(length);
      signList.addAll(btcList);
    } else {
      List<int> intList = List.from(signData);
      //多少笔签名
      signList.add(1);
      signList.addAll(_pad4ToList(intList.length));
      signList.addAll(intList);
    }

    int totalPage = signList.length ~/ maxPackageLength;
    if (signList.length % maxPackageLength > 0) {
      totalPage++;
    }

    if (totalPage > 1) {
      // 如果有分包,需要加校验
      List<int> sha256List = sha256.convert(signList).bytes;
      List<int> hashLsit = sha256List.sublist(0, 4);
      signList.addAll(hashLsit);
    }

    List<List<int>> pageList = [];
    for (int i = 0; i < totalPage; i++) {
      int start = i * maxPackageLength;
      int end = start + maxPackageLength;
      if (end > signList.length) {
        end = signList.length;
      }
      List<int> sublist = signList.sublist(start, end);
      pageList.add(sublist);
    }

    return pageList;
  }

  Future<List<dynamic>> getRawWaitSignData() async {
    if (coinType.isEthereumSeries) {
      final result = await Get.walletCore.getEthereumSeriesWaitingSignatureList(
        chain: tsModel.chain,
        chainId: coinType.chainId!,
        decimal: tsModel.coinDecimal!,
        slip44: coinType.bip44Id,
        gasPrice: tsModel.gasPrice!,
        gasLimit: tsModel.gasLimit!,
        txAmount: tsModel.amount!,
        toAddress: tsModel.toAddress!,
        nonce: tsModel.nonce!.decimalToHex,
        contract: tsModel.contract,
      );
      Log.s("result=$result");
      return result?.toList() ?? <int>[];
    } else if (coinType.isBitCoinNetWork) {
      final result = await Get.walletCore.getBtcWaitingSignatureList(
            slip44: coinType.bip44Id,
            utxoList: tsModel.utxoList ?? [],
            chain: coinType.chain,
            dust: coinType.dust,
            decimal: tsModel.coinDecimal!,
            txAmount: tsModel.amount!,
            fromAddress: tsModel.fromAddress!,
            toAddress: tsModel.toAddress!,
            btcSatB: tsModel.btcSatB!,
            availableBalance: tsModel.availableBalance!,
          ) ??
          <String>[];
      Log.s("result1=$result");
      return result;
    } else if (coinType is TronChain) {
      String txId = tsModel.txID ?? '';
      final result = txId.decimalHexToIntList;
      Log.s("result1=$result");
      return result;
    } else if (coinType is SolanaChain) {
      final result = tsModel.waitSignature!.decimalHexToIntList;
      Log.s("result1=$result");
      return result;
    }

    return [];
  }

  List<int> _pad4ToList(int length) => [length ~/ 256, length % 256];
}
