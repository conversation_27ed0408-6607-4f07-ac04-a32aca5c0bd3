/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-25 16:35:47
 */

import 'dart:typed_data';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_finish_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_model.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/extension/uint8_hex.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';

extension TouchCommandResultController on TouchReadCardController {
  Future<Uint8List?> sendCommandResult({
    required Uint8List responseData,
    required TouchCommand command,
    required TouchCommand nextCommand,
    TouchXpubModel? xpubModel,
  }) async {
    Uint8List resultList = responseData.sublist(0, responseData.length - 2);
    Uint8List swList = responseData.sublist(responseData.length - 2);

    String sw1 = swList.first.toRadixString(16);
    this.sw1 = sw1;
    String sw2 = swList.last.toRadixString(16);
    sw2 = sw2.padLeft(2, '0');

    String resultStr = '$sw1$sw2';
    Log.logPrint('command: $command');
    Log.logPrint('responseData: ${responseData.toHexString}');

    if (resultStr == '9000') {
      if (command == TouchCommand.readCard) {
        cardModel = TouchModel.fromPayload(resultList);
        // Log.logPrint(jsonEncode(cardModel?.toJson()));
        // 进行下一步
        if (!(await _verifyTouch(cardModel!))) {
          return null;
        }
      }

      return resultList;
    }

    return await commandErrorResult(
      resultStr: resultStr,
      sw1: sw1,
      sw2: sw2,
      command: command,
      nextCommand: nextCommand,
      responseData: resultList,
    );
  }

  Future<Uint8List?> commandErrorResult({
    required String resultStr,
    required String sw1,
    required String sw2,
    required TouchCommand command,
    required TouchCommand nextCommand,
    required Uint8List responseData,
  }) async {
    int intSw2 = DecimalUtils.toIntSafe(sw2.hexToDecimal);
    String alertMessage = '';
    if (sw1 == '61') {
      if (command == TouchCommand.signature ||
          command == TouchCommand.packageFinish) {
        return responseData;
      } else {
        alertMessage = sw1;
      }
    } else if (sw1 == '6C') {
      alertMessage = sw1;
    } else if (sw1 == '96') {
      if (intSw2 <= 5) {
        if (intSw2 == 0) {
          /// 密码错误次数已达上限，钱包已重置
          alertMessage = ID.stringNfcPswMax.tr;
          password = '';
          await nfcKitFinish(errorMessage: alertMessage);
          return null;
        } else if (intSw2 == 1) {
          alertMessage = ID.stringNfcInputOnePsw.tr;
          password = '';
        } else {
          alertMessage =
              ID.stringNfcPswErrorNum.trParams({'count': intSw2.toString()});
          password = '';
        }
      } else {
        alertMessage = ID.stringNfcPswError.tr;
        password = '';
      }
    } else if (sw1 == '97') {
      alertMessage = ID.stringNfcPswError.tr;
      password = '';
    } else {
      switch (resultStr) {
        case '6200':
        case '6400':
        case '6401':
        case '6403':
        case '6405':
        case '6406':
        case '6700':
        case '6701':
        case '6900':
        case '6984':
        case '6985':
        case '6988':
        case '6989':
        case '6A80':
        case '6A86':
        case '6D00':
        case '6E00':
        case '6E03':
        case '6E05':
        case '9303':
          alertMessage = ID.stringNfcScanError.tr;
          break;
        case '6201':
          alertMessage = ID.stringCardHaveWallet.tr;
          break;
        case '6202':
          alertMessage = ID.stringCardWalletError.tr;
          break;
        case '6402':
          alertMessage = ID.stringCardNumberError.tr;
          break;
        case '6F00':
          alertMessage = ID.stringNfcUnknownError.tr;
          break;
        case '6404':
          {
            /// 设置熵验证状态
            if (command == TouchCommand.entropyStatus) {
              backupFinish();
              return null;
            } else {
              alertMessage = resultStr;
            }
          }
          break;
        case '9403':
          {
            alertMessage = ID.stringNfcPswError.tr;
            password = '';
            break;
          }

        default:
          alertMessage = ID.stringNfcUnknownError.tr;
          break;
      }
    }

    await nfcKitFinish(errorMessage: alertMessage);
    if (command == TouchCommand.password) {
      // 如果是校验密码 校验失败 再次弹出密码输入框
      showPasswordWidget(nextCommand: nextCommand);
    }

    return null;
  }

  Future<bool> _verifyTouch(TouchModel touchModel) async {
    bool result = _isCoinbagTouch(touchModel.chipId);
    if (result == false) {
      // Get.showToast('此卡片非官方出品', toastMode: ToastMode.failed);
      await nfcKitFinish(errorMessage: ID.stringTouchNoOfficial.tr);
      return result;
    }

    int batch = DecimalUtils.toIntSafe(touchModel.batch ?? '0');
    if (batch < TouchWallet.minBatch || batch > TouchWallet.maxBatch) {
      // Get.showToast('无法识别，请读取正确的卡片', toastMode: ToastMode.failed);
      await nfcKitFinish(errorMessage: ID.stringTouchSpotError.tr);
      result = false;
    }
    return result;
  }

  /// T201302600017783
  /// 1.去除前两位得到：01302600017783
  /// 2.提取最后一位得到：lastNumber=3，移除最后一位得到：number=0130200000578
  /// 3.遍历number，得到顺序orderList=[0,1,3,0,2,6,0,0,0,1,7,7,8]，倒序reverseList=[8,7,7,1,0,0,0,6,2,0,3,1,0]
  /// 4.遍历reverseList，(偶数位值*2)~/10 保存为list1=[0,0,4,6,0]，(偶数位值*2)%10 保存为list2=[6,1,4,1]，奇数位值保存为list3=[7,1,0,6,0,1]
  /// 5.list1值相加得到number1，list2值相加得到number2，list3值相加得到number3
  /// 6. int result = (lastNumber + number1 + number2 + number3) % 10，如果result==0验证成功，反之验证失败
  bool _isCoinbagTouch(String? chipId) {
    if (Get.isEmptyString(chipId)) {
      chipId = '';
    }
    if (chipId!.length < 2) return false;

    // Step 1: 去除前两位
    String trimmedNumber = chipId.substring(2); // 去掉前两位数字

    // Step 2: 提取最后一位作为 lastNumber，移除最后一位得到 number
    if (trimmedNumber.isEmpty) return false;

    int lastNumber = DecimalUtils.toIntSafe(
        trimmedNumber.substring(trimmedNumber.length - 1));
    String number = trimmedNumber.substring(0, trimmedNumber.length - 1);

    // Step 3: 遍历 number，生成顺序列表 orderList 和倒序列表 reverseList
    List<int> orderList = number.split('').map(int.parse).toList(); // 顺序列表
    List<int> reverseList = orderList.reversed.toList(); // 倒序列表

    // Step 4: 遍历 reverseList，处理偶数位和奇数位
    List<int> list1 = []; // 偶数位值 * 2 / 10
    List<int> list2 = []; // 偶数位值 * 2 % 10
    List<int> list3 = []; // 奇数位值

    for (int i = 0; i < reverseList.length; i++) {
      int value = reverseList[i];
      if (i % 2 == 0) {
        // 偶数位（从0开始计数）
        int doubled = value * 2;
        list1.add(doubled ~/ 10); // 整除 10
        list2.add(doubled % 10); // 取余 10
      } else {
        // 奇数位
        list3.add(value);
      }
    }

    // Step 5: 计算 list1、list2 和 list3 的总和
    int number1 = list1.fold(0, (prev, element) => prev + element);
    int number2 = list2.fold(0, (prev, element) => prev + element);
    int number3 = list3.fold(0, (prev, element) => prev + element);

    // Step 6: 计算最终结果
    int result = (lastNumber + number1 + number2 + number3) % 10;

    // 验证结果
    return result == 0;
  }
}
