/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-01-13 10:00:34
 */

import 'dart:math';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_mnemonics_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchMnemonicsVerifyController extends BaseController {
  @override
  void loadData() {}

  late TouchReadCardController readCardCtr;
  late TouchCommand targetCommand;
  late TouchMnemonicsController mnemonicsCtr;

  List<TouchMnemonicsModel> mnemonicsList = [];
  List<TouchMnemonicsModel> inputMnemonicsList = [];

  @override
  void onInit() {
    super.onInit();
    readCardCtr = Get.arguments[GetArgumentsKey.controller];
    targetCommand = Get.arguments[GetArgumentsKey.targetCommand];

    mnemonicsCtr = Get.find<TouchMnemonicsController>();
    int index = 0;
    for (var e in mnemonicsCtr.mnemonicsList) {
      mnemonicsList.add(TouchMnemonicsModel(phrase: e, index: index));
      index++;
    }
    mnemonicsList.shuffle(Random());
  }

  void updateInputMnemonics(int index) {
    if (index < inputMnemonicsList.length) {
      inputMnemonicsList = inputMnemonicsList.getRange(0, index).toList();
      update([GetKey.verifyMnemonicsId]);
    }
  }

  void verifyMnemonics() {
    if (inputMnemonicsList.length == mnemonicsList.length) {
      String inputValue =
          inputMnemonicsList.map((e) => e.phrase).toList().join(' ');
      String mnemonicsStr = mnemonicsCtr.mnemonicsList.join(' ');
      if (inputValue == mnemonicsStr) {
        // 验证成功
        readCardCtr.cardModel!.mnemonicsList =
            inputMnemonicsList.map((e) => e.phrase).toList();

        Get.offNamedUntil(
          AppRoutes.touchReadCardPage,
          (route) => (route.settings.name == AppRoutes.connectWalletPage ||
              route.settings.name == AppRoutes.walletDetailsPage),
          arguments: {
            GetArgumentsKey.targetCommand: targetCommand,
            GetArgumentsKey.targetTouchModel: readCardCtr.cardModel,
            GetArgumentsKey.walletModel: readCardCtr.walletModel,
            GetArgumentsKey.password: readCardCtr.password,
          },
        );
      } else {
        // 验证失败
        Get.showToast(ID.stringVerifyFail.tr, toastMode: ToastMode.failed);
      }
    }
  }
}

class TouchMnemonicsVerifyBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchMnemonicsVerifyController());
  }
}
