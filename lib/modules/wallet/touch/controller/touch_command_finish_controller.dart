/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-03 17:06:32
 */
import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

extension TouchCommandFinishController on TouchReadCardController {
  void finishCommand() {
    switch (targetCommand) {
      case TouchCommand.create:
      case TouchCommand.import:
        _createWaletFinish();
        break;
      case TouchCommand.bind:
        _bindWalletFinish();
        break;
      case TouchCommand.backupEnd:
        backupFinish();
        break;
      case TouchCommand.changeName:
        _changeNameFinish();
        break;
      case TouchCommand.changePsw:
        _changePasswordFinish();
        break;
      case TouchCommand.reset:
        _resetFinish();
        break;
      default:
    }
  }

  Future<void> _resetFinish() async {
    await nfcKitFinish(successMessage: ID.stringTouchResetSuccess.tr);
    Get.back(result: {GetArgumentsKey.resetStatus: true});
  }

  Future<void> _changePasswordFinish() async {
    await nfcKitFinish(successMessage: ID.stringNameChangeSuccess.tr);
    Get.back();
  }

  Future<void> _changeNameFinish() async {
    await nfcKitFinish(successMessage: ID.stringNameChangeSuccess.tr);
    await Get.database.walletDao
        .updateWalletName(walletModel!.walletId!, walletName!);
    Get.back();
  }

  Future<void> backupFinish() async {
    await nfcKitFinish(successMessage: ID.stringBackupComplete.tr);
    Get.back();
  }

  Future<void> _bindWalletFinish() async {
    /// 获取公钥结束
    cardModel!.xpubModelsList = xpubModels;
    await nfcKitFinish(successMessage: ID.stringTouchNfcSuccess.tr);
    Get.connectWallet.handleConnectWallet(jsonEncode(cardModel!.bindJson()));
  }

  Future<void> _createWaletFinish() async {
    String iosAlertMessage = '';
    if (targetCommand == TouchCommand.create) {
      iosAlertMessage = ID.stringCreateSuccess.tr;
    } else if (targetCommand == TouchCommand.import) {
      iosAlertMessage = ID.stringImportSuccess.tr;
    }
    await nfcKitFinish(successMessage: iosAlertMessage);

    Get.offAndToNamed(AppRoutes.touchStatusPage, arguments: {
      GetArgumentsKey.command: targetCommand,
      GetArgumentsKey.targetCommand: TouchCommand.bind,
    });
  }
}
