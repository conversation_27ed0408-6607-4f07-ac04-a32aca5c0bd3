/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-04 09:50:11
 */

import 'dart:typed_data';

import 'package:coinbag/modules/wallet/touch/controller/touch_command_data_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_finish_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_modify_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_password_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_result_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

extension TouchCommandController on TouchReadCardController {
  Future<Uint8List?> sendCommand({
    required TouchCommand command,
    required TouchCommand nextCommand,
    TouchXpubModel? xpubModel,
    Function(TouchCommand nextCommand)? nextCommandCallback,
  }) async {
    if (command != TouchCommand.readCard) {
      if (cardModel == null) {
        await nfcKitFinish(errorMessage: ID.stringTouchReadError.tr);
        return null;
      }
    }

    try {
      Uint8List capdu = await commandData(
        command,
        xpubModel: xpubModel,
      );
      final responseData = await FlutterNfcKit.transceive(capdu);

      Uint8List? resultList = await sendCommandResult(
        command: command,
        nextCommand: nextCommand,
        responseData: responseData,
        xpubModel: xpubModel,
      );

      if (resultList == null) return null;

      if (command == TouchCommand.readCard) {
        passwordCommand();
        return null;
      } else if (command == TouchCommand.password) {
        if (nextCommand == TouchCommand.signature) {
          signatureCommand();
        }
      } else if (command == TouchCommand.randomEntropy) {
        _randomEntropyCommand(resultList);
        return null;
      } else if (command == TouchCommand.xpub) {
        // 保存公钥
        xpubCommandResult(xpubModel, resultList);
      } else if (command == TouchCommand.signature ||
          command == TouchCommand.packageFinish) {
        signatureResponse(
          responseData: resultList,
          command: command,
          nextCommand: nextCommand,
        );

        return resultList;
      }

      switch (nextCommand) {
        case TouchCommand.password:
          passwordCommand();
          break;
        case TouchCommand.xpub:
          xpubDataCommand(command, nextCommand);
          break;
        case TouchCommand.cardEntropy:
          cardEntropyCommand();
          break;
        case TouchCommand.cardEntropyNext:
          cardEntropyNextCommand(resultList);
          break;
        case TouchCommand.entropyStatus:
          entropyStatusCommand();
          break;
        case TouchCommand.changeName:
          changeNameCommand();
          break;
        case TouchCommand.changePsw:
          changePasswordCommand(command);
          break;
        case TouchCommand.reset:
          resetCommand();
          break;
        case TouchCommand.finish:
          finishCommand();
          break;

        default:
      }
    } catch (e) {
      Log.logPrint(e);
      await nfcKitFinish(errorMessage: ID.stringNfcScanError.tr);
    }
    return null;
  }

  /// 随机种子 -> 创建钱包(新建，导入)
  Future<void> _randomEntropyCommand(Uint8List resultList) async {
    cardModel?.entropy = resultList;
    await nfcKitFinish(successMessage: ID.stringTouchNfcSuccess.tr);
    Get.offAndToNamed(AppRoutes.touchNewWalletPage, arguments: this);
  }
}
