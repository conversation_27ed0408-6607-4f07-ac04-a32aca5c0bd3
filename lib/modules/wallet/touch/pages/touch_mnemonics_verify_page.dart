/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-01-13 09:56:29
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_verify_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_mnemonics_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:flutter/material.dart';

class TouchMnemonicsVerifyPage
    extends BaseStatelessWidget<TouchMnemonicsVerifyController> {
  const TouchMnemonicsVerifyPage({super.key});

  double get _itemWidth => (Get.width - Get.setPaddingSize(32)) / 3;
  double get _itemHeight => 50;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TouchMnemonicsVerifyController>(
      id: GetKey.verifyMnemonicsId,
      builder: (_) {
        return Scaffold(
          appBar: baseAppBar(title: ID.stringVerifyMnemonic.tr),
          bottomNavigationBar: _bottomWidget(),
          body: Padding(
            padding: EdgeInsets.all(Get.setPaddingSize(16)),
            child: Column(
              children: [
                _inputWidget(),
                SizedBox(height: Get.setPaddingSize(24)),
                Text(
                  ID.stringOrderInputMnemonic.tr,
                  style: styleSecond_14,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  SizedBox _inputWidget() {
    return SizedBox(
      height: 4 * _itemHeight,
      child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: _itemWidth / _itemHeight,
          ),
          itemCount: 12,
          itemBuilder: (BuildContext context, int index) => Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: Get.setPaddingSize(10)),
                child: GestureDetector(
                  onTap: () => controller.updateInputMnemonics(index),
                  behavior: HitTestBehavior.translucent,
                  child: Column(
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            controller.inputMnemonicsList.length > index
                                ? controller.inputMnemonicsList[index].phrase
                                : '',
                            style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(16),
                              fontWeight: FontWeightX.medium,
                            ),
                          ),
                        ),
                      ),
                      const DividerWidget()
                    ],
                  ),
                ),
              )),
    );
  }

  Container _bottomWidget() {
    double itemWidth = (Get.width - Get.setPaddingSize(32)) / 3;
    double itemHeight = 55;
    return Container(
      color: Get.theme.colorECECEC,
      height: 4 * itemHeight +
          Get.getSafetyBottomPadding() +
          Get.setPaddingSize(10),
      child: Padding(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(10),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: itemWidth / itemHeight,
          ),
          itemCount: controller.mnemonicsList.length,
          itemBuilder: (BuildContext context, int index) {
            TouchMnemonicsModel model = controller.mnemonicsList[index];

            return Padding(
              padding: EdgeInsets.all(Get.setPaddingSize(5)),
              child: HighLightInkWell(
                onTap: () {
                  if (!controller.inputMnemonicsList.contains(model)) {
                    controller.inputMnemonicsList.add(model);
                    controller.update([GetKey.verifyMnemonicsId]);
                  }
                  controller.verifyMnemonics();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Get.theme.bgColor,
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Center(
                    child: Text(
                      model.phrase,
                      style: TextStyle(
                        color: controller.inputMnemonicsList.contains(model)
                            ? Get.theme.textTertiary
                            : Get.theme.textPrimary,
                        fontSize: Get.setFontSize(16),
                        fontWeight: FontWeightX.medium,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
