/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 16:46:28
 */
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_bind_controller.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_nfc_close_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_read_button_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_read_card_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchBindPage extends BaseStatelessWidget<TouchBindController> {
  const TouchBindPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: baseAppBar(title: controller.title),
          bottomNavigationBar: controller.nfcAvailable.value == true
              ? TouchReadButtonWidget(
                  onPressed: () => controller.startSession(
                    command: TouchCommand.readCard,
                    nextCommand: controller.targetCommand,
                  ),
                )
              : null,
          body: controller.nfcAvailable.value == true
              ? const TouchReadCardWidget(isBind: true)
              : const TouchNFCCloseWidget(),
        ));
  }
}
