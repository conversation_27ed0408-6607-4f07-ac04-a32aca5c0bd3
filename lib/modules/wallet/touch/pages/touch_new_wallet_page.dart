/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 16:46:38
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_model.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

enum TouchNewWalletMethod { create, import }

class TouchNewWalletPage extends StatefulWidget {
  const TouchNewWalletPage({super.key});

  @override
  State<TouchNewWalletPage> createState() => _TouchNewWalletPageState();
}

class _TouchNewWalletPageState extends State<TouchNewWalletPage> {
  bool isRead = true;

  String deviceId = '';

  @override
  void initState() {
    super.initState();
    TouchReadCardController controller = Get.arguments;
    TouchModel touchModel = controller.cardModel!;
    deviceId = touchModel.chipId ?? '';
    deviceId = deviceId.replaceAllMapped(
        RegExp('.{4}'), (match) => '${match.group(0)} ');
  }

  String get userArgument {
    LanguageModel model = AppController.languageModel.value;
    if (model.type == LanguageType.en) {
      return AgreementUrl.userArgumentEN;
    }
    return AgreementUrl.userArgumentZH;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringNewWallet.tr),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _iconWidget(),
            SizedBox(height: Get.setPaddingSize(24)),
            Text(ID.stringCreateTitle.tr, style: stylePrimary_16_m),
            SizedBox(height: Get.setPaddingSize(8)),
            Text(ID.stringCreateDesTitle.tr, style: styleSecond_14),
            SizedBox(height: Get.setPaddingSize(24)),
            Text(ID.stringImportTitle.tr, style: stylePrimary_16_m),
            SizedBox(height: Get.setPaddingSize(8)),
            Text(ID.stringImportDesTitle.tr, style: styleSecond_14),
            SizedBox(height: Get.setPaddingSize(36)),
            _buttonsWidget(),
          ],
        ),
      ),
    );
  }

  void _buttonAction(TouchNewWalletMethod method) {
    if (isRead == false) {
      Get.showToast(ID.stringAgreementTip.tr, toastMode: ToastMode.waring);
      return;
    }

    if (method == TouchNewWalletMethod.create) {
      Get.toNamed(AppRoutes.touchWalletNamePage, arguments: {
        GetArgumentsKey.controller: Get.arguments,
        GetArgumentsKey.newTouchWalletMethod: method,
        GetArgumentsKey.targetCommand: TouchCommand.create,
      });
    } else {
      Get.toNamed(AppRoutes.touchImportWalletPage, arguments: Get.arguments);
    }
  }

  Padding _buttonsWidget() => Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: Column(
          children: [
            ButtonWidget(
              text: ID.stringCreateWalletTitle.tr,
              width: Get.width,
              onPressed: () => _buttonAction(TouchNewWalletMethod.create),
            ),
            SizedBox(height: Get.setPaddingSize(16)),
            OutlinedButtonWidget(
                text: ID.stringImportWalletTitle.tr,
                width: Get.width,
                onPressed: () => _buttonAction(TouchNewWalletMethod.import)),
            SizedBox(height: Get.setPaddingSize(16)),
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      isRead = !isRead;
                    });
                  },
                  child: ImageWidget(
                    assetUrl: isRead ? 'touch_selected' : 'touch_no_selected',
                    height: Get.setImageSize(20),
                    width: Get.setImageSize(20),
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(4)),
                Text.rich(
                  TextSpan(text: ID.stringAgreement1.tr, children: [
                    TextSpan(
                        text: ID.stringAgreement2.tr,
                        recognizer: TapGestureRecognizer()
                          ..onTap = () => Get.toWeb(url: userArgument),
                        style: TextStyle(
                          color: Get.theme.colorFF6A16,
                          fontSize: Get.setFontSize(14),
                        ))
                  ]),
                  style: styleSecond_14,
                )
              ],
            )
          ],
        ),
      );

  Stack _iconWidget() {
    return Stack(
      children: [
        ImageWidget(
          assetUrl: 'touch_icon',
          width: (Get.width - Get.setPaddingSize(16) * 2),
          height: (Get.width - Get.setPaddingSize(16) * 2) * 0.55,
        ),
        Positioned(
          right: Get.setPaddingSize(16),
          left: Get.setPaddingSize(16),
          bottom: Get.setPaddingSize(10),
          child: Text(
            deviceId,
            style: TextStyle(
                color: Get.theme.bgColor,
                fontSize: Get.setFontSize(16),
                fontFamily: Get.setNumberFontFamily()),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}
