/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-19 13:20:23
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_new_wallet_page.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';

class TouchWalletPasswordPage extends StatefulWidget {
  const TouchWalletPasswordPage({super.key});

  @override
  State<TouchWalletPasswordPage> createState() => _TouchPasswordPageState();
}

class _TouchPasswordPageState extends State<TouchWalletPasswordPage> {
  String onePassword = '';
  String twoPassword = '';

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
        appBar: baseAppBar(title: ID.stringSetPassword.tr),
        body: Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                ID.stringTouchPasswordTip.tr,
                style: styleSecond_14,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              TextFieldWidget(
                hintText: ID.stringTouchInputPsw.tr,
                keyboardType: TextInputType.visiblePassword,
                obscureText: true,
                autofocus: true,
                showEyes: true,
                showClear: true,
                onValueChanged: (value) => onePassword = value,
              ),
              SizedBox(height: Get.setPaddingSize(16)),
              TextFieldWidget(
                hintText: ID.stringTouchInputPswTwo.tr,
                keyboardType: TextInputType.visiblePassword,
                obscureText: true,
                showEyes: true,
                showClear: true,
                onValueChanged: (value) => twoPassword = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              ButtonWidget(
                text: ID.stringConfirm.tr,
                width: Get.width,
                onPressed: () async {
                  KeyboardUtils.hideKeyboardNoContext();

                  bool result = RegExp(r'^[a-zA-Z0-9]+$').hasMatch(onePassword);
                  if (result == false || onePassword.length < 6) {
                    Get.showToast(ID.stringTouchImInputCorrectPsw.tr,
                        toastMode: ToastMode.failed);
                    return;
                  }

                  if (onePassword != twoPassword) {
                    Get.showToast(ID.stringTouchTowPswFail.tr,
                        toastMode: ToastMode.waring);
                    return;
                  }

                  if (GetPlatform.isIOS) {
                    await Get.walletCore
                        .isSecureKeyboard(isSecureKeyboard: false);
                  }

                  TouchReadCardController controller =
                      Get.arguments[GetArgumentsKey.controller];
                  controller.cardModel!.cardPassword = onePassword;

                  TouchNewWalletMethod method =
                      Get.arguments[GetArgumentsKey.newTouchWalletMethod];
                  if (method == TouchNewWalletMethod.create) {
                    Get.toNamed(
                      AppRoutes.touchStatusPage,
                      arguments: {
                        GetArgumentsKey.controller: controller,
                        GetArgumentsKey.targetCommand:
                            Get.arguments[GetArgumentsKey.targetCommand],
                      },
                    );
                  } else if (method == TouchNewWalletMethod.import) {
                    Get.offNamedUntil(
                        AppRoutes.touchReadCardPage,
                        (route) => (route.settings.name ==
                                AppRoutes.connectWalletPage ||
                            route.settings.name == AppRoutes.walletDetailsPage),
                        arguments: {
                          GetArgumentsKey.targetTouchModel:
                              controller.cardModel,
                          GetArgumentsKey.targetCommand:
                              Get.arguments[GetArgumentsKey.targetCommand],
                        });
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
