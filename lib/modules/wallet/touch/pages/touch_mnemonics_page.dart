/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-28 14:48:07
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_bottom_button_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_mnemonics_type_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:flutter/material.dart';

class TouchMnemonicsPage extends BaseStatelessWidget<TouchMnemonicsController> {
  const TouchMnemonicsPage({super.key});

  double get _itemWidth => (Get.width - 32) / 3;
  double get _itemHeight => 50;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringBackupMnemonic.tr),
      bottomNavigationBar: TouchBottomButtonWidget(
        onPressed: () => Get.toNamed(
          AppRoutes.touchMnemonicsVerifyPage,
          arguments: {
            GetArgumentsKey.controller: controller.readCardCtr,
            GetArgumentsKey.targetCommand: controller.targetCommand,
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        child: GetBuilder<TouchMnemonicsController>(
          id: GetKey.backupMnemonicsId,
          builder: (_) {
            return Column(
              children: [
                Text(
                  ID.stringBackupMnemonicDes.tr,
                  style: styleSecond_14,
                ),
                SizedBox(height: Get.setPaddingSize(40)),
                SizedBox(
                  width: Get.width,
                  height: _itemHeight * 4 + 10,
                  child: _gridViewWidget(),
                ),
                SizedBox(height: Get.setPaddingSize(24)),
                TouchMnemonicsTypeWidget(
                  onTap: (type) => controller.loadDataSource(type),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  GridView _gridViewWidget() {
    return GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: _itemWidth / _itemHeight,
        ),
        itemCount: controller.mnemonicsList.length,
        itemBuilder: (BuildContext context, int index) => Column(
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      controller.mnemonicsList[index],
                      style: stylePrimary_16_m,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                DividerWidget(
                  padding:
                      EdgeInsets.symmetric(horizontal: Get.setPaddingSize(10)),
                ),
              ],
            ));
  }
}
