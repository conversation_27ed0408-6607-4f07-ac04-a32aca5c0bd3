/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-19 13:29:26
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_change_password_controller.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_read_button_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class TouchChangePasswordPage
    extends BaseStatelessWidget<TouchChangePasswordController> {
  const TouchChangePasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
        appBar: baseAppBar(title: controller.navTitle),
        bottomNavigationBar: _bottomWidget(),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(16),
          ),
          child: Column(
            children: [
              _itemWidget(
                title: ID.oldPassword.tr,
                hintText: ID.oldHintPassword.tr,
                autofocus: true,
                onValueChanged: (value) => controller.oldPassword = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              _itemWidget(
                title: ID.newPassword.tr,
                hintText: ID.newHintPassword.tr,
                onValueChanged: (value) => controller.newPassword = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              _itemWidget(
                title: ID.okNewPassword.tr,
                hintText: ID.okNewHintPassword.tr,
                onValueChanged: (value) => controller.newPasswordTwo = value,
              ),
              SizedBox(height: Get.setPaddingSize(16)),
              Text(ID.stringTouchPasswordTip.tr, style: styleSecond_14),
            ],
          ),
        ),
      ),
    );
  }

  TouchReadButtonWidget _bottomWidget() {
    return TouchReadButtonWidget(
      title: ID.stringConfirm.tr,
      padding: EdgeInsets.only(
        bottom: Get.getSafetyBottomPadding(),
        top: Get.setPaddingSize(16),
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
      ),
      onPressed: () => controller.buttonAction(),
    );
  }

  Column _itemWidget({
    required String title,
    required String hintText,
    bool? autofocus,
    ValueChanged<String>? onValueChanged,
  }) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: styleSecond_14,
          ),
          SizedBox(height: Get.setPaddingSize(8)),
          SizedBox(
            height: Get.setHeight(44),
            child: TextFieldWidget(
              hintText: hintText,
              keyboardType: TextInputType.visiblePassword,
              obscureText: true,
              autofocus: autofocus,
              showEyes: true,
              showClear: true,
              onValueChanged: onValueChanged,
            ),
          ),
        ],
      );
}
