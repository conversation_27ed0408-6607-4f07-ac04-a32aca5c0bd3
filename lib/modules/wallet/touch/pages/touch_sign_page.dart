/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 16:46:44
 */
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_sign_controller.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_read_button_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_read_card_widget.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchSignPage extends BaseStatelessWidget<TouchSignController> {
  const TouchSignPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: controller.title),
      bottomNavigationBar: TouchReadButtonWidget(
        onPressed: () => controller.startSession(
          command: TouchCommand.readCard,
          nextCommand: controller.targetCommand,
        ),
      ),
      body: const TouchReadCardWidget(),
    );
  }
}
