/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-07 15:02:33
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_import_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_mnemonics_model.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_bottom_button_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_mnemonics_type_widget.dart';
import 'package:coinbag/modules/wallet/touch/widgets/touch_textfield_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:flutter/material.dart';

class TouchImportWalletPage extends BaseStatelessWidget<TouchImportController> {
  const TouchImportWalletPage({super.key});

  double get _itemWidth => (Get.width - 32) / 3;
  double get _itemHeight => 50;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringImportMnemonic.tr),
      bottomNavigationBar: TouchBottomButtonWidget(
        onPressed: () => controller.nextAction(),
      ),
      body: KeyboardDismissWidget(
        child: GetBuilder<TouchImportController>(
          id: GetKey.importWalletId,
          builder: (_) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
              child: Column(
                children: [
                  SizedBox(height: Get.setPaddingSize(24)),
                  TouchMnemonicsTypeWidget(
                    onTap: (type) async => controller.updateDataSource(type),
                  ),
                  SizedBox(height: Get.setPaddingSize(16)),
                  SizedBox(
                    width: Get.width,
                    height: _itemHeight * 4 + 10,
                    child: _gridViewWidget(controller.type),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  GridView _gridViewWidget(MnemonicsType type) {
    return GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: _itemWidth / _itemHeight,
        ),
        itemCount: controller.mnemonicsList.length,
        itemBuilder: (BuildContext context, int index) => Column(
              children: [
                _textFieldWidget(index, type),
                DividerWidget(
                  padding:
                      EdgeInsets.symmetric(horizontal: Get.setPaddingSize(10)),
                ),
              ],
            ));
  }

  Expanded _textFieldWidget(int index, MnemonicsType type) {
    TouchMnemonicsModel model = controller.mnemonicsList[index];
    return Expanded(
      child: TouchTextFieldWidget(
        key: Key('TouchTextFieldWidget-$index-${type.index}'),
        index: index,
        width: _itemWidth,
        height: _itemHeight,
        type: type,
        model: model,
        onValueChanged: (value) {
          model.phrase = value;
          controller.updatePhraseStatus(
            index: index,
            value: value,
            isEnd: false,
          );
        },
      ),
    );
  }
}
