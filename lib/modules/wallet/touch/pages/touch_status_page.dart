/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-11 16:46:51
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_status_controller.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchStatusPage extends BaseStatelessWidget<TouchStatusController> {
  const TouchStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(actionWidget: _actionWidget()),
        body: Scaffold(
            body: SingleChildScrollView(
          child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: Get.setHeight(40),
                    ),
                    ImageWidget(
                      width: Get.setImageSize(84),
                      height: Get.setImageSize(84),
                      assetUrl: controller.targetCommand == TouchCommand.create
                          ? 'icon_sending'
                          : 'icon_success',
                    ),
                    SizedBox(
                      height: Get.setHeight(24),
                    ),
                    Visibility(
                      visible: !Get.isEmptyString(controller.title),
                      child: Text(
                        controller.title,
                        style: TextStyle(
                            color:
                                controller.targetCommand == TouchCommand.create
                                    ? Get.theme.textPrimary
                                    : Get.theme.color02B58A,
                            fontWeight: FontWeightX.medium,
                            fontSize: Get.setFontSize(20)),
                      ),
                    ),
                    Visibility(
                      visible: !Get.isEmptyString(controller.desStr),
                      child: SizedBox(
                        height: Get.setHeight(8),
                      ),
                    ),
                    Visibility(
                      visible: !Get.isEmptyString(controller.desStr),
                      child: Text(
                        controller.desStr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Get.theme.textSecondary,
                            fontWeight: FontWeightX.regular,
                            fontSize: Get.setFontSize(14)),
                      ),
                    ),
                    SizedBox(
                      height: Get.setHeight(36),
                    ),
                    Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: Get.setPaddingSize(Get.width * 68 / 375)),
                      child: OutlinedButtonWidget(
                        text: controller.buttonTitle,
                        buttonSize: ButtonSize.full,
                        onPressed: () => controller.nextAction(),
                      ),
                    )
                  ])),
        )));
  }

  List<Widget> _actionWidget() {
    if (controller.targetCommand != TouchCommand.create) return <Widget>[];
    return [
      GestureDetector(
        onTap: () async {
          final arg = Get.arguments;
          TouchReadCardController readCardCtr = arg[GetArgumentsKey.controller];
          TouchCommand targetCommand = arg[GetArgumentsKey.targetCommand];
          Get.offNamedUntil(
            AppRoutes.touchReadCardPage,
            (route) => (route.settings.name == AppRoutes.connectWalletPage ||
                route.settings.name == AppRoutes.walletDetailsPage),
            arguments: {
              GetArgumentsKey.targetCommand: targetCommand,
              GetArgumentsKey.targetTouchModel: readCardCtr.cardModel,
              GetArgumentsKey.walletModel: readCardCtr.walletModel,
              GetArgumentsKey.password: readCardCtr.password,
            },
          );
        },
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(8),
          ),
          child: Text(
            ID.stringJumpTitle.tr,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              color: Get.theme.textPrimary,
            ),
          ),
        ),
      )
    ];
  }
}
