/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-05 09:40:08
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';

class TouchWalletNamePage extends StatefulWidget {
  const TouchWalletNamePage({super.key});

  @override
  State<TouchWalletNamePage> createState() => _TouchWalletNamePageState();
}

class _TouchWalletNamePageState extends State<TouchWalletNamePage> {
  String walletName = '';

  @override
  Widget build(BuildContext context) {
    return KeyboardDismissWidget(
      child: Scaffold(
        appBar: baseAppBar(title: ID.stringTouchSetWalletName.tr),
        body: Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                ID.walletName.tr,
                style: styleSecond_14,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              TextFieldWidget(
                hintText: ID.stringTouchInputWalletNameHint.tr,
                maxLength: 12,
                autofocus: true,
                showClear: true,
                onValueChanged: (value) => walletName = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              ButtonWidget(
                text: ID.stringConfirm.tr,
                width: Get.width,
                onPressed: () async {
                  if (Get.isEmptyString(walletName)) {
                    Get.showToast(ID.stringTouchWalletNameToast.tr,
                        toastMode: ToastMode.waring);
                    return;
                  }

                  if (GetPlatform.isIOS) {
                    await Get.walletCore
                        .isSecureKeyboard(isSecureKeyboard: true);
                  }

                  TouchReadCardController controller =
                      Get.arguments[GetArgumentsKey.controller];
                  controller.cardModel?.newWalletName = walletName;

                  Get.toNamed(
                    AppRoutes.touchWalletPasswordPage,
                    arguments: {
                      GetArgumentsKey.controller: controller,
                      GetArgumentsKey.newTouchWalletMethod:
                          Get.arguments[GetArgumentsKey.newTouchWalletMethod],
                      GetArgumentsKey.targetCommand:
                          Get.arguments[GetArgumentsKey.targetCommand],
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
