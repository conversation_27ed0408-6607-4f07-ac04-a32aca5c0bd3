/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2025-03-24 12:59:15
 */
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/extension/string_to_hex.dart';

class TouchXpubModel {
  String? pubData;
  final int bip44Index;
  final String path;
  final int isXpub;
  final int addrNum;

  TouchXpubModel({
    this.pubData,
    required this.bip44Index,
    required this.path,
    required this.isXpub,
    required this.addrNum,
  });

  CoinType? get coinType => CoinBase.getCoinTypeBySlip44Id(bip44Index);

  List<int> pathIntList() {
    String pathStr = path;
    if (pathStr.contains('9006')) {
      pathStr = pathStr.replaceFirst('9006', '60');
    }
    return pathList(pathStr);
  }

  static List<int> pathList(String path) {
    List<int> data = [];
    String pathStr = path;
    List<String> pathList = pathStr.split('/');
    if (pathList.first == 'm') {
      pathList.removeAt(0);
    }
    int pathLength = pathList.length;
    data.addAll([pathLength]);

    int baseValue = DecimalUtils.toIntSafe('80000000'.hexToDecimal);
    String hexValue = '';
    int length = 2;
    for (int i = 0; i < pathList.length; i++) {
      String indexStr = pathList[i];
      if (indexStr.contains('\'')) {
        indexStr = indexStr.replaceAll('\'', '');
        int value = DecimalUtils.toIntSafe(indexStr);

        int resultValue = baseValue | value;
        hexValue = resultValue.toString().decimalToHexRaw;
      } else {
        hexValue = indexStr.padLeft(8, '0');
      }
      if (hexValue.length % 2 > 0) {
        length = 1;
      }
      for (int j = 0; j < hexValue.length; j += length) {
        String str = hexValue.substring(j, j + length);
        int indexData = DecimalUtils.toIntSafe(str.hexToDecimal);
        data.addAll([indexData]);
      }
    }
    return data;
  }

  factory TouchXpubModel.fromJson(Map<String, dynamic> json) => TouchXpubModel(
        bip44Index: json['bip44Index'] as int,
        path: json['path'] as String,
        isXpub: json['is_xpub'] as int,
        addrNum: json['addr_num'] as int,
        pubData: json['pub_data'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'pub_data': pubData,
        'bip44Index': bip44Index,
        'path': path,
        'is_xpub': isXpub,
        'addr_num': addrNum,
      };
}
