/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-06 17:51:14
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/material.dart';

class TouchReadButtonWidget extends StatelessWidget {
  final Function()? onPressed;
  final String? title;
  final EdgeInsetsGeometry? padding;
  const TouchReadButtonWidget(
      {super.key, this.onPressed, this.title, this.padding});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ??
          EdgeInsets.only(
            bottom: Get.getSafetyBottomPadding(),
            top: Get.setPaddingSize(16),
            left: Get.setPaddingSize(40),
            right: Get.setPaddingSize(40),
          ),
      child: ButtonWidget(
        text: title ?? ID.stringToucReadCard.tr,
        onPressed: onPressed,
      ),
    );
  }
}
