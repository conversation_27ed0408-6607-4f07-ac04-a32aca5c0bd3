/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-06 10:49:23
 */
/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-07-09 15:43:35
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class TouchMnemonicsPopWidget extends StatelessWidget {
  const TouchMnemonicsPopWidget({super.key});

  static void show() {
    Get.showAlertDialog(
      barrierDismissible: false,
      disableBack: false,
      child: const TouchMnemonicsPopWidget(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16),
        ),
        child: SizedBox(
          child: Material(
            borderRadius: BorderRadius.circular(12.0),
            color: Get.theme.bgColor,
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(24),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    ID.stringTouchSafeTitle.tr,
                    style: stylePrimary_16_m,
                  ),
                  SizedBox(height: Get.setPaddingSize(24)),
                  ImageWidget(
                    assetUrl: 'touch_pop',
                    height: Get.setImageSize(56),
                    width: Get.setImageSize(56),
                  ),
                  SizedBox(height: Get.setPaddingSize(24)),
                  SizedBox(
                    width: Get.width,
                    child: Text(
                      ID.stringTouchPopTip1.tr,
                      style: styleNarmal,
                    ),
                  ),
                  SizedBox(height: Get.setPaddingSize(24)),
                  SizedBox(
                    width: Get.width,
                    child: Text(
                      ID.stringTouchPopTip2.tr,
                      style: styleNarmal,
                    ),
                  ),
                  SizedBox(height: Get.setPaddingSize(32)),
                  ButtonWidget(
                    width: Get.width * 0.6,
                    text: ID.stringTouchPopOkTitle.tr,
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
