/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-07-08 13:55:25
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:flutter/material.dart';

class TouchBottomButtonWidget extends StatelessWidget {
  final String? text;
  final Function()? onPressed;
  const TouchBottomButtonWidget({super.key, this.text, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: ButtonWidget(
        text: text ?? ID.stringTouchNextTitle.tr,
        onPressed: onPressed,
      ),
    );
  }
}
