/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-10 10:54:03
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class TouchMnemonicsTypeWidget extends StatefulWidget {
  final Function(MnemonicsType type)? onTap;
  const TouchMnemonicsTypeWidget({super.key, this.onTap});

  @override
  State<TouchMnemonicsTypeWidget> createState() => _TouchMnemonicsTypeState();
}

class _TouchMnemonicsTypeState extends State<TouchMnemonicsTypeWidget> {
  MnemonicsType type = MnemonicsType.english;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        _typeWidget(
          title: ID.stringTouchEnglishTitle.tr,
          type: MnemonicsType.english,
        ),
        _typeWidget(
          title: ID.stringTouchNumberTitle.tr,
          type: MnemonicsType.number,
        ),
        _typeWidget(
          title: ID.stringTouchChineseTitle.tr,
          type: MnemonicsType.chinese,
        ),
      ],
    );
  }

  Flexible _typeWidget({
    required String title,
    required MnemonicsType type,
  }) {
    return Flexible(
      flex: 1,
      child: GestureDetector(
        onTap: () {
          if (widget.onTap != null) {
            widget.onTap!(type);
          }
          setState(() {
            this.type = type;
          });
        },
        behavior: HitTestBehavior.translucent,
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(4),
            vertical: Get.setPaddingSize(10),
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ImageWidget(
                  assetUrl: type == this.type
                      ? 'touch_selected'
                      : 'touch_no_selected',
                  height: Get.setImageSize(20),
                  width: Get.setImageSize(20),
                ),
                SizedBox(width: Get.setPaddingSize(4)),
                Flexible(
                  child: Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(16),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
