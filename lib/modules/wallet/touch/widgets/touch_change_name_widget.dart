import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';

class TouchChangeWalletNameWidget extends StatelessWidget {
  final String? walletName;
  final Function(String value) callback;
  const TouchChangeWalletNameWidget(
      {super.key, this.walletName, required this.callback});

  static void show(String walletName, Function(String value) callback) {
    Get.showAlertDialog(
      barrierDismissible: false,
      disableBack: false,
      child: TouchChangeWalletNameWidget(
        walletName: walletName,
        callback: callback,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    String text = walletName ?? '';
    return Center(
      child: Padding(
        padding: EdgeInsets.only(bottom: Get.setPaddingSize(120)),
        child: SizedBox(
          width: Get.width * 0.8,
          child: Material(
            borderRadius: BorderRadius.circular(12.0),
            color: Get.theme.bgColor,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: Get.setPaddingSize(16),
                    left: Get.setPaddingSize(16),
                    right: Get.setPaddingSize(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: Get.width,
                        child: Text(
                          ID.stringSetWalletName.tr,
                          textAlign: TextAlign.center,
                          style: stylePrimary_16_m,
                        ),
                      ),
                      SizedBox(height: Get.setPaddingSize(16)),
                      SizedBox(
                        height: Get.setHeight(44),
                        child: TextFieldWidget(
                          maxLength: 12,
                          autofocus: true,
                          showClear: true,
                          controller: TextEditingController(text: walletName),
                          onValueChanged: (value) => text = value,
                        ),
                      ),
                      SizedBox(height: Get.setPaddingSize(4)),
                      Text(
                        ID.stringWalletNameMax12.tr,
                        style: TextStyle(
                          color: Get.theme.textSecondary,
                          fontSize: Get.setFontSize(12),
                          fontWeight: FontWeightX.regular,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: Get.setPaddingSize(16)),
                const DividerWidget(),
                SizedBox(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      _buttonWidget(
                        title: ID.stringCancel.tr,
                        color: Get.theme.textSecondary,
                        onTap: () => Get.back(),
                      ),
                      Container(
                        width: 1,
                        height: Get.setHeight(50),
                        color: Get.theme.colorECECEC,
                      ),
                      _buttonWidget(
                        title: ID.stringConfirm.tr,
                        color: Get.theme.textPrimary,
                        onTap: () => _okAction(text),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _okAction(String text) {
    if (Get.isEmptyString(text)) {
      Get.showToast(ID.stringWalletNameNoEmpty.tr, toastMode: ToastMode.waring);
      Get.back();
      return;
    }

    if (text.length > 12) {
      Get.showToast(ID.stringWalletNameMax12.tr, toastMode: ToastMode.waring);
      Get.back();
      return;
    }

    if (walletName == text) {
      Get.showToast(ID.stringNewNameSameOldName.tr,
          toastMode: ToastMode.waring);
      Get.back();
      return;
    }

    Get.back();

    callback(text);
  }

  Expanded _buttonWidget({
    required String title,
    required Color color,
    GestureTapCallback? onTap,
  }) =>
      Expanded(
        child: GestureDetector(
          onTap: onTap,
          behavior: HitTestBehavior.translucent,
          child: HighLightInkWell(
            child: SizedBox(
              height: Get.setHeight(50),
              child: Center(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontSize: Get.setFontSize(16),
                    fontWeight: FontWeightX.medium,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}
