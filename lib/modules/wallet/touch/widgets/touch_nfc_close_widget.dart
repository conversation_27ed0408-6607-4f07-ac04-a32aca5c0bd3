import 'package:app_settings/app_settings.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class TouchNFCCloseWidget extends StatelessWidget {
  const TouchNFCCloseWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(
            height: Get.setPaddingSize(40),
            width: Get.width,
          ),
          Padding(
            padding: EdgeInsets.only(bottom: Get.setPaddingSize(12)),
            child: ImageWidget(
              assetUrl: 'touch',
              width: Get.setImageSize(180),
              height: Get.setImageSize(112),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: Get.setPaddingSize(24)),
            child: Text('Touch',
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(16),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.medium,
                )),
          ),
          ImageWidget(
            assetUrl: 'nfc_close',
            width: Get.setImageSize(200),
            height: Get.setImageSize(150),
          ),
          SizedBox(height: Get.setPaddingSize(12)),
          Text(
            GetPlatform.isAndroid
                ? ID.stringNfcCloseAndroidTip.tr
                : ID.stringNfcCloseIOSTip.tr,
            style: TextStyle(
              color: Get.theme.colorF44D4D,
              fontSize: Get.setFontSize(16),
            ),
          ),
          SizedBox(height: Get.setPaddingSize(24)),
          ButtonWidget(
            text: ID.stringSetting.tr,
            width: Get.width * 0.8,
            onPressed: () async {
              if (GetPlatform.isAndroid) {
                await AppSettings.openAppSettings(
                    type: AppSettingsType.nfc, asAnotherTask: true);
              } else {
                await AppSettings.openAppSettings(
                    type: AppSettingsType.settings);
                Get.back();
              }
            },
          ),
        ],
      ),
    );
  }
}
