/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-13 09:30:24
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class TouchReadCardWidget extends StatelessWidget {
  final bool isBind;
  const TouchReadCardWidget({super.key, this.isBind = false});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: Get.setPaddingSize(40)),
          Visibility(
            visible: isBind,
            child: Padding(
              padding: EdgeInsets.only(bottom: Get.setPaddingSize(12)),
              child: ImageWidget(
                assetUrl: 'touch',
                width: Get.setImageSize(180),
                height: Get.setImageSize(112),
              ),
            ),
          ),
          Visibility(
            visible: isBind,
            child: Padding(
              padding: EdgeInsets.only(bottom: Get.setPaddingSize(24)),
              child: Text('Touch',
                  style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(16),
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.medium,
                  )),
            ),
          ),
          ImageWidget(
            assetUrl: GetPlatform.isIOS ? 'ios_touch' : 'android_touch',
            width: Get.setImageSize(280),
            imageformat: '.gif',
            height: Get.setImageSize(161),
          ),
          SizedBox(height: Get.setPaddingSize(12)),
          Text(
            ID.stringTouchPhone.tr,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(16),
              fontFamily: Get.setNumberFontFamily(),
              fontWeight: FontWeightX.medium,
            ),
          ),
          SizedBox(height: Get.setPaddingSize(24)),
          const DividerWidget(),
          SizedBox(height: Get.setPaddingSize(24)),
          Text(
            ID.stringTouchReadTips.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
            ),
          ),
        ],
      ),
    );
  }
}
