import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_android_window_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';

class TouchAndroidWindowWidget extends StatelessWidget {
  const TouchAndroidWindowWidget({super.key});

  TouchAndroidWindowController get controller {
    return Get.find<TouchAndroidWindowController>();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(8)),
          child: SizedBox(
            width: Get.width,
            child: Material(
              borderRadius: BorderRadius.circular(20),
              color: Get.theme.bgColor,
              child: Obx(() => Column(
                    children: [
                      SizedBox(height: Get.setPaddingSize(36)),
                      Text(
                        ID.stringNfcReading.tr,
                        style: TextStyle(
                          fontSize: Get.setFontSize(24),
                          color: Get.theme.textPrimary,
                          fontWeight: FontWeightX.medium,
                        ),
                      ),
                      SizedBox(height: Get.setPaddingSize(8)),
                      Text(
                        controller.message.value,
                        style: TextStyle(
                          color: Get.theme.textPrimary,
                          fontSize: Get.setFontSize(16),
                        ),
                      ),
                      SizedBox(height: Get.setPaddingSize(24)),
                      ImageWidget(
                        assetUrl: controller.statusImage,
                        width: Get.setImageSize(200),
                        imageformat: controller.imageformat,
                        height: Get.setImageSize(150),
                      ),
                      SizedBox(height: Get.setPaddingSize(36)),
                      OutlinedButtonWidget(
                          width: Get.width * 0.8,
                          text: ID.stringCancel.tr,
                          onPressed: () {
                            Get.back();
                            FlutterNfcKit.finish();
                          }),
                      SizedBox(height: Get.setPaddingSize(36)),
                    ],
                  )),
            ),
          ),
        )
      ],
    );
  }
}
