/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-19 13:30:51
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/material.dart';

class TouchPasswordWidget extends StatelessWidget {
  final TouchReadCardController controller;
  final Function(String text)? callback;
  const TouchPasswordWidget(
      {super.key, required this.controller, this.callback});

  static void show(
      {required TouchReadCardController controller,
      Function(String text)? callback}) {
    Get.showAlertDialog(
      barrierDismissible: false,
      disableBack: false,
      child: TouchPasswordWidget(
        controller: controller,
        callback: callback,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    String text = '';
    return Center(
      child: Padding(
        padding: EdgeInsets.only(bottom: Get.setPaddingSize(80)),
        child: SizedBox(
          width: Get.width * 0.8,
          child: Material(
            borderRadius: BorderRadius.circular(12.0),
            color: Get.theme.bgColor,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: Get.setPaddingSize(16),
                    left: Get.setPaddingSize(16),
                    right: Get.setPaddingSize(16),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        ID.stringTouchVerifyPswTitle.tr,
                        style: stylePrimary_16_m,
                      ),
                      SizedBox(height: Get.setPaddingSize(16)),
                      TextFieldWidget(
                        hintText: ID.stringTouchPsw.tr,
                        keyboardType: TextInputType.visiblePassword,
                        obscureText: true,
                        autofocus: true,
                        showEyes: true,
                        showClear: true,
                        onValueChanged: (value) => text = value,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: Get.setPaddingSize(16)),
                const DividerWidget(),
                SizedBox(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      _buttonWidget(
                        title: ID.stringCancel.tr,
                        color: Get.theme.textSecondary,
                        onTap: () => Get.back(),
                      ),
                      Container(
                        width: 1,
                        height: Get.setHeight(50),
                        color: Get.theme.colorECECEC,
                      ),
                      _buttonWidget(
                        title: ID.stringConfirm.tr,
                        color: Get.theme.textPrimary,
                        onTap: () {
                          if (text.length < 6) {
                            Get.showToast(ID.stringTouchImInputCorrectPsw.tr,
                                toastMode: ToastMode.waring);
                            return;
                          }

                          if (callback != null) {
                            callback!(text);
                          }
                          Get.back();
                        },
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Expanded _buttonWidget({
    required String title,
    required Color color,
    GestureTapCallback? onTap,
  }) =>
      Expanded(
        child: GestureDetector(
          onTap: onTap,
          behavior: HitTestBehavior.translucent,
          child: HighLightInkWell(
            child: SizedBox(
              height: Get.setHeight(50),
              child: Center(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontSize: Get.setFontSize(16),
                    fontWeight: FontWeightX.medium,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}
