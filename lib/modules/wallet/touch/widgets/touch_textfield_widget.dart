/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-10 10:56:28
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_import_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_mnemonics_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:flutter/material.dart';

class TouchTextFieldWidget extends BaseStatelessWidget<TouchImportController> {
  final int index;
  final double width;
  final double height;
  final MnemonicsType type;
  final TouchMnemonicsModel model;
  final Function(String value) onValueChanged;
  const TouchTextFieldWidget({
    super.key,
    required this.index,
    required this.width,
    required this.height,
    required this.type,
    required this.model,
    required this.onValueChanged,
  });

  OptionsViewOpenDirection get _optionsViewOpenDirection =>
      index < 6 ? OptionsViewOpenDirection.down : OptionsViewOpenDirection.up;

  Alignment get _alignment =>
      _optionsViewOpenDirection == OptionsViewOpenDirection.down
          ? Alignment.topLeft
          : Alignment.bottomLeft;

  @override
  Widget build(BuildContext context) {
    return Autocomplete<String>(
      optionsBuilder: _optionsBuilder(),
      optionsViewBuilder: _optionsViewBuilder(),
      optionsViewOpenDirection: _optionsViewOpenDirection,
      onSelected: (value) {
        onValueChanged(value);
        if (index + 1 < controller.focusNodeList.length) {
          controller.focusNodeList[index + 1].requestFocus();
        } else {
          KeyboardUtils.hideKeyboard(context);
        }
      },
      fieldViewBuilder: (BuildContext context,
          TextEditingController textEditingController,
          FocusNode focusNode,
          VoidCallback onFieldSubmitted) {
        controller.focusNodeList[index] = focusNode;
        return _textfield(textEditingController, focusNode, onFieldSubmitted);
      },
    );
  }

  TextFormField _textfield(TextEditingController textEditingController,
          FocusNode focusNode, VoidCallback onFieldSubmitted) =>
      TextFormField(
        key: Key('TextFormField-$index-${type.index}'),
        textAlign: TextAlign.center,
        maxLength: type == MnemonicsType.number ? 4 : null,
        controller: textEditingController,
        focusNode: focusNode,
        keyboardType: type == MnemonicsType.chinese
            ? null
            : TextInputType.visiblePassword,
        onChanged: (value) {
          onValueChanged(value);
        },
        style: TextStyle(
          fontSize: Get.setFontSize(16),
          color:
              model.isCorrect ? Get.theme.textPrimary : Get.theme.colorF44D4D,
          fontFamily: Get.setFontFamily(),
          fontWeight: FontWeightX.medium,
        ),
        decoration: InputDecoration(
          counterText: '',
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.transparent,
              width: Get.setWidth(0),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Colors.transparent,
              width: Get.setWidth(0),
            ),
          ),
          border: const OutlineInputBorder(),
        ),
        onFieldSubmitted: (String value) {
          onFieldSubmitted();
        },
      );

  Iterable<String> Function(TextEditingValue textEditingValue)
      _optionsBuilder() => (TextEditingValue textEditingValue) {
            String value = textEditingValue.text;
            if (Get.isEmptyString(value) || value.length < 2) {
              return const Iterable<String>.empty();
            }
            return controller.phraseList
                .where((option) => option.startsWith(value));
          };

  Align Function(
      BuildContext context,
      AutocompleteOnSelected<String> onSelected,
      Iterable<String> options) _optionsViewBuilder() => (BuildContext context,
          AutocompleteOnSelected<String> onSelected, Iterable<String> options) {
        return Align(
          alignment: _alignment,
          child: Material(
            elevation: 4.0,
            color: Get.theme.bgColor,
            borderRadius: BorderRadius.circular(4),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: width,
                maxHeight: height * 3.5,
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final option = options.elementAt(index);
                  return HighLightInkWell(
                    onTap: () => onSelected(option),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: Get.setPaddingSize(8),
                          horizontal: Get.setPaddingSize(4)),
                      child: Text(
                        option,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: stylePrimary_16_m,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      };
}
