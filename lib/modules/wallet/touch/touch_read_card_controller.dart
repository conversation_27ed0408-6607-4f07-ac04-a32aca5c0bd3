/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-10 16:28:52
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_android_window_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_command_controller.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_model.dart';
import 'package:coinbag/modules/wallet/touch/model/touch_xpub_model.dart';
import 'package:coinbag/modules/wallet/touch/widgets/Touch_password_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:flutter/services.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:wallet_core/wallet/cold/touch/touch_command.dart';

class TouchReadCardController extends BaseController {
  String title = ID.bindWallet.tr;

  String password = '';
  // 修改密码
  String newPassword = '';
  TouchModel? cardModel;

  /// NFC 是否打开
  RxBool nfcAvailable = true.obs;

  /// 目标
  late TouchCommand targetCommand;
  TouchModel? targetModel;

  /// 获取第几个公钥
  int xpubIndex = 0;
  List<TouchXpubModel> xpubModels = [];

  /// 钱包
  WalletModel? walletModel;
  String? walletName;

  String sw1 = CommonConstant.nfcSw1_90;

  @override
  void onInit() {
    super.onInit();

    Map? arg = Get.arguments;
    if (arg != null) {
      if (arg.keys.contains(GetArgumentsKey.targetCommand)) {
        targetCommand = arg[GetArgumentsKey.targetCommand];
      }
      if (arg.keys.contains(GetArgumentsKey.targetTouchModel)) {
        targetModel = arg[GetArgumentsKey.targetTouchModel];
      }
      if (arg.keys.contains(GetArgumentsKey.walletModel)) {
        walletModel = arg[GetArgumentsKey.walletModel];
      }
      if (arg.keys.contains(GetArgumentsKey.walletName)) {
        walletName = arg[GetArgumentsKey.walletName];
      }
      if (arg.keys.contains(GetArgumentsKey.password)) {
        password = arg[GetArgumentsKey.password];
      }
      if (arg.keys.contains(GetArgumentsKey.newPassword)) {
        newPassword = arg[GetArgumentsKey.newPassword];
      }
    }

    _updateNavgationTitle();
  }

  @override
  void onReady() async {
    super.onReady();

    if (GetPlatform.isIOS) {
      await Get.walletCore.isSecureKeyboard(isSecureKeyboard: true);
    }

    if ((targetCommand == TouchCommand.reset ||
            targetCommand == TouchCommand.backup ||
            targetCommand == TouchCommand.changeName ||
            targetCommand == TouchCommand.signature) &&
        Get.isEmptyString(password)) {
      Future.delayed(const Duration(milliseconds: 200)).then((value) =>
          showPasswordWidget(
              command: TouchCommand.readCard, nextCommand: targetCommand));
      return;
    }

    startSession(
      command: TouchCommand.readCard,
      nextCommand: targetCommand,
    );
  }

  Future<void> startSession({
    required TouchCommand command,
    required TouchCommand nextCommand,
    String? alertMessage,
  }) async {
    xpubIndex = 0;
    xpubModels = [];

    if (Get.isEmptyString(alertMessage)) {
      alertMessage = ID.stringTouchPhone.tr;
    }

    var availability = await FlutterNfcKit.nfcAvailability;
    // 安卓判断NFC是否打开
    if (GetPlatform.isAndroid) {
      nfcAvailable.value = availability == NFCAvailability.available;
    }

    if (availability == NFCAvailability.available) {
      await FlutterNfcKit.finish();

      try {
        if (GetPlatform.isAndroid) {
          AndroidNfcKit.read();
        }
        NFCTag tag = await FlutterNfcKit.poll(
          readIso15693: false,
          iosAlertMessage: alertMessage!,
        );
        if (tag.type == NFCTagType.iso7816) {
          sendCommand(
            command: command,
            nextCommand: nextCommand,
          );
        }
      } catch (e) {
        PlatformException exception = e as PlatformException;

        if (exception.code == '408') {
          Log.logPrint('超时');
          if (GetPlatform.isAndroid) {
            nfcKitFinish(isTimeOut: true);
          }
        } else if (exception.code == '500') {
          if (exception.details == 'NFC radio is disabled') {
            Log.logPrint('NFC 开关关闭');
            nfcAvailable.value = false;
          }
        } else {
          String msg = 'code=${exception.code}, msg:${exception.message}';
          nfcKitFinish(errorMessage: msg);
        }
        Log.logPrint('error message: ${exception.details}');
      }
    } else {
      Get.showToast(ID.stringTouchNONfc.tr, toastMode: ToastMode.failed);
    }
  }

  @override
  void onResumed() async {
    final nfcAvailability = await FlutterNfcKit.nfcAvailability;
    if (GetPlatform.isAndroid) {
      nfcAvailable.value = nfcAvailability == NFCAvailability.available;
    }
  }

  dynamic showPasswordWidget({
    required TouchCommand nextCommand,
    TouchCommand command = TouchCommand.password,
  }) =>
      TouchPasswordWidget.show(
        controller: this,
        callback: (text) {
          password = text;

          startSession(
            command: command,
            nextCommand: nextCommand,
          );
        },
      );

  void _updateNavgationTitle() {
    if (targetCommand == TouchCommand.bind) {
      title = ID.bindWallet.tr;
    } else if (targetCommand == TouchCommand.create) {
      title = ID.stringCreateWalletTitle.tr;
    } else if (targetCommand == TouchCommand.import) {
      title = ID.stringImportWalletTitle.tr;
    } else if (targetCommand == TouchCommand.backup ||
        targetCommand == TouchCommand.backupEnd) {
      title = ID.stringBackupMnemonic.tr;
    } else if (targetCommand == TouchCommand.changePsw) {
      title = ID.stringChangePswTitle.tr;
    } else if (targetCommand == TouchCommand.reset) {
      title = ID.stringResetCardTitle.tr;
    } else if (targetCommand == TouchCommand.changeName) {
      title = ID.stringChangeWalletNameTitle.tr;
    } else if (targetCommand == TouchCommand.entropyStatus) {
      title = ID.stringVerifybackupTitle.tr;
    }
  }

  Future<void> xpubDataCommand(
    TouchCommand command,
    TouchCommand nextCommand,
  ) async {}

  void xpubCommandResult(TouchXpubModel? xpubModel, Uint8List resultList) {}

  /// 交易相关
  int p1Parameter(TouchCommand command) => 0x00;
  int p2Parameter(TouchCommand command) => 0x00;
  List<int> signatureLc(TouchCommand command) => [];

  /// 签名结果
  void signatureResponse({
    required Uint8List responseData,
    required TouchCommand command,
    required TouchCommand nextCommand,
  }) {}

  Future<void> signatureCommand() async {}

  Future<void> nfcKitFinish(
      {String? successMessage,
      String? errorMessage,
      bool isTimeOut = false}) async {
    await FlutterNfcKit.finish(
        iosAlertMessage: successMessage, iosErrorMessage: errorMessage);

    if (GetPlatform.isAndroid) {
      await AndroidNfcKit.finish(
          alertMessage: successMessage,
          errorMessage: errorMessage,
          isTimeOut: isTimeOut);
    }
  }

  Future<void> nfcKitMessage(String message) async {
    if (GetPlatform.isAndroid) {
      AndroidNfcKit.setAlertMessage(message);
    } else {
      await FlutterNfcKit.setIosAlertMessage(message);
    }
  }

  @override
  void onClose() async {
    super.onClose();

    if (GetPlatform.isIOS) {
      await Get.walletCore.isSecureKeyboard(isSecureKeyboard: false);
    }
  }

  @override
  void loadData() {}
}

class TouchReadCardBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TouchReadCardController());
  }
}
