import 'dart:convert';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/constant/common_constant.dart';
import 'package:wallet_core/wallet/wallet.dart';

enum VerifyDeviceStatus { loading, fail, success }

class VerifyDeviceController extends BaseController<ApiService> {
  String? scanResult;
  int? cmd;
  String? deviceId;
  Wallet? wallet;
  String? firstQueryTime;
  Rx<VerifyDeviceStatus> verifyStatus = VerifyDeviceStatus.loading.obs;

  @override
  void onInit() {
    super.onInit();
    final arg = Get.arguments;
    scanResult = arg?[GetArgumentsKey.scanResult];
    cmd = arg?[GetArgumentsKey.cmd];

    if (!Get.isEmptyString(scanResult)) {
      Map map = jsonDecode(scanResult!);
      deviceId = map['device_id'];
      int? batch = map[GetArgumentsKey.batch];
      if (batch != null) {
        wallet = Wallet.getWalletByBatch(batch);
      }
    }
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {
    httpRequest(
        api.verifyDeviceId(RequestParams()
            .put(GetArgumentsKey.deviceId, deviceId ?? '')
            .getRequestBody()),
        handleSuccess: false,
        handleError: false, (value) {
      // 200
      if (value.code == APIConstant.responseCode) {
        int isExist = value.data[APIConstant.isExist] ?? 0;
        firstQueryTime = value.data['firstQueryTime'];
        if (cmd == CmdCode.authenticateDeviceID) {
          String challenge = value.data[APIConstant.challenge] ?? '';
          String signature = value.data[APIConstant.signature] ?? '';
          if (isExist != 0) {
            final qrData = Wallet.getVerifyDeviceIdData(
                deviceId: deviceId ?? '',
                challenge: challenge,
                signature: signature);
            // 生成二维码
            Get.offNamed(AppRoutes.walletQRCodePage, arguments: {
              GetArgumentsKey.wallet: wallet,
              GetArgumentsKey.qrData: qrData,
              GetArgumentsKey.qrType: QRCodeType.verifyDivice,
            });
          } else {
            verifyStatus.value = VerifyDeviceStatus.fail;
          }
        } else {
          if (isExist != 0) {
            // 成功
            verifyStatus.value = VerifyDeviceStatus.success;
          } else {
            // 失败
            verifyStatus.value = VerifyDeviceStatus.fail;
          }
        }
      } else {
        Get.showToast(value.message, toastMode: ToastMode.failed);
        Get.back();
      }
    }, error: (_) {
      Get.dismissLoadingDialog();
      Get.showToast(ID.p4verifyfail.tr, toastMode: ToastMode.failed);
      Get.back();
    });
  }

  void backToMain() =>
      Get.until((route) => (route.settings.name == AppRoutes.mainPage));
}

class VerifyDeviceBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => VerifyDeviceController());
  }
}
