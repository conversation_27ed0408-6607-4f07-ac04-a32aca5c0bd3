/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-06 17:14:32
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/verify/verify_device_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class VerifyDevicePage extends BaseStatelessWidget<VerifyDeviceController> {
  const VerifyDevicePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(hideLeading: true, actionWidget: _actionWidget()),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            top: Get.setPaddingSize(40),
            bottom: Get.setPaddingSize(Get.getSafetyBottomPadding())),
        child: Obx(() => SizedBox(
              width: Get.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ImageWidget(
                    assetUrl: _imageName(),
                    width: Get.setImageSize(84),
                    height: Get.setImageSize(84),
                  ),
                  SizedBox(
                    height: Get.setPaddingSize(24),
                  ),
                  _titleWidget(),
                  SizedBox(
                    height: Get.setPaddingSize(8),
                  ),
                  _titleSubWidget(),
                  _sunccessWidget(),
                  SizedBox(
                    height: Get.setPaddingSize(36),
                  ),
                  _buttonWidget(),
                ],
              ),
            )),
      ),
    );
  }

  Widget _buttonWidget() {
    if (controller.verifyStatus.value == VerifyDeviceStatus.loading) {
      return const SizedBox.shrink();
    }
    return OutlinedButtonWidget(
      text: ID.goBackHome.tr,
      width: Get.setWidth(240),
      onPressed: () => controller.backToMain(),
    );
  }

  Widget _sunccessWidget() {
    if (controller.verifyStatus.value != VerifyDeviceStatus.success) {
      return const SizedBox.shrink();
    }

    if (Get.isEmptyString(controller.firstQueryTime)) {
      return DividerWidget(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(20)),
      );
    }

    return Column(
      children: [
        DividerWidget(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(20)),
        ),
        Text(
          ID.verifySnTime.tr,
          style: styleSecond_14,
        ),
        SizedBox(
          height: Get.setPaddingSize(4),
        ),
        Text(
          controller.firstQueryTime!,
          style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(16),
              fontWeight: FontWeightX.semibold,
              fontFamily: Get.setNumberFontFamily()),
        )
      ],
    );
  }

  Widget _titleSubWidget() {
    if (controller.verifyStatus.value == VerifyDeviceStatus.loading) {
      return const SizedBox.shrink();
    }

    String title = '';
    if (controller.verifyStatus.value == VerifyDeviceStatus.success) {
      title = ID.verifySuccessSubTitle.tr;
    } else if (controller.verifyStatus.value == VerifyDeviceStatus.fail) {
      title = ID.verifyFailSubTitle.tr;
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: Get.setFontSize(14),
        color: Get.theme.textSecondary,
      ),
    );
  }

  Widget _titleWidget() {
    if (controller.verifyStatus.value == VerifyDeviceStatus.loading) {
      return const SizedBox.shrink();
    }

    String title = '';
    if (controller.verifyStatus.value == VerifyDeviceStatus.success) {
      title = ID.verifySuccessTitle.tr;
    } else if (controller.verifyStatus.value == VerifyDeviceStatus.fail) {
      title = ID.verifyFailTitle.tr;
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: Get.setFontSize(20),
        fontWeight: FontWeightX.medium,
        color: controller.verifyStatus.value == VerifyDeviceStatus.success
            ? Get.theme.color02B58A
            : Get.theme.colorF44D4D,
      ),
    );
  }

  List<Padding> _actionWidget() => [
        Padding(
          padding: EdgeInsets.only(right: Get.setPaddingSize(12)),
          child: IconButton(
              onPressed: () => controller.backToMain(),
              icon: ImageWidget(
                assetUrl: 'icon_quite',
                cacheRawData: true,
                shape: BoxShape.circle,
                width: Get.setImageSize(28),
                height: Get.setImageSize(28),
              )),
        )
      ];

  String _imageName() {
    if (controller.verifyStatus.value == VerifyDeviceStatus.success) {
      return 'icon_success';
    } else if (controller.verifyStatus.value == VerifyDeviceStatus.fail) {
      return 'icon_failed';
    }
    return 'icon_sending';
  }
}
