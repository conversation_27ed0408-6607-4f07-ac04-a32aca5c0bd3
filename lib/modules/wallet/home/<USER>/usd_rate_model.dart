/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-12 13:04:56
 * @LastEditTime: 2024-03-20 14:26:53
 */
import 'package:coinbag/database/storage/models/storage_base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'usd_rate_model.g.dart';

@JsonSerializable()
class UsdRateModel extends BaseStorageModel {
  String? btc;
  String? usd;
  String? usdt;
  String? cny;

  UsdRateModel({
    this.btc,
    this.usd,
    this.usdt,
    this.cny,
  });

  factory UsdRateModel.fromJson(Map<String, dynamic> json) =>
      _$UsdRateModelFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$UsdRateModelToJson(this);
}
