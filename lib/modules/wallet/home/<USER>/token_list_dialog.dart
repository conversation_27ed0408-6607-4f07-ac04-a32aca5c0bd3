/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-11-01 10:46:53
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_coin_model.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class TokenListDialog extends BaseStatelessWidget {
  final WalletController? walletController;
  final WalletAction? action;
  const TokenListDialog({super.key, this.walletController, this.action});

  void showBottomSheet() {
    Get.showBottomSheet(
        hideHeader: true,
        fullScreenBodyWidget: this,
        paddingBottom: 0,
        bottomWidget: const BottomCancelWidget());
  }

  @override
  build(BuildContext context) {
    var dataList = walletController!.getTokenList(action!);
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
      child: ListView.builder(
          itemCount: dataList.length,
          padding: EdgeInsets.zero,
          itemBuilder: (_, index) =>
              _itemBuilder(context, walletController!, dataList[index])),
    );
  }

  HighLightInkWell _itemBuilder(
      BuildContext context, WalletController controller, HomeCoinModel model) {
    return HighLightInkWell(
        onTap: () {
          Get.back();
          model.toNavigate(context, action!, controller);
        },
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setHeight(14), horizontal: Get.setWidth(16)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SymbolWidget(coinModel: model.coinModel),
              SizedBox(
                width: Get.setWidth(12),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          model.coinModel.symbol ?? CommonConstant.emptyAsstes,
                          style: styleHomeSymbol,
                        ),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          model.coinModel.chainName ?? "",
                          style: styleHomeRateSymbol,
                        ),
                        SizedBox(
                          width: Get.setWidth(10),
                        ),
                        Text(
                          model.getTokenBalance(),
                          style: styleHomeRateSymbol,
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
