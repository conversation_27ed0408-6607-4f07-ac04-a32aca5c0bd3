/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-12-10 10:00:37
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_coin_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_token_search_empty.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class HomeTokenListWidget extends BaseStatelessWidget<WalletController> {
  const HomeTokenListWidget({super.key});

  @override
  build(BuildContext context) {
    return GetBuilder<WalletController>(
        id: GetKey.walletTokenList,
        builder: (controller) {
          return controller.obx((state) => _bodyWidget(context),
              onLoading: controller.firstRefresh
                  ? const HomeTokeListSkeletonWidget()
                  : _bodyWidget(context),
              onError: (error) => _bodyWidget(context),
              onEmpty: const HomeTokenSearchEmptyWidget());
        });
  }

  RefreshWidget<WalletController> _bodyWidget(BuildContext context) =>
      RefreshWidget<WalletController>(
          enablePullDown: true,
          enablePullUp: false,
          refreshController: controller.refreshController,
          child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              controller: controller.scrollController,
              slivers: [
                SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                  return (controller.dataList.isNotEmpty)
                      ? _itemBuilder(
                          context, controller, controller.dataList[index])
                      : const SizedBox.shrink();
                }, childCount: controller.dataList.length))
              ]));

  HighLightInkWell _itemBuilder(
      BuildContext context, WalletController controller, HomeCoinModel model) {
    return HighLightInkWell(
        key: Key('walelt_token_item_${model.coinModel.symbol ?? ""}'),
        onTap: () => model.toNavigate(context, WalletAction.asstes, controller),
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setPaddingSize(14),
              horizontal: Get.setPaddingSize(16)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SymbolWidget(
                coinModel: model.coinModel,
              ),
              SizedBox(
                width: Get.setPaddingSize(12),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          model.coinModel.symbol ?? CommonConstant.emptyAsstes,
                          style: styleHomeSymbol,
                        ),
                        Visibility(
                          visible:
                              model.isShowChainName() && controller.isAllChains,
                          child: Container(
                            margin:
                                EdgeInsets.only(left: Get.setPaddingSize(6)),
                            padding: EdgeInsets.symmetric(
                              vertical: Get.setPaddingSize(1),
                              horizontal: Get.setPaddingSize(5),
                            ),
                            decoration: ShapeDecoration(
                              color: Get.theme.colorF3F3F5,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(Get.setRadius(5)),
                              ),
                            ),
                            child: Text(model.getChainName(),
                                style: TextStyle(
                                    color: Get.theme.textSecondary,
                                    fontSize: Get.setFontSize(12),
                                    fontWeight: FontWeightX.regular,
                                    fontFamily: Get.setNumberFontFamily())),
                          ),
                        ),
                        Visibility(
                          visible: !model
                              .isNoBindWallet(controller.eosPublicekey ?? ""),
                          child: Expanded(
                            child: Text(
                              model.getBalance(
                                  hideAssets: controller.hideAssets),
                              textAlign: TextAlign.end,
                              style: styleHomeSymbol,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          model.getSymbolUnitPriceValue()!,
                          style: styleHomeRateSymbol,
                        ),
                        Visibility(
                          visible: !model
                              .isNoBindWallet(controller.eosPublicekey ?? ""),
                          child: Expanded(
                              child: Text(
                            model.getSymbolPrice(
                                hideAssets: controller.hideAssets),
                            textAlign: TextAlign.end,
                            style: styleHomeRateSymbol,
                          )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: model.isNoBindWallet(controller.eosPublicekey ?? ""),
                child: Expanded(
                    child: Text(
                  "${ID.stringBind.tr} >",
                  textAlign: TextAlign.end,
                  style: TextStyle(
                    color: Get.theme.textTertiary,
                    fontSize: Get.setFontSize(14),
                    fontWeight: FontWeightX.regular,
                  ),
                )),
              ),
            ],
          ),
        ));
  }
}
