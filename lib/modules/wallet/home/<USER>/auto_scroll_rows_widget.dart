/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-11-04 10:51:13
 */
import 'dart:async';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';

class AutoScrollRowsWidget extends StatefulWidget {
  const AutoScrollRowsWidget({super.key});

  @override
  AutoScrollRowsState createState() => AutoScrollRowsState();
}

class AutoScrollRowsState extends State<AutoScrollRowsWidget> {
  final ScrollController _scrollControllerTop = ScrollController();
  final ScrollController _scrollControllerBottom = ScrollController();
  late Timer _timerTop;
  late Timer _timerBottom;
  List<CoinType> supportedChains = UltraPlusWallet()
      .supportedChains
      .where((chain) => !chain.isTestNet && chain is! AllChain) // 移除测试币和All
      .toList();
  @override
  void initState() {
    super.initState();

    // 定时器控制上面Row缓慢向左滚动
    _timerTop = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (_scrollControllerTop.hasClients) {
        double maxScrollExtent = _scrollControllerTop.position.maxScrollExtent;
        double currentScrollPosition = _scrollControllerTop.position.pixels;

        double targetScrollPosition = currentScrollPosition + 1; // 缓慢向左滚动

        if (targetScrollPosition >= maxScrollExtent) {
          targetScrollPosition = 0;
        }

        _scrollControllerTop.jumpTo(targetScrollPosition);
      }
    });

    // 定时器控制下面Row缓慢向右滚动
    _timerBottom = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (_scrollControllerBottom.hasClients) {
        double maxScrollExtent =
            _scrollControllerBottom.position.maxScrollExtent;
        double currentScrollPosition = _scrollControllerBottom.position.pixels;

        double targetScrollPosition = currentScrollPosition - 1; // 缓慢向右滚动

        if (targetScrollPosition <= 0) {
          targetScrollPosition = maxScrollExtent;
        }

        _scrollControllerBottom.jumpTo(targetScrollPosition);
      }
    });
  }

  @override
  void dispose() {
    _timerTop.cancel();
    _timerBottom.cancel();
    _scrollControllerTop.dispose();
    _scrollControllerBottom.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: Get.setPaddingSize(36)),
        SingleChildScrollView(
          controller: _scrollControllerTop,
          scrollDirection: Axis.horizontal,
          physics: const NeverScrollableScrollPhysics(),
          child: Row(
            children: List.generate(supportedChains.length, (index) {
              return Padding(
                padding: EdgeInsets.only(right: Get.setPaddingSize(14)),
                child: Center(
                    child: ImageWidget(
                  assetUrl: supportedChains[index].chainIcon,
                  width: Get.setImageSize(48),
                  height: Get.setImageSize(48),
                )),
              );
            }),
          ),
        ),
        SizedBox(height: Get.setPaddingSize(26)),
        SingleChildScrollView(
          controller: _scrollControllerBottom,
          scrollDirection: Axis.horizontal,
          physics: const NeverScrollableScrollPhysics(),
          child: Row(
            children: List.generate(supportedChains.length, (index) {
              return Padding(
                padding: EdgeInsets.only(right: Get.setPaddingSize(14)),
                child: Center(
                  child: ImageWidget(
                    assetUrl: supportedChains[index].chainIcon,
                    width: Get.setImageSize(48),
                    height: Get.setImageSize(48),
                  ),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}
