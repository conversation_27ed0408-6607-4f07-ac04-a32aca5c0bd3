/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-06 13:32:37
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/home/<USER>/supprot_token_list_dialog.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class HomeTokenSearchEmptyWidget extends BaseStatelessWidget<WalletController> {
  const HomeTokenSearchEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        top: Get.setPaddingSize(50),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: SizedBox(
        width: Get.width,
        child: Column(
          children: [
            ImageWidget(
              assetUrl: "ic_no_data",
              width: Get.setImageSize(88),
              height: Get.setImageSize(88),
            ),
            SizedBox(
              height: Get.setHeight(14),
            ),
            Text(ID.stringSearchEmpty.tr,
                style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            SizedBox(
              height: Get.setHeight(30),
            ),
            OutlinedButtonWidget(
                text: ID.stringAddToken.tr,
                textStyle: TextStyle(
                    fontSize: Get.setFontSize(14),
                    color: Get.theme.textPrimary,
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.medium),
                onPressed: () {
                  KeyboardUtils.hideKeyboard(context);
                  controller.isAllChains
                      ? SupportTokenListDialog(
                          wallet: controller.wallet,
                          homeDataList: controller.dataList,
                          walletModel: controller.walletModel,
                        ).showBottomSheet()
                      : Get.toNamed(AppRoutes.tokenManagerPage, arguments: {
                          GetArgumentsKey.coinType: controller.coinType,
                          GetArgumentsKey.walletModel: controller.walletModel,
                        });
                }),
            SizedBox(
              height: Get.setPaddingSize(20),
            ),
          ],
        ),
      ),
    );
  }
}
