/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-12-13 13:43:13
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/service/wallet_database_service.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/base_request_model.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/extra.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_coin_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/market_price_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/usd_rate_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:drift/drift.dart' as drift;
import 'package:wallet_core/chain/bitcoin/usdt.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/chain/tron/trx.dart';
import 'package:wallet_core/wallet/wallet.dart';

class HomeRepository {
  // 定义构造方法并初始化所有成员变量
  HomeRepository({
    required this.wallet,
    required this.walletId,
    required this.deviceId,
    required this.batchId,
    required this.chain,
    required this.blockChainService,
    required this.apiService,
    required this.appDatabase,
    required this.currentAddress,
  });

  final ApiService apiService;
  final AppDatabase appDatabase;
  final BlockChainService blockChainService;
  final Wallet wallet;
  final String walletId;
  final String deviceId;
  final String chain;
  final int batchId;
  final String currentAddress;
  Future<List<CoinModel>> createCoinModel() async {
    List<CoinModel> allCoinModel =
        await appDatabase.coinDao.getAllSupportedCoinModel(chain: chain);
    // 获取支持的链
    List<CoinType> supportedChains = wallet.supportedChainsByBachId(batchId);
    // 根据配置决定是否移除测试币
    if (!AppConfig.instance.enableTestNetwork) {
      supportedChains =
          supportedChains.where((chain) => !chain.isTestNet).toList();
    }

    ///找到该硬件钱包支持的币种
    List<CoinModel> coinModels = allCoinModel.where((coinModel) {
      return supportedChains.any((supportedChain) =>
          (supportedChain.chain == coinModel.chain &&
              coinModel.isCoinSupported == true));
    }).toList();

    return sortCoinModel(coinModels);
  }

  List<CoinModel> sortCoinModel(List<CoinModel> coinModels) {
    List<CoinModel> allCoinModels = [];

    List<CoinModel> mainCoinModels = [];
    List<CoinModel> tokenCoinModels = [];

    for (var coinModel in coinModels) {
      if (!coinModel.isToken! && coinModel.chain != UsdtMain.get.chain) {
        mainCoinModels.add(coinModel);
      }
    }

    mainCoinModels.sort((a, b) => a.sortedId!.compareTo(b.sortedId!));

    for (var coinModel in mainCoinModels) {
      if (coinModel.isSupportToken!) {
        allCoinModels.add(coinModel);
        for (var tokenCoinModel in coinModels) {
          if (tokenCoinModel.isToken! &&
              coinModel.chain == tokenCoinModel.chain) {
            if (tokenCoinModel.symbol == UsdtMain.get.symbol) {
              allCoinModels.add(tokenCoinModel);
            } else {
              tokenCoinModels.add(tokenCoinModel);
            }
          }
        }
      } else {
        allCoinModels.add(coinModel);
      }
    }
    allCoinModels.addAll(tokenCoinModels);

    return allCoinModels;
  }

  Future<AddressModel?> getDefaultAddressModel(
      String chain, String address) async {
    AddressModel? addressModel;
    if (Get.isEmptyString(address)) {
      List<AddressModel> addressList =
          await appDatabase.addressDao.getAddressModelList(
        deviceId: deviceId,
        walletId: walletId,
        chain: chain,
      );

      if (addressList.isNotEmpty) {
        addressModel = addressList[0];
        await appDatabase.walletDao.updateWalletAddress(walletId, addressModel);
      }
    } else {
      addressModel = await appDatabase.addressDao.getAddressModel(
          walletId: walletId,
          deviceId: deviceId,
          chain: chain,
          address: address);
    }
    return addressModel;
  }

  Future<List<HomeCoinModel>> createHomeCoinModel(String address) async {
    List<CoinModel> allCoinModel = await createCoinModel();
    List<HomeCoinModel> homeCoinModels = [];

    for (final coinModel in allCoinModel) {
      final addresses = await appDatabase.addressDao.getAddressModelList(
          deviceId: deviceId,
          walletId: walletId,
          chain: coinModel.chain!,
          address: address);

      if (coinModel.isToken!) {
        List<TokenModel> tokens = [];

        for (final addressModel in addresses) {
          TokenModel? tokenModel = await appDatabase.tokenDao.getTokenModel(
                walletId: walletId,
                chain: coinModel.chain!,
                contract: coinModel.contract!,
                address: addressModel.address!,
              ) ??
              TokenModel(
                id: -1,
                contract: coinModel.contract!,
                address: addressModel.address!,
                chain: coinModel.chain!,
                walletId: walletId,
                addressLabel: addressModel.addressLabel,
                balance: "",
                tokenType: coinModel.tokenType,
              );

          tokens.add(TokenModel(
            id: tokenModel.id,
            contract: tokenModel.contract!,
            address: tokenModel.address!,
            chain: tokenModel.chain!,
            walletId: tokenModel.walletId,
            addressLabel: addressModel.addressLabel,
            balance: tokenModel.balance,
            tokenType: tokenModel.tokenType,
            derivedAddresses: tokenModel.derivedAddresses,
          ));
        }

        homeCoinModels.add(HomeCoinModel(
          coinModel: coinModel,
          tokens: tokens,
          isToken: true,
          totalBalance: calculateTotalBalance(tokens, coinModel.balanceDecimal),
        ));
      } else {
        addresses.sort((a, b) {
          return (a.sortedId ?? 0).compareTo(b.sortedId ?? 0);
        });

        homeCoinModels.add(HomeCoinModel(
          coinModel: coinModel,
          addresses: addresses,
          isToken: false,
          totalBalance:
              calculateMainChainBalance(addresses, coinModel.balanceDecimal),
        ));
      }
    }

    return homeCoinModels;
  }

  Future<String?> getEosPublickey() async {
    return await appDatabase.monitorDao
        .getPublicKey(walletId, EosChain.get.chain);
  }

  Future<BaseResponseV1<dynamic>> getHomeEosAccountFuture(
      String? publicekey) async {
    return await blockChainService.getEosAccout(
        BlockChainParamsManager.createParams(
            method: BlockChainAPI.getEosAccout,
            requestParams: RequestParams()
                .put(APIConstant.chain, EosChain.get.chain)
                .put(APIConstant.publickey, publicekey)
                .getRequestBody()));
  }

  Future<List<Future<dynamic>>> getHomeBalanceFuture(
      List<HomeCoinModel> coinModelsList) async {
    List<Future<dynamic>> futures = [];
    futures.add(apiService.getMarketPriceList(
      1,
      200,
    ));
    futures.add(apiService.getUSDPrices());
    if (coinModelsList.isEmpty) {
      return futures;
    }

    if (coinModelsList.isEmpty) {
      return futures;
    }

    List<Future<dynamic>> coinfutures = [];

    for (HomeCoinModel homeCoinModel in coinModelsList) {
      CoinModel coinModel = homeCoinModel.coinModel;

      String chain = coinModel.chain!;
      CoinType? cointype = CoinBase.getCoinTypeByChain(chain);
      if (cointype == null || coinModel.isToken!) {
        continue;
      }

      Map<String, dynamic> params = await getRequestParams(
          walletId, deviceId, coinModel, cointype, appDatabase);
      if (params.isNotEmpty) {
        coinfutures.add(blockChainService.getHomeBalance(true, params));
      }
    }

    futures.addAll(coinfutures);
    return futures;
  }

  Future<Map<String, dynamic>> getRequestParams(
      String walletId,
      String deviceId,
      CoinModel coinModel,
      CoinType cointype,
      AppDatabase appDatabase) async {
    String chain = coinModel.chain!;

    List<AddressModel> addressModelList =
        await appDatabase.addressDao.getAddressModelList(
      walletId: walletId,
      deviceId: deviceId,
      chain: chain,
      address: currentAddress,
    );
    if (addressModelList.isEmpty) {
      return {};
    }

    BaseRequestModel baseRequestModel =
        BlockChainParamsManager.createRequestMode(
            method: BlockChainAPI.getHomeBalance);

    if (addressModelList.isNotEmpty) {
      List<Map<String, dynamic>> paramsList = [];
      Map<String, dynamic> requestParams;
      for (var addressModel in addressModelList) {
        Extra extra = Extra();
        Map<String, dynamic> extraMap;
        if (cointype.isSupportToken) {
          List<CoinModel> tokenList = await appDatabase.coinDao
                  .getCoinSupportedTokenCoinModels(chain: chain) ??
              [];
          extraMap = extra.toTokenModelBalanceJson(tokenList);
        } else {
          extraMap = extra.toBalanceJson();
        }
        requestParams = RequestParams()
            .put(APIConstant.address, addressModel.address)
            .put(APIConstant.chain, chain)
            .put(APIConstant.extra, extraMap)
            .getRequestBody();

        paramsList.add(requestParams);
      }
      baseRequestModel.params = paramsList;
    }

    return baseRequestModel.toJson();
  }

  Future<void> handleResponse({
    required,
    dynamic value,
    required List<HomeCoinModel> homeCoinModelsList,
  }) async {
    var markPriceData = value[0];
    var rateData = value[1];
    List<MarketPriceModel> markModelList = [];
    if (markPriceData != null &&
        APIConstant.responseCode == markPriceData.code) {
      if (markPriceData.data is Map) {
        markModelList = Map<String, dynamic>.from(markPriceData.data)['data']
            .map<MarketPriceModel>((item) => MarketPriceModel.fromJson(item))
            .toList();
      }
    }

    if (rateData != null && APIConstant.responseCode == rateData.code) {
      if (rateData.data is Map) {
        UsdRateModel usdRateModel = UsdRateModel.fromJson(
            Map<String, dynamic>.from(rateData.data)['USDT']);
        AppController.usdRateModel.value = usdRateModel;

        StorageManager.saveObject(key: StorageKey.rateModel, obj: usdRateModel);
      }
    }

    List<BaseResponseV1<dynamic>> tokenResponse = [];
    if (value.length > 2) {
      tokenResponse = value.sublist(2).cast<BaseResponseV1<dynamic>>();
    }
    if (tokenResponse.isEmpty) return;

    await handleTokenResponse(
        tokenResponse: tokenResponse, homeCoinModelsList: homeCoinModelsList);
    await appDatabase.coinDao.updateCoinPrices(markModelList);
  }

  Future<void> handleTokenResponse(
      {required List<BaseResponseV1> tokenResponse,
      required List<HomeCoinModel> homeCoinModelsList}) async {
    for (var obj in tokenResponse) {
      if (obj.data == null || obj.error != null) {
        continue;
      }

      List<dynamic> pList = obj.data;
      if (pList.isEmpty) {
        continue;
      }
      bool isJsonArray = pList.first is List;

      if (isJsonArray) {
        for (var jsonArrayNew in pList) {
          var balanceModelList = List<BalanceModel>.from(
            (jsonArrayNew as List).map((item) => BalanceModel.fromJson(item)),
          );

          if (balanceModelList.isEmpty) {
            continue;
          }

          String? chain = balanceModelList[0].chain;

          CoinType? coinType = CoinBase.getCoinTypeByChain(chain);
          if (coinType == null) {
            continue;
          }

          for (var balanceModel in balanceModelList) {
            if (coinType.isEthereumSeries) {
              await handleEtherData(
                  coinType: coinType,
                  balanceModel: balanceModel,
                  homeCoinModelsList: homeCoinModelsList);
            } else if (coinType is TronChain) {
              await handleTronData(
                  coinType: coinType,
                  balanceModel: balanceModel,
                  homeCoinModelsList: homeCoinModelsList);
            } else if (coinType is EosChain) {
              await handleEosData(
                  coinType: coinType,
                  balanceModel: balanceModel,
                  homeCoinModelsList: homeCoinModelsList);
            } else if (coinType is SolanaChain) {
              await handleSolanaData(
                  coinType: coinType,
                  balanceModel: balanceModel,
                  homeCoinModelsList: homeCoinModelsList);
            }
          }
        }
      } else {
        var balanceModelList = List<BalanceModel>.from(
          obj.data.map((item) => BalanceModel.fromJson(item)),
        );

        if (balanceModelList.isEmpty) {
          continue;
        }

        String? chain = balanceModelList[0].chain;

        CoinType? coinType = CoinBase.getCoinTypeByChain(chain);
        if (coinType == null) {
          continue;
        }

        for (var balanceModel in balanceModelList) {
          await handleChainData(
              coinType: coinType,
              balanceModel: balanceModel,
              homeCoinModelsList: homeCoinModelsList);
        }
      }
    }
  }

  Future<void> handleSolanaData({
    required CoinType coinType,
    required BalanceModel balanceModel,
    required List<HomeCoinModel> homeCoinModelsList,
  }) async {
    if (balanceModel.address!.toLowerCase() ==
        balanceModel.contract!.toLowerCase()) {
      var mainCoinModel = findHomeCoinModel(coinType.chain, homeCoinModelsList);
      if (mainCoinModel == null) {
        return;
      }
      List<AddressModel> addressList = mainCoinModel.addresses;

      await appDatabase.addressDao
          .updateBalances(addressList, balanceModel, coinType);
      if (balanceModel.decimal != null &&
          mainCoinModel.coinModel.chainDecimal == null) {
        await appDatabase.coinDao.updateDecimal(mainCoinModel.coinModel,
            balanceModel.decimal!, coinType.balanceDecimals);
      }
    } else {
      if (Get.isEmptyString(balanceModel.contract)) {
        return;
      }

      var coinModel = await appDatabase.coinDao.getTokenCoinModel(
          chain: coinType.chain, contract: balanceModel.contract);

      if (coinModel != null) {
        var tokenModel = await appDatabase.tokenDao.getTokenModel(
            walletId: walletId,
            chain: coinType.chain,
            contract: balanceModel.contract!,
            address: balanceModel.address!);

        if (tokenModel == null) {
          await appDatabase.tokenDao
              .intertToken(balanceModel, coinType, walletId, "");
        } else {
          if (balanceModel.logoUrl!.isNotEmpty) {
            await appDatabase.coinDao
                .updateTokenSymbolUrl(coinModel, balanceModel.logoUrl!);
          }

          if (coinType is SolanaChain) {
            /// 更新 derivedAddresses
            if (Get.isEmptyString(tokenModel.derivedAddresses) &&
                !Get.isEmptyString(balanceModel.derivedAddresses)) {
              await appDatabase.tokenDao.updateDerivedAddresses(
                  tokenModel, balanceModel.derivedAddresses!);
            }

            /// 更新 tokenType
            await appDatabase.tokenDao
                .updateTokenType(tokenModel, balanceModel.tokenType);
          }

          if (balanceModel.chain == tokenModel.chain &&
              balanceModel.contract == tokenModel.contract) {
            await appDatabase.tokenDao
                .updateBalances(tokenModel, balanceModel, coinType);
            if (balanceModel.decimal != null &&
                coinModel.chainDecimal == null) {
              await appDatabase.coinDao.updateDecimal(
                  coinModel, balanceModel.decimal!, coinType.balanceDecimals);
            }
          }
        }
      } else {
        await intertTokenModel(coinType, balanceModel);
      }
    }
  }

  Future<void> handleEtherData({
    required CoinType coinType,
    required BalanceModel balanceModel,
    required List<HomeCoinModel> homeCoinModelsList,
  }) async {
    if (balanceModel.address!.toLowerCase() ==
        balanceModel.contract!.toLowerCase()) {
      var mainCoinModel = findHomeCoinModel(coinType.chain, homeCoinModelsList);
      if (mainCoinModel == null) {
        return;
      }
      List<AddressModel> addressList = mainCoinModel.addresses;

      await appDatabase.addressDao
          .updateBalances(addressList, balanceModel, coinType);
      if (balanceModel.decimal != null &&
          mainCoinModel.coinModel.chainDecimal == null) {
        await appDatabase.coinDao.updateDecimal(mainCoinModel.coinModel,
            balanceModel.decimal!, coinType.balanceDecimals);
      }
    } else {
      if (Get.isEmptyString(balanceModel.contract)) {
        return;
      }

      var coinModel = await appDatabase.coinDao.getTokenCoinModel(
          chain: coinType.chain, contract: balanceModel.contract);

      if (coinModel != null) {
        var tokenModel = await appDatabase.tokenDao.getTokenModel(
            walletId: walletId,
            chain: coinType.chain,
            contract: balanceModel.contract!,
            address: balanceModel.address!);

        if (tokenModel == null) {
          await appDatabase.tokenDao
              .intertToken(balanceModel, coinType, walletId, "");
        } else {
          if (balanceModel.logoUrl!.isNotEmpty) {
            await appDatabase.coinDao
                .updateTokenSymbolUrl(coinModel, balanceModel.logoUrl!);
          }

          if (balanceModel.chain == tokenModel.chain &&
              balanceModel.contract == tokenModel.contract) {
            await appDatabase.tokenDao
                .updateBalances(tokenModel, balanceModel, coinType);
            if (balanceModel.decimal != null &&
                coinModel.chainDecimal == null) {
              await appDatabase.coinDao.updateDecimal(
                  coinModel, balanceModel.decimal!, coinType.balanceDecimals);
            }
          }
        }
      } else {
        await intertTokenModel(coinType, balanceModel);
      }
    }
  }

  Future<void> intertTokenModel(
      CoinType coinType, BalanceModel balanceModel) async {
    await Future.wait([
      appDatabase.coinDao.intertTokenCoinModel(balanceModel, coinType),
      appDatabase.tokenDao.intertToken(balanceModel, coinType, walletId, "")
    ]);
  }

  Future<void> handleTronData({
    required CoinType coinType,
    required BalanceModel balanceModel,
    required List<HomeCoinModel> homeCoinModelsList,
  }) async {
    CoinType coinType = TronChain.get;
    if (balanceModel.tokenType == 0) {
      var mainCoinModel = findHomeCoinModel(coinType.chain, homeCoinModelsList);
      if (mainCoinModel == null) {
        return;
      }
      List<AddressModel> addressList = mainCoinModel.addresses;

      await appDatabase.addressDao
          .updateBalances(addressList, balanceModel, coinType);
      if (balanceModel.decimal != null &&
          mainCoinModel.coinModel.chainDecimal == null) {
        await appDatabase.coinDao.updateDecimal(mainCoinModel.coinModel,
            balanceModel.decimal!, coinType.balanceDecimals);
      }
    } else {
      if (Get.isEmptyString(balanceModel.contract)) {
        return;
      }

      var coinModel = await appDatabase.coinDao.getTokenCoinModel(
          chain: coinType.chain, contract: balanceModel.contract);

      if (coinModel != null) {
        ///旧版本tokenType 错误这里需要更新一下
        await appDatabase.coinDao
            .updateTokenType(coinModel, balanceModel.tokenType);
        var tokenModel = await appDatabase.tokenDao.getTokenModel(
            walletId: walletId,
            chain: coinType.chain,
            contract: balanceModel.contract!,
            address: balanceModel.address!);

        if (tokenModel == null) {
          await appDatabase.tokenDao
              .intertToken(balanceModel, coinType, walletId, "");
        } else {
          if (balanceModel.logoUrl!.isNotEmpty) {
            await appDatabase.coinDao
                .updateTokenSymbolUrl(coinModel, balanceModel.logoUrl!);
          }
          if (balanceModel.tokenType == tokenModel.tokenType &&
              balanceModel.chain == tokenModel.chain &&
              balanceModel.contract == tokenModel.contract) {
            await appDatabase.tokenDao
                .updateBalances(tokenModel, balanceModel, coinType);
            if (balanceModel.decimal != null &&
                coinModel.chainDecimal == null) {
              await appDatabase.coinDao.updateDecimal(
                  coinModel, balanceModel.decimal!, coinType.balanceDecimals);
            }
          }
        }
      } else {
        await intertTokenModel(coinType, balanceModel);
      }
    }
  }

  Future<void> handleChainData({
    required CoinType coinType,
    required BalanceModel balanceModel,
    required List<HomeCoinModel> homeCoinModelsList,
  }) async {
    var mainCoinModel = findHomeCoinModel(coinType.chain, homeCoinModelsList);
    if (mainCoinModel == null) {
      return;
    }

    List<AddressModel> addressList = mainCoinModel.addresses;

    await appDatabase.addressDao
        .updateBalances(addressList, balanceModel, coinType);
    if (balanceModel.decimal != null &&
        mainCoinModel.coinModel.chainDecimal == null) {
      await appDatabase.coinDao.updateDecimal(mainCoinModel.coinModel,
          balanceModel.decimal!, coinType.balanceDecimals);
    }
  }

  Future<void> handleEosAcctount(dynamic value) async {
    if (value == null || value.data == null) {
      return;
    }

    var accountData = value.data["account_names"];

    if (accountData == null) {
      return;
    }
    List<String> eosAccountList = accountData.cast<String>();
    if (eosAccountList.isEmpty) {
      return;
    }

    List<AddressTableCompanion> addressModelList = <AddressTableCompanion>[];
    CoinType coinType = EosChain.get;
    MonitorModel? monitorModel = await appDatabase.monitorDao
        .getMonitorModel(walletId: walletId, chain: coinType.chain);

    for (int j = 0; j < eosAccountList.length; j++) {
      String account = eosAccountList[j];

      addressModelList.add(AddressTableCompanion.insert(
        walletId: drift.Value(walletId),
        chainCode: drift.Value(coinType.id),
        chain: drift.Value(coinType.chain),
        address: drift.Value(account),
        addressIndex: const drift.Value(0),
        isSelected: const drift.Value(false),
        slip44Id: drift.Value(coinType.bip44Id),
        publickey: drift.Value(monitorModel!.publickey),
        path: drift.Value(monitorModel.path),
        isUploaded: const drift.Value(false),
        sortedId: drift.Value(j),
        addressLabel: drift.Value(CoinBase.getDefaultLabel(coinType, j + 1)),
        walletType: const drift.Value(1),
      ));
    }
    if (addressModelList.isNotEmpty) {
      await appDatabase.addressDao.insertBatchIfNotExists(addressModelList);
      await WalletDatabaseService().insertOnConflictUpdateAddressMapping(
          deviceId, walletId, addressModelList);
    }
  }

  Future<void> handleEosData({
    required CoinType coinType,
    required BalanceModel balanceModel,
    required List<HomeCoinModel> homeCoinModelsList,
  }) async {
    CoinType coinType = EosChain.get;
    if (EosChain.get.getEosMainContract() == balanceModel.contract &&
        coinType.symbol == balanceModel.symbol) {
      var mainCoinModel = findHomeCoinModel(coinType.chain, homeCoinModelsList);
      if (mainCoinModel == null) {
        return;
      }
      List<AddressModel> addressList = mainCoinModel.addresses;

      await appDatabase.addressDao
          .updateBalances(addressList, balanceModel, coinType);
      if (balanceModel.decimal != null &&
          mainCoinModel.coinModel.chainDecimal == null) {
        await appDatabase.coinDao.updateDecimal(mainCoinModel.coinModel,
            balanceModel.decimal!, coinType.balanceDecimals);
      }
    } else {
      if (Get.isEmptyString(balanceModel.contract)) {
        return;
      }

      var coinModel = await appDatabase.coinDao.getTokenCoinModel(
          chain: coinType.chain, contract: balanceModel.contract);

      if (coinModel != null) {
        var tokenModel = await appDatabase.tokenDao.getTokenModel(
            walletId: walletId,
            chain: coinType.chain,
            contract: balanceModel.contract!,
            address: balanceModel.address!);

        if (tokenModel == null) {
          await appDatabase.tokenDao
              .intertToken(balanceModel, coinType, walletId, "");
        } else {
          if (balanceModel.logoUrl!.isNotEmpty) {
            await appDatabase.coinDao
                .updateTokenSymbolUrl(coinModel, balanceModel.logoUrl!);
          }

          if (balanceModel.tokenType == tokenModel.tokenType &&
              balanceModel.chain == tokenModel.chain &&
              balanceModel.contract == tokenModel.contract) {
            await appDatabase.tokenDao
                .updateBalances(tokenModel, balanceModel, coinType);
            if (balanceModel.decimal != null &&
                coinModel.chainDecimal == null) {
              await appDatabase.coinDao.updateDecimal(
                  coinModel, balanceModel.decimal!, coinType.balanceDecimals);
            }
          }
        }
      } else {
        await intertTokenModel(coinType, balanceModel);
      }
    }
  }

  HomeCoinModel? findHomeCoinModel(
      String chain, List<HomeCoinModel> homeCoinModelsList,
      {bool isToken = false, String? contract, String? address}) {
    for (var homeCoin in homeCoinModelsList) {
      if (isToken) {
        if (homeCoin.coinModel.chain == chain &&
            homeCoin.coinModel.isToken == true &&
            homeCoin.coinModel.contract == contract!) {
          return homeCoin;
        }
      } else {
        if (homeCoin.coinModel.chain == chain &&
            homeCoin.coinModel.isToken == false) {
          return homeCoin;
        }
      }
    }
    return null; // 如果没有找到匹配的 HomeCoinModel，返回 null
  }

  bool findTokenInList(List<TokenModel> tokenList, BalanceModel balanceModel) {
    // 使用any方法检查是否存在满足条件的TokenModel
    return tokenList.any((token) =>
        token.chain == balanceModel.chain &&
        token.address!.toLowerCase() == balanceModel.address!.toLowerCase() &&
        token.contract == balanceModel.contract);
  }

  String calculateTotalBalance(List<TokenModel> tokens, int? balanceDecimal) {
    bool allNull = tokens.every((token) => Get.isEmptyString(token.balance));
    if (allNull) {
      return "";
    }

    return tokens.fold(
        "0",
        (String total, TokenModel token) => DecimalUtils.add(
            total,
            (Get.isEmptyString(token.balance) ||
                    !GetUtils.isNum(token.balance!) ||
                    token.balance == CommonConstant.emptyAsstes ||
                    token.balance == CommonConstant.emptyTotalAsstes)
                ? "0"
                : token.balance!,
            scale: balanceDecimal ?? 8));
  }

  String calculateMainChainBalance(
      List<AddressModel> tokens, int? balanceDecimal) {
    bool allNull = tokens.every((token) => Get.isEmptyString(token.balance));
    if (allNull) {
      return "";
    }
    return tokens.fold(
        "0",
        (String total, addressModel) => DecimalUtils.add(
            total,
            (Get.isEmptyString(addressModel.balance) ||
                    !GetUtils.isNum(addressModel.balance!) ||
                    addressModel.balance == CommonConstant.emptyAsstes ||
                    addressModel.balance == CommonConstant.emptyTotalAsstes)
                ? "0"
                : addressModel.balance!,
            scale: balanceDecimal ?? 8));
  }
}
