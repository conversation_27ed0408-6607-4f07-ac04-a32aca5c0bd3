/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-08-20 09:20:49
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/wallet/home/<USER>/auto_scroll_rows_widget.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_all_chain_widget.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class HomeEmptyWalletWidget extends BaseStatelessWidget<WalletController> {
  const HomeEmptyWalletWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: Get.setHeight(Get.mediaQuery.viewPadding.top + 10)),
          _headerWidget(),
          const DividerWidget(),
          const AutoScrollRowsWidget(),
          _allChainTextWidget(),
          DividerBoldWidget(height: Get.setPaddingSize(8)),
          _web3Widget(),
        ],
      ),
    ));
  }

  Padding _headerWidget() => Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: () => Get.toScanner(),
                  behavior: HitTestBehavior.translucent,
                  child: ImageWidget(
                    assetUrl: 'icon_scan',
                    width: Get.setImageSize(36),
                    height: Get.setImageSize(36),
                    fit: BoxFit.contain,
                  ),
                ),
              ],
            ),
            ImageWidget(
              assetUrl: 'icon_wallet_all',
              width: Get.width,
              height: Get.setHeight(342),
            ),
            Text(
              ID.stringEmptyWalletTitle.tr,
              style: TextStyle(
                fontSize: Get.setFontSize(24),
                color: Get.theme.textPrimary,
                fontWeight: FontWeightX.semibold,
              ),
            ),
            SizedBox(height: Get.setPaddingSize(8)),
            Text(
              ID.stringEmptyWalletSubTitle.tr,
              style: TextStyle(
                fontSize: Get.setFontSize(16),
                color: Get.theme.textPrimary,
              ),
            ),
            SizedBox(height: Get.setHeight(36)),
            ButtonWidget(
                text: ID.bindHardwareWallet.tr,
                height: Get.setHeight(44),
                buttonSize: ButtonSize.full,
                width: Get.width,
                onPressed: () => Get.toNamed(AppRoutes.connectWalletPage)),
            SizedBox(height: Get.setHeight(24)),
          ],
        ),
      );

  Padding _allChainTextWidget() => Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      child: Column(
        children: [
          SizedBox(height: Get.setPaddingSize(36)),
          Text(
            ID.stringEmptyTitle2.tr,
            style: TextStyle(
              fontSize: Get.setFontSize(24),
              color: Get.theme.textPrimary,
              fontWeight: FontWeightX.semibold,
            ),
          ),
          SizedBox(height: Get.setPaddingSize(16)),
          Text(
            ID.stringEmptySubTitle2.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: Get.setFontSize(16),
              color: Get.theme.textPrimary,
            ),
          ),
          SizedBox(height: Get.setPaddingSize(16)),
          ButtonWidget(
            text: ID.stringAllNetTitle.tr,
            height: Get.setHeight(44),
            width: Get.width * 0.6,
            onPressed: () => HomeAllChainWidget.show(),
          ),
          SizedBox(height: Get.setPaddingSize(36)),
        ],
      ));

  Padding _web3Widget() => Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
        child: Column(
          children: [
            SizedBox(height: Get.setPaddingSize(36)),
            Text(
              ID.stringWeb3Title.tr,
              style: TextStyle(
                fontSize: Get.setFontSize(24),
                color: Get.theme.textPrimary,
                fontWeight: FontWeightX.semibold,
              ),
            ),
            SizedBox(height: Get.setPaddingSize(8)),
            Text(
              ID.stringWeb3SubTitle.tr,
              style: TextStyle(
                fontSize: Get.setFontSize(16),
                color: Get.theme.textPrimary,
              ),
            ),
            SizedBox(height: Get.setPaddingSize(16)),
            _web3ItemWIdget(
              title: ID.stringWeb3Title1.tr,
              subTitle: ID.stringWeb3SubTitle1.tr,
              imageName: 'empty_web_icon1',
            ),
            _web3ItemWIdget(
              title: ID.stringWeb3Title2.tr,
              subTitle: ID.stringWeb3SubTitle2.tr,
              imageName: 'empty_web_icon2',
            ),
            _web3ItemWIdget(
              title: ID.stringWeb3Title3.tr,
              subTitle: ID.stringWeb3SubTitle3.tr,
              imageName: 'empty_web_icon3',
            ),
          ],
        ),
      );

  Padding _web3ItemWIdget(
          {required String title,
          required String subTitle,
          required String imageName}) =>
      Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: stylePrimary_14_m,
                  ),
                  SizedBox(height: Get.setPaddingSize(8)),
                  Text(
                    subTitle,
                    style: TextStyle(
                      fontSize: Get.setFontSize(12),
                      color: Get.theme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: Get.setPaddingSize(8)),
            ImageWidget(
              assetUrl: imageName,
              width: Get.setImageSize(82),
              height: Get.setImageSize(82),
            )
          ],
        ),
      );
}
