/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-10 17:37:24
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/eos/eos.dart';

class HomeEOSAccountEmptyWidget extends StatelessWidget {
  final bool isChainPage;
  const HomeEOSAccountEmptyWidget({super.key, this.isChainPage = false});

  static void showEOSAccountEmpty() {
    Get.showBottomSheet(
      hideHeader: true,
      bodyWidget: const HomeEOSAccountEmptyWidget(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.getSafetyBottomPadding()),
      child: Column(
        children: [
          _headerWidget(),
          Visibility(visible: !isChainPage, child: const DividerWidget()),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
            child: Column(
              children: [
                SizedBox(height: Get.setPaddingSize(24)),
                Text(
                  ID.stringEosEmptyTitle.tr,
                  style: TextStyle(
                    fontSize: Get.setFontSize(18),
                    color: Get.theme.textPrimary,
                    fontWeight: FontWeightX.medium,
                  ),
                ),
                SizedBox(height: Get.setPaddingSize(16)),
                Text(
                  ID.stringEosEmptyDes.tr,
                  style: TextStyle(
                    fontSize: Get.setFontSize(14),
                    color: Get.theme.textPrimary,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  SingleChildRenderObjectWidget _headerWidget() {
    if (isChainPage == true) return const SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Get.setPaddingSize(14),
        horizontal: Get.setPaddingSize(16),
      ),
      child: Row(
        children: [
          ImageWidget(
            assetUrl: EosChain.get.chainIcon,
            height: Get.setImageSize(20),
            width: Get.setImageSize(20),
          ),
          SizedBox(width: Get.setPaddingSize(8)),
          Expanded(
            child: Text(
              EosChain.get.symbol,
              style: stylePrimary_16_m,
            ),
          ),
          GestureDetector(
            onTap: () => Get.back(),
            child: ImageWidget(
              assetUrl: "icon_exit",
              width: Get.setWidth(24),
              height: Get.setHeight(24),
            ),
          ),
        ],
      ),
    );
  }
}
