/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-22 14:30:58
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';

class HomeAllChainWidget extends StatelessWidget {
  const HomeAllChainWidget({super.key});

  static void show() {
    Get.showBottomSheet(
        paddingBottom: 0,
        fullScreenBodyWidget: const HomeAllChainWidget(),
        bottomWidget: null);
  }

  List<CoinType> get _dataSource {
    List<CoinType> data = UltraPlusWallet()
        .supportedChains
        .where((chain) => !chain.isTestNet) // 移除测试币
        .toList();
    data.removeAt(0);
    return data;
  }

  int get _count => _dataSource.length;
  double get _itemWidth => (Get.width - 36 - 42) / 4;
  double get _itemHeight => (_itemWidth + 16);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ID.chainAllChains.tr,
                  style: TextStyle(
                      fontSize: Get.setFontSize(30),
                      color: Get.theme.textPrimary,
                      fontWeight: FontWeightX.medium),
                ),
                SizedBox(
                  height: Get.setPaddingSize(8),
                ),
                Text(
                  ID.stringSupportChains.trParams({'count': _count.toString()}),
                  style: styleSecond_14,
                ),
                SizedBox(height: Get.setPaddingSize(24)),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: GridView.builder(
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          crossAxisSpacing: 0.0,
                          mainAxisSpacing: 0.0,
                          childAspectRatio: _itemWidth / _itemHeight,
                        ),
                        itemCount: _count,
                        itemBuilder: (_, index) {
                          if (index < _count) {
                            CoinType coinType = _dataSource[index];
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: Get.setPaddingSize(4)),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  ImageWidget(
                                    assetUrl: coinType.chainIcon,
                                    width: _itemWidth,
                                    height: Get.setImageSize(42),
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Text(
                                    coinType.chainName,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                        fontSize: Get.setFontSize(12)),
                                  )
                                ],
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                  ),
                )
              ],
            ),
          ),
        ),
        const BottomCancelWidget(),
      ],
    );
  }
}
