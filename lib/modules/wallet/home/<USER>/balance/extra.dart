/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-04-24 10:56:42
 */
class Extra {
  String? unconfirmed;
  String? accountNumber;

  Extra({this.unconfirmed, this.accountNumber});

  factory Extra.fromJson(Map<String, dynamic> json) => Extra(
        unconfirmed:
            json['unconfirmed'] != null ? json['unconfirmed'].toString() : '0',
        accountNumber: json['account_number']?.toString(),
      );

  Map<String, dynamic> toJson() => {
        'unconfirmed': unconfirmed,
        'account_number': accountNumber,
      };
}
