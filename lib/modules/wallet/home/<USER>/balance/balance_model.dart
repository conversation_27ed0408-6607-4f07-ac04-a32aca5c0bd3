/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-04-02 14:03:16
 */
import 'extra.dart';

class BalanceModel {
  String? address;
  String? balance;
  String? chain;
  String? contract;
  int? decimal;
  Extra? extra;
  String? name;
  String? symbol;
  int? tokenType;
  String? nonce;
  String? logoUrl;
  String? derivedAddresses;
  int? accountNumber;
  BalanceModel({
    this.address,
    this.balance,
    this.chain,
    this.contract,
    this.decimal,
    this.extra,
    this.name,
    this.symbol,
    this.tokenType,
    this.nonce,
    this.logoUrl,
    this.derivedAddresses,
    this.accountNumber,
  });

  factory BalanceModel.fromJson(Map<String, dynamic> json) => BalanceModel(
        address: json['address'] as String?,
        balance: json['balance'] != null ? json['balance'].toString() : '',
        chain: json['chain'] as String?,
        contract: json['contract'] as String?,
        decimal: json['decimal'] as int?,
        extra: json['extra'] == null
            ? null
            : Extra.fromJson(json['extra'] as Map<String, dynamic>),
        name: json['name'] as String?,
        symbol: json['symbol'] as String?,
        tokenType: json['token_type'] is int ? json['token_type'] : null,
        logoUrl: json['logoUrl'] != null ? json['logoUrl'].toString() : '',
        nonce: json['nonce'] != null ? json['nonce'].toString() : '0',
        derivedAddresses: json['tokenDerivedAddresses'] != null
            ? json['tokenDerivedAddresses'].toString()
            : '',
        accountNumber: json['account_number'] as int?,
      );

  Map<String, dynamic> toJson() => {
        'address': address,
        'balance': balance,
        'chain': chain,
        'contract': contract,
        'decimal': decimal,
        'extra': extra?.toJson(),
        'name': name,
        'symbol': symbol,
        'token_type': tokenType,
        'nonce': nonce,
        'logoUrl': logoUrl,
        'derivedAddresses': derivedAddresses,
        'account_number': accountNumber,
      };
}
