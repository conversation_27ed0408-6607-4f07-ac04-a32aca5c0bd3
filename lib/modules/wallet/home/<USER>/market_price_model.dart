/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-03-28 14:47:15
 */
import 'package:json_annotation/json_annotation.dart';

part 'market_price_model.g.dart';

@JsonSerializable()
class MarketPriceModel {
  String? currentPrice;
  String? id;
  String? image;
  dynamic lastUpdated;
  String? marketCap;
  dynamic marketCapLong;
  String? marketCapRank;
  String? name;
  String? priceChangePercentage24h;
  String? symbol;
  String? tokenContract;
  String? chain;
  MarketPriceModel(
      {this.currentPrice,
      this.id,
      this.image,
      this.lastUpdated,
      this.marketCap,
      this.marketCapLong,
      this.marketCapRank,
      this.name,
      this.priceChangePercentage24h,
      this.symbol,
      this.tokenContract,
      this.chain});

  factory MarketPriceModel.fromJson(Map<String, dynamic> json) =>
      _$MarketPriceModelFromJson(json);
  Map<String, dynamic> toJson() => _$MarketPriceModelToJson(this);
}
