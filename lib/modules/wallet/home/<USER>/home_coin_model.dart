/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-03-24 16:50:04
 */

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/address_list_dialog.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_eos_account_empty.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/chain/ethereum/testnet/heth.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/cold/touch_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class HomeCoinModel {
  final CoinModel coinModel;
  final List<AddressModel> addresses;
  final List<TokenModel> tokens;
  final bool isToken;
  final String? totalBalance;

  HomeCoinModel({
    required this.coinModel,
    this.addresses = const [],
    this.tokens = const [],
    this.isToken = false,
    this.totalBalance,
  });

  String? getSymbolUnitPriceValue() {
    if (coinModel.price == null || AppController.usdRateModel.value == null) {
      return CommonConstant.emptyAsstes;
    }

    String value = CurrencyController.convertCurrency(
        usdRateModel: AppController.usdRateModel.value,
        unitPrice: coinModel.price,
        scale: 8,
        balance: "1");
    value = CurrencyController.formatMoney(value,
        legal: false, withThousandmat: true, hasUnit: true);
    return value;
  }

  bool isShowChainName() {
    CoinType? coinType = CoinBase.getCoinTypeByChain(coinModel.chain!);
    if (coinType == null) {
      return false;
    }
    return isToken ||
        ((coinType is EthereumChain ||
                coinType.isLayer2 ||
                coinType is EthereumHoleskyChain) &&
            !coinModel.isToken!);
  }

  String getChainName() {
    CoinType? coinType = CoinBase.getCoinTypeByChain(coinModel.chain!);
    if (coinType == null) {
      return "";
    }
    if (coinType is EosChain) {
      return coinType.symbol;
    }
    return coinType.chainName;
  }

  String getBalance({bool hideAssets = false}) {
    if (hideAssets) {
      return CommonConstant.hideAssetsStr;
    }
    return Get.isEmptyString(totalBalance!)
        ? CommonConstant.emptyAsstes
        : totalBalance!;
  }

  String getSymbolPrice({bool hideAssets = false}) {
    if (hideAssets) {
      return CommonConstant.hideAssetsStr;
    }
    if (coinModel.price == null || Get.isEmptyString(totalBalance)) {
      return CommonConstant.emptyAsstes;
    }

    String value = CurrencyController.convertCurrency(
        usdRateModel: AppController.usdRateModel.value,
        unitPrice: coinModel.price,
        balance: totalBalance);
    value = CurrencyController.formatMoney(value,
        withThousandmat: true, hasUnit: true);

    return value;
  }

  String getTokenBalance() {
    if (totalBalance!.isEmpty) {
      return '';
    }
    return '${ID.stringBalance.tr}$totalBalance';
  }

  String getSymbolPriceValue() {
    if (coinModel.price == null ||
        Get.isEmptyString(totalBalance) ||
        !GetUtils.isNum(totalBalance!) ||
        AppController.usdRateModel.value == null) {
      return "0";
    }

    String value = CurrencyController.convertCurrency(
        usdRateModel: AppController.usdRateModel.value,
        unitPrice: coinModel.price,
        scale: 8,
        balance: totalBalance);

    return value;
  }

  void toNavigate(
      BuildContext context, WalletAction action, WalletController controller,
      {Function? callBack}) {
    KeyboardUtils.hideKeyboard(context);
    if (!controller.isClickable.value) {
      return;
    }
    if (isNoBindWallet(controller.eosPublicekey ?? "")) {
      if (controller.wallet.isP1P2P3Wallet) {
        Get.showOldWalletBindBottomSheet(controller.wallet);
      } else {
        if (controller.wallet is TouchWallet) {
          Get.toNamed(AppRoutes.touchBindPage);
        } else {
          Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
            GetArgumentsKey.walletId: controller.walletId,
            GetArgumentsKey.qrData:
                CoinBase.createWalletBindingQRCode(coinModel.chain!),
            GetArgumentsKey.qrType: QRCodeType.bindChain,
            GetArgumentsKey.wallet:
                Wallet.getWalletByBatch(controller.walletModel!.batchId!)
          });
        }
      }
      return;
    }

    CoinType coinType = CoinBase.getCoinTypeByChain(coinModel.chain!)!;
    if (coinType is EosChain) {
      if (isToken) {
        if (tokens.isEmpty) {
          HomeEOSAccountEmptyWidget.showEOSAccountEmpty();
          return;
        }
      } else {
        if (addresses.isEmpty) {
          HomeEOSAccountEmptyWidget.showEOSAccountEmpty();
          return;
        }
      }
    }

    if (controller.walletModel?.chain == AllChain.get.chain) {
      if (isToken) {
        var tokens = this.tokens;
        if (tokens.length == 1) {
          Get.navigateWallet(
            action,
            coinModel: coinModel,
            tokenModel: tokens.first,
            callBack: () => onRrefreshData(action, controller),
            walletModel: controller.walletModel!,
          );

          enableClickable(action, controller);
        } else {
          AddressListDialog(
            homeCoinModel: this,
            action: action,
            walletController: controller,
          ).showBottomSheet();
        }
      } else {
        var addresses = this.addresses;
        if (addresses.length == 1) {
          Get.navigateWallet(
            action,
            coinModel: coinModel,
            addressModel: addresses.first,
            callBack: () => onRrefreshData(action, controller),
            walletModel: controller.walletModel!,
          );
          enableClickable(action, controller);
        } else {
          AddressListDialog(
            homeCoinModel: this,
            action: action,
            walletController: controller,
          ).showBottomSheet();
        }
      }
    } else {
      TokenModel? tokenModel;
      AddressModel? addressModel;
      if (isToken) {
        List<TokenModel> dataList = tokens
            .where((element) => element.address == controller.address)
            .toList();
        if (dataList.isNotEmpty) {
          tokenModel = dataList.first;
        } else {
          tokenModel = tokens.first;
        }
      } else {
        List<AddressModel> dataList = addresses
            .where((element) => element.address == controller.address)
            .toList();
        if (dataList.isNotEmpty) {
          addressModel = dataList.first;
        } else {
          addressModel = addresses.first;
        }
      }

      Get.navigateWallet(
        action,
        coinModel: coinModel,
        tokenModel: tokenModel,
        addressModel: addressModel,
        callBack: () => onRrefreshData(action, controller),
        walletModel: controller.walletModel!,
      );
    }
  }

  bool isNoBindWallet(String? eosPublicKey) {
    if (coinModel.chain == EosChain.get.chain) {
      return Get.isEmptyString(eosPublicKey);
    } else {
      return coinModel.isToken! ? tokens.isEmpty : addresses.isEmpty;
    }
  }

  Future<void> onRrefreshData(
    WalletAction action,
    WalletController controller,
  ) async {
    if (action == WalletAction.asstes) {
      await Future.delayed(
          const Duration(milliseconds: 500)); // pull_to_refresh过快进入 会发生bug

      controller.refreshData();
    }
  }

  ///防止用户过快退出交易记录误触再进入
  void enableClickable(WalletAction action, WalletController controller) {
    if (action == WalletAction.asstes) {
      controller.isClickable.value = false;
    }
  }
}
