/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-11-01 10:35:11
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class HomeChainWidget extends BaseStatelessWidget<WalletController> {
  const HomeChainWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<WalletController>(
        id: GetKey.walletHomtTopList,
        builder: (controller) {
          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              KeyboardUtils.hideKeyboard(context);
              ChainManagerPage(
                wallet: controller.walletModel,
                isFullPage: false,
                chainManagerMode: ChainManagerMode.wallet,
              ).showChainPage();
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                  horizontal: Get.setPaddingSize(12),
                  vertical: Get.setPaddingSize(8)),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(Get.setRadius(18)),
                border: Border.all(
                  color: Get.theme.colorD3D3D3,
                  width: 0.5,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ImageWidget(
                    assetUrl: controller.topChainIcon,
                    width: Get.setImageSize(16),
                    height: Get.setImageSize(16),
                  ),
                  SizedBox(width: Get.setPaddingSize(4)),
                  Text(
                    controller.isAllChains
                        ? ID.chainAllChains.tr
                        : controller.topAddressLabel,
                    style: TextStyle(
                        fontSize: Get.setFontSize(14),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.semibold,
                        color: Get.theme.textPrimary),
                  ),
                  SizedBox(width: Get.setWidth(4)),
                  ImageWidget(
                    assetUrl: 'arrow',
                    width: Get.setImageSize(10),
                    height: Get.setImageSize(10),
                  ),
                ],
              ),
            ),
          );
        });
  }
}
