/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-12-10 10:06:59
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_coin_model.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/eos/eos.dart';

class AddressListDialog extends BaseStatelessWidget {
  final WalletController walletController;
  final HomeCoinModel? homeCoinModel;
  final WalletAction? action;
  const AddressListDialog(
      {super.key,
      required this.walletController,
      this.homeCoinModel,
      this.action});

  void showBottomSheet() {
    Get.showBottomSheet(
      bodyWidget: this,
      paddingBottom: 0,
      customHeadWidget: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(12)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SymbolWidget(coinModel: homeCoinModel!.coinModel),
            SizedBox(width: Get.setWidth(8)),
            Text(
              homeCoinModel!.coinModel.symbol!,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(16),
                fontFamily: Get.setNumberFontFamily(),
                fontWeight: FontWeightX.semibold,
              ),
            ),
            SizedBox(width: Get.setWidth(6)),
            Text(
              getCount(),
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(12),
                fontFamily: Get.setNumberFontFamily(),
                fontWeight: FontWeightX.semibold,
              ),
            ),
          ],
        ),
      ),
      bottomWidget: const BottomCancelWidget(),
    );
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: homeCoinModel!.isToken
              ? homeCoinModel!.tokens
                  .map((model) => _itemTokenBuilder(model))
                  .toList()
              : homeCoinModel!.addresses
                  .map((model) => _itemBuilder(model))
                  .toList()),
    );
  }

  Widget _itemTokenBuilder(TokenModel tokenModel) {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.setPaddingSize(10)),
      child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Get.back();
            Get.navigateWallet(action!,
                coinModel: homeCoinModel!.coinModel,
                tokenModel: tokenModel,
                walletModel: walletController.walletModel!, callBack: () {
              if (action == WalletAction.asstes) {
                walletController.refreshData();
              }
            });
          },
          child: _buildAddressWidget(
              tokenModel.balance, tokenModel.address, tokenModel.addressLabel)),
    );
  }

  Widget _itemBuilder(AddressModel addressModel) {
    return Padding(
      padding: EdgeInsets.only(bottom: Get.setPaddingSize(10)),
      child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Get.back();
            Get.navigateWallet(action!,
                coinModel: homeCoinModel!.coinModel,
                addressModel: addressModel,
                walletModel: walletController.walletModel!, callBack: () {
              if (action == WalletAction.asstes) {
                walletController.refreshData();
              }
            });
          },
          child: _buildAddressWidget(addressModel.balance, addressModel.address,
              addressModel.addressLabel)),
    );
  }

  Container _buildAddressWidget(
      String? balannce, String? address, String? addressLabel) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
      decoration: BoxDecoration(
        color: getChainThemeColor(homeCoinModel!.coinModel.chain!),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
          padding: const EdgeInsets.only(left: 5),
          child: Container(
            decoration: BoxDecoration(
                color: Get.theme.bgColor,
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(color: Get.theme.colorECECEC, width: 0.5)),
            child: Padding(
              padding: EdgeInsets.all(Get.setPaddingSize(14)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          addressLabel ?? "",
                          maxLines: 1,
                          style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(16),
                              fontFamily: Get.setNumberFontFamily(),
                              fontWeight: FontWeightX.semibold,
                              overflow: TextOverflow.ellipsis),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          balannce ?? "",
                          maxLines: 1,
                          textAlign: TextAlign.end,
                          style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(14),
                              fontFamily: Get.setNumberFontFamily(),
                              fontWeight: FontWeightX.bold,
                              overflow: TextOverflow.ellipsis),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          AddressUtils.omitAddress(address ?? "", len: 8),
                          maxLines: 1,
                          style: TextStyle(
                              color: Get.theme.textPrimary,
                              fontSize: Get.setFontSize(12),
                              fontFamily: Get.setNumberFontFamily(),
                              fontWeight: FontWeightX.regular,
                              overflow: TextOverflow.ellipsis),
                        ),
                      ),
                      Expanded(
                          child: Text(getSymbolPriceValue(balannce ?? ""),
                              maxLines: 1,
                              textAlign: TextAlign.end,
                              style: styleHomeRateSymbol)),
                    ],
                  ),
                ],
              ),
            ),
          )),
    );
  }

  String getCount() {
    CoinType? coinType =
        CoinBase.getCoinTypeByChain(homeCoinModel!.coinModel.chain);

    int count = homeCoinModel!.isToken
        ? homeCoinModel!.tokens.length
        : homeCoinModel!.addresses.length;
    if (coinType is EosChain) {
      return ID.textAccountCount.trParams({
        'indexAcc': count.toString(),
      });
    }
    return ID.textAddressCount.trParams({
      'indexAdd': count.toString(),
    });
  }

  String getSymbolPriceValue(String balance) {
    if (homeCoinModel!.coinModel.price == null ||
        Get.isEmptyString(balance) ||
        AppController.usdRateModel.value == null) {
      return CommonConstant.emptyAsstes;
    }

    String value = CurrencyController.convertCurrency(
        usdRateModel: AppController.usdRateModel.value,
        unitPrice: homeCoinModel!.coinModel.price,
        balance: balance);

    value = CurrencyController.formatMoney(value,
        withThousandmat: true, hasUnit: true);

    return value;
  }
}
