/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-12-11 10:26:11
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/supprot_token_list_dialog.dart';
import 'package:coinbag/modules/wallet/home/<USER>/token_list_dialog.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class HomeTopWidget extends BaseStatelessWidget<WalletController>
    implements PreferredSizeWidget {
  const HomeTopWidget({super.key});

  @override
  Size get preferredSize => Size.fromHeight(Get.setHeight(265) -
      (controller.visibleSearch ? 0 : controller.searchHeight));
  @override
  Widget build(BuildContext context) {
    return GetBuilder<WalletController>(
        id: GetKey.walletHomtTopList,
        builder: (controller) {
          return LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _addressItem(controller),
                  _totalAssets(controller),
                  _actionWallet(context, controller),
                  const DividerWidget(),
                  _searchItem(context, controller),
                ],
              ),
            );
          });
        });
  }

  Padding _addressItem(WalletController controller) => Padding(
        padding: EdgeInsets.only(bottom: Get.setHeight(6)),
        child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                controller.isAllChains
                    ? Text(ID.totalAsset.tr,
                        style: TextStyle(
                            color: Get.theme.textSecondary,
                            fontSize: Get.setFontSize(14),
                            fontWeight: FontWeightX.medium))
                    : GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () => Get.copy(controller.address),
                        child: Row(
                          children: [
                            Text(AddressUtils.omitAddress(controller.address),
                                style: TextStyle(
                                    color: Get.theme.textSecondary,
                                    fontSize: Get.setFontSize(14),
                                    fontWeight: FontWeightX.medium)),
                            SizedBox(width: Get.setWidth(8)),
                            ImageWidget(
                              assetUrl: 'copy',
                              width: Get.setImageSize(18),
                              height: Get.setImageSize(18),
                            ),
                          ],
                        ),
                      ),
                Padding(
                  padding: EdgeInsets.only(left: Get.setPaddingSize(8)),
                  child: Container(
                    height: Get.setHeight(12),
                    width: Get.setWidth(0.5),
                    color: Get.theme.colorD3D3D3,
                  ),
                ),
                GestureDetector(
                  onTap: () => controller.hideAssetsAction(),
                  behavior: HitTestBehavior.opaque,
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: Get.setPaddingSize(8)),
                    child: ImageWidget(
                      assetUrl: controller.getHideImage(),
                      width: Get.setImageSize(20),
                      height: Get.setImageSize(20),
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              ImageWidget(
                assetUrl: 'icon_deveice_bar',
                width: Get.setImageSize(51),
                height: Get.setImageSize(18),
              ),
              Text(
                controller.getDeviceType()!,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    color: Get.theme.bgColor),
              )
            ],
          )
        ]),
      );

  Obx _totalAssets(WalletController controller) => Obx(() => Padding(
        padding: EdgeInsets.only(bottom: Get.setPaddingSize(8)),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                controller.getUnit(),
                style: TextStyle(
                  fontSize: Get.setFontSize(20),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.medium,
                  color: Get.theme.textPrimary,
                ),
              ),
              SizedBox(
                width: Get.setWidth(4),
              ),
              Expanded(
                child: Text.rich(
                    maxLines: 1,
                    TextSpan(children: <InlineSpan>[
                      TextSpan(
                        text: controller.getTotalAssets(),
                        style: TextStyle(
                            fontSize: Get.setFontSize(40),
                            fontFamily: Get.setNumberFontFamily(),
                            fontWeight: FontWeightX.semibold,
                            color: Get.theme.textPrimary,
                            overflow: TextOverflow.ellipsis),
                      ),
                      WidgetSpan(
                        child: Visibility(
                          visible: controller.isShowLoading.value,
                          child: Padding(
                            padding: EdgeInsets.only(
                                bottom: Get.setPaddingSize(10),
                                left: Get.setPaddingSize(4)),
                            child: CupertinoActivityIndicator(
                              key: const Key('wallet_page_top_loading'),
                              radius: Get.setRadius(6),
                            ),
                          ),
                        ),
                      ),
                    ])),
              )
            ]),
      ));

  Padding _actionWallet(BuildContext context, WalletController controller) =>
      Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setHeight(20)),
        child: Row(children: [
          _actionItem('icon_receive', ID.receive.tr, onTap: () async {
            KeyboardUtils.hideKeyboard(context);
            WalletAction action = WalletAction.receive;
            if (controller.isAllChains) {
              TokenListDialog(walletController: controller, action: action)
                  .showBottomSheet();
            } else {
              controller
                  .getTokenList(action)[0]
                  .toNavigate(context, action, controller);
            }
          }),
          _actionItem(
              controller.wallet.isP1P2P3Wallet ? 'icon_cpu_lease' : 'icon_send',
              controller.wallet.isP1P2P3Wallet
                  ? ID.stringSyncAmount.tr
                  : ID.send.tr, onTap: () {
            KeyboardUtils.hideKeyboard(context);
            WalletAction? action = controller.wallet.isP1P2P3Wallet
                ? WalletAction.syncBalance
                : WalletAction.send;
            if (controller.isAllChains) {
              TokenListDialog(walletController: controller, action: action)
                  .showBottomSheet();
            } else {
              controller
                  .getTokenList(action)[0]
                  .toNavigate(context, action, controller);
            }
          }),
          _actionItem('icon_scan', ID.scan.tr, onTap: () async {
            KeyboardUtils.hideKeyboard(context);
            // 导航到详情页面
            await Get.toScanner();
          }),
          _actionItem('icon_record', ID.activity.tr, onTap: () {
            KeyboardUtils.hideKeyboard(context);

            WalletAction action = WalletAction.asstes;
            if (controller.isAllChains) {
              TokenListDialog(
                      walletController: controller, action: WalletAction.asstes)
                  .showBottomSheet();
            } else {
              controller
                  .getTokenList(action)[0]
                  .toNavigate(context, action, controller);
            }
          }),
        ]),
      );

  Expanded _actionItem(String? assetUrl, String? title,
          {GestureTapCallback? onTap}) =>
      Expanded(
        child: GestureDetector(
          key: Key('walelt_home_action_item_${title ?? ""}'),
          onTap: onTap,
          child: Column(
            children: [
              ImageWidget(
                assetUrl: assetUrl,
                width: Get.setImageSize(48),
                height: Get.setImageSize(48),
                fit: BoxFit.contain,
              ),
              SizedBox(
                height: Get.setHeight(8),
              ),
              Text(
                title ?? '',
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.medium,
                    color: Get.theme.textPrimary,
                    overflow: TextOverflow.ellipsis),
              )
            ],
          ),
        ),
      );

  Visibility _searchItem(BuildContext context, WalletController controller) {
    return Visibility(
      visible: controller.visibleSearch,
      child: SizedBox(
        height: controller.searchHeight,
        child: Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: 'icon_search',
                width: Get.setImageSize(16),
                height: Get.setImageSize(16),
              ),
              Expanded(
                child: TextFieldWidget(
                    controller: controller.searchController,
                    showClear: true,
                    onClear: () => controller.onClear(context),
                    onValueChanged: (text) =>
                        controller.onSearch(context, text),
                    maxLines: 1,
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: Get.setPaddingSize(6),
                        vertical: Get.setPaddingSize(13)),
                    hintText: ID.currencySearch.tr,
                    textStyle: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setNumberFontFamily(),
                      fontWeight: FontWeightX.medium,
                    ),
                    hintStyle: TextStyle(
                      color: Get.theme.textTertiary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setFontFamily(),
                      fontWeight: FontWeightX.regular,
                    ),
                    textInputType: TextInputType.emailAddress,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  KeyboardUtils.hideKeyboard(context);
                  controller.isAllChains
                      ? SupportTokenListDialog(
                          wallet: controller.wallet,
                          homeDataList: controller.dataList,
                          walletModel: controller.walletModel,
                        ).showBottomSheet()
                      : Get.toNamed(AppRoutes.tokenManagerPage, arguments: {
                          GetArgumentsKey.coinType: controller.coinType,
                          GetArgumentsKey.walletModel: controller.walletModel,
                        });
                },
                child: Container(
                  padding: EdgeInsets.all(Get.setPaddingSize(8)),
                  child: ImageWidget(
                    assetUrl: 'icon_add_circle',
                    width: Get.setImageSize(24),
                    height: Get.setImageSize(24),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
