/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-12-05 17:29:54
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_coin_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/solana/sol.dart';
import 'package:wallet_core/wallet/wallet.dart';

class SupportTokenListDialog extends BaseStatelessWidget {
  final Wallet wallet;
  final CoinType? coinType;
  final WalletModel? walletModel;
  final List<HomeCoinModel>? homeDataList;
  final Function(CoinType coinType)? callback;
  final bool? isAddToken;
  const SupportTokenListDialog({
    super.key,
    required this.wallet,
    this.walletModel,
    this.coinType,
    this.homeDataList,
    this.callback,
    this.isAddToken,
  });

  Future<void> showBottomSheet() async {
    Get.showBottomSheet(
      title: ID.stringSelectChain.tr,
      hideHeader: false,
      paddingBottom: 0,
      bodyWidget: this,
      bottomWidget: const BottomCancelWidget(),
    );
  }

  @override
  build(BuildContext context) {
    List<CoinType> dataList = CoinBase.filterWalletCoinTypesByToken(wallet);
    if (isAddToken == true) {
      dataList = dataList.where((e) => e is! SolanaChain).toList();
    }
    dataList = dataList
        .where(
          (coinType) => (homeDataList ?? []).isEmpty
              ? coinType.isSupportToken
              : homeDataList!.any(
                  (homeModel) => homeModel.coinModel.chain == coinType.chain),
        )
        .toList();

    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: dataList.map((model) => _itemBuilder(model)).toList()),
    );
  }

  Widget _itemBuilder(CoinType model) {
    return HighLightInkWell(
        onTap: () {
          if (callback == null) {
            Get.back();
            Get.toNamed(AppRoutes.tokenManagerPage, arguments: {
              GetArgumentsKey.coinType: model,
              GetArgumentsKey.walletModel: walletModel
            });
          } else {
            callback!(model);
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setPaddingSize(18),
              horizontal: Get.setPaddingSize(16)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: CoinBase.getChainIcon(model.chain),
                width: Get.setImageSize(20),
                height: Get.setImageSize(20),
              ),
              SizedBox(
                width: Get.setWidth(6),
              ),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      model.symbol,
                      style: styleHomeSymbol,
                    ),
                    Text(
                      " - ${model.chainName}",
                      style: TextStyle(
                        color: Get.theme.textSecondary,
                        fontSize: Get.setFontSize(12),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.regular,
                      ),
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: model.chain == coinType?.chain,
                child: ImageWidget(
                  assetUrl: 'icon_select',
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(20),
                ),
              ),
            ],
          ),
        ));
  }
}
