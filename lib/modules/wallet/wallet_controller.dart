/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-16 13:36:56
 */

import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:coinbag/main/main_controller.dart';
import 'package:coinbag/modules/wallet/common/wallet_action.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_coin_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/market_price_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/home_repository.dart';
import 'package:coinbag/res/strings.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/eos/eos.dart';
import 'package:wallet_core/wallet/wallet.dart';

class WalletController extends BaseRefreshController<ApiService> {
  final ScrollController scrollController = ScrollController();

  var dataList = <HomeCoinModel>[];
  var sourceDataList = <HomeCoinModel>[];
  var markModelList = <MarketPriceModel>[];

  final TextEditingController searchController = TextEditingController();

  var isClickable = true.obs; //可点击进入交易记录

  var canOpenSacn = true.obs; //可点击进入扫码

  DateTime? _lastRefreshTime; //时间戳判断两次刷新操作之间的间隔

  Rx<String> unit = "".obs;
  String topAddressLabel = ID.chainAllChains.tr;
  String topChainIcon = AllChain.get.chainIcon;
  Rx<RxStatus> walletStatus = RxStatus.empty().obs;
  String? eosPublicekey;

  bool isAllChains = true;
  bool hideAssets = false;
  bool visibleSearch = true;
  double searchHeight = Get.setHeight(44);
  bool firstRefresh = false; // 首页资产导入钱包首次刷新
  Rx<bool> isShowLoading = false.obs; // 首页资产是否在Loading
  String totalAssets = CommonConstant.emptyTotalAsstes;
  late BlockChainService blockChainService;

  late WalletModel? walletModel;
  late AppDatabase appDatabase;
  late HomeRepository homeRepository;

  late CoinType coinType; //当前CoinType
  late String chain; //当前选中链
  late String address; //当前选中地址
  late Wallet wallet; //当前Wallet
  late String walletId; //当前WalletId
  late int batch;

  @override
  void onInit() {
    super.onInit();
    initData();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  void initData() async {
    blockChainService = Get.find<BlockChainService>();
    appDatabase = Get.database;

    /// 切换币种刷新页面
    ever(AppController.currency, (model) {
      unit.value = CurrencyController.getCurrencyUnitByCurrency(model);

      setTotalAssets();
      update([GetKey.walletHomtTopList, GetKey.walletTokenList]);
    });

    /// 被动刷新首页,关闭链，添加币种，或广播成功时，绑定或切换钱包时
    ever(AppController.refreshHomeAssets, (refresh) {
      if (!refresh) return;

      // 重置刷新状态
      AppController.refreshHomeAssets.value = false;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        // 处理加载显示
        _handleLoadingState();

        // 滚动到顶部
        _handleScrollToTop();

        // 刷新数据
        refreshData();
      });
    });
  }

  @override
  void loadData() {
    unit.value = CurrencyController.getCurrencyUnit();
    hideAssets = isHideAssets();
    refreshData();
  }

  // 处理加载状态显示
  void _handleLoadingState() {
    if (AppController.homeShowLoading.value) {
      firstRefresh = true; // 标记为首次刷新
      showLoading(); // 显示加载指示器
      isShowLoading.value = true;
      AppController.homeShowLoading.value = false; // 更新加载状态
    }
  }

// 处理滚动到顶部
  void _handleScrollToTop() {
    if (AppController.homeScrollToTop.value) {
      scrollToTop(); // 执行滚动到顶部
      AppController.homeScrollToTop.value = false; // 更新滚动状态
    }
  }

  Future<void> refreshData() async {
    final now = DateTime.now();
    if (_lastRefreshTime == null ||
        now.difference(_lastRefreshTime!).inSeconds > 0.3) {
      // 时间间隔大于0.3秒执行实际的刷新逻辑
      _lastRefreshTime = now;
      isClickable.value = true;

      walletModel = await appDatabase.walletDao.getCheckedWallets();

      if (walletModel == null) {
        walletStatus.value = RxStatus.empty();
        Get.find<MainController>().isSupportNft.value = false;

        return;
      }

      walletId = walletModel!.walletId!;
      chain = walletModel!.chain!;
      if (Get.isEmptyString(chain)) {
        chain = AllChain.get.chain;
      }
      batch = walletModel!.batchId!;
      walletStatus.value = RxStatus.success();
      wallet = Wallet.getWalletByBatch(batch);
      if (AppController.isConnencWallet.value &&
          Get.isRegistered<MainController>()) {
        AppController.isConnencWallet.value = false;
        Get.find<MainController>().updataItem(wallet);
      }
      coinType = CoinBase.getCoinTypeByChain(chain)!;
      Get.appController.showVIPCase(wallet, walletModel!);

      if (coinType is AllChain) {
        isAllChains = true;
        topAddressLabel = ID.chainAllChains.tr;
        topChainIcon = AllChain.get.chainIcon;
        requestPageData();
      } else {
        isAllChains = false;
        requestPageData();
      }
    }
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) async {
    if (refresh == Refresh.first || refresh == Refresh.pull) {
      dataList.clear();
    }
    if (refresh != Refresh.pull && !firstRefresh) {
      isShowLoading.value = true;
    }

    address = isAllChains ? "" : walletModel!.address!;
    searchController.clear();
    homeRepository = HomeRepository(
        deviceId: walletModel!.deviceId!,
        walletId: walletId,
        batchId: walletModel!.batchId!,
        chain: chain,
        wallet: wallet,
        blockChainService: blockChainService,
        apiService: api,
        currentAddress: address,
        appDatabase: appDatabase);

    if (!isAllChains) {
      AddressModel? addressModel =
          await homeRepository.getDefaultAddressModel(chain, address);
      if (addressModel != null) {
        address = addressModel.address!;
        topAddressLabel = addressModel.addressLabel!;
        topChainIcon = coinType.chainIcon;
      }
    }

    await setDataList();

    if (wallet.isSupportEos && (isAllChains || coinType is EosChain)) {
      eosPublicekey = await homeRepository.getEosPublickey();
      if (Get.isEmptyString(eosPublicekey)) {
        _fetchBalanceData();
      } else {
        httpRequest<BaseResponseV1<dynamic>>(
            homeRepository.getHomeEosAccountFuture(eosPublicekey),
            (value) async {
              await homeRepository.handleEosAcctount(value);
              await setDataList(isLoadEosCache: true);
              _fetchBalanceData();
            },
            handleError: false,
            error: (e) async {
              _fetchBalanceData();
            });
      }
    } else {
      _fetchBalanceData();
    }
  }

  Future<void> _fetchBalanceData() async {
    List<Future<dynamic>> futures =
        await homeRepository.getHomeBalanceFuture(dataList);
    multiHttpRequest(futures, handleError: false, (value) async {
      await homeRepository.handleResponse(
          value: value, homeCoinModelsList: dataList);
      await setDataList();
      firstRefresh = false;
      isShowLoading.value = false;
      hideRefresh(refreshController);
      if (AppConfig.instance.isTestMode) {
        Get.dismissLoadingDialog();
      }
    }, error: (e) {
      showToastMessage(e: e, isShowToast: true);
      firstRefresh = false;
      isShowLoading.value = false;
      hideRefresh(refreshController);
      if (AppConfig.instance.isTestMode) {
        Get.dismissLoadingDialog();
      }
    });
  }

  Future<void> setDataList({bool isLoadEosCache = false}) async {
    dataList = await homeRepository.createHomeCoinModel(address);
    sourceDataList = dataList;
    if (dataList.isEmpty) {
      sourceDataList = dataList;
      showEmpty();
    } else {
      showSuccess();
    }
    if (!isLoadEosCache) {
      setTotalAssets();
      update([GetKey.walletHomtTopList, GetKey.walletTokenList]);
    }

    updateSearchStatus();
  }

  void updateSearchStatus() {
    if (coinType.isSupportToken || coinType is AllChain) {
      List<CoinType> supportTokens =
          CoinBase.filterWalletCoinTypesByToken(wallet);
      List<CoinType> resultList = supportTokens
          .where((coinType) => dataList
              .any((homeModel) => homeModel.coinModel.chain == coinType.chain))
          .toList();
      visibleSearch = resultList.isNotEmpty;
    } else {
      visibleSearch = false;
    }
  }

  String getUnit() {
    if (hideAssets) {
      return "";
    }
    return unit.value;
  }

  String? getWaleltName() {
    return walletModel!.walletName;
  }

  String? getDeviceType() {
    return walletModel!.deviceType;
  }

  void hideAssetsAction() {
    hideAssets = !hideAssets;
    update([GetKey.walletHomtTopList, GetKey.walletTokenList]);
    StorageManager.saveValue(key: StorageKey.hideAssets, value: hideAssets);
  }

  bool isHideAssets() {
    return StorageManager.getValue(
          key: StorageKey.hideAssets,
        ) ??
        false;
  }

  String getHideImage() {
    return hideAssets ? 'icon_eye_close' : 'icon_eye_open';
  }

  void setTotalAssets() {
    totalAssets = CommonConstant.emptyTotalAsstes;
    if (dataList.isEmpty || AppController.usdRateModel.value == null) {
      return;
    }
    totalAssets = dataList.fold(
        "0",
        (String total, HomeCoinModel homeCoinModel) => DecimalUtils.add(
            (Get.isEmptyString(total) ||
                    !GetUtils.isNum(total) ||
                    total == CommonConstant.emptyAsstes ||
                    total == CommonConstant.emptyTotalAsstes)
                ? "0"
                : total,
            homeCoinModel.getSymbolPriceValue(),
            scale: 2));
    Get.database.walletDao.updateTotalAssets(walletId, totalAssets);
  }

  String getTotalAssets() {
    if (hideAssets) {
      return CommonConstant.hideAssetsStr;
    }
    if (dataList.isEmpty ||
        AppController.usdRateModel.value == null ||
        firstRefresh) {
      return CommonConstant.emptyTotalAsstes;
    }
    if (DecimalUtils.isZeros(totalAssets)) {
      return CommonConstant.zeroAsstes;
    }
    return CurrencyController.formatMoney(totalAssets,
        legal: false, withThousandmat: true, hasUnit: false);
  }

  void onClear(BuildContext context) {
    KeyboardUtils.hideKeyboard(context);
    dataList = sourceDataList;
    searchController.clear();
    showSuccess();
    update([GetKey.walletTokenList]);
  }

  void onSearch(BuildContext context, String key) {
    if (ObjectUtil.isEmptyString(key)) {
      KeyboardUtils.hideKeyboard(context);
      dataList = sourceDataList;
      showSuccess();
    } else {
      dataList = sourceDataList
          .where((model) =>
              model.coinModel.symbol!.toLowerCase().contains(key.toLowerCase()))
          .toList();
      if (dataList.isEmpty) {
        showEmpty();
      } else {
        showSuccess();
      }
    }
    update([GetKey.walletTokenList]);
  }

  void scrollToTop() {
    if (scrollController.positions.isEmpty) return;

    if (scrollController.hasClients) {
      scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  List<HomeCoinModel> getTokenList(WalletAction action) {
    if (action == WalletAction.syncBalance) {
      return sourceDataList
          .where((model) => model.coinModel.chain != EosChain.get.chain)
          .toList();
    } else {
      return sourceDataList;
    }
  }

  @override
  void onClose() {
    scrollController.dispose();
    searchController.dispose();
    // 关闭所有弹窗
    if (Get.isDialogOpen!) {
      Get.back(); // 关闭弹窗
    }
    super.onClose();
  }
}

class WalletBinding extends Bindings {
  @override
  void dependencies() {}
}
