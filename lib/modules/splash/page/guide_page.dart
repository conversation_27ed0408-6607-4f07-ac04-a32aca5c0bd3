/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-08-13 10:59:58
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/splash/controllers/guide_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class GuidePage extends BaseStatelessWidget<GuideController> {
  const GuidePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(hideLeading: true),
      body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: 'guide_icon2',
                width: Get.width,
                height: Get.setImageSize(287),
              ),
              SizedBox(height: Get.setPaddingSize(56)),
              Text(
                ID.stringGuideTitle2.tr,
                style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(30),
                    fontWeight: FontWeightX.semibold),
              ),
              SizedBox(height: Get.setHeight(16)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                child: Text(ID.stringGuideSubTitle2.tr,
                    style: TextStyle(
                      color: Get.theme.textPrimary,
                      fontSize: Get.setFontSize(14),
                    )),
              ),
              SizedBox(height: Get.setHeight(40)),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: Get.setWidth(44)),
                child: ButtonWidget(
                  width: Get.width,
                  height: Get.setHeight(44),
                  text: ID.stringGuideimmediatelyExperience.tr,
                  onPressed: () {
                    Get.offAllNamed(AppRoutes.mainPage);
                  },
                ),
              )
            ],
          )),
    );
  }
}
