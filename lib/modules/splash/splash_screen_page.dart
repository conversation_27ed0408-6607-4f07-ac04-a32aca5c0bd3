/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-20 13:51:38
 */
/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-03-20 13:37:45
 */
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/splash/splash_screen_controller.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SplashScreenPage extends BaseStatelessWidget<SplashScreenController> {
  const SplashScreenPage({super.key});

  @override
  Widget build(BuildContext context) {
    controller.delayedAction();
    return const Scaffold(
      body: Center(
        child: ImageWidget(
          width: 142,
          height: 142,
          assetUrl: "icon_screen_logo",
        ),
      ),
    );
  }
}
