/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-20 13:41:32
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/main/main_controller.dart';
import 'package:coinbag/modules/dapp/dapp_controller.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/modules/profile/profile_controller.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SplashScreenController extends BaseController {
  @override
  void loadData() {}

  ///旧版本用户开启过生物识别
  bool _isOldAuth() {
    bool? auth = StorageManager.getValue(key: StorageKey.isAuth);
    if (auth == true) {
      StorageManager.remove(key: StorageKey.isAuth);
      return true;
    }
    return false;
  }

  void delayedAction() {
    Future.delayed(const Duration(seconds: 2)).then((value) async {
      if (_isOldAuth()) {
        Get.showAlertDialog(
          title: ID.stringTips.tr,
          barrierDismissible: false,
          disableBack: false,
          content: ID.stringIsOpenSecurity.tr,
          onConfirmText: ID.stringConfirm.tr,
          onCancelText: ID.strinTalkAboutItlater.tr,
          onContinue: () => toMain(),
          onCancel: () => toMain(),
          onConfirm: () async {
            Get.back();
            Get.offAllNamed(AppRoutes.securitySettingPage, arguments: true);
          },
        );
      } else {
        launch();
      }
    });
    if (GetPlatform.isAndroid) {
      SystemUiOverlayStyle style = const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      );
      SystemChrome.setSystemUIOverlayStyle(style);
    }
  }
}

void launch() {
  bool isSecurity = Get.isSecurity();
  if (isSecurity == false) {
    if (Get.isFirstLaunch()) {
      Get.offAllNamed(AppRoutes.guidePage);
    } else {
      Get.offAllNamed(AppRoutes.mainPage);
    }
  } else {
    Get.offAllNamed(AppRoutes.securityPage, arguments: SecurityState.launch);
  }
}

void toMain() {
  Get.offAllNamed(AppRoutes.mainPage);
}

class SplashScreenBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SplashScreenController());
    Get.lazyPut(() => MainController());
    Get.lazyPut(() => WalletController());
    Get.lazyPut(() => NftController());
    Get.lazyPut(() => DappController());
    Get.lazyPut(() => ProfileController());
  }
}
