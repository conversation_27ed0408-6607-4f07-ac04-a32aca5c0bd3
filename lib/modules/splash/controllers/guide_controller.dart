/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-05 14:46:09
 */

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/res/resource.dart';

class GuideController extends BaseController {
  @override
  void loadData() async {}
}

class GuideBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GuideController());
  }
}
