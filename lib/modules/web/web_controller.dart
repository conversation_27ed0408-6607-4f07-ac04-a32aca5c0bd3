/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-02-26 16:59:59
 */

import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/modules/dapp/browser/dapp_browser_controller.dart';
import 'package:coinbag/modules/dapp/browser/ethereum/etherum_dapp_controller.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/dapp/inject/inject_dapp_controller.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/modules/web/dialog/web_action_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';
import 'package:wallet_core/wallet/wallet.dart';

class WebController extends BaseController<AppDatabase> {
  var title = ''.obs;
  var url = ''.obs;
  var chain = EthereumChain.get.chain;
  var chainId = 1;
  int callbackId = -1;
  var rpcUrl = '';
  var address = '';
  var defaultEthereumAddress = '';

  var approveContractAddress = <String>[];

  var maxFee = '';
  var baseFee = '';
  var maxPriorityFee = '';
  var minPriorityFee = '';
  var gasLimit = '';
  var gasPrice = '';

  var gasLimitText = '';

  ///当前页面是否关闭
  var isAlive = false.obs;

  bool isInitLoad = false;

  bool isSendResutling = false;

  ///当前对话框是否
  bool isDialogShowing = false;

  var isApproveAddress = false;
  var isPending = false.obs;
  var visibleRawData = false.obs;
  var visibleData = false.obs;
  var visibleBabylonChainData = false.obs;
  var onWebViewCreated = true.obs;
  late BlockChainService blockChainService;
  var authWarningCheckOne = false.obs;
  var authWarningCheckTwo = false.obs;

  ///是否合约交易
  bool isApproveTransaction = false;

  CoinType? coinType = EthereumChain.get;

  ///dapp相关数据
  CoinModel? coinModel;
  String? amount = '';

  ///金额显示
  String? amountText = '';

  ///金额显示
  List<String>? fromList = []; //发送地址
  List<String>? toList = []; //接受地址
  var fee = ''.obs; //展示矿工费
  var feeValue = ''; //实际矿工费
  ///矿工费详情
  var feePrice = ''.obs;

  ///签名原始数据
  String? rawData;

  ///展示的签名消息
  String? uftMessage;

  ///isEip1559Transaction
  bool isEip1559Transaction = false;

  ///消息签名方法
  String? signMethod;

  ///签名原始数据
  TransferModel? tsModel; //交易model

  ///当前地址Model
  AddressModel? addressModel;

  /// BABY addressModel
  AddressModel? bbnAddressModel;

  ///主链余额

  BalanceModel? balanceModel;

  String? mainChainBalance = '';

  ///network
  String network = '';

  ///FeeList
  List<FeeGearModel>? feeGearList;

//当前fee对象
  FeeGearModel? gearModel;

//交易二维码类型
  QRCodeType? qrCodeType;

  var showTitle = true.obs;
  bool isOpenDappBrowser = false;
  bool isOpenDappBrowserFromSearch = false;
  var isLoaded = false.obs; // 网页是否加载完成
  bool isWalletSupportDapp = false;
  var isFavorited = false.obs; //是否已收藏
  var onLoadError = false.obs;
  var progress = 0.0.obs; // 加载进度
  late DappModels? dappModel;
  late WalletModel? walletModel;
  late Wallet? wallet;
  InAppWebViewController? webViewController; // WebView 控制器

  @override
  void onInit() {
    super.onInit();
    Log.r("onInit");
    url.value = Get.arguments?[GetArgumentsKey.url] ?? '';
    title.value = Get.arguments?[GetArgumentsKey.title] ?? '';
    showTitle.value = Get.arguments?[GetArgumentsKey.showTitle] ?? true;
    isOpenDappBrowser =
        Get.arguments?[GetArgumentsKey.isOpenDappBrowser] ?? false;
    isOpenDappBrowserFromSearch =
        Get.arguments?[GetArgumentsKey.isOpenDappBrowserFromSearch] ?? false;
    dappModel = Get.arguments?[GetArgumentsKey.dappModel];
    if (dappModel != null) {
      title.value = dappModel!.dAppName ?? "";
      url.value = dappModel!.getDappUrl();
      if (dappModel!.chain != null && !Get.isEmptyString(dappModel!.chain!)) {
        chain = dappModel!.chain!;
      }
    }

    url.value = FileUtils.formatUrl(url.value);
    walletModel = Get.arguments?[GetArgumentsKey.walletModel];
    wallet = Get.arguments?[GetArgumentsKey.wallet];
    blockChainService = Get.find<BlockChainService>();

    /// 取消交易
    ever(AppController.cancelTx, (refresh) {
      if (!refresh) return;
      // 重置刷新状态
      AppController.cancelTx.value = false;
      onSendError("cancel");
    });
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() async {
    if (wallet != null) {
      isWalletSupportDapp = wallet!.isSupportDapp;
      if (isWalletSupportDapp && walletModel != null) {
        isApproveAddress = await isApprove();
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Log.r("onReady");
          loadChainData(selectChain: chain);
        });
      }
    }
    checkFavorited();
  }

  Future<void> checkFavorited() async {
    if (dappModel != null && showWalletAction()) {
      isFavorited.value = await api.dappDao.isFavorited(dappModel!.dAppId!);
    }
  }

  // 处理加载进度
  void updateProgress(double newProgress) {
    progress.value = newProgress;
  }

  // 加载特定的 URL
  void loadUrl() {
    onLoadError.value = false;
    isLoaded.value = false;
    progress.value = 0.0;
    webViewController!.loadUrl(urlRequest: URLRequest(url: WebUri(url.value)));
  }

  // 处理网页加载完成
  void onWebViewLoaded() {
    isLoaded.value = true;
  }

  bool showWalletAction() {
    return isWalletSupportDapp &&
        isOpenDappBrowser &&
        AppConfig.instance.enableDapp;
  }

  // 重新加载网页
  void reload() {
    if (onLoadError.value) {
      onLoadError.value = false;
      isLoaded.value = false;
      progress.value = 0.0;
    } else {
      onLoadError.value = false;
      isLoaded.value = false;
      progress.value = 0.0;
      if (!onLoadError.value) {
        webViewController!.reload();
      }
    }
  }

  // 重新加载网页
  void goBack() async {
    if (webViewController == null) {
      Get.back();
    } else {
      if (!onLoadError.value && await webViewController!.canGoBack()) {
        webViewController!.goBack();
      } else {
        Get.back();
      }
    }
  }

  // 复制网页
  void actionCoinLink() {
    Get.back();
    Get.copy(url.value);
  }

  // 分享网页
  void actionShare() {
    Get.back();
    Get.share(url.value);
  }

  String getFavoriteIcon() {
    return isFavorited.value
        ? 'icon_action_favorite_f'
        : 'icon_action_favorite';
  }

  String getFavoriteText() {
    return isFavorited.value ? ID.stringFavorited.tr : ID.favorites.tr;
  }

  // 收藏
  void actionFavorites() async {
    await favoritesDapp();
    isFavorited.value = !isFavorited.value;
    Get.back();
    Get.showToast(isFavorited.value
        ? ID.successfullyAddedToFavorites.tr
        : ID.unfavorited.tr);
  }

  // 浏览器打开
  void actionOpenInBrowser() {
    Get.back();
    Get.openLink(url.value);
  }

  void showActionDialog() {
    WebAcitonDialog(webController: this).showBottomSheet();
  }

  Future<void> onLoadStop(
      InAppWebViewController inAppWebViewController, Uri? uri) async {
    Log.r("onLoadStop=");

    if (onLoadError.value) {
      return;
    }

    if (CoinBase.isEthereumSeriesNetWork(chain)) {
      injectjs();
    }
    isInitLoad = false;

    if (showWalletAction()) {
      saveDappRecord(DappMode.rentMode);
    }
    Future.delayed(const Duration(milliseconds: 300), () {
      isLoaded.value = true;
    });
  }

  Future<void> favoritesDapp() async {
    if (isFavorited.value) {
      await api.dappDao.cancelFavorited(dappModel!.dAppId);
    } else {
      await api.dappDao.insertOrUpdateDapp(dappModel!, DappMode.favoritesMode);
    }
  }

  void saveDappRecord(int mode) {
    if (dappModel != null) {
      api.dappDao.insertOrUpdateDapp(dappModel!, mode);
    } else {
      DappModels customdappModels = DappModels(
          abs: url.value,
          dAppId: url.value,
          dAppName: title.value,
          dAppUrl: url.value);
      api.dappDao.insertOrUpdateDapp(customdappModels, mode);
    }
  }

  Future<void> onReceivedError(Uri? uri, WebResourceError error) async {
    Log.r("onReceivedError=$error");

    final errorTypesToHandle = {
      WebResourceErrorType.CANNOT_CONNECT_TO_HOST,
      WebResourceErrorType.CONNECTION_ABORTED,
      WebResourceErrorType.NETWORK_CONNECTION_LOST,
      WebResourceErrorType.TIMEOUT
    };

    onLoadError.value = errorTypesToHandle.contains(error.type) &&
        (error.type != WebResourceErrorType.TIMEOUT || isInitLoad);
  }

  Future<void> onReceivedHttpError(Uri? uri, WebResourceResponse error) async {
    Log.r("onReceivedHttpError=$error");
    if (error.statusCode! == 401) {
      onLoadError.value = true;
    }
  }

  Future<HttpAuthResponse> httpAuth(InAppWebViewController controller) async {
    final url = await controller.getUrl();
    final host = url?.host;

    String? userName;
    String? password;

    await Get.showBottomSheet(
        title: "${ID.loginString.tr} $host ?? ${this.url.value}",
        disableBack: false,
        enableDrag: false,
        barrierDismissible: false,
        bodyWidget: Padding(
          padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            top: Get.setPaddingSize(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                ID.stringWebUserName.tr,
                style: styleSecond_14,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              TextFieldWidget(
                onValueChanged: (value) => userName = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              Text(
                ID.stringWebPassword.tr,
                style: styleSecond_14,
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              TextFieldWidget(
                obscureText: true,
                onValueChanged: (value) => password = value,
              ),
              SizedBox(height: Get.setPaddingSize(24)),
              ButtonWidget(
                width: Get.width,
                text: ID.stringWebLogin.tr,
                onPressed: () {
                  Get.back();
                },
              ),
            ],
          ),
        ));

    if (!Get.isEmptyString(userName) && !Get.isEmptyString(password)) {
      return HttpAuthResponse(
        action: HttpAuthResponseAction.PROCEED,
        username: userName!,
        password: password!,
      );
    }

    return HttpAuthResponse(action: HttpAuthResponseAction.CANCEL);
  }
}

class WebBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => WebController());
  }
}
