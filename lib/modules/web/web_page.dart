/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-11 17:20:04
 * @LastEditTime: 2025-02-26 17:03:20
 */
import 'dart:collection';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/modules/dapp/inject/inject_dapp_controller.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/modules/web/widgets/web_action_widget.dart';
import 'package:coinbag/modules/web/widgets/web_error_weidget.dart';
import 'package:coinbag/modules/web/widgets/web_progress_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:url_launcher/url_launcher.dart';

class WebViewPage extends BaseStatefulWidget<WebController> {
  WebViewPage({super.key}) {
    // 确保 WebController 已注入，防止 Get.find 报错
    if (!Get.isRegistered<WebController>()) {
      Get.lazyPut<WebController>(() => WebController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: baseAppBar(
              onPressed: () async => controller.goBack(),
              titleWidget: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Text(controller.title.value, style: styleAppbarTitle),
              ),
              actionWidget: [
                Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: Get.setPaddingSize(6),
                        horizontal: Get.setPaddingSize(
                            12)), // paddingVertical 和 paddingStart
                    child: const WebActionWidget())
              ]),
          body: controller.onLoadError.value
              ? const WebErrorWidget() // 显示错误 UI
              : _buildWebView(),
        ));
  }

  PopScope<Object> _buildWebView() {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (result != null) {
          return; // 如果 result 不为空，直接返回
        }
        if (didPop) {
          return;
        }
        controller.goBack();
      },
      child: Stack(
        children: [
          InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(controller.url.value)),
            initialUserScripts: UnmodifiableListView<UserScript>([]),
            initialSettings: InAppWebViewSettings(
              mediaPlaybackRequiresUserGesture: false,
              allowsInlineMediaPlayback: true,
              iframeAllow: "camera; microphone",
              javaScriptEnabled: true, // 启用 JavaScript
              useShouldOverrideUrlLoading: true,
              iframeAllowFullscreen: true,
            ),
            onReceivedHttpAuthRequest: (controller, challenge) async {
              // 提供 HTTP 鉴权所需的用户名和密码
              return await this.controller.httpAuth(controller);
            },
            onWebViewCreated: (webController) {
              controller.setupJavaScriptHandlers(webController);
            },
            shouldOverrideUrlLoading:
                (controller, shouldOverrideUrlLoadingRequest) async {
              Log.r("shouldOverrideUrlLoading=");
              WebUri? webUri = shouldOverrideUrlLoadingRequest.request.url;
              final urlString = webUri!.rawValue;
              if (urlString.isEmpty) {
                return NavigationActionPolicy.ALLOW;
              }

              String action = urlString.split(':').first;
              String launchUri = urlString.split(':').last;

              List<String> customActions = [
                'tel',
                'whatsapp',
                'mailto',
                "sms",
                "url"
              ];
              bool isCustomAction = customActions.contains(action);

              Uri uri = Uri(scheme: action, path: launchUri);

              if (isCustomAction) {
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri);
                  return NavigationActionPolicy.CANCEL;
                }
              }
              return NavigationActionPolicy.ALLOW;
            },
            onLoadResource: (webController, url) async {
              Log.r("onLoadResource=");
            },
            onLoadStart: (webController, url) async {
              controller.isInitLoad = true;
              if (GetPlatform.isAndroid) {
                controller.injectjs();
              }
            },
            onPageCommitVisible: (webController, url) async {
              Log.d("onPageCommitVisible= url =$url");
              if (GetPlatform.isIOS) {
                controller.injectjs();
              }
            },
            onLoadStop: (webController, url) async {
              controller.onLoadStop(webController, url);
            },
            onReceivedError: (webController, request, error) async {
              controller.onReceivedError(request.url, error);
            },
            onReceivedHttpError: (webController, request, error) async {
              Log.r("onReceivedHttpError=$error");
              controller.onReceivedHttpError(request.url, error);
            },
            onProgressChanged:
                (InAppWebViewController inAppWebViewController, int progress) {
              controller.progress.value = progress.toDouble() / 100;

              if (!controller.onLoadError.value && controller.isAlive.value) {
                inAppWebViewController
                    .evaluateJavascript(source: 'document.title')
                    .then((value) {
                  if (controller.showTitle.value) {
                    controller.title.value = value ?? '';
                  }
                });
              }

              if (progress == 100) {
                Future.delayed(const Duration(milliseconds: 300), () {
                  if (Get.isRegistered<WebController>()) {
                    controller.isLoaded.value = true;
                  }
                });
              }
            },
          ),
          const WebProgressWidget(),
        ],
      ),
    );
  }
}
