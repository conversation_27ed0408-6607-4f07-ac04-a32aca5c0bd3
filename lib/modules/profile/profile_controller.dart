import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/locale/locale_controller.dart';
import 'package:coinbag/modules/profile/pages/currency_page.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:flutter/material.dart';

enum SettingType {
  walletManager,
  addressBook,
  walletTool,
  currency,
  baseSetting,
  customerService,
  security,
  vipBenefits,
  about,
  network,
}

class ProfileController extends BaseController<AppDatabase> {
  late WalletModel? walletModel;
  Rx<RxStatus> walletStatus = RxStatus.empty().obs;

  /// 货币
  Rx<Currency> currency = CurrencyController.getCurrency.obs;
  RxBool localAuth = false.obs;
  RxBool showVip = false.obs;
  @override
  void onInit() {
    super.onInit();
    localAuth =
        (StorageManager.getValue<bool>(key: StorageKey.isAuth) ?? false).obs;

    /// 绑定或切换钱包时
    ever(AppController.refreshHomeAssets, (refresh) async {
      if (!refresh) return;

      AppController.refreshHomeAssets.value = false;
      loadData();
    });
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() async {
    walletModel = await api.walletDao.getCheckedWallets();
    walletStatus.value =
        walletModel == null ? RxStatus.empty() : RxStatus.success();
    showVip.value = Get.appController.isVip(walletModel);
  }

  void currencyAction() {
    Get.showBottomSheet(
        hideHeader: true,
        paddingBottom: 0,
        padding: const EdgeInsets.symmetric(horizontal: 0),
        bodyWidget: CurrencyPage(controller: this),
        bottomWidget: const BottomCancelWidget());
  }

  // 更新 货币单位
  void updateCurrency(Currency type) {
    if (type != AppController.currency.value) {
      CurrencyController.updateCurrency(type);
      currency.value = type;
      AppController.currency.value = type;
    }
    delayedBack();
  }

  String title(SettingType type) {
    if (type == SettingType.walletManager) {
      return ID.chainWalletManager.tr;
    } else if (type == SettingType.addressBook) {
      return ID.profileAddressBook.tr;
    } else if (type == SettingType.walletTool) {
      return ID.profileTool.tr;
    } else if (type == SettingType.currency) {
      return ID.profileCurrency.tr;
    } else if (type == SettingType.baseSetting) {
      return ID.baseSettings.tr;
    } else if (type == SettingType.security) {
      return ID.stringSecurityVerification.tr;
    } else if (type == SettingType.customerService) {
      return ID.stringonlineservice.tr;
    } else if (type == SettingType.vipBenefits) {
      return ID.stringVIPbenefits.tr;
    } else if (type == SettingType.about) {
      return ID.profileAbout.tr;
    } else if (type == SettingType.network) {
      return ID.stringNetworkDiagnostics.tr;
    }
    return '';
  }

  String icon(SettingType type) {
    if (type == SettingType.walletManager) {
      return 'wallet_manager';
    } else if (type == SettingType.addressBook) {
      return 'address_book';
    } else if (type == SettingType.walletTool) {
      return 'wallet_tool';
    } else if (type == SettingType.currency) {
      return 'currency_unit';
    } else if (type == SettingType.baseSetting) {
      return 'base_setting';
    } else if (type == SettingType.security) {
      return 'icon_security';
    } else if (type == SettingType.customerService) {
      return 'icon_customer';
    } else if (type == SettingType.vipBenefits) {
      return 'icon_vip';
    } else if (type == SettingType.about) {
      return 'about_icon';
    } else if (type == SettingType.network) {
      return 'network_icon';
    }
    return '';
  }

  String getVIPURL() {
    String url = FileUtils.deCodeBase64(AgreementUrl.vipBenefitsUrl);
    Uri builder = Uri.parse(url).replace(queryParameters: {
      'language': LocaleController.getStandardCountryCode(),
      'vipLevel': walletModel!.vipLevel!.toString(),
    });
    return builder.toString();
  }

  void walletManagerAction() {
    if (walletModel == null) {
      return;
    }

    Get.to(
        () => const ChainManagerPage(
              isFullPage: true,
            ),
        arguments: walletModel);
  }

  /// 0.5秒返回
  Future<void> delayedBack() =>
      Future.delayed(const Duration(milliseconds: 500))
          .then((value) => Get.back());
}

class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ProfileController());
  }
}
