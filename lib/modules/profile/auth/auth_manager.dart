import 'package:android_intent_plus/android_intent.dart';
import 'package:app_settings/app_settings.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_platform_interface/local_auth_platform_interface.dart';

class AuthManager {
  static final auth = LocalAuthentication();

  // 判断是否能用生物识别
  static Future<bool> checkBiometrics() async {
    bool? isBiometrics = StorageManager.getValue(key: StorageKey.isBiometrics);
    if (isBiometrics != null) {
      return isBiometrics;
    }
    try {
      bool result = await LocalAuthPlatform.instance.deviceSupportsBiometrics();
      await StorageManager.saveValue(
          key: StorageKey.isBiometrics, value: result);
      return result;
    } on PlatformException catch (_) {
      return false;
    }
  }

  /// 当前生物识别类型
  static Future<BiometricType> biometricType() async {
    int? typeIndex = StorageManager.getValue(key: StorageKey.biometricsType);
    if (typeIndex != null) {
      return BiometricType.values[typeIndex];
    }

    List<BiometricType> list = await getAvailableBiometrics();
    if (GetPlatform.isIOS) {
      BiometricType type = BiometricType.face;
      if (list.isNotEmpty) {
        if (list.contains(BiometricType.face)) {
          type = BiometricType.face;
        } else {
          type = BiometricType.face;
        }
        await StorageManager.saveValue(
            key: StorageKey.biometricsType, value: type.index);
      }
      return type;
    } else {
      await StorageManager.saveValue(
          key: StorageKey.biometricsType,
          value: BiometricType.fingerprint.index);
      return BiometricType.fingerprint;
    }
  }

  // 生物识别列表
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    List<BiometricType> availableBiometrics = [];
    try {
      availableBiometrics = await auth.getAvailableBiometrics();
      Log.logPrint(availableBiometrics);
      return availableBiometrics;
    } on PlatformException catch (_) {
      return [];
    }
  }

  static Future<bool> authenticate() async {
    bool authenticated = false;
    try {
      authenticated = await LocalAuthPlatform.instance.authenticate(
        localizedReason: ID.authFingerprintTip.tr,
        authMessages: [],
        options: AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: GetPlatform.isAndroid,
        ),
      );
      return authenticated;
    } on PlatformException catch (e) {
      Log.logPrint(e);
      return Future.value(false);
    }
  }

  static Future<String> _seetingContent() async {
    if (GetPlatform.isIOS) {
      if (AppController.biometricType == BiometricType.face) {
        return ID.authContnetTextFace.tr;
      } else {
        return ID.authContnetTextFingerprint.tr;
      }
    } else {
      List list = await getAvailableBiometrics();
      if (list.isNotEmpty) {
        return ID.authContnetTextFingerprint.tr;
      } else {
        return ID.authContnetTextSaveFingerprint.tr;
      }
    }
  }

  static Future<void> showAuthSetting() async {
    String contentText = await _seetingContent();
    Get.showAlertDialog(
        title: ID.stringTips.tr,
        content: contentText,
        onConfirmText: ID.gotoSettingTitle.tr,
        onConfirm: () async {
          if (GetPlatform.isAndroid) {
            AndroidIntent intent = const AndroidIntent(
              action: 'android.settings.SETTINGS',
            );
            await intent.launch();
          } else if (GetPlatform.isIOS) {
            await AppSettings.openAppSettings(
                type: AppSettingsType.settings, asAnotherTask: true);
          }

          Get.back();
        });
  }
}
