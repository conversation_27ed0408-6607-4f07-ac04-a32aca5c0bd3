/*
 * @description: Do not edit
 * @Author: wangliming
 * @Date: 2024-01-18 10:19:19
 * @LastEditTime: 2024-09-18 17:14:28
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/chain.dart';

class AddressBookController extends BaseController<AppDatabase>
    with GetTickerProviderStateMixin {
  final Rx<List<AddressBookModel>> addressBookList =
      Rx<List<AddressBookModel>>([]);
  final Rx<List<AddressBookModel>> tabs = Rx<List<AddressBookModel>>([]);
  late TabController tabController;
  Rx<bool> isSelectMode = false.obs;
  late String? chain;
  late WalletModel? walletModel;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 0, vsync: this);
    chain = Get.arguments?[GetArgumentsKey.chain] ?? "";
    isSelectMode.value = !Get.isEmptyString(chain);
    Map? arg = Get.arguments;
    if (arg != null) {
      if (arg.keys.contains(GetArgumentsKey.walletModel)) {
        walletModel = arg[GetArgumentsKey.walletModel];
      }
    }
    loadData();
  }

  @override
  void loadData() {
    Get.database.addressBookDao.watchAllData().listen((data) {
      addressBookList.value = data;
      if (addressBookList.value.isEmpty) {
        tabs.value.clear();
        showEmpty();
      } else {
        if (!isSelectMode.value) {
          var uniqueChain = <String, AddressBookModel>{};
          for (var model in addressBookList.value) {
            uniqueChain[model.chain] = model;
          }
          tabs.value.clear();
          tabs.value = uniqueChain.values.toList();
          AddressBookModel allModel = const AddressBookModel(
              chain: "all", id: -1, address: "", constantName: "");
          tabs.value.insert(0, allModel);
          tabController = TabController(length: tabs.value.length, vsync: this);
        }
        showSuccess();
      }
    });
  }

  String getTag(AddressBookModel? model) {
    if (model!.chain == "all") {
      return ID.stringAll.tr;
    }

    CoinType? coinType = CoinBase.getCoinTypeByChain(model.chain);

    return coinType!.chainName;
  }

  List<AddressBookModel> getDataList(String chain) {
    if (chain == "all") return addressBookList.value;
    return addressBookList.value
        .where((model) => model.chain == chain)
        .toList();
  }

  void changeTab(int index) {
    Get.back();
    tabController.animateTo(index);
  }

  void navigator(AddressBookModel model) {
    if (isSelectMode.value) {
      Get.back(result: model.address);
    } else {
      Get.toNamed(AppRoutes.addContact, arguments: {
        GetArgumentsKey.addressModel: model,
        GetArgumentsKey.walletModel: walletModel,
        GetArgumentsKey.addressBookAction: true,
      });
    }
  }
}

class AddressBookBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AddressBookController());
  }
}
