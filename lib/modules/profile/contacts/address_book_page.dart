/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-26 09:28:35
 * @LastEditTime: 2024-09-13 10:23:07
 */
/*
 * @description: Do not edit
 * @Author: wangliming
 * @Date: 2024-01-18 10:19:19
 * @LastEditTime: 2024-01-26 09:30:00
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/contacts/address_book_controller.dart';
import 'package:coinbag/modules/profile/contacts/views/address_book_list.dart';
import 'package:coinbag/modules/profile/contacts/views/address_tapbar_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:flutter/material.dart';

class AddressBookPage extends BaseStatelessWidget<AddressBookController> {
  const AddressBookPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: baseAppBar(
            title: ID.profileAddressBook.tr,
            actionWidget: [
              IconButton(
                  onPressed: () =>
                      Get.toNamed(AppRoutes.addContact, arguments: {
                        GetArgumentsKey.walletModel: controller.walletModel,
                      }),
                  icon: Padding(
                    padding: EdgeInsets.only(right: Get.setWidth(6)),
                    child: ImageWidget(
                      assetUrl: 'add_scan',
                      width: Get.setImageSize(28),
                      height: Get.setImageSize(28),
                    ),
                  )),
            ],
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(controller.isSelectMode.value ||
                      controller.addressBookList.value.isEmpty
                  ? 0
                  : Get.setHeight(40)),
              child: AddressTabWidget(controller: controller),
            ),
          ),
          body: _bodyWidget(),
        ));
  }

  Widget _bodyWidget() {
    if (controller.addressBookList.value.isEmpty) {
      return const AppEmptyWidget(isCenter: false);
    }

    if (controller.isSelectMode.value) {
      return AddressBookListWidget(
        chain: controller.chain,
      );
    }
    return TabBarView(
        controller: controller.tabController,
        children: controller.tabs.value
            .map((item) => AddressBookListWidget(
                  chain: item.chain,
                ))
            .toList());
  }
}
