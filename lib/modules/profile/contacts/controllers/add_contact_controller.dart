/*
 * @description: Do not edit
 * @Author: wangliming
 * @Date: 2024-01-18 10:19:19
 * @LastEditTime: 2024-12-02 16:06:34
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:drift/drift.dart' as drift;
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/wallet/wallet.dart';

class AddContactController extends BaseController<AppDatabase> {
  final chainController = TextEditingController();
  final addressController = TextEditingController();
  final contactNameController = TextEditingController();

  Rx<ButtonStatus> buttonStatus = ButtonStatus.disable.obs;
  List<CoinType> allChainsList = <CoinType>[];
  Rx<String> chain = "".obs;
  var address = ''.obs;
  var contactName = ''.obs;
  Rx<CoinType?> coinType = Rx<CoinType?>(null);
  bool editAction = false;
  bool sendToContactPageAction = false;

  late AddressBookModel? model;
  late WalletModel? walletModel;
  @override
  void onInit() {
    super.onInit();
    Map? arg = Get.arguments;

    if (arg != null) {
      if (arg.keys.contains(GetArgumentsKey.walletModel)) {
        walletModel = arg[GetArgumentsKey.walletModel];
      }

      if (arg.keys.contains(GetArgumentsKey.address)) {
        address.value = arg[GetArgumentsKey.address]!;
      }

      if (arg.keys.contains(GetArgumentsKey.chain)) {
        chain.value = arg[GetArgumentsKey.chain]!;
      }

      if (arg.keys.contains(GetArgumentsKey.sendToaddressBookAction)) {
        sendToContactPageAction = arg[GetArgumentsKey.sendToaddressBookAction]!;
      }
    }
    if (walletModel != null) {
      Wallet wallet = Wallet.getWalletByBatch(walletModel!.batchId!);
      var supportedChains =
          wallet.supportedChainsByBachId(walletModel!.batchId!);
      supportedChains.sort((a, b) => a.sortIndex.compareTo(b.sortIndex));

      allChainsList = supportedChains.where((coinType) {
        return coinType.isSupportContacts == true;
      }).toList();
    } else {
      allChainsList = CoinBase.filterCoinTypesByContacts();
    }
    // 根据配置决定是否移除测试币
    if (!AppConfig.instance.enableTestNetwork) {
      allChainsList = allChainsList.where((chain) => !chain.isTestNet).toList();
    }
    editAction = Get.arguments?[GetArgumentsKey.addressBookAction] ?? false;
    model = Get.arguments?[GetArgumentsKey.addressModel];
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {
    setEidtData();
  }

  void setEidtData() {
    if (editAction) {
      chain.value = model!.chain;
      address.value = model!.address;
      contactName.value = model!.constantName;
      coinType.value = CoinBase.getCoinTypeByChain(chain.value);
      chainController.text = getChainTag();
      addressController.text = model!.address;
      contactNameController.text = model!.constantName;
      buttonStatus.value = ButtonStatus.enable;
    } else if (sendToContactPageAction) {
      coinType.value = CoinBase.getCoinTypeByChain(chain.value);
      chainController.text = getChainTag();
      addressController.text = address.value;
    }
  }

  String getChainTag() {
    if (coinType.value == null) return "";
    return "${coinType.value!.symbol} - ${coinType.value!.chainName}";
  }

  void onSelectChain(CoinType? selectCoinType) {
    updateButtonStatus();
    coinType.value = selectCoinType!;
    chain.value = selectCoinType.chain;
    chainController.text = getChainTag();
    Get.back();
    coinType.value = null;
  }

  void onAddressChanged(String value) {
    address.value = value;
    updateButtonStatus();
  }

  void onContactChanged(String value) {
    contactName.value = value;
    updateButtonStatus();
  }

  void updateButtonStatus() {
    if (Get.isEmptyString(chain.value) ||
        Get.isEmptyString(address.value) ||
        Get.isEmptyString(contactName.value)) {
      buttonStatus.value = ButtonStatus.disable;
    } else {
      buttonStatus.value = ButtonStatus.enable;
    }
  }

  Future<void> saveAction() async {
    buttonStatus.value = ButtonStatus.loading;

    bool isVerify = await Get.walletCore
        .verifyAddress(chain: chain.value, address: address.value);

    if (!isVerify) {
      buttonStatus.value = ButtonStatus.enable;
      Get.showToast(ID.textCorrectAddress.tr);
      return;
    }

    if (editAction) {
      if (await api.addressBookDao.checkAddressExistsIfUnique(
        address: address.value,
        chain: chain.value,
        constantName: contactName.value,
      )) {
        buttonStatus.value = ButtonStatus.enable;
        Get.back();
        return;
      }

      await api.addressBookDao.upsertAddressBook(
        chain: chain.value,
        address: address.value,
        constantName: contactName.value,
      );
    } else {
      if (await api.addressBookDao.checkAddressExists(
        address: address.value,
        chain: chain.value,
      )) {
        buttonStatus.value = ButtonStatus.enable;
        Get.showToast(ID.addressAlreadyExists.tr);
        return;
      }
      await api.addressBookDao.insertData(AddressBookTableCompanion.insert(
        chain: chain.value,
        address: address.value,
        constantName: contactName.value,
      ));
    }
    buttonStatus.value = ButtonStatus.enable;
    Get.back(result: "result");
  }

  Future<void> deleteAction() async {
    Get.showBottomSheet(
      title: ID.addressBookDelContacts.tr,
      bodyWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              ID.addressBookDelContactsContent.trParams({
                'contacts': model!.constantName,
              }),
              style: TextStyle(
                  fontSize: Get.setFontSize(16),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                  color: Get.theme.textPrimary),
            ),
            SizedBox(height: Get.setHeight(20)),
          ],
        ),
      ),
      bottomWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        child: ButtonWidget(
            text: ID.addressBookDelContactsConfirm.tr,
            onPressed: () async {
              Get.back();
              await api.addressBookDao.deleteData(AddressBookTableCompanion(
                id: drift.Value(model!.id),
              ));

              Get.back();
            }),
      ),
    );
  }

  @override
  void onClose() {
    super.onClose();
    chainController.dispose();
    addressController.dispose();
    contactNameController.dispose();
  }
}

class AddContactBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AddContactController());
  }
}
