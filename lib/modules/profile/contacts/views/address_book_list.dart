/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-08-23 13:13:11
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/profile/contacts/address_book_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:coinbag/widgets/status/app_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';

class AddressBookListWidget extends BaseStatelessWidget<AddressBookController> {
  final String? chain;
  const AddressBookListWidget({super.key, this.chain});

  @override
  build(BuildContext context) {
    return controller.obx((state) => buildContent(controller, chain),
        onLoading: const AppLoadingWidget(),
        onError: (error) =>
            AppErrorWidget(onRefresh: () => controller.loadData()),
        onEmpty: const AppEmptyWidget(isCenter: false));
  }

  ListView buildContent(AddressBookController controller, String? chain) {
    return ListView.builder(
        itemCount: controller.getDataList(chain!).length,
        padding: controller.isSelectMode.value
            ? EdgeInsets.zero
            : EdgeInsets.only(
                top: Get.setPaddingSize(8),
              ),
        itemBuilder: (_, index) => controller.getDataList(chain).isNotEmpty
            ? _itemBuilder(controller, controller.getDataList(chain)[index])
            : const SizedBox.shrink());
  }
}

Widget _itemBuilder(AddressBookController controller, AddressBookModel model) {
  if (model.chain == "all") {
    return const SizedBox.shrink();
  }
  return Container(
    margin: EdgeInsets.symmetric(
      vertical: Get.setPaddingSize(5),
      horizontal: Get.setPaddingSize(16),
    ),
    decoration: ShapeDecoration(
      shape: RoundedRectangleBorder(
        side:
            BorderSide(width: Get.setWidth(0.5), color: Get.theme.colorD3D3D3),
        borderRadius: BorderRadius.circular(Get.setRadius(12)),
      ),
    ),
    child: HighLightInkWell(
      borderRadius: BorderRadius.circular(Get.setRadius(12)),
      onTap: () => controller.navigator(model),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
              Expanded(
                child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text.rich(
                          TextSpan(children: [
                            TextSpan(
                                text: model.constantName,
                                style: TextStyle(
                                  color: Get.theme.textPrimary,
                                  fontSize: Get.setFontSize(16),
                                  fontFamily: Get.setNumberFontFamily(),
                                  fontWeight: FontWeightX.semibold,
                                )),
                            WidgetSpan(
                              alignment: PlaceholderAlignment.middle,
                              child: Container(
                                  margin:
                                      EdgeInsets.only(left: Get.setWidth(8)),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: Get.setPaddingSize(6)),
                                  decoration: ShapeDecoration(
                                    color: getChainThemeColor(model.chain,
                                        alphaColor: true),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          Get.setRadius(5)),
                                    ),
                                  ),
                                  child: Text(
                                    CoinBase.getChanNameByChain(model.chain),
                                    style: TextStyle(
                                      color: getChainThemeColor(model.chain),
                                      fontSize: Get.setFontSize(12),
                                      fontFamily: Get.setNumberFontFamily(),
                                      fontWeight: FontWeightX.semibold,
                                    ),
                                  )),
                            ),
                          ]),
                          textAlign: TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ]),
              ),
              ImageWidget(
                height: Get.setImageSize(12),
                width: Get.setImageSize(12),
                assetUrl: 'icon_new_arrow01',
              ),
            ]),
            SizedBox(height: Get.setHeight(5)),
            Text(
              AddressUtils.omitAddress(model.address, len: 10),
              style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(12),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.regular,
                  overflow: TextOverflow.ellipsis),
            )
          ],
        ),
      ),
    ),
  );
}
