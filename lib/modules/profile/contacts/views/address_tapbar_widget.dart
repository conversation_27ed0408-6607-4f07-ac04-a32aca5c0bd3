/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-04-01 21:16:50
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/profile/contacts/address_book_controller.dart';
import 'package:coinbag/modules/profile/contacts/dialog/chain_tag_dialog.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/tab_bar/base_tab_bar.dart';
import 'package:flutter/material.dart';

class AddressTabWidget extends StatelessWidget {
  const AddressTabWidget({
    super.key,
    required this.controller,
  });

  final AddressBookController controller;

  @override
  Widget build(BuildContext context) {
    if (controller.isSelectMode.value ||
        controller.addressBookList.value.isEmpty) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                  right: Get.setWidth(50), bottom: Get.setHeight(1.5)),
              child: baseTabBar(
                  controller: controller.tabController,
                  hideDivider: true,
                  isScrollable: true,
                  labelPadding: EdgeInsets.only(left: Get.setWidth(20)),
                  tabs: controller.tabs.value
                      .map((element) => Tab(
                            text: controller.getTag(element),
                          ))
                      .toList()),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: GestureDetector(
                onTap: () => ChainTagDialog(addressBookController: controller)
                    .showBottomSheet(),
                child: Container(
                  color: Get.theme.bgColor,
                  padding: const EdgeInsets.all(16),
                  child: ImageWidget(
                    assetUrl: 'arrow_down_gray',
                    width: Get.setImageSize(12),
                    height: Get.setImageSize(12),
                  ),
                ),
              ),
            ),
            const Positioned(
                bottom: 0, left: 0, right: 0, child: DividerWidget())
          ],
        ),
      ],
    );
  }
}
