/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-26 09:28:53
 * @LastEditTime: 2024-08-23 14:06:46
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/contacts/controllers/add_contact_controller.dart';
import 'package:coinbag/modules/profile/contacts/dialog/chain_list_dialog.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';

class AddContactPage extends BaseStatelessWidget<AddContactController> {
  const AddContactPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => KeyboardDismissWidget(
        child: Scaffold(
            appBar: baseAppBar(
                titleWidget: Text(
                  controller.editAction
                      ? ID.addressBookEditContacts.tr
                      : ID.addAddress.tr,
                  style: styleAppbarTitle,
                ),
                actionWidget: [
                  Visibility(
                    visible: controller.editAction,
                    child: IconButton(
                        onPressed: () => controller.deleteAction(),
                        icon: Padding(
                          padding: EdgeInsets.only(right: Get.setWidth(6)),
                          child: ImageWidget(
                            assetUrl: 'icon_del',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                        )),
                  )
                ]),
            body: SingleChildScrollView(
              child: Padding(
                  padding: EdgeInsets.only(
                      top: Get.setHeight(20),
                      right: Get.setWidth(16),
                      left: Get.setWidth(16)),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ID.textChain.tr,
                          style: styleSecond_14,
                        ),
                        SizedBox(
                          height: Get.setHeight(8),
                        ),
                        TextFieldWidget(
                          readOnly: true,
                          controller: controller.chainController,
                          hintText: ID.textChooseChain.tr,
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(Get.setRadius(12)),
                            borderSide: BorderSide(
                              color: Get.theme.colorD3D3D3,
                              width: Get.setWidth(1),
                            ),
                          ),
                          textStyle: TextStyle(
                              fontSize: Get.setFontSize(16),
                              color: Get.theme.textPrimary,
                              fontFamily: Get.setNumberFontFamily(),
                              fontWeight: FontWeightX.medium),
                          prefixIcon: Get.isEmptyString(controller.chain.value)
                              ? null
                              : ImageWidget(
                                  assetUrl: CoinBase.getChainIcon(
                                      controller.chain.value),
                                  width: Get.setImageSize(20),
                                  height: Get.setImageSize(20),
                                ),
                          suffixIcon: ImageWidget(
                            assetUrl: 'down',
                            width: Get.setImageSize(10),
                            height: Get.setImageSize(10),
                          ),
                          onTap: () =>
                              ChainListDialog(addContactController: controller)
                                  .showBottomSheet(),
                        ),
                        SizedBox(
                          height: Get.setHeight(24),
                        ),
                        Text(
                          ID.stringAddress.tr,
                          style: styleSecond_14,
                        ),
                        SizedBox(
                          height: Get.setHeight(8),
                        ),
                        TextFieldWidget(
                          maxLines: 3,
                          maxLength: 60,
                          textInputType: TextInputType.emailAddress,
                          showClear: true,
                          hintText: ID.insertaddress.tr,
                          controller: controller.addressController,
                          onValueChanged: (value) =>
                              controller.onAddressChanged(value),
                          suffixIcon: GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () async {
                              final result = await Get.toScanner(arguments: {
                                GetArgumentsKey.scanAction:
                                    ScanAction.resultAction
                              });
                              KeyboardUtils.hideKeyboardNoContext();
                              if (!Get.isEmptyString(result)) {
                                controller.addressController.text = result;
                                controller.onAddressChanged(result);
                              }
                            },
                            child: ImageWidget(
                              assetUrl: 'icon_scan_black',
                              width: Get.setImageSize(20),
                              height: Get.setImageSize(20),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: Get.setHeight(24),
                        ),
                        Text(ID.addressTag.tr, style: styleSecond_14),
                        SizedBox(
                          height: Get.setHeight(8),
                        ),
                        TextFieldWidget(
                          maxLength: 16,
                          controller: controller.contactNameController,
                          showClear: true,
                          onValueChanged: (value) =>
                              controller.onContactChanged(value),
                          hintText: ID.addressTag.tr,
                        ),
                      ])),
            ),
            bottomNavigationBar: Padding(
              padding: EdgeInsets.only(
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                bottom: Get.getSafetyBottomPadding(),
              ),
              child: ButtonWidget(
                buttonSize: ButtonSize.full,
                text: ID.stringConfirm.tr,
                buttonStatus: controller.buttonStatus.value,
                onPressed: () async => controller.saveAction(),
              ),
            )),
      ),
    );
  }
}
