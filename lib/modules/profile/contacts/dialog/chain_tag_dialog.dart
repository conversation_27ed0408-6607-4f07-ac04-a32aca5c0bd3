/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-02-01 17:18:30
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/profile/contacts/address_book_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:flutter/material.dart';

class ChainTagDialog extends StatelessWidget {
  final AddressBookController? addressBookController;
  const ChainTagDialog({super.key, this.addressBookController});

  Future<void> showBottomSheet() async {
    Get.showBottomSheet(
        hideHeader: true,
        bodyWidget: this,
        paddingBottom: 0,
        bottomWidget: const BottomCancelWidget());
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: Get.setPaddingSize(30), horizontal: Get.setPaddingSize(16)),
      child: Wrap(
          spacing: Get.setWidth(20),
          runSpacing: Get.setWidth(16),
          children: addressBookController!.tabs.value
              .map((item) => tabItem(addressBookController, item,
                  addressBookController!.tabs.value.indexOf(item)))
              .toList()),
    );
  }

  Widget tabItem(
      AddressBookController? controller, AddressBookModel model, int index) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => controller.changeTab(index),
        child: Text(
          controller!.getTag(model),
          style: TextStyle(
            color: Get.theme.textTertiary,
            fontSize: Get.setFontSize(14),
            fontFamily: Get.setNumberFontFamily(),
            fontWeight: FontWeightX.semibold,
          ),
        ));
  }
}
