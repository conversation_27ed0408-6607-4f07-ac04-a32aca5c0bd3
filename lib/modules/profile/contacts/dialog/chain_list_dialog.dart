/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-08-22 14:43:09
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/profile/contacts/controllers/add_contact_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/coin_type.dart';

class ChainListDialog extends BaseStatelessWidget {
  final AddContactController? addContactController;
  const ChainListDialog({super.key, this.addContactController});

  Future<void> showBottomSheet() async {
    Get.showBottomSheet(
        title: ID.stringSelectChain.tr,
        hideHeader: false,
        fullScreenBodyWidget: this,
        paddingBottom: 0,
        bottomWidget: const BottomCancelWidget());
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(12)),
      child: ListView.builder(
          itemCount: addContactController!.allChainsList.length,
          padding: EdgeInsets.zero,
          itemBuilder: (_, index) => _itemBuilder(addContactController!,
              addContactController!.allChainsList[index])),
    );
  }

  HighLightInkWell _itemBuilder(
      AddContactController controller, CoinType model) {
    return HighLightInkWell(
        onTap: () => controller.onSelectChain(model),
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setPaddingSize(18),
              horizontal: Get.setPaddingSize(16)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: CoinBase.getChainIcon(model.chain),
                width: Get.setImageSize(20),
                height: Get.setImageSize(20),
              ),
              SizedBox(
                width: Get.setWidth(6),
              ),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      model.symbol,
                      style: styleHomeSymbol,
                    ),
                    Text(
                      " - ${model.chainName}",
                      style: TextStyle(
                        color: Get.theme.textSecondary,
                        fontSize: Get.setFontSize(12),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.regular,
                      ),
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: model.chain == controller.chain.value,
                child: ImageWidget(
                  assetUrl: 'icon_select',
                  width: Get.setImageSize(20),
                  height: Get.setImageSize(20),
                ),
              ),
            ],
          ),
        ));
  }
}
