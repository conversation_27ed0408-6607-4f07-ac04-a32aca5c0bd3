/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-22 14:05:30
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/auth/auth_manager.dart';
import 'package:coinbag/modules/profile/security/controller/security_setting_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:flutter/cupertino.dart';
import 'package:local_auth/local_auth.dart';

enum SecurityPasswordStatus {
  /// 设置密码
  setting,

  /// 确认密码
  confirm,

  /// 修改密码
  change,

  /// 忘记密码
  forget,
}

class SecurityPasswordController extends BaseController {
  late SecurityPasswordStatus targetStatus;
  late Rx<SecurityPasswordStatus> securityStatus;

  String password = '';
  String password2 = '';

  String tipTitle = '';

  @override
  void onInit() {
    final Map arg = Get.arguments;
    SecurityPasswordStatus securityStatus = arg[GetArgumentsKey.securityStatus];
    targetStatus = securityStatus;

    this.securityStatus = Rx(SecurityPasswordStatus.setting);

    super.onInit();
  }

  Future<void> passwordInput(
      String password, TextEditingController controller) async {
    tipTitle = '';
    if (securityStatus.value == SecurityPasswordStatus.setting) {
      this.password = password;
      await Future.delayed(Duration(milliseconds: 500));
      securityStatus.value = SecurityPasswordStatus.confirm;
    } else if (securityStatus.value == SecurityPasswordStatus.confirm) {
      password2 = password;
      await updateTipTitle();
      update([GetKey.securityPassword]);
      if (this.password != password2) {
        controller.text = '';
      } else {
        if (targetStatus == SecurityPasswordStatus.change) {
          SecuritySettingController.changePassword(
              {GetArgumentsKey.password: password});
          Get.until(
            (route) => route.settings.name == AppRoutes.securitySettingPage,
          );
        } else {
          List<BiometricType> bioList =
              await AuthManager.getAvailableBiometrics();
          if (bioList.isNotEmpty) {
            openBiometricsAction();
          } else {
            goBackAction();
          }
        }
      }
    }
  }

  void goBackAction({bool biometrics = false}) {
    Get.back(result: {
      GetArgumentsKey.password: password,
      GetArgumentsKey.biometrics: biometrics,
    });
  }

  void openBiometricsAction() {
    Get.showAlertDialog(
      title: ID.stringTips.tr,
      barrierDismissible: false,
      content: ID.stringOpenBiometricsTip.tr,
      onConfirmText: ID.stringConfirm.tr,
      onCancelText: ID.stringCancel.tr,
      onConfirm: () async {
        Get.back();
        if (await AuthManager.authenticate()) {
          goBackAction(biometrics: true);
        }
      },
      onCancel: () {
        goBackAction();
      },
      onContinue: () {
        goBackAction();
      },
    );
  }

  String get title {
    if (targetStatus == SecurityPasswordStatus.setting) {
      return ID.stringCreatePasswordTitle.tr;
    } else if (targetStatus == SecurityPasswordStatus.change) {
      return ID.stringChangePassword.tr;
    } else if (targetStatus == SecurityPasswordStatus.forget) {
      return ID.stringResetPasscode.tr;
    }
    return '';
  }

  String get contentTitle {
    if (securityStatus.value == SecurityPasswordStatus.confirm) {
      return ID.stringConfirmPsw.tr;
    } else {
      if (targetStatus == SecurityPasswordStatus.setting) {
        return ID.stringCreatePassCode.tr;
      } else if (targetStatus == SecurityPasswordStatus.change) {
        return ID.stringCreateNewPasscode.tr;
      } else if (targetStatus == SecurityPasswordStatus.forget) {
        return ID.stringCreateNewPasscode.tr;
      }
    }
    return '';
  }

  Future<void> updateTipTitle() async {
    if (securityStatus.value == SecurityPasswordStatus.confirm) {
      if (password2.isNotEmpty && password != password2) {
        tipTitle = ID.stringPasswordNotEqualTip.tr;
      }
    }
  }

  @override
  void loadData() {}
}

class SecurityPasswordBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecurityPasswordController());
  }
}
