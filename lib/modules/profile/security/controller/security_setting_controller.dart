/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-02 11:15:16
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/profile/security/controller/Security_password_controller.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/profile/security/model/security_setting_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/pin_storage.dart';
import 'package:coinbag/widgets/toast/toast.dart';

class SecuritySettingController extends BaseController {
  late SecuritySettingModel securityModel;

  late bool hideLeading;
  Rx<bool> isAuthorizeTransaction = false.obs;

  @override
  void onInit() {
    securityModel = SecuritySettingModel.init();

    hideLeading = Get.arguments ?? false;
    isAuthorizeTransaction.value =
        StorageManager.getValue(key: StorageKey.authorizeTransaction) ?? false;
    super.onInit();
  }

  Future<void> changSucerity(bool isOpen) async {
    if (isOpen == false) {
      final result = await Get.toNamed(AppRoutes.securityPage,
          arguments: SecurityState.security);
      if (result == true) {
        PinStorage().clearPIN();
        securityModel = SecuritySettingModel(isOpen: false);
        StorageManager.saveValue(
          key: StorageKey.sucerity,
          value: securityModel.toJson(),
        );
        isAuthorizeTransaction.value = false;
        StorageManager.saveValue(
          key: StorageKey.authorizeTransaction,
          value: false,
        );
        update([GetKey.securitySetting]);
      }
    } else {
      final result = await Get.toNamed(
        AppRoutes.securityPasswordPage,
        arguments: {
          GetArgumentsKey.securityStatus: SecurityPasswordStatus.setting
        },
      );

      _pssword(result);
    }
  }

  Future<void> authorizeTransaction() async {
    if (securityModel.isOpen) {
      final result = await Get.toNamed(AppRoutes.securityPage,
          arguments: SecurityState.security);
      if (result == true) {
        isAuthorizeTransaction.value = !isAuthorizeTransaction.value;
        StorageManager.saveValue(
          key: StorageKey.authorizeTransaction,
          value: isAuthorizeTransaction.value,
        );
      }
    } else {
      isAuthorizeTransaction.value = !isAuthorizeTransaction.value;
      StorageManager.saveValue(
        key: StorageKey.authorizeTransaction,
        value: isAuthorizeTransaction.value,
      );
    }
  }

  Future<void> _pssword(dynamic result, [bool isChange = false]) async {
    if (result != null) {
      String password = result[GetArgumentsKey.password];
      bool biometrics = result[GetArgumentsKey.biometrics] ?? false;
      await PinStorage().saveHashedPIN(password);
      securityModel.isOpen = true;
      if (isChange == false) {
        securityModel.mode = biometrics
            ? VerifyMode.biometrics.index
            : VerifyMode.password.index;
      }
      StorageManager.saveValue(
        key: StorageKey.sucerity,
        value: securityModel.toJson(),
      );

      isAuthorizeTransaction.value = true;
      StorageManager.saveValue(
        key: StorageKey.authorizeTransaction,
        value: true,
      );
      update([GetKey.securitySetting]);

      if (hideLeading == true) {
        Get.offAllNamed(AppRoutes.mainPage);
      }
    }
  }

  static void changePassword(Map<String, dynamic> result) {
    SecuritySettingController ctr = Get.find<SecuritySettingController>();
    Get.showToast(ID.stringChangeSuccess.tr, toastMode: ToastMode.success);
    ctr._pssword(result, true);
  }

  @override
  void loadData() {}
}

class SecuritySettingBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecuritySettingController());
  }
}
