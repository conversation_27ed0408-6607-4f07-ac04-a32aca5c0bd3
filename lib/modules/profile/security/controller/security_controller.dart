/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-22 14:01:12
 */
import 'dart:async';

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/profile/auth/auth_manager.dart';
import 'package:coinbag/modules/profile/security/controller/Security_password_controller.dart';
import 'package:coinbag/modules/profile/security/model/security_setting_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:coinbag/utils/pin_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:local_auth/local_auth.dart';

enum SecurityState { launch, security, change }

class SecurityController extends BaseController {
  late SecuritySettingModel securityModel;
  late SecurityState securityState;

  /// 错误提示
  String? errorTip;

  Timer? timer;

  int lockSeconds = 0;

  FocusNode? focusNode;

  @override
  void onInit() {
    securityModel = SecuritySettingModel.init();
    final arg = Get.arguments;
    assert((arg == null || arg! is SecurityState), 'SecurityState不能为空');
    securityState = arg;
    lockSeconds = securityModel.lockSeconds();
    if (lockSeconds > 0) {
      KeyboardUtils.hideKeyboardNoContext();
      startTimer();
    }
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();

    bool checkResult = await AuthManager.checkBiometrics();
    List bioList = await AuthManager.getAvailableBiometrics();

    if (checkResult == false ||
        bioList.isEmpty ||
        securityModel.mode == null ||
        securityModel.mode == VerifyMode.password.index) {
      securityModel.mode = VerifyMode.password.index;
      StorageManager.saveValue(
        key: StorageKey.sucerity,
        value: securityModel.toJson(),
      );
      update([GetKey.security]);
    } else {
      biometricAction();
    }
  }

  Future<void> passwordAction(String password, TextEditingController controller,
      FocusNode focusNode) async {
    this.focusNode = focusNode;
    focusNode.unfocus();
    bool result = await PinStorage().verifyPIN(password);
    if (result) {
      securityModel.errorCount = 0;
      securityModel.timestamp = null;
      StorageManager.saveValue(
          key: StorageKey.sucerity, value: securityModel.toJson());
      backAction();
    } else {
      focusNode.requestFocus();
      securityModel.errorCount++;
      int count = 5 - securityModel.errorCount;
      if (count <= 0) {
        securityModel.timestamp = DateTime.timestamp().toString();
        lockSeconds = securityModel.lockSeconds();
        startTimer();
      } else {
        errorTip = ID.stringPasswordErrorNumber
            .trParams({'value': '${5 - securityModel.errorCount}'});
      }
      await StorageManager.saveValue(
          key: StorageKey.sucerity, value: securityModel.toJson());

      await Future.delayed(Duration(milliseconds: 300));
      controller.text = '';
      update([GetKey.security]);
    }
  }

  Future<void> biometricAction() async {
    if (await AuthManager.authenticate()) {
      securityModel.errorCount = 0;
      securityModel.timestamp = null;
      StorageManager.saveValue(
          key: StorageKey.sucerity, value: securityModel.toJson());
      backAction();
    }
  }

  Future<void> forgetPassword() async {
    // 可用列表
    List<BiometricType> bioList = await AuthManager.getAvailableBiometrics();
    if (bioList.isEmpty) {
      // 为空提示添加指纹/开启权限
      AuthManager.showAuthSetting();
    } else {
      if (!(await AuthManager.authenticate())) {
        return;
      }

      final result = await Get.toNamed(
        AppRoutes.securityPasswordPage,
        arguments: {
          GetArgumentsKey.securityStatus: SecurityPasswordStatus.forget
        },
      );

      if (result != null) {
        String password = result[GetArgumentsKey.password];
        bool biometrics = result[GetArgumentsKey.biometrics];
        await PinStorage().saveHashedPIN(password);
        await cancelTimer();
        securityModel.isOpen = true;
        securityModel.mode = biometrics
            ? VerifyMode.biometrics.index
            : VerifyMode.password.index;
        StorageManager.saveValue(
          key: StorageKey.sucerity,
          value: securityModel.toJson(),
        );
        update([GetKey.security]);
      }
    }
  }

  void backAction() {
    if (securityState == SecurityState.launch) {
      Get.offAllNamed(AppRoutes.mainPage);
    } else if (securityState == SecurityState.security) {
      Get.back(result: true);
    } else if (securityState == SecurityState.change) {
      Get.toNamed(
        AppRoutes.securityPasswordPage,
        arguments: {
          GetArgumentsKey.securityStatus: SecurityPasswordStatus.change,
        },
      );
    }
  }

  void startTimer() {
    timer?.cancel();
    timer = Timer.periodic(
      Duration(seconds: 1),
      (timer) async {
        lockSeconds -= 1;
        if (lockSeconds <= 0) {
          cancelTimer();
        } else {
          errorTip = ID.stringPasswordErrorMax
              .trParams({'value': securityModel.lockTip(lockSeconds)});
          if (Get.currentRoute == AppRoutes.securityPage) {
            KeyboardUtils.hideKeyboardNoContext();
          }
        }
        update([GetKey.security]);
      },
    );
  }

  Future<void> cancelTimer() async {
    timer?.cancel();
    lockSeconds = 0;
    securityModel.timestamp = null;
    await StorageManager.saveValue(
      key: StorageKey.sucerity,
      value: securityModel.toJson(),
    );
    errorTip = null;
    focusNode?.requestFocus();
  }

  @override
  void loadData() {}
}

class SecurityBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => SecurityController());
  }
}
