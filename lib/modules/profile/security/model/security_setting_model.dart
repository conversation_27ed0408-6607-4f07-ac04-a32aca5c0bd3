/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-21 16:38:51
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/res/resource.dart';

enum VerifyMode { password, biometrics }

class SecuritySettingModel {
  /// 是否开启
  bool isOpen;

  /// 验证方式
  int? mode;

  /// 密码错误次数
  int errorCount;

  /// 错误时间
  String? timestamp;

  SecuritySettingModel({
    required this.isOpen,
    this.mode,
    this.errorCount = 0,
    this.timestamp,
  });

  VerifyMode? get verifyMode => mode == null ? null : VerifyMode.values[mode!];

  factory SecuritySettingModel.init() {
    final json = StorageManager.getValue(key: StorageKey.sucerity);
    if (json == null) return SecuritySettingModel(isOpen: false);
    bool isOpen = json['isOpen'] as bool;
    int? mode = json['mode'] as int?;
    int? errorCount = json['errorCount'] as int?;
    String? timestamp = json['timestamp'] as String?;
    return SecuritySettingModel(
      isOpen: isOpen,
      mode: mode,
      errorCount: errorCount ?? 0,
      timestamp: timestamp,
    );
  }

  Map<String, Object?> toJson() {
    return {
      'isOpen': isOpen,
      'mode': mode,
      'errorCount': errorCount,
      'timestamp': timestamp,
    };
  }

  /// 还需锁定的时间
  int lockSeconds() {
    if (Get.isEmptyString(timestamp)) return 0;
    DateTime date = DateTime.parse(timestamp!);
    DateTime currentDate = DateTime.now();

    Duration difference = currentDate.difference(date);
    int seconds = difference.inSeconds;
    if (seconds < 0) return 0;
    int lockSeconds = 0;
    if (errorCount == 5) {
      // 1分钟
      lockSeconds = 60 - seconds;
    } else if (errorCount == 6) {
      // 5分钟
      lockSeconds = 5 * 60 - seconds;
    } else if (errorCount == 7) {
      // 30分钟
      lockSeconds = 30 * 60 - seconds;
    } else {
      // 2小时
      lockSeconds = 120 * 60 - seconds;
    }
    return lockSeconds;
  }

  String lockTip(int seconds) {
    int h = seconds ~/ (60 * 60);
    int m = seconds ~/ 60;
    if (h > 0) {
      m = m % 60;
    }
    int s = seconds % 60;

    String hTip = ID.stringPaswordHour.trParams({'value': '$h'});
    String mTip = ID.stringPaswordMinute.trParams({'value': '$m'});
    String sTip = ID.stringPaswordSecond.trParams({'value': '$s'});

    String tip = sTip;
    if (m > 0) {
      tip = '$mTip $tip';
    }

    if (h > 0) {
      tip = '$hTip $tip';
    }
    return ' $tip';
  }
}
