import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class SecurityPasswordWidget extends StatefulWidget {
  final Function(String password, TextEditingController controller,
      FocusNode focusNode)? onPassword;
  final Function(String password)? onValueChanged;

  const SecurityPasswordWidget({
    super.key,
    this.onPassword,
    this.onValueChanged,
  });

  @override
  State<SecurityPasswordWidget> createState() => _SecurityPasswordWidgetState();
}

class _SecurityPasswordWidgetState extends State<SecurityPasswordWidget> {
  final FocusNode focusNode = FocusNode();
  final TextEditingController _controller = TextEditingController();
  String _currentValue = "";

  @override
  void initState() {
    super.initState();
    focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (!focusNode.hasFocus) {
          focusNode.requestFocus(); // 直接请求焦点
        } else {
          focusNode.unfocus(); // 先失焦
          Future.delayed(Duration.zero, () {
            focusNode.requestFocus(); // 再次请求焦点
          });
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          _textWidget(),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(6, _buildCircle),
          ),
        ],
      ),
    );
  }

  Widget _buildCircle(int index) {
    bool isFilled = index < _controller.text.length;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(8)),
      child: Container(
        width: Get.setWidth(20),
        height: Get.setWidth(20),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Get.theme.textPrimary,
            width: Get.setWidth(2),
          ),
        ),
        child: isFilled
            ? Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Get.theme.textPrimary,
                ),
              )
            : null,
      ),
    );
  }

  Widget _textWidget() {
    return SizedBox(
      height: Get.setWidth(20),
      child: IgnorePointer(
        child: TextFieldWidget(
          textStyle: const TextStyle(fontSize: 2),
          controller: _controller,
          obscureText: true,
          maxLength: 6,
          showCursor: false,
          keyboardType: TextInputType.number,
          isNumberInputFormatter: false,
          radius: Get.setRadius(10),
          focusNode: focusNode,
          enabledBorder: _inputBorder(),
          focusedBorder: _inputBorder(),
          onValueChanged: (value) {
            if (_currentValue == value) return;
            setState(() {
              _currentValue = value;
            });
            if (value.length == 6) {
              widget.onPassword?.call(value, _controller, focusNode);
            } else {
              widget.onValueChanged?.call(value);
            }
          },
        ),
      ),
    );
  }

  OutlineInputBorder _inputBorder() {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(Get.setRadius(10)),
      borderSide: BorderSide.none,
    );
  }
}
