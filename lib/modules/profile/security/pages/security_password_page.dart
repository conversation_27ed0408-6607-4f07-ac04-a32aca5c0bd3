/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-21 16:55:14
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/security/controller/Security_password_controller.dart';
import 'package:coinbag/modules/profile/security/widgets/security_password_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';

class SecurityPasswordPage
    extends BaseStatelessWidget<SecurityPasswordController> {
  const SecurityPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SecurityPasswordController>(
        id: GetKey.securityPassword,
        builder: (_) => Scaffold(
              appBar: baseAppBar(title: controller.title),
              body: SingleChildScrollView(
                padding: EdgeInsets.only(
                  top: Get.setPaddingSize(60),
                  left: Get.setPaddingSize(16),
                  right: Get.setPaddingSize(16),
                  bottom: Get.getSafetyBottomPadding(),
                ),
                child: Obx(() => Column(
                      children: [
                        SizedBox(
                          height: Get.setPaddingSize(24),
                        ),
                        Text(
                          controller.contentTitle,
                          textAlign: TextAlign.center,
                          style: stylePrimary_16_m,
                        ),
                        SizedBox(
                          height: Get.setPaddingSize(36),
                          width: Get.width,
                        ),
                        _passwordWidget(),
                        SizedBox(height: Get.setPaddingSize(36)),
                        Text(
                          controller.tipTitle,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Get.theme.colorF44D4D,
                            fontSize: Get.setFontSize(14),
                          ),
                        ),
                      ],
                    )),
              ),
            ));
  }

  SecurityPasswordWidget _passwordWidget() => SecurityPasswordWidget(
        key: Key(controller.securityStatus.value.index.toString()),
        onPassword: (password, controller, focusNode) {
          this.controller.passwordInput(password, controller);
        },
      );
}
