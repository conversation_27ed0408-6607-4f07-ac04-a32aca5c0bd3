/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-04-01 09:04:25
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/profile/security/controller/security_setting_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/switch/switch_widget.dart';
import 'package:flutter/material.dart';

class SecuritySettingPage
    extends BaseStatelessWidget<SecuritySettingController> {
  const SecuritySettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
          title: ID.stringSecurityVerification.tr,
          hideLeading: controller.hideLeading),
      body: GetBuilder<SecuritySettingController>(
        id: GetKey.securitySetting,
        builder: (_) {
          return SingleChildScrollView(
            padding: EdgeInsets.only(
              bottom: Get.getSafetyBottomPadding(),
            ),
            child: Column(
              children: [
                _securityWidget(),
                _siezboxWidget(),
                _changePasswordWidget(),
                _authorizeTransactionWidget()
              ],
            ),
          );
        },
      ),
    );
  }

  Padding _securityWidget() {
    return Padding(
      padding: EdgeInsets.all(Get.setPaddingSize(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  ID.stringOpenSecurityTitle.tr,
                  style: stylePrimary_16_m,
                ),
              ),
              SizedBox(width: Get.setPaddingSize(24)),
              SwitchWidget(
                isOn: controller.securityModel.isOpen,
                onChanged: (value) => controller.changSucerity(value),
              ),
            ],
          ),
          SizedBox(height: Get.setPaddingSize(8)),
          Text(
            ID.stringAppLockInfo.tr,
            style: styleSecond_14,
          )
        ],
      ),
    );
  }

  Visibility _changePasswordWidget() {
    return Visibility(
      visible: controller.securityModel.isOpen,
      child: HighLightInkWell(
        onTap: () => Get.toNamed(AppRoutes.securityPage,
            arguments: SecurityState.change),
        child: Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(16)),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  ID.stringVerChangePswTitle.tr,
                  style: stylePrimary_16_m,
                ),
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              ImageWidget(
                assetUrl: 'icon_new_arrow01',
                width: Get.setImageSize(12),
                height: Get.setImageSize(12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Visibility _siezboxWidget() {
    return Visibility(
      visible: controller.securityModel.isOpen,
      child: Container(
        color: Get.theme.colorECECEC,
        height: Get.setPaddingSize(8),
      ),
    );
  }

  Obx _authorizeTransactionWidget() {
    return Obx(() => Visibility(
        visible: controller.securityModel.isOpen,
        child: Padding(
          padding: EdgeInsets.all(Get.setPaddingSize(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      ID.stringAuthTsTitle.tr,
                      style: stylePrimary_16_m,
                    ),
                  ),
                  SizedBox(width: Get.setPaddingSize(24)),
                  SwitchWidget(
                    isOn: controller.isAuthorizeTransaction.value,
                    onChanged: (value) => controller.authorizeTransaction(),
                  ),
                ],
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              Text(
                ID.stringAuthTsSub2Title.tr,
                style: styleSecond_14,
              )
            ],
          ),
        )));
  }
}
