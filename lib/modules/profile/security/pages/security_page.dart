/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-21 16:35:51
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/profile/security/model/security_setting_model.dart';
import 'package:coinbag/modules/profile/security/widgets/security_password_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/package_info_manager.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SecurityPage extends BaseStatelessWidget<SecurityController> {
  const SecurityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(
          hideLeading: controller.securityState == SecurityState.launch),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(20),
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: GetBuilder<SecurityController>(
          id: GetKey.security,
          builder: (_) {
            return Column(
              children: [
                _userWidget(),
                SizedBox(
                  height: Get.setPaddingSize(40),
                ),
                _titleWidget(),
                SizedBox(height: Get.setPaddingSize(36), width: Get.width),
                _passwordWidget(),
                _biometricWidget(),
                _tipWidget(),
                _forgetWidget(),
              ],
            );
          },
        ),
      ),
    );
  }

  Text _titleWidget() {
    return Text(
      ID.stringInputPassword.tr,
      textAlign: TextAlign.center,
      style: stylePrimary_16_m,
    );
  }

  Visibility _tipWidget() {
    return Visibility(
      visible: !Get.isEmptyString(controller.errorTip),
      child: Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(36)),
        child: GestureDetector(
          onTap: () => controller.forgetPassword(),
          child: Text(
            controller.errorTip ?? '123',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Get.theme.colorF44D4D,
              fontSize: Get.setFontSize(14),
            ),
          ),
        ),
      ),
    );
  }

  Padding _forgetWidget() {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(36)),
      child: GestureDetector(
        onTap: () => controller.forgetPassword(),
        child: Text(
          ID.stringForgetPassword.tr,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Get.theme.colorF44D4D,
            fontSize: Get.setFontSize(14),
          ),
        ),
      ),
    );
  }

  dynamic _passwordWidget() => controller.lockSeconds > 0
      ? IgnorePointer(child: _password())
      : _password();

  SecurityPasswordWidget _password() => SecurityPasswordWidget(
        onPassword: (password, controller, focusNode) =>
            this.controller.passwordAction(password, controller, focusNode),
        onValueChanged: (password) {},
      );

  Visibility _biometricWidget() {
    return Visibility(
      visible: controller.securityModel.verifyMode == VerifyMode.biometrics,
      child: Padding(
        padding: EdgeInsets.only(top: Get.setPaddingSize(50)),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => controller.biometricAction(),
          child: ImageWidget(
              assetUrl: GetPlatform.isIOS ? 'faceId_icon' : 'icon_fingerprint',
              width: Get.setImageSize(80),
              height: Get.setImageSize(80),
              fit: BoxFit.contain),
        ),
      ),
    );
  }

  Row _userWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ImageWidget(
          assetUrl: 'icon_logo',
          width: Get.setImageSize(24),
          height: Get.setImageSize(24),
          fit: BoxFit.contain,
        ),
        const SizedBox(
          width: 12,
        ),
        Text(
          PackageInfoManager().appName,
          style: stylePrimary_16_m,
        ),
      ],
    );
  }
}
