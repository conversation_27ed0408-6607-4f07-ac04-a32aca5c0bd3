/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-25 15:21:19
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/http/response/base_response.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/toast/toast.dart';

class FeedbookController extends BaseController<ApiService> {
  Rx<ButtonStatus> buttonStatus = ButtonStatus.disable.obs;
  String nickname = '';
  String content = '';
  String contact = '';

  void updateButtonStatus() {
    if (Get.isEmptyString(nickname) ||
        Get.isEmptyString(content) ||
        Get.isEmptyString(contact)) {
      buttonStatus.value = ButtonStatus.disable;
    } else {
      buttonStatus.value = ButtonStatus.enable;
    }
  }

  @override
  void loadData() {
    content = content.replaceAll('\n', '');
    buttonStatus.value = ButtonStatus.loading;
    httpRequest<BaseResponse<dynamic>>(
        api.feedback(RequestParams()
            .put("info", content)
            .put("phone", contact)
            .put("userName", nickname)
            .getRequestBody()),
        (value) {
          buttonStatus.value = ButtonStatus.enable;
          if (value.code == APIConstant.responseCode) {
            Get.showToast(ID.textFeedbackDialogMessage.tr,
                toastMode: ToastMode.success);
            Get.back();
          } else {
            String msg = value.message ?? '';
            if (value.data is String) {
              msg = value.data;
            }
            Get.showToast(msg, toastMode: ToastMode.failed);
          }
        },
        handleError: false,
        error: (e) {
          Get.showToast(ID.netConnectError.tr, toastMode: ToastMode.waring);
          buttonStatus.value = ButtonStatus.enable;
        });
  }
}

class FeedbookBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => FeedbookController());
  }
}
