/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-07 16:23:13
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/controller/network_controller.dart';
import 'package:coinbag/res/resource.dart';

class NetworkDiagnosticsResultController extends BaseController {
  late NetworkModel model;

  @override
  void onInit() {
    model = Get.arguments[GetArgumentsKey.model];
    super.onInit();
  }

  @override
  void loadData() {}
}

class NetworkDiagnosticsResultBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => NetworkDiagnosticsResultController());
  }
}
