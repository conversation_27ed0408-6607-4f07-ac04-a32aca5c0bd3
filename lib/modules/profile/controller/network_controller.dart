import 'dart:convert';

import 'package:android_intent_plus/android_intent.dart';
import 'package:app_settings/app_settings.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkDiagnosticsController extends BaseController<ApiService> {
  int loadIndex = 0;
  Connectivity connectivity = Connectivity();
  bool connectivityResult = true;
  List<NetworkModel> dataList = [];

  @override
  void onInit() {
    _setup();
    super.onInit();
  }

  @override
  void onResumed() {
    loadIndex = 0;
    _setup();
    _loadNetwork();
  }

  void _setup() {
    dataList.clear();
    dataList.add(NetworkModel(
      domainName: 'api.coinbagbit.com',
      url: 'https://api.coinbagbit.com',
      callback: (model) => _toResultPage(model),
    ));

    dataList.add(NetworkModel(
      domainName: 'api.hengshengjia.com',
      url: 'https://api.hengshengjia.com',
      callback: (model) => _toResultPage(model),
    ));
  }

  void _toResultPage(NetworkModel model) {
    if (model.status != ButtonStatus.enable) return;
    Get.toNamed(
      AppRoutes.networkResultPage,
      arguments: {GetArgumentsKey.model: model},
    );
  }

  @override
  void onReady() async {
    super.onReady();

    _loadNetwork();
  }

  /// 网络是否可用
  bool neworkCanUse() {
    final resultList =
        dataList.where((e) => e.status == ButtonStatus.disable).toList();
    if (resultList.length == dataList.length) {
      return false;
    }

    if (connectivityResult == false) return false;

    return true;
  }

  Future<void> settingNetworkAction() async {
    if (GetPlatform.isAndroid) {
      AndroidIntent intent = const AndroidIntent(
        action: 'android.settings.SETTINGS',
      );
      await intent.launch();
    } else if (GetPlatform.isIOS) {
      await AppSettings.openAppSettings(
          type: AppSettingsType.settings, asAnotherTask: true);
    }
  }

  Future<void> _loadNetwork() async {
    var connectivityList = await connectivity.checkConnectivity();
    if (connectivityList.contains(ConnectivityResult.none)) {
      // 未连接网络
      connectivityResult = false;
      dataList = dataList.map((e) {
        e.status = ButtonStatus.disable;
        return e;
      }).toList();
    } else {
      connectivityResult = true;
    }
    if (loadIndex >= dataList.length) return;
    NetworkModel model = dataList[loadIndex];
    loadIndex++;
    model.status = ButtonStatus.loading;
    update([GetKey.networkId]);
    ApiService api = ApiService(baseUrl: model.url);
    httpRequest(api.getMarketPriceList(1, 50),
        handleError: false, handleSuccess: false, showLoading: false, (value) {
      model.status = ButtonStatus.enable;
      try {
        model.resultJson = json.encode(value.data);
      } catch (_) {}
      update([GetKey.networkId]);
      _loadNetwork();
    }, error: (e) {
      model.status = ButtonStatus.disable;
      update([GetKey.networkId]);
      _loadNetwork();
    });
  }

  @override
  void loadData() {}
}

class NetworkDiagnosticsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => NetworkDiagnosticsController());
  }
}

class NetworkModel {
  final String domainName;
  final String url;
  ButtonStatus? status;
  String? resultJson;
  final Function(NetworkModel model)? callback;

  NetworkModel({
    required this.domainName,
    required this.url,
    this.status,
    this.callback,
    this.resultJson,
  });
}
