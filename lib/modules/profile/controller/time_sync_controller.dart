/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-09 18:16:49
 * @LastEditTime: 2024-10-22 09:14:35
 */

import 'dart:async';
import 'dart:convert';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';
import 'package:wallet_core/encrypt/key/encrypt_key.dart';
import 'package:wallet_core/encrypt/utils/aes_helper.dart';

class TimeSyncController extends BaseController {
  // 使用RxString来存储二维码数据，这样可以轻松监听变化
  var qrData = ''.obs;
  var seconds = 10.obs; // 倒计时秒数
  late String currentTimeZone;
  Timer? timer;

  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  @override
  void onReady() {
    super.onReady();

    AppController.setBrightness();
  }

  @override
  void loadData() async {
    currentTimeZone = await FlutterTimezone.getLocalTimezone();

    qrData.value = creartQR();
    timer =
        Timer.periodic(const Duration(seconds: 1), (Timer t) => _updateTime());
  }

  String creartQR() {
    String millisecond = DateTime.now().millisecondsSinceEpoch.toString();
    String jsonStr = jsonEncode({
      "timezone": currentTimeZone,
      "timestamp": millisecond,
    });

    return AesHelpler(EncryptKey.aesMainKey, EncryptKey.aesIVKey)
        .encrypt(jsonStr);
  }

  void _updateTime() {
    if (seconds > 1) {
      seconds--;
    } else {
      qrData.value = creartQR(); // 更新二维码数据
      seconds.value = 10; // 重置秒数
    }
  }

  @override
  void onClose() {
    timer?.cancel();
    super.onClose();
    AppController.resetBrightness();
  }
}

class TimeSyncBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TimeSyncController());
  }
}
