/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-23 14:01:01
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/locale/locale_controller.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/modules/profile/qr/qr_controller.dart';
import 'package:coinbag/modules/profile/qr/qr_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/bottom_cancel_widget.dart';
import 'package:flutter/material.dart';

class PreferencesController extends BaseController {
  @override
  void loadData() {}

  /// 二维码容量
  RxList qrList = QRController.qrList.obs;

  /// 多语言
  RxList lngList = getLanguageList.obs;

  /// 更新多语言
  Future<void> updateLanguage(LanguageModel model) async {
    if (model.isSelect == false) {
      await StorageManager.saveObject(key: StorageKey.language, obj: model);
      LocaleController.updateLocale(model);
      lngList.value = getLanguageList;
      qrList.value = QRController.qrList;
      AppController.languageModel.value = model;
      AppController.qrModel.value = QRController.qrModel;
    }
    delayedBack();
  }

  /// 更新二维码容量
  Future<void> updateQR(QRModel model) async {
    if (model.selected == false) {
      await StorageManager.saveObject(key: StorageKey.qr, obj: model);
      qrList.value = QRController.qrList;
      AppController.qrModel.value = model;
    }

    delayedBack();
  }

  Future<void> showBottomSheet(Widget widget) => Get.showBottomSheet(
      hideHeader: true,
      paddingBottom: 0,
      padding: const EdgeInsets.symmetric(horizontal: 0),
      bodyWidget: widget,
      bottomWidget: const BottomCancelWidget());

  static List<LanguageModel> get getLanguageList {
    LanguageModel model = LocaleController.defaultLanguageModel();
    return LanguageSource.languageList.map((e) {
      e.isSelect = e.type == model.type;
      return e;
    }).toList();
  }

  /// 0.5秒返回
  Future<void> delayedBack() =>
      Future.delayed(const Duration(milliseconds: 500))
          .then((value) => Get.back());
}

class PreferencesBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => PreferencesController());
  }
}
