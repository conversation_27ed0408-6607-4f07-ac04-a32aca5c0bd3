import 'dart:math';

import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:get/get.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';

class BestFeeController extends BaseRefreshController<BlockChainService> {
  var btcFee = ''.obs;
  var btcSat = ''.obs;
  var ethGwei = ''.obs;

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {
    requestPageData();
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    List<Future<dynamic>> futures = [];
    futures.add(api.getBestBtcFee(BlockChainParamsManager.createParams(
        method: BlockChainAPI.btcgasprice,
        requestParams: RequestParams()
            .put(APIConstant.chain, BitcoinChain.get.chain)
            .put(APIConstant.blockNumber, 5)
            .getRequestBody())));

    futures.add(api.getBestEthFee(BlockChainParamsManager.createParams(
        method: BlockChainAPI.ethgasprice,
        requestParams: RequestParams()
            .put(APIConstant.chain, EthereumChain.get.chain)
            .put(APIConstant.integerAccuracy, false)
            .put(APIConstant.accuracy, true)
            .getRequestBody())));
    multiHttpRequest(futures, (value) {
      if (refresh == Refresh.first || refresh == Refresh.pull) {
        btcFee.value = "";
        btcSat.value = "";
        ethGwei.value = "";
      }
      if (value != null) {
        showSuccess();
      }
      var btcData = value[0];
      var ethData = value[1];

      if (btcData == null ||
          ethData == null ||
          btcData.error != null ||
          ethData.error != null) {
        return;
      }

      if (btcData.data != null) {
        String fee = btcData.data.toString();
        btcSat.value = DecimalUtils.divide(fee, "1000", scale: 0);
        int decimals = BitcoinChain.get.balanceDecimals;

        // 计算 BTC/KB
        String btcPerKb = DecimalUtils.divide(fee, pow(10, decimals).toString(),
            scale: decimals);

        btcFee.value = DecimalUtils.stripTrailingZeros(btcPerKb);
      } else {
        btcSat.value = "";
        btcFee.value = "";
      }

      if (ethData != null) {
        String feeV2 = ethData.data["estimatefee"];
        ethGwei.value =
            DecimalUtils.divide(feeV2, pow(10, 9).toString(), scale: 2);
      } else {
        ethGwei.value = "";
      }

      hideRefresh(refreshController);
    });
  }
}

class BestFeeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => BestFeeController());
  }
}
