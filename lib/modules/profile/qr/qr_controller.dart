/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-04-15 17:31:49
 */
/*
 * @description: Do not edit
 * @Author: wangliming
 * @Date: 2024-01-17 10:50:35
 * @LastEditTime: 2024-04-01 15:38:02
 */

import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/profile/qr/qr_model.dart';
import 'package:coinbag/res/resource.dart';

class QRController {
  /// 获取当前二维码容量Model
  static QRModel get qrModel {
    QRModel? model = StorageManager.getObject(
        key: StorageKey.qr, fromJson: QRModel.fromJson);
    if (model != null) {
      for (QRModel obj in _list) {
        if (obj.index == model.index) {
          obj.selected = true;
          return obj;
        }
      }
      return model;
    }
    return QRModel(
        title: ID.qrStandard.tr,
        qrSize: 400,
        index: QRType.standard.index,
        selected: true);
  }

  static List<QRModel> get _list {
    return <QRModel>[
      QRModel(title: ID.qrSmall.tr, qrSize: 320, index: QRType.small.index),
      QRModel(
          title: ID.qrStandard.tr, qrSize: 400, index: QRType.standard.index),
      QRModel(title: ID.qrLarge.tr, qrSize: 600, index: QRType.large.index),
      QRModel(
          title: ID.qrSuperLarge.tr,
          qrSize: 800,
          index: QRType.superLarge.index),
    ];
  }

  /// 二维码容量List
  static List<QRModel> get qrList {
    QRModel qr = qrModel;
    return _list.map((e) {
      e.selected = (e.qrType == qr.qrType);
      return e;
    }).toList();
  }
}
