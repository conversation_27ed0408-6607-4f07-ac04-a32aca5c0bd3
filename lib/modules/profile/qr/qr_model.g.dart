// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qr_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QRModel _$QRModelFromJson(Map<String, dynamic> json) => QRModel(
      title: json['title'] as String,
      index: (json['index'] as num?)?.toInt() ?? 0,
      selected: json['selected'] as bool? ?? false,
      qrSize: (json['qrSize'] as num?)?.toInt() ?? 400,
    );

Map<String, dynamic> _$QRModelToJson(QRModel instance) => <String, dynamic>{
      'title': instance.title,
      'qrSize': instance.qrSize,
      'index': instance.index,
      'selected': instance.selected,
    };
