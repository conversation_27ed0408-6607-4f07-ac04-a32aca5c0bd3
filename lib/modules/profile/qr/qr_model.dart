/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-04-01 15:38:14
 */
import 'package:coinbag/database/storage/models/storage_base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'qr_model.g.dart';

enum QRType<Int> {
  small, // 较小
  standard, // 标准
  large, // 较大
  superLarge, // 超大
}

@JsonSerializable()
class QRModel extends BaseStorageModel {
  String title = '';
  int qrSize;
  int index = 0;
  bool selected = false;

  QRModel(
      {required this.title,
      this.index = 0,
      this.selected = false,
      this.qrSize = 400});

  /// 二维码容量类型
  QRType get qrType {
    return QRType.values[index];
  }

  factory QRModel.fromJson(Map<String, dynamic> json) =>
      _$QRModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$QRModelToJson(this);
}
