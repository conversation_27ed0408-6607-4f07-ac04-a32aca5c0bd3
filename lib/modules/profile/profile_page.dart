import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/profile_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';

class ProfilePage extends BaseStatelessWidget<ProfileController> {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.myTitle.tr, hideLeading: true),
      body: Obx(() => SingleChildScrollView(
            child: Column(
              children: [
                Visibility(
                  visible: !controller.walletStatus.value.isEmpty,
                  child: Column(
                    children: [
                      _itemWidget(
                          type: SettingType.walletManager,
                          onTap: () => controller.walletManagerAction()),
                      DividerBoldWidget(height: Get.setPaddingSize(8))
                    ],
                  ),
                ),
                _itemWidget(
                    type: SettingType.addressBook,
                    onTap: () =>
                        Get.toNamed(AppRoutes.addressBookPage, arguments: {
                          GetArgumentsKey.walletModel: controller.walletModel,
                        })),
                _itemWidget(
                    type: SettingType.walletTool,
                    onTap: () => Get.toNamed(AppRoutes.walletToolPage)),
                _itemWidget(
                    type: SettingType.currency,
                    onTap: () => controller.currencyAction()),
                DividerBoldWidget(height: Get.setPaddingSize(8)),
                _itemWidget(
                    type: SettingType.baseSetting,
                    onTap: () => Get.toNamed(AppRoutes.preferencesPage)),
                _itemWidget(
                  type: SettingType.security,
                  onTap: () => Get.toNamed(AppRoutes.securitySettingPage),
                ),
                _itemWidget(
                    type: SettingType.customerService,
                    onTap: () => Get.toNamed(AppRoutes.customerServicePage)),
                Visibility(
                    visible: controller.showVip.value,
                    child: Showcase(
                        targetPadding: EdgeInsets.all(Get.setPaddingSize(0)),
                        key: Get.appController.guildeTwo,
                        tooltipPosition: TooltipPosition.top,
                        title: ID.stringVIPTip1.tr,
                        titlePadding:
                            EdgeInsets.only(bottom: Get.setPaddingSize(10)),
                        titleTextStyle: TextStyle(
                            color: Get.theme.bgColor,
                            fontWeight: FontWeightX.semibold,
                            fontSize: Get.setFontSize(16)),
                        description: ID.stringVIPTip3.tr,
                        tooltipPadding: EdgeInsets.all(Get.setPaddingSize(14)),
                        descTextStyle: TextStyle(
                            color: Get.theme.bgColor,
                            fontWeight: FontWeightX.medium,
                            fontSize: Get.setFontSize(14)),
                        tooltipBackgroundColor: Get.theme.colorFF6A16,
                        textColor: Get.theme.bgColor,
                        disposeOnTap: true,
                        onTargetClick: () => Get.toWeb(
                            url: controller.getVIPURL(), showTitle: false),
                        onToolTipClick: () {},
                        onBarrierClick: () {},
                        child: _itemWidget(
                            type: SettingType.vipBenefits,
                            onTap: () => Get.toWeb(
                                url: controller.getVIPURL(),
                                showTitle: false)))),
                _itemWidget(
                    type: SettingType.network,
                    onTap: () => Get.toNamed(AppRoutes.networkPage)),
                _itemWidget(
                    type: SettingType.about,
                    onTap: () => Get.toNamed(AppRoutes.aboutPage)),
              ],
            ),
          )),
    );
  }

  HighLightInkWell _itemWidget({required SettingType type, Function()? onTap}) {
    return HighLightInkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        child: Row(
          children: [
            ImageWidget(
              assetUrl: controller.icon(type),
              width: Get.setImageSize(28),
              height: Get.setImageSize(28),
            ),
            SizedBox(
              width: Get.setPaddingSize(12),
            ),
            Expanded(
              child: Text(
                controller.title(type),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: stylePrimary_16_m,
              ),
            ),
            SizedBox(
              width: Get.setPaddingSize(8),
            ),
            Visibility(
                visible: type == SettingType.currency,
                child: Padding(
                  padding: EdgeInsets.only(right: Get.setPaddingSize(8)),
                  child: Text(
                    controller.currency.value == Currency.cny ? 'CNY' : 'USD',
                    style: styleSecond_14,
                  ),
                )),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(12),
              height: Get.setImageSize(12),
            ),
          ],
        ),
      ),
    );
  }
}
