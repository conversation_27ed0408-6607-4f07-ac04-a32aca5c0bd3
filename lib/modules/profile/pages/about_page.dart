/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-04 13:05:14
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/package_info_manager.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class AboutPage extends BaseStatefulWidget<AppController> {
  const AboutPage({super.key});

  String get userArgument {
    LanguageModel model = AppController.languageModel.value;
    if (model.type == LanguageType.en) {
      return AgreementUrl.userArgumentEN;
    }
    return AgreementUrl.userArgumentZH;
  }

  @override
  void initState() {
    controller.checkUpgrade(isShowUpgradeDialog: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(title: ID.profileAbout.tr),
        body: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _header(),
              SizedBox(
                height: Get.setHeight(24),
              ),
              _contentItem(title: ID.aboutKS.tr, url: AgreementUrl.aboutUrl),
              _contentItem(title: ID.userAgreement.tr, url: userArgument),
              _checkForUpdatesItem(title: ID.stringCheckForUpdates.tr),
            ],
          ),
        ));
  }

  Padding _header() => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(24)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ImageWidget(
              assetUrl: 'icon_screen_logo',
              width: Get.setImageSize(120),
              height: Get.setImageSize(120),
            ),
            Text(
              'V ${PackageInfoManager().version}+${PackageInfoManager().buildNumber}${AppConfig.instance.getFlavorName}',
              style: styleSecond_14,
            )
          ],
        ),
      );

  HighLightInkWell _contentItem({String? title, String? url}) =>
      HighLightInkWell(
        onTap: () => Get.toWeb(url: url, title: title),
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setPaddingSize(14),
              horizontal: Get.setPaddingSize(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  title ?? '',
                  style: stylePrimary_16_m,
                ),
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              ImageWidget(
                assetUrl: 'icon_new_arrow01',
                width: Get.setImageSize(12),
                height: Get.setImageSize(12),
              )
            ],
          ),
        ),
      );

  HighLightInkWell _checkForUpdatesItem({String? title}) => HighLightInkWell(
        onTap: () {
          if (!AppController.isAppUpgrading.value) {
            controller.checkVersion();
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: Get.setPaddingSize(14),
              horizontal: Get.setPaddingSize(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  title ?? '',
                  style: stylePrimary_16_m,
                ),
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              Visibility(
                visible: controller.hasNewVersion(),
                child: AppController.isAppUpgrading.value
                    ? Text(ID.stringBackgroundUpgradeIng.tr,
                        style: TextStyle(
                            color: Get.theme.colorF44D4D,
                            fontSize: Get.setFontSize(14),
                            fontWeight: FontWeightX.regular,
                            fontFamily: Get.setFontFamily()))
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(ID.stringNewVersion.tr,
                              style: TextStyle(
                                  color: Get.theme.colorF44D4D,
                                  fontSize: Get.setFontSize(14),
                                  fontWeight: FontWeightX.regular,
                                  fontFamily: Get.setFontFamily())),
                          Container(
                              width: Get.setFontSize(6),
                              height: Get.setFontSize(6),
                              margin: EdgeInsets.symmetric(
                                  horizontal: Get.setFontSize(4)),
                              decoration: BoxDecoration(
                                color: Get.theme.colorF44D4D,
                                shape: BoxShape.circle,
                              )),
                        ],
                      ),
              ),
              Visibility(
                visible: !AppController.isAppUpgrading.value,
                child: ImageWidget(
                  assetUrl: 'icon_new_arrow01',
                  width: Get.setImageSize(12),
                  height: Get.setImageSize(12),
                ),
              )
            ],
          ),
        ),
      );
}
