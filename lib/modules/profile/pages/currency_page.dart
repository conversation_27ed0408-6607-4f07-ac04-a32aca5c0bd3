/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-04-01 10:13:33
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/profile/profile_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class CurrencyPage extends StatelessWidget {
  const CurrencyPage({super.key, required this.controller});

  final ProfileController controller;

  @override
  Widget build(BuildContext context) {
    Log.logPrint(controller.currency.value);
    return Obx(() => Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setHeight(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _item(
                  title: 'CNY',
                  cry: Currency.cny,
                  selected: controller.currency.value == Currency.cny),
              _item(
                  title: 'USD',
                  cry: Currency.usd,
                  selected: controller.currency.value == Currency.usd),
            ],
          ),
        ));
  }

  HighLightInkWell _item(
      {required String title,
      Currency cry = Currency.usd,
      bool selected = false}) {
    return HighLightInkWell(
      onTap: () => controller.updateCurrency(cry),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setWidth(16), vertical: Get.setHeight(14)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: _children(title: title, selected: selected),
        ),
      ),
    );
  }

  List<Widget> _children({String title = '', bool selected = false}) {
    List<Widget> list = [];
    list.add(Expanded(
        child: Text(
      title,
      style: stylePrimary_16_m,
    )));
    if (selected) {
      list.add(ImageWidget(
          assetUrl: selected ? 'icon_select' : '',
          width: Get.setWidth(20),
          height: Get.setHeight(20)));
    }
    return list;
  }
}
