/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-25 15:17:58
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/profile/controller/feedbook_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class FeedbookPage extends BaseStatelessWidget<FeedbookController> {
  const FeedbookPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(title: ID.stringuserfeedback.tr),
        bottomNavigationBar: _bottomWidget(),
        body: KeyboardDismissWidget(
          child: SingleChildScrollView(
            child: _submitbody(context),
          ),
        ));
  }

  Padding _submitbody(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFieldWidget(
              hintText: ID.feedbackname.tr,
              showClear: true,
              onValueChanged: (String value) {
                controller.nickname = value;
                controller.updateButtonStatus();
              }),
          SizedBox(
            height: Get.setHeight(24),
          ),
          TextFieldWidget(
              keyboardType: TextInputType.multiline,
              maxLength: 300,
              minLines: 7,
              maxLines: 20,
              hintText: ID.feedbackcontext.tr,
              onValueChanged: (String value) {
                controller.content = value;
                controller.updateButtonStatus();
              }),
          SizedBox(
            height: Get.setHeight(24),
          ),
          TextFieldWidget(
              hintText: ID.feedbackmode.tr,
              onValueChanged: (String value) {
                controller.contact = value;
                controller.updateButtonStatus();
              }),
        ],
      ),
    );
  }

  Padding _bottomWidget() => Padding(
        padding: EdgeInsets.only(
          left: Get.setPaddingSize(16),
          right: Get.setPaddingSize(16),
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Obx(() => ButtonWidget(
              text: ID.submitfeedback.tr,
              buttonStatus: controller.buttonStatus.value,
              onPressed: () => controller.loadData(),
            )),
      );
}
