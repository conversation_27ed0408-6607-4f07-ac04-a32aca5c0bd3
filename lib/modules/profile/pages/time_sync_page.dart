/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-30 16:19:20
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/profile/controller/time_sync_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:flutter/material.dart';

class TimeSyncPage extends BaseStatelessWidget<TimeSyncController> {
  const TimeSyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(
          title: ID.timesync.tr,
        ),
        body: _bodyWidget());
  }

  Obx _bodyWidget() => Obx(() => Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  QRWidget(
                    data: controller.qrData.value,
                  ),
                  Text(
                    ID.afterRefresh.trParams({
                      'seconds': controller.seconds.value.toString(),
                    }),
                    style: TextStyle(
                        fontFeatures: const [FontFeature.tabularFigures()],
                        fontSize: Get.setFontSize(16),
                        color: Get.theme.disabledColor),
                  ),
                ],
              ),
            ],
          ),
          DividerWidget(
            padding: EdgeInsets.symmetric(
              vertical: Get.setFontSize(24),
            ),
          ),
          Text(
            ID.stringNotices.tr,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              color: Get.theme.textPrimary,
              fontWeight: FontWeightX.medium,
            ),
          ),
          SizedBox(height: Get.setHeight(10)),
          Text(
            ID.textTimesyncInfo.tr,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              color: Get.theme.textSecondary,
              fontWeight: FontWeightX.regular,
            ),
          ),
        ]),
      ));
}
