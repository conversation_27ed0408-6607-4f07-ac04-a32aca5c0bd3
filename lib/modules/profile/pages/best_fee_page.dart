import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/profile/controller/best_fee_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:coinbag/widgets/status/app_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';

class BsetFeePage extends BaseStatelessWidget<BestFeeController> {
  const BsetFeePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(
          titleWidget: Text(
            ID.feenotegasprice.tr,
            style: styleAppbarTitle,
          ),
        ),
        body: controller.obx((state) => buildContent(controller),
            onLoading: const AppLoadingWidget(),
            onError: (error) => AppErrorWidget(onRefresh: () {
                  controller.showLoading();
                  controller.loadData();
                }),
            onEmpty: const AppEmptyWidget()));
  }

  RefreshWidget<BestFeeController> buildContent(BestFeeController controller) {
    return RefreshWidget<BestFeeController>(
        enablePullDown: true,
        enablePullUp: false,
        refreshController: controller.refreshController,
        child: SingleChildScrollView(
          child: Obx(() => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildRowItem(
                    BitcoinChain.get.symbolIcon,
                    BitcoinChain.get.symbol,
                    BitcoinChain.get.chainName,
                    '${controller.btcSat.value} sat/vB',
                    '${controller.btcFee.value} BTC/KB',
                  ),
                  SizedBox(height: Get.setHeight(14)),
                  _buildRowItem(
                    EthereumChain.get.symbolIcon,
                    EthereumChain.get.symbol,
                    EthereumChain.get.chainName,
                    '${controller.ethGwei.value} GWEI',
                    'GasPrice',
                  ),
                ],
              )),
        ));
  }

  Widget _buildRowItem(
    String assetUrl,
    String topText,
    String bottomText,
    String rightTopText,
    String rightBottomText,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ImageWidget(
            height: Get.setImageSize(28),
            width: Get.setImageSize(28),
            assetUrl: assetUrl,
          ),
          SizedBox(width: Get.setWidth(8)),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                topText,
                style: TextStyle(
                    fontSize: Get.setFontSize(16),
                    fontWeight: FontWeightX.medium,
                    fontFamily: Get.setFontFamily()),
              ),
              Text(
                bottomText,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    color: Get.theme.textSecondary,
                    fontFamily: Get.setFontFamily()),
              ),
            ],
          ),
          const Spacer(),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                rightTopText,
                style: TextStyle(
                    fontSize: Get.setFontSize(16),
                    fontWeight: FontWeightX.medium),
              ),
              Text(
                rightBottomText,
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    color: Get.theme.textSecondary,
                    fontFamily: Get.setFontFamily()),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
