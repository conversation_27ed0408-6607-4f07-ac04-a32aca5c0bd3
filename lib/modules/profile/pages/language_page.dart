import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/modules/profile/controller/preferences_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class LanguagePage extends StatelessWidget {
  const LanguagePage({super.key, required this.controller});

  final PreferencesController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() => Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setHeight(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: controller.lngList
                .map((model) => _languageItem(model))
                .toList(),
          ),
        ));
  }

  Widget _languageItem(LanguageModel model) {
    return HighLightInkWell(
      onTap: () {
        controller.updateLanguage(model);
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setWidth(16), vertical: Get.setHeight(14)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: _items(model),
        ),
      ),
    );
  }

  List<Widget> _items(LanguageModel model) {
    List<Widget> list = <Widget>[];
    if (model.country.isNotEmpty) {
      list.add(ImageWidget(
        assetUrl: model.imagePath,
        width: Get.setWidth(20),
        height: Get.setHeight(20),
      ));
      list.add(SizedBox(
        width: Get.setWidth(6),
      ));
    }

    list.add(Expanded(
        child: Text(
      model.name,
      style: stylePrimary_16_m,
    )));

    if (model.isSelect) {
      list.add(ImageWidget(
          assetUrl: model.isSelect ? 'icon_select' : '',
          width: Get.setWidth(20),
          height: Get.setHeight(20)));
    }

    return list;
  }
}
