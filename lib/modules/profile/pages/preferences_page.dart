/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-08-23 10:08:02
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/profile/controller/preferences_controller.dart';
import 'package:coinbag/modules/profile/pages/language_page.dart';
import 'package:coinbag/modules/profile/pages/qr_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class PreferencesPage extends BaseStatelessWidget<PreferencesController> {
  const PreferencesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.baseSettings.tr),
      body: Obx(() => Column(
            children: [
              _itemWidget(
                  title: ID.profileLanguage.tr,
                  value: AppController.languageModel.value.name,
                  onTap: () => controller
                      .showBottomSheet(LanguagePage(controller: controller))),
              _itemWidget(
                  title: ID.profileQR.tr,
                  value: AppController.qrModel.value.title,
                  onTap: () => controller
                      .showBottomSheet(QRPage(controller: controller))),
            ],
          )),
    );
  }

  HighLightInkWell _itemWidget(
      {required String title, required String value, Function()? onTap}) {
    return HighLightInkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        child: Row(
          children: [
            Text(
              title,
              style: stylePrimary_16_m,
            ),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            Expanded(
              child: Text(
                value,
                maxLines: 1,
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
                style: styleSecond_14,
              ),
            ),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(12),
              height: Get.setImageSize(12),
            )
          ],
        ),
      ),
    );
  }
}
