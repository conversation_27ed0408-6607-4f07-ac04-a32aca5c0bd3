/*
 * @description: Do not edit
 * @Author: wanglimiing
 * @Date: 2024-01-10 09:18:17
 * @LastEditTime: 2024-09-24 09:42:56
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class CustomerServicePage extends BaseStatelessWidget {
  const CustomerServicePage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(title: ID.stringonlineservice.tr),
        body: _buildLineColumn());
  }
}

Widget _createLineItem(String label, String content, String type) {
  return _itemWidget(
    label: label,
    content: content,
    onTap: () => _handleTap(type, content),
  );
}

Widget _buildLineColumn() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      _createLineItem(ID.stringhelptel.tr, CustomerService.helpPhone, 'Phone'),
      _itemWidget(
        label: ID.stringuserfeedback.tr,
        content: '',
        onTap: () => Get.toNamed(AppRoutes.feedbookPage),
      )
    ],
  );
}

Widget _itemWidget({
  required String label,
  required String content,
  required VoidCallback? onTap,
}) {
  return HighLightInkWell(
    onTap: onTap,
    child: Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Get.setPaddingSize(16),
        vertical: Get.setPaddingSize(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: Get.setFontSize(16),
                fontWeight: FontWeightX.medium,
                fontFamily: Get.setFontFamily(),
                color: Get.theme.textPrimary,
              ),
            ),
          ),
          Text(
            content,
            style: TextStyle(
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeight.w400,
              fontFamily: Get.setFontFamily(),
              color: Get.theme.textSecondary,
            ),
          ),
          SizedBox(width: Get.setPaddingSize(4)),
          ImageWidget(
            height: Get.setImageSize(12),
            width: Get.setImageSize(12),
            assetUrl: 'icon_new_arrow01',
          ),
        ],
      ),
    ),
  );
}

void _handleTap(String type, String content) async {
  if (type == 'Phone') {
    if (GetPlatform.isAndroid) {
      Get.showAlertDialog(
          onConfirm: () => Get.makePhoneCall(CustomerService.helpPhone),
          title: ID.stringonlineservice.tr,
          content: ID.stringTextDial.tr + CustomerService.helpPhone);
    } else {
      Get.makePhoneCall(CustomerService.helpPhone);
    }
  }
}
