/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-30 16:19:11
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/keyboard/keyboard_dismiss_widget.dart';
import 'package:coinbag/widgets/qr/qr_widget.dart';
import 'package:coinbag/widgets/text_field/text_field_widget.dart';
import 'package:flutter/material.dart';

class CreateQRPage extends StatefulWidget {
  const CreateQRPage({super.key});
  @override
  State<CreateQRPage> createState() => _MakeQrState();
}

class _MakeQrState extends State<CreateQRPage> {
  TextEditingController? textEditingController;
  String value = '';

  @override
  void initState() {
    textEditingController = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(
          title: ID.makeqr.tr,
        ),
        body: KeyboardDismissWidget(
          child: Padding(
            padding: EdgeInsets.only(
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                top: Get.setPaddingSize(8)),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    ID.contentText.tr,
                    style: TextStyle(
                        fontSize: Get.setFontSize(14),
                        color: Get.theme.textSecondary,
                        fontFamily: Get.setFontFamily(),
                        fontWeight: FontWeight.w400),
                  ),
                ],
              ),
              const SizedBox(
                height: 8,
              ),
              TextFieldWidget(
                  showClear: false,
                  onValueChanged: (value1) {
                    setState(() {
                      value = value1;
                    });
                  },
                  controller: textEditingController,
                  keyboardType: TextInputType.multiline,
                  maxLines: 500,
                  maxLength: 1000,
                  minLines: 4,
                  hintText: ID.textcontent.tr),
              const SizedBox(
                height: 24,
              ),
              ButtonWidget(
                  text: ID.makeqr.tr,
                  buttonSize: ButtonSize.full,
                  onPressed: () =>
                      Get.showBottomSheet(bodyWidget: buildDialog(value))),
            ]),
          ),
        ));
  }

  @override
  void dispose() {
    textEditingController?.dispose();
    super.dispose();
  }

  Center buildDialog(String value) {
    return Center(
      child: QRWidget(
        data: value,
      ),
    );
  }
}
