/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-04-08 18:39:34
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/profile/controller/preferences_controller.dart';
import 'package:coinbag/modules/profile/qr/qr_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class QRPage extends StatelessWidget {
  const QRPage({super.key, required this.controller});

  final PreferencesController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() => Padding(
          padding: EdgeInsets.only(top: Get.setHeight(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: _items(),
          ),
        ));
  }

  List<Widget> _items() {
    List<Widget> list = [];
    for (QRModel model in controller.qrList) {
      list.add(_content(model));
    }
    list.add(SizedBox(
      height: Get.setHeight(8),
    ));
    list.add(_tip());
    list.add(SizedBox(
      height: Get.setHeight(16),
    ));
    return list;
  }

  HighLightInkWell _content(QRModel model) {
    List<Widget> list = [];

    if (model.qrType == QRType.standard) {
      list.add(_text(model));
      list.add(SizedBox(
        width: Get.setWidth(10),
      ));
      list.add(_best());
      list.add(const Expanded(child: SizedBox()));
    } else {
      list.add(Expanded(child: _text(model)));
    }
    if (model.selected) {
      list.add(ImageWidget(
          assetUrl: 'icon_select',
          width: Get.setWidth(20),
          height: Get.setHeight(20)));
    }

    return HighLightInkWell(
      onTap: () => controller.updateQR(model),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setWidth(16), vertical: Get.setHeight(14)),
        child: Row(
          children: list,
        ),
      ),
    );
  }

  Text _text(QRModel model) {
    return Text(
      model.title,
      style: stylePrimary_16_m,
    );
  }

  Row _best() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Get.setRadius(5)),
              color: Get.theme.colorFD8114.withOpacity(0.1)),
          padding: EdgeInsets.symmetric(
              horizontal: Get.setWidth(6), vertical: Get.setHeight(2)),
          child: Text(
            ID.qrBest.tr,
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                fontFamily: Get.setFontFamily(),
                color: Get.theme.colorFD8114),
          ),
        ),
      ],
    );
  }

  Padding _tip() => Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        child: Container(
          padding: EdgeInsets.symmetric(
              horizontal: Get.setWidth(16), vertical: Get.setHeight(16)),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Get.setRadius(12)),
              color: Get.theme.colorF9F9F9),
          child: Text(
            ID.qrTip.tr,
            style: styleSecond_14,
          ),
        ),
      );
}
