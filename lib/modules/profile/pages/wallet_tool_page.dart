/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-07-30 10:38:48
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class WalletToolPage extends StatelessWidget {
  const WalletToolPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.profileTool.tr),
      body: Column(
        children: [
          _itemWidget(
            title: ID.feenotegasprice.tr,
            onTap: () => Get.toNamed(AppRoutes.bestFeePage),
          ),
          _itemWidget(
            title: ID.makeqr.tr,
            onTap: () => Get.toNamed(AppRoutes.createQRPage),
          ),
          _itemWidget(
            title: ID.timesync.tr,
            onTap: () => Get.toNamed(AppRoutes.timeSyncPage),
          )
        ],
      ),
    );
  }

  HighLightInkWell _itemWidget({required String title, Function()? onTap}) {
    return HighLightInkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: stylePrimary_16_m,
              ),
            ),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            ImageWidget(
              assetUrl: 'icon_new_arrow01',
              width: Get.setImageSize(12),
              height: Get.setImageSize(12),
            )
          ],
        ),
      ),
    );
  }
}
