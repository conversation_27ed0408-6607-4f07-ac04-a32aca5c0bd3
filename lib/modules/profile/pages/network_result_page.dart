/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-07 16:32:45
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/profile/controller/network_result_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';

class NetworkDiagnosticsResultPage
    extends BaseStatelessWidget<NetworkDiagnosticsResultController> {
  const NetworkDiagnosticsResultPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: controller.model.domainName),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          bottom: Get.getSafetyBottomPadding(),
        ),
        child: Text(
          controller.model.resultJson ?? '',
          style: TextStyle(
            color: Get.theme.textPrimary,
            fontSize: Get.setFontSize(12),
            fontFamily: Get.setNumberFontFamily(),
          ),
        ),
      ),
    );
  }
}
