/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-08 13:57:18
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/profile/controller/network_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NetworkDiagnosticsPage
    extends BaseStatelessWidget<NetworkDiagnosticsController> {
  const NetworkDiagnosticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(title: ID.stringNetworkDiagnostics.tr),
      body: GetBuilder<NetworkDiagnosticsController>(
          id: GetKey.networkId,
          builder: (controller) => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _networkStatusWidget(),
                  Expanded(
                    child: ListView.builder(
                      itemCount: controller.dataList.length,
                      itemBuilder: (_, index) =>
                          _itemWidget(controller.dataList[index]),
                    ),
                  ),
                ],
              )),
    );
  }

  Widget _networkStatusWidget() => controller.neworkCanUse() == true
      ? const SizedBox.shrink()
      : HighLightInkWell(
          onTap: () => controller.settingNetworkAction(),
          child: Container(
            color: Get.theme.colorF44D4D.withAlpha(125),
            width: Get.width,
            padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(16),
              vertical: Get.setPaddingSize(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    ID.stringNetworkStatus.tr,
                    style: stylePrimary_16_m,
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(8)),
                ImageWidget(
                  assetUrl: 'arrow_right',
                  width: Get.setImageSize(12),
                  height: Get.setImageSize(12),
                ),
              ],
            ),
          ),
        );

  HighLightInkWell _itemWidget(NetworkModel model) => HighLightInkWell(
        onTap: () => model.callback != null ? model.callback!(model) : {},
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(16),
            vertical: Get.setPaddingSize(10),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      model.domainName,
                      style: TextStyle(
                        color: Get.theme.textPrimary,
                        fontSize: Get.setFontSize(16),
                        fontWeight: FontWeightX.medium,
                        fontFamily: Get.setNumberFontFamily(),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              _rightWidget(model),
            ],
          ),
        ),
      );

  Widget _rightWidget(NetworkModel model) {
    if (model.status == ButtonStatus.loading) {
      return SizedBox(
        width: Get.setImageSize(16),
        height: Get.setImageSize(16),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Get.theme.primary),
        ),
      );
    } else if (model.status == ButtonStatus.enable) {
      return ImageWidget(
        assetUrl: 'icon_select',
        width: Get.setImageSize(20),
        height: Get.setImageSize(20),
      );
    } else if (model.status == ButtonStatus.disable) {
      return ImageWidget(
        assetUrl: 'icon_tx_fail',
        width: Get.setImageSize(20),
        height: Get.setImageSize(20),
      );
    }

    return const SizedBox();
  }
}
