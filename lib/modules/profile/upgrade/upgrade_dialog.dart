/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-12-04 13:37:32
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/modules/profile/upgrade/model/upgrade_model/upgrade_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class UpgradeDialog extends BaseStatefulWidget<AppController> {
  const UpgradeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    UpgradeModel? upgradeModel = AppController.upgradeModel.value;
    return PopScope(
        canPop: false,
        child: Obx(
          () => Center(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: Get.width - Get.setWidth(80),
                child: Material(
                  borderRadius: BorderRadius.circular(Get.setRadius(16)),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: Get.setWidth(24),
                        horizontal: Get.setWidth(16)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(ID.stringNewVersion.tr,
                                style: TextStyle(
                                    color: Get.theme.textPrimary,
                                    fontFamily: Get.setFontFamily(),
                                    fontSize: Get.setFontSize(20),
                                    fontWeight: FontWeightX.semibold)),
                          ],
                        ),
                        SizedBox(height: Get.setHeight(20)),
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Visibility(
                                visible: !Get.isEmptyString(
                                    upgradeModel!.releaseNotes),
                                child: Html(
                                    data: upgradeModel.releaseNotes!,
                                    style: {
                                      "p": Style(
                                        fontSize: FontSize(14),
                                        fontFamily: Get.setFontFamily(),
                                        color: Get.theme.textPrimary, // 设置字体颜色
                                        fontWeight:
                                            FontWeightX.regular, // 设置字体粗细
                                      ),
                                    }),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: Get.setHeight(10)),
                        _buildButton(upgradeModel),
                      ],
                    ),
                  ),
                ),
              ),
              Visibility(
                  visible: !upgradeModel.isMandatoryUpdate!,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () => Get.dismissDialog(),
                      child: ImageWidget(
                          assetUrl: 'icon_close',
                          width: Get.setImageSize(24),
                          height: Get.setImageSize(24)),
                    ),
                  )),
            ],
          )),
        ));
  }

  ButtonWidget _buildButton(UpgradeModel? upgradeModel) {
    // 提取按钮文本
    String buttonText =
        AppController.isAppUpgrading.value && GetPlatform.isAndroid
            ? ID.stringBackgroundUpgradeIng.tr
            : ID.stringUpgradeNow.tr;
    ButtonStatus status =
        AppController.isAppUpgrading.value && GetPlatform.isAndroid
            ? ButtonStatus.disable
            : ButtonStatus.enable;

    return ButtonWidget(
        buttonSize: ButtonSize.full,
        text: buttonText,
        buttonStatus: status,
        onPressed: () => controller.upgrade(upgradeModel));
  }
}
