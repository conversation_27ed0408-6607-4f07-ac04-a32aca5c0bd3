/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-12-04 10:06:50
 */
import 'package:json_annotation/json_annotation.dart';

part 'upgrade_model.g.dart';

@JsonSerializable()
class UpgradeModel {
  String? downloadLink;
  bool? hasNewVersion;
  bool? isMandatoryUpdate;
  String? releaseNotes;
  String? version;

  UpgradeModel({
    this.downloadLink,
    this.hasNewVersion,
    this.isMandatoryUpdate,
    this.releaseNotes,
    this.version,
  });

  factory UpgradeModel.fromJson(Map<String, dynamic> json) {
    return _$UpgradeModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpgradeModelToJson(this);
}
