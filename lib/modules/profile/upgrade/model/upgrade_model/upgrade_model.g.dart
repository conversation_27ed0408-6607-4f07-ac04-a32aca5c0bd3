// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upgrade_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpgradeModel _$UpgradeModelFromJson(Map<String, dynamic> json) => UpgradeModel(
      downloadLink: json['downloadLink'] as String?,
      hasNewVersion: json['hasNewVersion'] as bool?,
      isMandatoryUpdate: json['isMandatoryUpdate'] as bool?,
      releaseNotes: json['releaseNotes'] as String?,
      version: json['version'] as String?,
    );

Map<String, dynamic> _$UpgradeModelToJson(UpgradeModel instance) =>
    <String, dynamic>{
      'downloadLink': instance.downloadLink,
      'hasNewVersion': instance.hasNewVersion,
      'isMandatoryUpdate': instance.isMandatoryUpdate,
      'releaseNotes': instance.releaseNotes,
      'version': instance.version,
    };
