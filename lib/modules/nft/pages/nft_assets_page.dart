/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-14 14:58:13
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_controller.dart';
import 'package:coinbag/modules/nft/widgets/nft_assets_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/status_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NftAssetsPage extends BaseStatelessWidget<NftAssetsController> {
  const NftAssetsPage({super.key});

  bool get isEmpty => (controller.assetsModel?.nftList ?? []).isEmpty;

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: GetBuilder<NftAssetsController>(
        id: GetKey.nftAssetsId,
        builder: (_) => controller.obx(
          (state) => _buildWidget(),
          onLoading: _onLoadingWidget(),
          onError: (error) => _onErrorWidget(),
        ),
      ),
    );
  }

  double get width => (Get.width - Get.setPaddingSize(16)) / 2;
  Widget _buildWidget() {
    if (controller.assetsModel == null) return const SizedBox.shrink();
    return RefreshWidget<NftAssetsController>(
      child: isEmpty
          ? const AppEmptyWidget()
          : GridView.builder(
              padding: EdgeInsets.only(
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                bottom: Get.setPaddingSize(16),
              ),
              controller: controller.scrollController,
              gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: width,
                childAspectRatio: 0.63,
                crossAxisSpacing: Get.setPaddingSize(16),
                mainAxisSpacing: Get.setPaddingSize(16),
              ),
              itemCount: controller.assetsModel?.nftList?.length ?? 0,
              itemBuilder: (_, index) {
                final list = controller.assetsModel?.nftList;
                if (list == null || index >= list.length) {
                  return const SizedBox.shrink();
                }
                return NftAssetsWidget(width: width, model: list[index]);
              }),
    );
  }

  dynamic _onLoadingWidget() {
    return isEmpty ? const NftAssetsSkeletonWidget() : _buildWidget();
  }

  dynamic _onErrorWidget() {
    return isEmpty
        ? AppErrorWidget(onRefresh: () => controller.onLoadRefresh())
        : _buildWidget();
  }
}
