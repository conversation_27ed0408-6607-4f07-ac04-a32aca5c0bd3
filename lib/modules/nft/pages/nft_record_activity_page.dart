/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-11 09:07:34
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/controllers/nft_record_activity_controller.dart';
import 'package:coinbag/modules/nft/models/nft_record_activity_model/nft_record_activity_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/status_widget.dart';
import 'package:flutter/material.dart';

class NftRecordActivityPage
    extends BaseStatelessWidget<NftRecordActivityController> {
  final String tagValue;
  const NftRecordActivityPage({super.key, required this.tagValue});

  @override
  String? get tag => tagValue;

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: GetBuilder<NftRecordActivityController>(
        id: GetKey.nftRecordActivityId,
        tag: tag,
        builder: (_) => controller.obx(
          (state) {
            List<NftRecordActivityModel> dataList = controller.dataSource(tag!);
            return _buildWidget(dataList);
          },
          onLoading: _onLoadingWidget(),
          onError: (error) => _onErrorWidget(),
        ),
      ),
    );
  }

  RefreshWidget<NftRecordActivityController> _buildWidget(
      List<NftRecordActivityModel> dataList) {
    return RefreshWidget<NftRecordActivityController>(
      enablePullDown: true,
      enablePullUp: true,
      controllerTag: tag,
      refreshController: controller.refreshController,
      child: dataList.isEmpty
          ? const AppEmptyWidget()
          : ListView.builder(
              itemCount: dataList.length,
              itemBuilder: (_, index) => _buildItemWidget(dataList[index]),
            ),
    );
  }

  HighLightInkWell _buildItemWidget(NftRecordActivityModel model) =>
      HighLightInkWell(
        onTap: () => Get.toNamed(
          AppRoutes.nftActivityDetailPage,
          arguments: {
            GetArgumentsKey.model: model,
            GetArgumentsKey.coinModel: controller.coinModel,
            GetArgumentsKey.addressModel: controller.addressModel,
          },
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(12),
            horizontal: Get.setPaddingSize(16),
          ),
          child: Row(
            children: [
              ImageWidget(
                width: Get.setImageSize(32),
                height: Get.setImageSize(32),
                assetUrl: model.imageName(controller.addressModel.address),
              ),
              SizedBox(width: Get.setPaddingSize(10)),
              Expanded(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          model.eventTypeValue(controller.addressModel.address),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Get.theme.textPrimary,
                            fontSize: Get.setFontSize(16),
                            fontWeight: FontWeightX.semibold,
                          ),
                        ),
                        SizedBox(width: Get.setPaddingSize(24)),
                        Flexible(
                          flex: 2,
                          child: Text(
                            model.amountValue(controller.addressModel.address),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: model.amountValueColor(
                                  controller.addressModel.address),
                              fontSize: Get.setFontSize(16),
                              fontWeight: FontWeightX.semibold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: Get.setPaddingSize(2)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          model.address(controller.addressModel.address),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: Get.theme.textSecondary,
                            fontSize: Get.setFontSize(12),
                          ),
                        ),
                        SizedBox(width: Get.setPaddingSize(2)),
                        Flexible(
                          flex: 1,
                          child: Text(
                            model.dateFormat,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Get.theme.textSecondary,
                              fontSize: Get.setFontSize(12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      );

  NftRecordActivitySkeletonWidget _onLoadingWidget() =>
      const NftRecordActivitySkeletonWidget();

  AppErrorWidget _onErrorWidget() =>
      AppErrorWidget(onRefresh: () => controller.onLoadRefresh());
}
