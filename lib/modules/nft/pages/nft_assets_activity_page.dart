/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-22 10:16:39
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_detail_controller.dart';
import 'package:coinbag/modules/nft/models/nft_activity_model/nft_activity_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:flutter/material.dart';

class NftAssetsActivityPage
    extends BaseStatelessWidget<NftAssetsDetailController> {
  const NftAssetsActivityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: GetBuilder<NftAssetsDetailController>(
        id: GetKey.nftActivityId,
        builder: (_) => controller.obx(
          (state) => _buildWidget(),
          onLoading: _onLoadingWidget(),
          onError: (_) => _onErrorWidget(),
        ),
      ),
    );
  }

  StatelessWidget _buildWidget() => controller.activityList.isEmpty
      ? const AppEmptyWidget()
      : RefreshWidget<NftAssetsDetailController>(
          enablePullUp: true,
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
            itemCount: controller.activityList.length,
            itemBuilder: (_, index) =>
                _itemWidget(controller.activityList[index]),
          ),
        );

  Padding _itemWidget(NftActivityModel model) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(8)),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  ID.stringNftTransfer.tr,
                  style: TextStyle(
                    color: Get.theme.textPrimary,
                    fontSize: Get.setFontSize(16),
                    fontWeight: FontWeightX.semibold,
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(8)),
                Expanded(
                  child: Text(
                    ID.stringNftFrom.trParams(
                        {'value': AddressUtils.omitAddress(model.from)}),
                    maxLines: 1,
                    textAlign: TextAlign.end,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(12),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: Get.setPaddingSize(2)),
            Row(
              children: [
                Text(
                  DateHeleper.formatTimestamp(model.timestamp),
                  style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(12),
                  ),
                ),
                SizedBox(width: Get.setPaddingSize(8)),
                Expanded(
                  child: Text(
                    ID.stringNftTo.trParams(
                        {'value': AddressUtils.omitAddress(model.to)}),
                    maxLines: 1,
                    textAlign: TextAlign.end,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(12),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      );

  NftActivitySkeletonWidget _onLoadingWidget() =>
      const NftActivitySkeletonWidget();
  AppErrorWidget _onErrorWidget() =>
      AppErrorWidget(onRefresh: () => controller.loadData());
}
