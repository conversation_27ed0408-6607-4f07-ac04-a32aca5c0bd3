/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-14 16:17:54
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_detail_controller.dart';
import 'package:coinbag/modules/nft/widgets/nft_assets_detail_header_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/behavior/over_scroll_behavior.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/sliver/auto_sliver_appbar_delegate.dart';
import 'package:coinbag/widgets/tab_bar/base_tab_bar.dart';
import 'package:flutter/material.dart';

class NftAssetsDetailPage
    extends BaseStatelessWidget<NftAssetsDetailController> {
  const NftAssetsDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(),
      bottomNavigationBar: _bottomWidget(),
      body: ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: NestedScrollView(
            controller: controller.scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
              return <Widget>[
                // _appBarWidget(),
                const NftAssetsDetailHeaderWidget(),
                SliverPersistentHeader(
                  pinned: true,
                  floating: false,
                  delegate: AutoSliverPersistentHeaderDelegate(baseTabBar(
                      onTab: (index) =>
                          controller.pagerController.jumpToPage(index),
                      controller: controller.tabController,
                      tabs: controller.tabList
                          .map((element) => Tab(
                                text: element,
                              ))
                          .toList())),
                ),
              ];
            },
            body: PageView.builder(
                itemCount: controller.tabList.length,
                onPageChanged: (index) =>
                    controller.tabController.animateTo(index),
                controller: controller.pagerController,
                itemBuilder: (_, int index) => controller.pagerList[index])),
      ),
    );
  }

  // _appBarWidget() {
  //   return Obx(() => SliverAppBar(
  //         pinned: true,
  //         backgroundColor: controller.appBarColor.value,
  //         surfaceTintColor: Colors.transparent,
  //         title: Text(
  //           controller.appBarColor.value.a == 0
  //               ? ''
  //               : controller.model.tokenNameValue,
  //           style: styleAppbarTitle,
  //         ),
  //         leading: IconButton(
  //           onPressed: () => Get.back(),
  //           padding: EdgeInsets.only(left: Get.setPaddingSize(6)),
  //           icon: Semantics(
  //             label: ID.stringBackButton.tr,
  //             child: ImageWidget(
  //               assetUrl: 'titlebar',
  //               cacheRawData: true,
  //               shape: BoxShape.circle,
  //               width: Get.setImageSize(28),
  //               height: Get.setImageSize(28),
  //             ),
  //           ),
  //         ),
  //       ));
  // }

  Padding _bottomWidget() {
    return Padding(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        top: Get.setPaddingSize(16),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: ButtonWidget(
        text: ID.send.tr,
        onPressed: () => Get.toNamed(AppRoutes.sendPage, arguments: {
          GetArgumentsKey.isNft: true,
          GetArgumentsKey.coinModel: controller.coinModel,
          GetArgumentsKey.addressModel: controller.addressModel,
          GetArgumentsKey.model: controller.model,
        }),
      ),
    );
  }
}
