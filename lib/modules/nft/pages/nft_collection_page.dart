/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-14 15:01:05
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/controllers/nft_collection_controller.dart';
import 'package:coinbag/modules/nft/widgets/nft_collection_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class NftCollectionPage extends BaseStatelessWidget<NftCollectionController> {
  const NftCollectionPage({super.key});

  bool get isEmpty =>
      (controller.collectionModel?.collectionList ?? []).isEmpty;

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: GetBuilder<NftCollectionController>(
        id: GetKey.nftCollectionsId,
        builder: (_) => controller.obx(
          (state) => _buildWidget(),
          onLoading: _onLoadingWidget(),
          onError: (error) => _onErrorWidget(),
        ),
      ),
    );
  }

  double get width => (Get.width - Get.setPaddingSize(16)) / 2;

  double get height {
    double imageHeight = Get.setImageSize(60);
    double collectionNameHeight = controller
        .boundingTextSize(
            text: 'EYYJG', style: NftCollectionWidget.collectionNameStyle)
        .height;
    double countHeight = controller
        .boundingTextSize(
            text: ID.stringHoldValue.tr, style: NftCollectionWidget.styleSize12)
        .height;
    double amountHeight = Get.setHeight(50);
    double padding = Get.setPaddingSize(16 + 16 + 12 + 4 + 12);
    return imageHeight +
        collectionNameHeight +
        countHeight +
        amountHeight +
        padding +
        Get.setPaddingSize(25);
  }

  double get childAspectRatio {
    return double.parse(width
        .toString()
        .div(height.toString(), scale: 2, roundMode: RoundMode.down));
  }

  Widget _buildWidget() {
    if (controller.collectionModel == null) return const SizedBox.shrink();

    return RefreshWidget<NftCollectionController>(
      child: isEmpty
          ? const AppEmptyWidget()
          : GridView.builder(
              padding: EdgeInsets.only(
                left: Get.setPaddingSize(16),
                right: Get.setPaddingSize(16),
                bottom: Get.setPaddingSize(16),
              ),
              controller: controller.scrollController,
              gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: width,
                childAspectRatio: childAspectRatio,
                crossAxisSpacing: Get.setPaddingSize(16),
                mainAxisSpacing: Get.setPaddingSize(16),
              ),
              itemCount:
                  controller.collectionModel?.collectionList?.length ?? 0,
              itemBuilder: (_, index) => NftCollectionWidget(
                  width: width,
                  model: controller.collectionModel!.collectionList![index])),
    );
  }

  dynamic _onLoadingWidget() {
    return isEmpty ? const NftCollectionSkeletonWidget() : _buildWidget();
  }

  dynamic _onErrorWidget() {
    return isEmpty
        ? AppErrorWidget(onRefresh: () => controller.onLoadRefresh())
        : _buildWidget();
  }
}
