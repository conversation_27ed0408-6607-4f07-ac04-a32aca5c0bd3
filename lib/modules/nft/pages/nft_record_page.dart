/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-09 17:21:16
 */
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/nft/controllers/nft_record_controller.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/behavior/over_scroll_behavior.dart';
import 'package:coinbag/widgets/sliver/auto_sliver_appbar_delegate.dart';
import 'package:coinbag/widgets/tab_bar/base_tab_bar.dart';
import 'package:flutter/material.dart';

class NftRecordPage extends BaseStatelessWidget<NftRecordController> {
  const NftRecordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(),
      body: ScrollConfiguration(
        behavior: OverScrollBehavior(),
        child: NestedScrollView(
            controller: controller.scrollController,
            headerSliverBuilder:
                (BuildContext context, bool innerBoxIsScrolled) {
              return <Widget>[
                SliverPersistentHeader(
                  pinned: true,
                  floating: true,
                  delegate: AutoSliverPersistentHeaderDelegate(baseTabBar(
                      onTab: (index) =>
                          controller.pagerController.jumpToPage(index),
                      controller: controller.tabController,
                      tabs: controller.tabList
                          .map((element) => Tab(
                                text: element,
                              ))
                          .toList())),
                ),
              ];
            },
            body: PageView.builder(
                itemCount: controller.tabList.length,
                onPageChanged: (index) {
                  controller.tabController.animateTo(index);
                },
                controller: controller.pagerController,
                itemBuilder: (_, int index) => controller.pagerList[index])),
      ),
    );
  }
}
