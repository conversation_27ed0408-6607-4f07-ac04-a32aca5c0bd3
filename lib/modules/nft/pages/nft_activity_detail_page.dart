/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-11 10:09:41
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/nft/models/nft_record_activity_model/nft_record_activity_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NftActivityDetailPage extends StatelessWidget {
  const NftActivityDetailPage({super.key});

  CoinModel get coinModel => Get.arguments[GetArgumentsKey.coinModel];
  AddressModel get addressModel => Get.arguments[GetArgumentsKey.addressModel];
  NftRecordActivityModel get model => Get.arguments[GetArgumentsKey.model];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: baseAppBar(),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _headerWidget(),
            SizedBox(height: Get.setPaddingSize(16)),
            _itemWidget(
              isTsType: true,
              title: ID.stringTransactionType.tr,
              value: model.eventTypeValue(addressModel.address),
            ),
            _itemWidget(
              title: ID.stringTxTime.tr,
              value: model.dateFormat,
            ),
            DividerWidget(
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(8),
              ),
            ),
            _itemWidget(
              isCopy: true,
              title: ID.stringSendAddress.tr,
              value: AddressUtils.omitAddress(model.send ?? '', len: 10),
              copyValue: model.send ?? '',
            ),
            _itemWidget(
              isCopy: true,
              title: ID.stringReciveAddress.tr,
              value: AddressUtils.omitAddress(model.receive ?? '', len: 10),
              copyValue: model.receive ?? '',
            ),
            _itemWidget(
              isCopy: true,
              title: ID.stringTargetAddress.tr,
              value: AddressUtils.omitAddress(model.to ?? '', len: 10),
              copyValue: model.to ?? '',
            ),
            DividerWidget(
              padding: EdgeInsets.symmetric(
                horizontal: Get.setPaddingSize(16),
                vertical: Get.setPaddingSize(8),
              ),
            ),
            _itemWidget(
              isCopy: true,
              title: ID.stringTxId.tr,
              value: AddressUtils.omitAddress(model.hash ?? '', len: 10),
              copyValue: model.hash ?? '',
            ),
          ],
        ),
      ),
    );
  }

  Padding _itemWidget({
    required String title,
    required String value,
    bool isTsType = false,
    bool isCopy = false,
    String copyValue = '',
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Get.setPaddingSize(16),
        vertical: Get.setPaddingSize(10),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                title,
                style: styleSecond_14,
              ),
              SizedBox(width: Get.setPaddingSize(8)),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        value,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.end,
                        style: stylePrimary_14_m,
                      ),
                    ),
                    Visibility(
                      visible: isCopy,
                      child: GestureDetector(
                        onTap: () => Get.copy(copyValue),
                        behavior: HitTestBehavior.translucent,
                        child: Padding(
                          padding: EdgeInsets.only(left: Get.setPaddingSize(6)),
                          child: ImageWidget(
                            width: Get.setImageSize(16),
                            height: Get.setImageSize(16),
                            assetUrl: 'copy',
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          Visibility(
            visible: isTsType,
            child: Padding(
              padding: EdgeInsets.only(top: Get.setPaddingSize(4)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ImageWidget(
                    width: Get.setImageSize(20),
                    height: Get.setImageSize(20),
                    assetUrl: coinModel.chainIcon,
                  ),
                  SizedBox(width: Get.setPaddingSize(4)),
                  Text(
                    coinModel.chainName ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    style: styleSecond_14,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Padding _headerWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      child: Column(
        children: [
          SizedBox(height: Get.setPaddingSize(20)),
          ImageWidget(
            width: Get.setImageSize(60),
            height: Get.setImageSize(60),
            assetUrl: 'icon_success',
          ),
          SizedBox(height: Get.setPaddingSize(10)),
          Text(
            ID.stringNftSuccess.tr,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(16),
              fontWeight: FontWeightX.medium,
            ),
          ),
          SizedBox(height: Get.setPaddingSize(14)),
          Text(
            model.amountValue(addressModel.address),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: model.amountValueColor(addressModel.address),
              fontSize: Get.setFontSize(24),
              fontWeight: FontWeightX.semibold,
              fontFamily: Get.setNumberFontFamily(),
            ),
          ),
          SizedBox(height: Get.setPaddingSize(24)),
          const DividerWidget(),
        ],
      ),
    );
  }
}
