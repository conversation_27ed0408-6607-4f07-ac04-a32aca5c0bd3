import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_detail_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NftAssetsOverviewPage
    extends BaseStatelessWidget<NftAssetsDetailController> {
  const NftAssetsOverviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return KeepAliveWrapper(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(Get.setPaddingSize(16)),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _valueWidget(
                    value: AddressUtils.omitAddress(
                        controller.model.contractAddress ?? ''),
                    title: ID.stringContractAddress.tr,
                    isCopy: true,
                    copyValue: controller.model.contractAddress,
                  ),
                  SizedBox(height: Get.setPaddingSize(16)),
                  _valueWidget(
                    value: controller.model.ercType ?? '',
                    title: ID.stringTokenStandards.tr,
                  )
                ],
              ),
            ),
            SizedBox(width: Get.setPaddingSize(4)),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _valueWidget(
                    value: controller.model.tokenId ?? '',
                    title: 'Token ID',
                    isCopy: true,
                    copyValue: controller.model.contractAddress,
                  ),
                  SizedBox(height: Get.setPaddingSize(16)),
                  _valueWidget(
                    value: controller.model.chainName ?? '',
                    title: ID.stringNftNetwork.tr,
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Column _valueWidget({
    required String value,
    required String title,
    bool isCopy = false,
    String? copyValue,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Flexible(
              child: Text(
                value,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Get.theme.textPrimary,
                  fontSize: Get.setFontSize(16),
                  fontWeight: FontWeightX.semibold,
                  fontFamily: Get.setNumberFontFamily(),
                ),
              ),
            ),
            SizedBox(width: Get.setPaddingSize(6)),
            Visibility(
              visible: isCopy,
              child: GestureDetector(
                onTap: () => Get.copy(copyValue),
                child: ImageWidget(
                  width: Get.setImageSize(18),
                  height: Get.setImageSize(18),
                  assetUrl: 'copy',
                ),
              ),
            )
          ],
        ),
        SizedBox(height: Get.setPaddingSize(4)),
        Text(
          title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            color: Get.theme.textSecondary,
            fontSize: Get.setFontSize(12),
          ),
        ),
      ],
    );
  }
}
