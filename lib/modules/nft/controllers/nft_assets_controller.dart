/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-09 13:41:18
 */

import 'dart:convert';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/nft/models/nft_assets_item_model.dart';
import 'package:coinbag/modules/nft/models/nft_assets_model.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:get/get.dart';
import 'package:wallet_core/extension/string_decimal.dart';

class NftAssetsController extends NftController {
  NftAssetsController() {
    // 确保 NftController 已注入，防止 Get.find 报错
    if (!Get.isRegistered<NftController>()) {
      Get.lazyPut<NftController>(() => NftController());
    }
  }
  NftAssetsModel? assetsModel;

  @override
  void onInit() async {
    await initData();
    super.onInit();
    controller.isLoading = true;
    _loadCache();
    _initEverData();
  }

  void _loadCache() {
    String? json = addressModel!.nftAssetsCache;
    if (!Get.isEmptyString(json)) {
      assetsModel = NftAssetsModel.fromJson(jsonDecode(json!));
    } else {
      assetsModel = null;
    }
    updateNftHeader(assetsModel);
    update([GetKey.nftAssetsId]);
  }

  @override
  void onReady() {
    super.onReady();
    onLoadRefresh();
  }

  Future<void> _initEverData() async {
    ever(AppController.currency, (model) {
      controller.unit = CurrencyController.getCurrencyUnitByCurrency(model);
      updateNftHeader(assetsModel);
      update([GetKey.nftAssetsId]);
      controller.update([GetKey.nftHeaderId]);
    });

    ever(AppController.isRefreshNFT, (_) async {
      await initData();
      await controller.initData();
      controller.isLoading = true;
      _loadCache();
      scrollToTop();
      onLoadRefresh();
    });
  }

  @override
  onLoadMore() => requestPageData();

  @override
  onLoadRefresh() {
    pageIndex = 1;
    showLoading();
    requestPageData();
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    List<NftAssetsItemModel> itemModels = assetsModel?.nftList ?? [];

    httpRequest(
      api.nftRequest(
          cancelToken,
          BlockChainParamsManager.createParams(
              method: BlockChainAPI.nftAssets,
              requestParams: RequestParams()
                  .put(APIConstant.chain, coinModel!.chain!)
                  .put(APIConstant.address, addressModel!.address)
                  .put(
                APIConstant.extra,
                {
                  APIConstant.currentPage: pageIndex,
                  APIConstant.pageSize: 10,
                },
              ).getRequestBody())),
      (value) async {
        if (value.data != null) {
          assetsModel = NftAssetsModel.fromJson(value.data);
          controller.isLoading = false;
          updateNftHeader(assetsModel);
          if (pageIndex == 1) {
            await Get.database.addressDao
                .updateNftAssets(addressModel!, jsonEncode(value.data));
          } else {
            List<NftAssetsItemModel> nftList = assetsModel!.nftList ?? [];
            itemModels.addAll(nftList);
            assetsModel!.nftList = itemModels;
          }

          int total = assetsModel?.total ?? 0;
          int length = assetsModel?.nftList?.length ?? 0;

          hideRefresh(refreshController, finishLoadMore: total <= length);
          pageIndex++;
        } else {
          stopLoading();
          showError();
        }
      },
    );
  }

  /// 更新header
  void updateNftHeader(NftAssetsModel? assetsModel) {
    controller.totalAmount = assetsModel?.holdingCount ?? '0';
    String amount = assetsModel?.collectionSumUsd ?? '0';
    String price = AppController.currency.value == Currency.usd
        ? AppController.usdRate?.usdt ?? '0'
        : AppController.usdRate?.cny ?? '0';
    controller.totalBalance = amount.mul(price).decimal(scale: 2);
    controller.controller.update([GetKey.nftHeaderId]);
  }

  void stopLoading() {
    controller.isLoading = false;
    controller.controller.update([GetKey.nftHeaderId]);
  }
}
