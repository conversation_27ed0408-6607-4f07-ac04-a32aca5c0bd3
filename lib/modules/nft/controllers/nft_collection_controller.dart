/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-12-09 13:49:57
 */
import 'dart:convert';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/nft/models/nft_collection_item_model.dart';
import 'package:coinbag/modules/nft/models/nft_collection_model.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:flutter/material.dart';

class NftCollectionController extends NftController {
  NftCollectionModel? collectionModel;

  @override
  void onInit() async {
    await initData();
    super.onInit();
    controller.isLoading = true;
    _loadCache();
    _initEverData();
  }

  void _loadCache() {
    String? json = addressModel!.nftCollectionCache;
    if (!Get.isEmptyString(json)) {
      collectionModel = NftCollectionModel.fromJson(jsonDecode(json!));
    } else {
      collectionModel = null;
    }
    update([GetKey.nftCollectionsId]);
  }

  Future<void> _initEverData() async {
    ever(AppController.currency, (model) {
      controller.unit = CurrencyController.getCurrencyUnitByCurrency(model);
      update([GetKey.nftCollectionsId]);
      controller.controller.update([GetKey.nftHeaderId]);
    });

    ever(AppController.isRefreshNFT, (_) async {
      await initData();
      await controller.initData();
      controller.isLoading = true;
      _loadCache();
      scrollToTop();
      onLoadRefresh();
    });
  }

  @override
  void onReady() {
    super.onReady();
    onLoadRefresh();
  }

  @override
  onLoadMore() => requestPageData();

  @override
  onLoadRefresh() {
    pageIndex = 1;

    showLoading();
    requestPageData(refresh: Refresh.pull);
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    List<NftCollectionItemModel> itemModels =
        collectionModel?.collectionList ?? [];

    Log.logPrint(refresh);

    httpRequest(
      api.nftRequest(
          cancelToken,
          BlockChainParamsManager.createParams(
              method: BlockChainAPI.nftCollections,
              requestParams: RequestParams()
                  .put(APIConstant.chain, coinModel!.chain!)
                  .put(APIConstant.address, addressModel!.address)
                  .put(
                APIConstant.extra,
                {
                  APIConstant.currentPage: pageIndex,
                  APIConstant.pageSize: 10,
                  APIConstant.limit: 0,
                },
              ).getRequestBody())),
      (value) async {
        if (value.data != null) {
          collectionModel = NftCollectionModel.fromJson(value.data);
          controller.isLoading = false;
          // updateNftHeader(assetsModel);
          if (pageIndex == 1) {
            await Get.database.addressDao
                .updateNftCollection(addressModel!, jsonEncode(value.data));
          } else {
            List<NftCollectionItemModel> nftList =
                collectionModel!.collectionList ?? [];
            itemModels.addAll(nftList);
            collectionModel!.collectionList = itemModels;
          }

          int total = collectionModel?.total ?? 0;
          int length = collectionModel?.collectionList?.length ?? 0;

          hideRefresh(refreshController, finishLoadMore: total <= length);
          pageIndex++;
        } else {
          showError();
        }
      },
    );
  }

  Size boundingTextSize({
    required String text,
    required TextStyle style,
  }) {
    if (text.isEmpty) {
      return Size.zero;
    }
    final TextPainter textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      text: TextSpan(
        text: text,
        style: style,
      ),
      maxLines: 1,
    )..layout(maxWidth: double.infinity);
    return textPainter.size;
  }
}
