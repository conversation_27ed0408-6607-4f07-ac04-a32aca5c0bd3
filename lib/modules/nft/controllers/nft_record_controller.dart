/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-11 10:17:18
 */

import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/nft/controllers/nft_record_activity_controller.dart';
import 'package:coinbag/modules/nft/models/nft_record_activity_model/nft_record_activity_model.dart';
import 'package:coinbag/modules/nft/pages/nft_record_activity_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';

class NftRecordTag {
  static String all = 'nft_all';
  static String receive = 'Receive';
  static String send = 'Send';
  static String sale = 'Sale';
  static String mint = 'Mint';
  static String burn = 'Burn';
}

class NftRecordController extends BaseController
    with GetTickerProviderStateMixin {
  final List<String> tabList = [
    ID.stringAll.tr,
    ID.receive.tr,
    ID.send.tr,
    ID.stringNftSale.tr,
    ID.stringNftMint.tr,
    ID.stringNftBurn.tr,
  ];
  late TabController tabController =
      TabController(length: tabList.length, vsync: this);
  ScrollController scrollController = ScrollController();
  final PageController pagerController = PageController();
  final List<Widget> pagerList = [
    NftRecordActivityPage(
      tagValue: NftRecordTag.all,
    ),
    NftRecordActivityPage(
      tagValue: NftRecordTag.receive,
    ),
    NftRecordActivityPage(
      tagValue: NftRecordTag.send,
    ),
    NftRecordActivityPage(
      tagValue: NftRecordTag.sale,
    ),
    NftRecordActivityPage(
      tagValue: NftRecordTag.mint,
    ),
    NftRecordActivityPage(
      tagValue: NftRecordTag.burn,
    ),
  ];

  late AddressModel addressModel;
  late CoinModel coinModel;

  String next = '';
  List<NftRecordActivityModel> dataList = [];
  int? total;

  @override
  void onInit() {
    addressModel = Get.arguments[GetArgumentsKey.addressModel];
    coinModel = Get.arguments[GetArgumentsKey.coinModel];
    super.onInit();
  }

  @override
  void loadData() {}

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
    tabController.dispose();
    Get.delete<NftRecordActivityController>(tag: NftRecordTag.all);
    Get.delete<NftRecordActivityController>(tag: NftRecordTag.receive);
    Get.delete<NftRecordActivityController>(tag: NftRecordTag.send);
    Get.delete<NftRecordActivityController>(tag: NftRecordTag.sale);
    Get.delete<NftRecordActivityController>(tag: NftRecordTag.mint);
    Get.delete<NftRecordActivityController>(tag: NftRecordTag.burn);
  }
}

class NftRecordBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => NftRecordController());
    Get.lazyPut(() => NftRecordActivityController(), tag: NftRecordTag.all);
    Get.lazyPut(() => NftRecordActivityController(), tag: NftRecordTag.receive);
    Get.lazyPut(() => NftRecordActivityController(), tag: NftRecordTag.send);
    Get.lazyPut(() => NftRecordActivityController(), tag: NftRecordTag.sale);
    Get.lazyPut(() => NftRecordActivityController(), tag: NftRecordTag.mint);
    Get.lazyPut(() => NftRecordActivityController(), tag: NftRecordTag.burn);
  }
}
