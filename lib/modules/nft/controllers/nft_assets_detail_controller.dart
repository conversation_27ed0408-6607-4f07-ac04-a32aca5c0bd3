/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-09 17:36:35
 */
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/nft/models/nft_activity_model/nft_activity_model.dart';
import 'package:coinbag/modules/nft/models/nft_assets_item_model.dart';
import 'package:coinbag/modules/nft/pages/nft_assets_activity_page.dart';
import 'package:coinbag/modules/nft/pages/nft_assets_overview_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:flutter/material.dart';

class NftAssetsDetailController extends BaseRefreshController<BlockChainService>
    with GetTickerProviderStateMixin {
  late NftAssetsItemModel model;
  late WalletModel walletModel;
  late AddressModel addressModel;
  late CoinModel coinModel;

  ScrollController scrollController = ScrollController();
  PageController pagerController = PageController();
  late TabController tabController =
      TabController(length: tabList.length, vsync: this);
  final List<Widget> pagerList = [
    const NftAssetsOverviewPage(),
    const NftAssetsActivityPage(),
  ];

  final List<String> tabList = [
    ID.stringNftOverview.tr,
    ID.stringNftActivity.tr,
  ];

  late double topImageWidth = min(Get.width - Get.setPaddingSize(32), 400);
  late double topImageBgHeight = topImageWidth + Get.setPaddingSize(32);
  Rx<Color> appBarColor = Get.theme.white.withAlpha(0).obs;

  String next = '';

  List<NftActivityModel> activityList = [];

  @override
  void onInit() {
    final arg = Get.arguments;
    model = arg[GetArgumentsKey.model];
    coinModel = arg[GetArgumentsKey.coinModel];
    walletModel = arg[GetArgumentsKey.walletModel];
    addressModel = arg[GetArgumentsKey.addressModel];
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    scrollController.addListener(() {
      double padding = (scrollController.offset - 50) - topImageBgHeight;
      if (padding < 0) {
        appBarColor.value = Get.theme.white.withAlpha(0);
      } else {
        appBarColor.value = Get.theme.white;
      }
    });

    requestPageData();
  }

  @override
  onLoadMore() => requestPageData();

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    httpRequest(
      api.nftRequest(
          cancelToken,
          BlockChainParamsManager.createParams(
              method: BlockChainAPI.nftActivity,
              requestParams: RequestParams()
                  .put(APIConstant.chain, coinModel.chain!)
                  .put(APIConstant.contractAddress, model.contractAddress)
                  .put(APIConstant.tokenId, model.tokenId)
                  .put(APIConstant.extra, {
                APIConstant.limit: 50,
                APIConstant.cursor: next,
                APIConstant.eventType: '',
              }).getRequestBody())),
      (value) async {
        if (value.data != null) {
          Map data = value.data;
          dynamic next = data['next'];
          if (Get.isEmptyString(next)) {
            next = '';
          }
          this.next = next;
          int total = data['total'];
          List content = data['content'];
          List<NftActivityModel> list =
              content.map((e) => NftActivityModel.fromJson(e)).toList();
          activityList.addAll(list);
          hideRefresh(refreshController,
              finishLoadMore: activityList.length >= total);
        } else {
          showError();
        }
      },
    );
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
    tabController.dispose();
  }
}

class NftAssetsDetailBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => NftAssetsDetailController());
  }
}
