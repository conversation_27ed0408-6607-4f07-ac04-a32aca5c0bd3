/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-10 14:38:32
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/nft/controllers/nft_record_controller.dart';
import 'package:coinbag/modules/nft/models/nft_record_activity_model/nft_record_activity_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';

class NftRecordActivityController
    extends BaseRefreshController<BlockChainService> {
  // String? tag;
  // NftRecordActivityController({this.tag});

  late AddressModel addressModel;
  late CoinModel coinModel;

  late NftRecordController controller;

  List<NftRecordActivityModel> dataList = [];

  @override
  void onInit() {
    controller = Get.find<NftRecordController>();
    addressModel = controller.addressModel;
    coinModel = controller.coinModel;
    super.onInit();
  }

  List<NftRecordActivityModel> dataSource(String tag) {
    if (tag == NftRecordTag.all) {
      return controller.dataList;
    }
    List<NftRecordActivityModel> reuslt =
        controller.dataList.where((model) => model.eventType == tag).toList();

    return reuslt;
  }

  @override
  void onReady() {
    super.onReady();
    if (controller.dataList.isEmpty) {
      requestPageData();
    } else {
      updateRefreshState();
      showSuccess();
    }
  }

  @override
  onLoadRefresh() {
    controller.next = '';
    requestPageData(refresh: Refresh.pull);
  }

  void updateRefreshState() {
    List<String> tags = [
      NftRecordTag.all,
      NftRecordTag.receive,
      NftRecordTag.send,
      NftRecordTag.sale,
      NftRecordTag.mint,
      NftRecordTag.burn,
    ];

    for (var tag in tags) {
      NftRecordActivityController ctr =
          Get.find<NftRecordActivityController>(tag: tag);
      ctr.hideRefresh(refreshController,
          finishLoadMore: controller.total == null
              ? false
              : controller.dataList.length >= controller.total!);
    }
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    httpRequest(
      api.nftRequest(
          cancelToken,
          BlockChainParamsManager.createParams(
              method: BlockChainAPI.nftRecordActivity,
              requestParams: RequestParams()
                  .put(APIConstant.chain, addressModel.chain!)
                  .put(APIConstant.address, addressModel.address)
                  .put(APIConstant.extra, {
                APIConstant.limit: 50,
                APIConstant.cursor: controller.next,
                APIConstant.eventType: 'Mint;Transfer;Sale;Burn',
              }).getRequestBody())),
      (value) async {
        if (value.data != null) {
          Map data = value.data;
          dynamic next = data['next'];
          if (Get.isEmptyString(next)) {
            next = '';
          }
          controller.next = next;
          int total = data['total'];
          List content = data['content'];
          List<NftRecordActivityModel> list =
              content.map((e) => NftRecordActivityModel.fromJson(e)).toList();
          if (refresh == Refresh.pull || refresh == Refresh.first) {
            controller.dataList.clear();
          }
          controller.total = total;
          controller.dataList.addAll(list);

          updateRefreshState();

          update([GetKey.nftRecordActivityId]);
        } else {
          showError();
        }
      },
    );
  }
}
