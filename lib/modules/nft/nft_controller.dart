import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_controller.dart';
import 'package:coinbag/modules/nft/controllers/nft_collection_controller.dart';
import 'package:coinbag/modules/nft/pages/nft_assets_page.dart';
import 'package:coinbag/modules/nft/pages/nft_collection_page.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wallet_core/wallet/wallet.dart';

class NftController extends BaseRefreshController<BlockChainService>
    with GetTickerProviderStateMixin {
  String unit = CurrencyController.getCurrencyUnit();
  var isClickable = true.obs; //可点击进入交易记录

  RxInt selectedPage = 0.obs;
  late TabController tabController = TabController(length: 2, vsync: this);
  ScrollController scrollController = ScrollController();
  final PageController pagerController = PageController();
  final List<Widget> pagerList = [
    const NftAssetsPage(),
    const NftCollectionPage(),
  ];

  WalletModel? walletModel;
  CoinModel? coinModel;
  AddressModel? addressModel;
  bool hideAssets = false;
  bool isLoading = false;

  String totalAmount = '0';
  String totalBalance = '0';

  @override
  void onInit() async {
    _putController();
    await initData();
    _initWallet();
    super.onInit();
  }

  void _initWallet() {
    ever(
      AppController.isRefreshNFTWallet,
      (_) async {
        await initData();
        update([GetKey.nftHeaderId]);
        AppController.refreshNft();
      },
    );

    /// 绑定或切换钱包时
    ever(AppController.refreshHomeAssets, (refresh) async {
      await initData();
      update([GetKey.nftHeaderId]);
      AppController.refreshNft();
    });
  }

  void _putController() {
    if (!Get.isRegistered<NftCollectionController>()) {
      Get.lazyPut(() => NftCollectionController());
    }

    if (!Get.isRegistered<NftAssetsController>()) {
      Get.lazyPut(() => NftAssetsController());
    }
  }

  Future<void> initData() async {
    walletModel = (await Get.database.walletDao.getCheckedWallets())!;
    Wallet wallet = Wallet.getWalletByBatch(walletModel!.batchId!);
    if (!wallet.isSupportNft) {
      return;
    }

    /// CoinModel
    coinModel = (await Get.database.coinDao
        .getMainCoinModel(chain: walletModel!.nftChain))!;

    /// 地址
    List<AddressModel> addressList =
        await Get.database.addressDao.getAddressModelList(
      walletId: walletModel!.walletId!,
      chain: walletModel!.nftChain!,
      deviceId: walletModel!.deviceId!,
    );
    List<AddressModel> resultList =
        addressList.where((e) => e.address == walletModel!.nftAddress).toList();
    if (resultList.isNotEmpty) {
      addressModel = resultList.first;
    } else {
      addressModel = addressList.first;
    }
    hideAssets = isHideAssets();
  }

  void hideAssetsAction() {
    hideAssets = !hideAssets;
    update([GetKey.nftHeaderId]);
    StorageManager.saveValue(key: StorageKey.hideNftAssets, value: hideAssets);
  }

  bool isHideAssets() {
    return StorageManager.getValue(
          key: StorageKey.hideNftAssets,
        ) ??
        false;
  }

  NftController get controller => Get.find<NftController>();

  @override
  void loadData() {}

  void scrollToTop() {
    if (scrollController.positions.isEmpty) return;
    // 滚动到GridView的顶部
    if (scrollController.hasClients) {
      scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollController.dispose();
    pagerController.dispose();
    tabController.dispose();
  }
}

class NftBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => NftController());
    Get.lazyPut(() => NftAssetsController());
    Get.lazyPut(() => NftCollectionController());
  }
}
