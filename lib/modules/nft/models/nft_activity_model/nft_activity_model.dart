/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-09 15:25:19
 */
import 'package:json_annotation/json_annotation.dart';

part 'nft_activity_model.g.dart';

@JsonSerializable()
class NftActivityModel {
  String? from;
  int? timestamp;
  String? to;

  NftActivityModel({
    this.from,
    this.timestamp,
    this.to,
  });

  factory NftActivityModel.fromJson(Map<String, dynamic> json) {
    return _$NftActivityModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NftActivityModelToJson(this);
}
