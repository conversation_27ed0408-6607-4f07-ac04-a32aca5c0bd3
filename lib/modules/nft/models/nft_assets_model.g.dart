// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nft_assets_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NftAssetsModel _$NftAssetsModelFromJson(Map<String, dynamic> json) =>
    NftAssetsModel(
      collectionSumUsd:
          NftAssetsModel._stringFromJson(json['collection_sum_usd']),
      holdingCount: NftAssetsModel._stringFromJson(json['holding_count']),
      holdingValueUsdt:
          NftAssetsModel._stringFromJson(json['holding_value_usdt']),
      nftList: (json['nft_list'] as List<dynamic>?)
          ?.map((e) => NftAssetsItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NftAssetsModelToJson(NftAssetsModel instance) =>
    <String, dynamic>{
      'collection_sum_usd': instance.collectionSumUsd,
      'holding_count': instance.holdingCount,
      'holding_value_usdt': instance.holdingValueUsdt,
      'nft_list': NftAssetsModel._itemModelToJson(instance.nftList),
      'total': instance.total,
    };
