import 'package:json_annotation/json_annotation.dart';

import 'nft_assets_item_model.dart';

part 'nft_assets_model.g.dart';

@JsonSerializable()
class NftAssetsModel {
  @JsonKey(name: 'collection_sum_usd', fromJson: NftAssetsModel._stringFromJson)
  String? collectionSumUsd;
  @JsonKey(name: 'holding_count', fromJson: NftAssetsModel._stringFromJson)
  String? holdingCount;
  @<PERSON><PERSON><PERSON>ey(name: 'holding_value_usdt', fromJson: NftAssetsModel._stringFromJson)
  String? holdingValueUsdt;
  @JsonKey(name: 'nft_list', toJson: _itemModelToJson)
  List<NftAssetsItemModel>? nftList;
  int? total;

  NftAssetsModel({
    this.collectionSumUsd,
    this.holdingCount,
    this.holdingValueUsdt,
    this.nftList,
    this.total,
  });

  static List<Map<String, dynamic>>? _itemModelToJson(
          List<NftAssetsItemModel>? nftList) =>
      nftList?.map((e) => e.toJson()).toList();

  static String? _stringFromJson(dynamic value) {
    if (value == null) return null;
    if (value is! String) return value.toString();
    return value;
  }

  factory NftAssetsModel.fromJson(Map<String, dynamic> json) {
    return _$NftAssetsModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NftAssetsModelToJson(this);
}
