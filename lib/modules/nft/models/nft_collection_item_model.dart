/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-08 13:36:30
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

part 'nft_collection_item_model.g.dart';

@JsonSerializable()
class NftCollectionItemModel {
  String? chain;
  @JsonKey(name: 'chain_name')
  String? chainName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'collection_name')
  String? collectionName;
  @Json<PERSON>ey(name: 'collection_sum_usd')
  String? collectionSumUsd;
  @JsonKey(name: 'contract_address')
  String? contractAddress;
  @Json<PERSON>ey(name: 'logo_url')
  String? logoUrl;
  @Json<PERSON>ey(name: 'owns_total')
  String? ownsTotal;

  NftCollectionItemModel({
    this.chain,
    this.chainName,
    this.collectionName,
    this.collectionSumUsd,
    this.contractAddress,
    this.logoUrl,
    this.ownsTotal,
  });

  factory NftCollectionItemModel.fromJson(Map<String, dynamic> json) {
    return _$NftCollectionItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NftCollectionItemModelToJson(this);

  String get amountValue {
    if (Get.isEmptyString(collectionSumUsd)) {
      collectionSumUsd = '0';
    }

    String rate = AppController.currency.value == Currency.usd
        ? AppController.usdRate?.usdt ?? '0'
        : AppController.usdRate?.cny ?? '0';
    return '${CurrencyController.getCurrencyUnit()}${collectionSumUsd!.mul(rate).decimal(scale: 2, roundMode: RoundMode.down)}';
  }
}
