import 'dart:ui';

import 'package:coinbag/modules/nft/controllers/nft_record_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:json_annotation/json_annotation.dart';

part 'nft_record_activity_model.g.dart';

@JsonSerializable()
class NftRecordActivityModel {
  @JsonKey(name: 'aggregate_exchange_name')
  dynamic aggregateExchangeName;
  String? amount;
  @Json<PERSON>ey(name: 'block_hash')
  String? blockHash;
  @Json<PERSON>ey(name: 'block_number')
  int? blockNumber;
  @JsonKey(name: 'contract_address')
  String? contractAddress;
  @Json<PERSON>ey(name: 'contract_name')
  String? contractName;
  @JsonKey(name: 'contract_token_id')
  String? contractTokenId;
  @JsonKey(name: 'erc_type')
  String? ercType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'event_type')
  String? eventType;
  @<PERSON>son<PERSON>ey(name: 'exchange_name')
  dynamic exchangeName;
  String? from;
  @J<PERSON><PERSON><PERSON>(name: 'gas_fee')
  double? gasFee;
  @Json<PERSON><PERSON>(name: 'gas_price')
  String? gasPrice;
  @JsonKey(name: 'gas_used')
  String? gasUsed;
  String? hash;
  @JsonKey(name: 'nftscan_tx_id')
  String? nftscanTxId;
  String? receive;
  String? send;
  int? timestamp;
  String? to;
  @JsonKey(name: 'token_id')
  String? tokenId;
  @JsonKey(name: 'trade_price')
  double? tradePrice;
  @JsonKey(name: 'trade_symbol')
  String? tradeSymbol;
  @JsonKey(name: 'trade_symbol_address')
  dynamic tradeSymbolAddress;
  @JsonKey(name: 'trade_value')
  String? tradeValue;

  NftRecordActivityModel({
    this.aggregateExchangeName,
    this.amount,
    this.blockHash,
    this.blockNumber,
    this.contractAddress,
    this.contractName,
    this.contractTokenId,
    this.ercType,
    this.eventType,
    this.exchangeName,
    this.from,
    this.gasFee,
    this.gasPrice,
    this.gasUsed,
    this.hash,
    this.nftscanTxId,
    this.receive,
    this.send,
    this.timestamp,
    this.to,
    this.tokenId,
    this.tradePrice,
    this.tradeSymbol,
    this.tradeSymbolAddress,
    this.tradeValue,
  });

  factory NftRecordActivityModel.fromJson(Map<String, dynamic> json) {
    return _$NftRecordActivityModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NftRecordActivityModelToJson(this);

  String eventTypeValue(String? address) {
    if (eventType == NftRecordTag.receive) {
      return ID.receive.tr;
    } else if (eventType == NftRecordTag.send) {
      return ID.send.tr;
    } else if (eventType == NftRecordTag.sale) {
      if (address == receive) {
        return ID.stringNftSaleSell.tr;
      }
      return ID.stringNftSaleBuy.tr;
    } else if (eventType == NftRecordTag.mint) {
      return ID.stringNftMint.tr;
    } else if (eventType == NftRecordTag.burn) {
      return ID.stringNftBurn.tr;
    }
    return '';
  }

  String address(String? address) {
    String send = AddressUtils.omitAddress(this.send);
    String receive = AddressUtils.omitAddress(this.receive);
    if (eventType == NftRecordTag.receive) {
      return ID.stringNftSendAddress.trParams({'value': send});
    } else if (eventType == NftRecordTag.send) {
      return ID.stringNftReceiveAddress.trParams({'value': receive});
    } else if (eventType == NftRecordTag.mint ||
        eventType == NftRecordTag.burn) {
      return ID.stringNftContractAddress.trParams({'value': receive});
    } else if (eventType == NftRecordTag.sale) {
      if (address == receive) {
        return ID.stringNftReceiveAddress.trParams({'value': send});
      }
      return ID.stringNftSendAddress.trParams({'value': receive});
    }
    return '';
  }

  String get dateFormat => DateHeleper.formatTimestamp(timestamp);

  String amountValue(String? address) {
    String imageName = this.imageName(address);
    if (imageName == 'icon_tx_receive') {
      return '+${amount}x$contractName #$tokenId';
    }
    return '-${amount}x$contractName #$tokenId';
  }

  Color amountValueColor(String? address) {
    String imageName = this.imageName(address);
    if (imageName == 'icon_tx_send') {
      return Get.theme.colorF44D4D;
    }
    return Get.theme.color02B58A;
  }

  String imageName(String? address) {
    if (eventType == NftRecordTag.send) {
      return 'icon_tx_send';
    } else if (eventType == NftRecordTag.burn) {
      return 'icon_tx_burn';
    } else if (eventType == NftRecordTag.mint) {
      return 'icon_tx_mint';
    } else if (eventType == NftRecordTag.sale) {
      if (address == receive) {
        // 购买
        return 'icon_tx_send';
      }
      return 'icon_tx_receive'; // 出售
    }
    return 'icon_tx_receive';
  }
}
