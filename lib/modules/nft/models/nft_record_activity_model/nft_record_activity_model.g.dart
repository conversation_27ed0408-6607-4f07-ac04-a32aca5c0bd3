// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nft_record_activity_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NftRecordActivityModel _$NftRecordActivityModelFromJson(
        Map<String, dynamic> json) =>
    NftRecordActivityModel(
      aggregateExchangeName: json['aggregate_exchange_name'],
      amount: json['amount'] as String?,
      blockHash: json['block_hash'] as String?,
      blockNumber: (json['block_number'] as num?)?.toInt(),
      contractAddress: json['contract_address'] as String?,
      contractName: json['contract_name'] as String?,
      contractTokenId: json['contract_token_id'] as String?,
      ercType: json['erc_type'] as String?,
      eventType: json['event_type'] as String?,
      exchangeName: json['exchange_name'],
      from: json['from'] as String?,
      gasFee: (json['gas_fee'] as num?)?.toDouble(),
      gasPrice: json['gas_price'] as String?,
      gasUsed: json['gas_used'] as String?,
      hash: json['hash'] as String?,
      nftscanTxId: json['nftscan_tx_id'] as String?,
      receive: json['receive'] as String?,
      send: json['send'] as String?,
      timestamp: (json['timestamp'] as num?)?.toInt(),
      to: json['to'] as String?,
      tokenId: json['token_id'] as String?,
      tradePrice: (json['trade_price'] as num?)?.toDouble(),
      tradeSymbol: json['trade_symbol'] as String?,
      tradeSymbolAddress: json['trade_symbol_address'],
      tradeValue: json['trade_value'] as String?,
    );

Map<String, dynamic> _$NftRecordActivityModelToJson(
        NftRecordActivityModel instance) =>
    <String, dynamic>{
      'aggregate_exchange_name': instance.aggregateExchangeName,
      'amount': instance.amount,
      'block_hash': instance.blockHash,
      'block_number': instance.blockNumber,
      'contract_address': instance.contractAddress,
      'contract_name': instance.contractName,
      'contract_token_id': instance.contractTokenId,
      'erc_type': instance.ercType,
      'event_type': instance.eventType,
      'exchange_name': instance.exchangeName,
      'from': instance.from,
      'gas_fee': instance.gasFee,
      'gas_price': instance.gasPrice,
      'gas_used': instance.gasUsed,
      'hash': instance.hash,
      'nftscan_tx_id': instance.nftscanTxId,
      'receive': instance.receive,
      'send': instance.send,
      'timestamp': instance.timestamp,
      'to': instance.to,
      'token_id': instance.tokenId,
      'trade_price': instance.tradePrice,
      'trade_symbol': instance.tradeSymbol,
      'trade_symbol_address': instance.tradeSymbolAddress,
      'trade_value': instance.tradeValue,
    };
