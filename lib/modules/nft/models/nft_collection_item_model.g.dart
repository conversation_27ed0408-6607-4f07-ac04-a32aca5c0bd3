// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nft_collection_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NftCollectionItemModel _$NftCollectionItemModelFromJson(
        Map<String, dynamic> json) =>
    NftCollectionItemModel(
      chain: json['chain'] as String?,
      chainName: json['chain_name'] as String?,
      collectionName: json['collection_name'] as String?,
      collectionSumUsd: json['collection_sum_usd'] as String?,
      contractAddress: json['contract_address'] as String?,
      logoUrl: json['logo_url'] as String?,
      ownsTotal: json['owns_total'] as String?,
    );

Map<String, dynamic> _$NftCollectionItemModelToJson(
        NftCollectionItemModel instance) =>
    <String, dynamic>{
      'chain': instance.chain,
      'chain_name': instance.chainName,
      'collection_name': instance.collectionName,
      'collection_sum_usd': instance.collectionSumUsd,
      'contract_address': instance.contractAddress,
      'logo_url': instance.logoUrl,
      'owns_total': instance.ownsTotal,
    };
