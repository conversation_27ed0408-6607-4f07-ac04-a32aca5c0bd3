/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-14 13:31:21
 */
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:wallet_core/extension/string_decimal.dart';

part 'nft_assets_item_model.g.dart';

enum NftErcType { erc1155, erc721 }

@JsonSerializable()
class NftAssetsItemModel {
  String? amount;
  String? chain;
  @J<PERSON><PERSON><PERSON>(name: 'chain_name')
  String? chainName;
  @JsonKey(name: 'collection_name')
  String? collectionName;
  @JsonKey(name: 'content_type')
  String? contentType;
  @Json<PERSON>ey(name: 'contract_address')
  String? contractAddress;
  @JsonKey(name: 'erc_type')
  String? ercType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'image_uri')
  String? imageUri;
  @<PERSON>son<PERSON>ey(name: 'latest_trade_price')
  String? latestTradePrice;
  @JsonKey(name: 'latest_trade_price_usd')
  String? latestTradePriceUsd;
  @JsonKey(name: 'latest_trade_symbol')
  String? latestTradeSymbol;
  @JsonKey(name: 'latest_trade_symbol_img')
  String? latestTradeSymbolImg;
  @JsonKey(name: 'logo_url')
  String? logoUrl;
  @JsonKey(name: 'token_id')
  String? tokenId;
  @JsonKey(name: 'token_name')
  String? tokenName;

  NftAssetsItemModel({
    this.amount,
    this.chain,
    this.chainName,
    this.collectionName,
    this.contentType,
    this.contractAddress,
    this.ercType,
    this.imageUri,
    this.latestTradePrice,
    this.latestTradePriceUsd,
    this.latestTradeSymbol,
    this.latestTradeSymbolImg,
    this.logoUrl,
    this.tokenId,
    this.tokenName,
  });

  factory NftAssetsItemModel.fromJson(Map<String, dynamic> json) {
    return _$NftAssetsItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NftAssetsItemModelToJson(this);

  bool get isShowAmount {
    if (Get.isEmptyString(amount)) return false;
    return amount!.moreThan('1');
  }

  String? get amountValue {
    if (Get.isEmptyString(amount)) return null;
    if (amount!.moreThan('10000')) {
      return '9999+';
    }
    return amount;
  }

  String get tokenNameValue {
    if (Get.isEmptyString(tokenName)) {
      String tokenId = this.tokenId ?? '';
      if (!tokenId.startsWith('#')) {
        tokenId = '#$tokenId';
      }
      return tokenId;
    } else {
      return tokenName!;
    }
  }

  String get tradePriceValue {
    if (Get.isEmptyString(latestTradePrice)) return '-';
    return latestTradePrice!;
  }

  String get amountBalance {
    if (Get.isEmptyString(latestTradePriceUsd)) return '';

    String rate = AppController.currency.value == Currency.usd
        ? AppController.usdRate?.usdt ?? '0'
        : AppController.usdRate?.cny ?? '0';
    return '≈${CurrencyController.getCurrencyUnit()}${latestTradePriceUsd!.mul(rate).decimal(scale: 2, roundMode: RoundMode.down)}';
  }

  NftErcType get ercTypeValue {
    if (ercType?.contains('1155') == true) return NftErcType.erc1155;
    return NftErcType.erc721;
  }
}
