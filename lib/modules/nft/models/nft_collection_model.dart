import 'package:json_annotation/json_annotation.dart';

import 'nft_collection_item_model.dart';

part 'nft_collection_model.g.dart';

@JsonSerializable()
class NftCollectionModel {
  @JsonKey(toJson: _itemModelToJson)
  List<NftCollectionItemModel>? collectionList;
  @JsonKey(name: 'collection_sum_usd')
  double? collectionSumUsd;
  @JsonKey(name: 'holding_count')
  String? holdingCount;
  @JsonKey(name: 'holding_value_usdt', fromJson: _holdingValueUsdt)
  String? holdingValueUsdt;
  int? total;

  NftCollectionModel({
    this.collectionList,
    this.collectionSumUsd,
    this.holdingCount,
    this.holdingValueUsdt,
    this.total,
  });

  static List<Map<String, dynamic>>? _itemModelToJson(
          List<NftCollectionItemModel>? nftList) =>
      nftList?.map((e) => e.toJson()).toList();

  static String? _holdingValueUsdt(dynamic value) {
    if (value == null) return null;
    if (value is! String) return value.toString();
    return value;
  }

  factory NftCollectionModel.fromJson(Map<String, dynamic> json) {
    return _$NftCollectionModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$NftCollectionModelToJson(this);
}
