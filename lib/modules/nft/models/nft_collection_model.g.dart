// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nft_collection_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NftCollectionModel _$NftCollectionModelFromJson(Map<String, dynamic> json) =>
    NftCollectionModel(
      collectionList: (json['collectionList'] as List<dynamic>?)
          ?.map(
              (e) => NftCollectionItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      collectionSumUsd: (json['collection_sum_usd'] as num?)?.toDouble(),
      holdingCount: json['holding_count'] as String?,
      holdingValueUsdt:
          NftCollectionModel._holdingValueUsdt(json['holding_value_usdt']),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$NftCollectionModelToJson(NftCollectionModel instance) =>
    <String, dynamic>{
      'collectionList':
          NftCollectionModel._itemModelToJson(instance.collectionList),
      'collection_sum_usd': instance.collectionSumUsd,
      'holding_count': instance.holdingCount,
      'holding_value_usdt': instance.holdingValueUsdt,
      'total': instance.total,
    };
