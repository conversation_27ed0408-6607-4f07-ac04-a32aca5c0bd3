// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nft_assets_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NftAssetsItemModel _$NftAssetsItemModelFromJson(Map<String, dynamic> json) =>
    NftAssetsItemModel(
      amount: json['amount'] as String?,
      chain: json['chain'] as String?,
      chainName: json['chain_name'] as String?,
      collectionName: json['collection_name'] as String?,
      contentType: json['content_type'] as String?,
      contractAddress: json['contract_address'] as String?,
      ercType: json['erc_type'] as String?,
      imageUri: json['image_uri'] as String?,
      latestTradePrice: json['latest_trade_price'] as String?,
      latestTradePriceUsd: json['latest_trade_price_usd'] as String?,
      latestTradeSymbol: json['latest_trade_symbol'] as String?,
      latestTradeSymbolImg: json['latest_trade_symbol_img'] as String?,
      logoUrl: json['logo_url'] as String?,
      tokenId: json['token_id'] as String?,
      tokenName: json['token_name'] as String?,
    );

Map<String, dynamic> _$NftAssetsItemModelToJson(NftAssetsItemModel instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'chain': instance.chain,
      'chain_name': instance.chainName,
      'collection_name': instance.collectionName,
      'content_type': instance.contentType,
      'contract_address': instance.contractAddress,
      'erc_type': instance.ercType,
      'image_uri': instance.imageUri,
      'latest_trade_price': instance.latestTradePrice,
      'latest_trade_price_usd': instance.latestTradePriceUsd,
      'latest_trade_symbol': instance.latestTradeSymbol,
      'latest_trade_symbol_img': instance.latestTradeSymbolImg,
      'logo_url': instance.logoUrl,
      'token_id': instance.tokenId,
      'token_name': instance.tokenName,
    };
