import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/cupertino.dart';

class NftHeaderWidget extends BaseStatelessWidget<NftController> {
  const NftHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NftController>(
      id: GetKey.nftHeaderId,
      builder: (_) {
        if (controller.addressModel == null || controller.coinModel == null) {
          return const SizedBox.shrink();
        }
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
          child: Column(
            children: [
              _addressWidget(),
              _amountWidget(),
              _actionWidget(),
              const DividerWidget(),
            ],
          ),
        );
      },
    );
  }

  Padding _actionWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(24)),
      child: SizedBox(
        width: Get.width * 0.8,
        child: Row(
          children: [
            _actionItem(
              'icon_receive',
              ID.receive.tr,
              onTap: () => Get.toNamed(AppRoutes.receivePage, arguments: {
                GetArgumentsKey.coinModel: controller.coinModel,
                GetArgumentsKey.address: controller.addressModel!.address,
                GetArgumentsKey.isNft: true,
              }),
            ),
            _actionItem(
              'icon_record',
              ID.activity.tr,
              onTap: () async {
                final nftController = Get.find<NftController>();

                // 防止快速点击pull_to_refresh 出现bug
                if (!nftController.isClickable.value) {
                  return;
                }

                // 禁用点击事件
                nftController.isClickable.value = false;

                // 导航到详情页面
                await toRecordPage();

                // 等待动画结束（假设动画时间为500ms）
                await Future.delayed(const Duration(milliseconds: 500));

                // 重新启用点击事件
                nftController.isClickable.value = true;
              },
            )
          ],
        ),
      ),
    );
  }

  Expanded _actionItem(String? assetUrl, String? title,
          {GestureTapCallback? onTap}) =>
      Expanded(
        flex: 1,
        child: GestureDetector(
          onTap: onTap,
          child: Column(
            children: [
              ImageWidget(
                assetUrl: assetUrl,
                width: Get.setImageSize(48),
                height: Get.setImageSize(48),
                fit: BoxFit.contain,
              ),
              SizedBox(
                height: Get.setHeight(8),
              ),
              Text(
                title ?? '',
                style: TextStyle(
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.medium,
                    color: Get.theme.textPrimary,
                    overflow: TextOverflow.ellipsis),
              )
            ],
          ),
        ),
      );

  Padding _amountWidget() {
    return Padding(
      padding: EdgeInsets.only(top: Get.setPaddingSize(8)),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Visibility(
                    visible: !controller.hideAssets,
                    child: Padding(
                      padding: EdgeInsets.only(right: Get.setPaddingSize(4)),
                      child: Text(
                        controller.unit,
                        style: TextStyle(
                          fontSize: Get.setFontSize(20),
                          fontFamily: Get.setNumberFontFamily(),
                          fontWeight: FontWeightX.medium,
                          color: Get.theme.textPrimary,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text.rich(
                        maxLines: 1,
                        TextSpan(children: <InlineSpan>[
                          TextSpan(
                            text: controller.hideAssets
                                ? CommonConstant.hideAssetsStr
                                : controller.totalBalance,
                            style: TextStyle(
                                fontSize: Get.setFontSize(40),
                                fontFamily: Get.setNumberFontFamily(),
                                fontWeight: FontWeightX.semibold,
                                color: Get.theme.textPrimary,
                                overflow: TextOverflow.ellipsis),
                          ),
                          WidgetSpan(
                            child: Visibility(
                              visible: controller.isLoading,
                              child: Padding(
                                padding: EdgeInsets.only(
                                    bottom: Get.setPaddingSize(10),
                                    left: Get.setPaddingSize(4)),
                                child: CupertinoActivityIndicator(
                                  radius: Get.setRadius(6),
                                ),
                              ),
                            ),
                          ),
                        ])),
                  )
                ]),
          ),
          Expanded(
            flex: 1,
            child: Text(
              controller.hideAssets
                  ? CommonConstant.hideAssetsStr
                  : controller.totalAmount,
              textAlign: TextAlign.end,
              style: TextStyle(
                  fontSize: Get.setFontSize(40),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.semibold,
                  color: Get.theme.textPrimary,
                  overflow: TextOverflow.ellipsis),
            ),
          ),
        ],
      ),
    );
  }

  Row _addressWidget() {
    return Row(
      children: [
        Text(
          AddressUtils.omitAddress(controller.addressModel!.address),
          style: TextStyle(
            color: Get.theme.textSecondary,
            fontSize: Get.setFontSize(14),
            fontWeight: FontWeightX.medium,
            fontFamily: Get.setNumberFontFamily(),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        GestureDetector(
          onTap: () => Get.copy(controller.addressModel!.address),
          child: ImageWidget(
            assetUrl: 'copy',
            width: Get.setImageSize(16),
            height: Get.setImageSize(16),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Get.setPaddingSize(8),
          ),
          child: Container(
            width: Get.setWidth(1),
            height: Get.setHeight(12),
            color: Get.theme.colorECECEC,
          ),
        ),
        GestureDetector(
          onTap: () => controller.hideAssetsAction(),
          child: ImageWidget(
            assetUrl: controller.hideAssets ? 'icon_eye_close' : 'icon_eye_on',
            width: Get.setImageSize(20),
            height: Get.setImageSize(20),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Expanded(
          child: Text(
            ID.stringNftAmount.tr,
            textAlign: TextAlign.end,
            style: styleSecond_14,
          ),
        ),
      ],
    );
  }

  Future toRecordPage() async {
    await Get.toNamed(AppRoutes.nftRecordPage, arguments: {
      GetArgumentsKey.addressModel: controller.addressModel,
      GetArgumentsKey.coinModel: controller.coinModel,
    });
  }
}
