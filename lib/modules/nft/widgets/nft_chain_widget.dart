/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-30 16:18:36
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NftChainWidget extends BaseStatelessWidget<NftController> {
  const NftChainWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NftController>(
        id: GetKey.nftHeaderId,
        builder: (_) {
          if (controller.addressModel == null || controller.coinModel == null) {
            return const SizedBox.shrink();
          }

          return GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () => ChainManagerPage(
              isFullPage: false,
              wallet: controller.walletModel,
              chainManagerMode: ChainManagerMode.nft,
            ).showChainPage(),
            child: Container(
              padding: EdgeInsets.symmetric(
                  horizontal: Get.setPaddingSize(12),
                  vertical: Get.setPaddingSize(8)),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(Get.setRadius(18)),
                border: Border.all(
                  color: Get.theme.colorD3D3D3,
                  width: 0.5,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ImageWidget(
                    assetUrl: controller.coinModel!.chainIcon,
                    width: Get.setImageSize(16),
                    height: Get.setImageSize(16),
                  ),
                  SizedBox(width: Get.setPaddingSize(4)),
                  Text(
                    controller.addressModel!.addressLabel ?? '',
                    style: TextStyle(
                        fontSize: Get.setFontSize(14),
                        fontFamily: Get.setNumberFontFamily(),
                        fontWeight: FontWeightX.semibold,
                        color: Get.theme.textPrimary),
                  ),
                  SizedBox(width: Get.setWidth(4)),
                  ImageWidget(
                    assetUrl: 'arrow',
                    width: Get.setImageSize(10),
                    height: Get.setImageSize(10),
                  ),
                ],
              ),
            ),
          );
        });
  }
}
