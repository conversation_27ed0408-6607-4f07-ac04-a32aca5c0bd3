import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_detail_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NftAssetsDetailHeaderWidget
    extends BaseStatelessWidget<NftAssetsDetailController> {
  const NftAssetsDetailHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _topWidget(),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Get.setPaddingSize(16),
              vertical: Get.setPaddingSize(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _tokenNameWidget(),
                Sized<PERSON>ox(height: Get.setPaddingSize(4)),
                _amountWidget(),
                Sized<PERSON>ox(height: Get.setPaddingSize(8)),
                _nameWidget(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Text _tokenNameWidget() {
    return Text(
      controller.model.tokenNameValue,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Get.theme.textPrimary,
        fontFamily: Get.setNumberFontFamily(),
        fontSize: Get.setFontSize(24),
        fontWeight: FontWeightX.semibold,
      ),
    );
  }

  Row _amountWidget() {
    return Row(
      children: [
        ImageWidget(
          imageUrl: controller.model.latestTradeSymbolImg ?? '',
          width: Get.setImageSize(14),
          height: Get.setImageSize(14),
          loadingWidget: ImageWidget(
            assetUrl: 'icon_chain_default',
            width: Get.setImageSize(14),
            height: Get.setImageSize(14),
          ),
          errorWidget: ImageWidget(
            assetUrl: 'icon_chain_default',
            width: Get.setImageSize(14),
            height: Get.setImageSize(14),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Flexible(
          child: Text(
            '${controller.model.tradePriceValue} ${Get.isEmptyString(controller.model.latestTradePrice) ? '' : controller.coinModel.symbol}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontFamily: Get.setNumberFontFamily(),
              fontSize: Get.setFontSize(18),
              fontWeight: FontWeightX.semibold,
            ),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Expanded(
          child: Text(
            controller.model.amountBalance,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontFamily: Get.setNumberFontFamily(),
              fontSize: Get.setFontSize(14),
            ),
          ),
        ),
      ],
    );
  }

  Container _nameWidget() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Get.setPaddingSize(12),
        vertical: Get.setPaddingSize(10),
      ),
      decoration: BoxDecoration(
        color: Get.theme.colorF9F9F9,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        children: [
          _imageWidget(
            imageUrl: controller.model.logoUrl ?? '',
            loading: 'nft_collection_item',
            error: 'nft_collection_item',
            width: Get.setImageSize(16),
            height: Get.setImageSize(16),
            radius: Get.setRadius(8),
          ),
          SizedBox(width: Get.setPaddingSize(6)),
          Text(
            controller.model.collectionName ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontFamily: Get.setNumberFontFamily(),
              fontSize: Get.setFontSize(14),
            ),
          ),
        ],
      ),
    );
  }

  Center _topWidget() => Center(
        child: Padding(
          padding: EdgeInsets.only(
            left: Get.setPaddingSize(16),
            right: Get.setPaddingSize(16),
            bottom: Get.setPaddingSize(16),
          ),
          child: _imageWidget(
            imageUrl: controller.model.imageUri ?? '',
            loading: 'icon_nft_empty',
            error: 'icon_nft_failed',
            width: controller.topImageWidth,
            height: controller.topImageWidth,
            radius: Get.setRadius(12),
          ),
        ),
      );

  ClipRRect _imageWidget({
    required String imageUrl,
    required String loading,
    required String error,
    required double width,
    required double height,
    required double radius,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius),
      child: ImageWidget(
        width: width,
        height: height,
        imageUrl: imageUrl,
        loadingWidget: ImageWidget(
          width: width,
          height: width,
          assetUrl: loading,
        ),
        errorWidget: ImageWidget(
          width: width,
          height: width,
          assetUrl: error,
        ),
      ),
    );
  }
}
