/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-15 13:21:34
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_controller.dart';
import 'package:coinbag/modules/nft/models/nft_assets_item_model.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NftAssetsWidget extends BaseStatelessWidget<NftAssetsController> {
  final double width;
  final NftAssetsItemModel model;
  const NftAssetsWidget({
    super.key,
    required this.width,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final nftController = Get.find<NftController>();

        // 防止快速点击pull_to_refresh 出现bug
        if (!nftController.isClickable.value) {
          return;
        }

        // 禁用点击事件
        nftController.isClickable.value = false;

        // 导航到详情页面
        await toDetails();

        // 等待动画结束（假设动画时间为500ms）
        await Future.delayed(const Duration(milliseconds: 500));

        // 重新启用点击事件
        nftController.isClickable.value = true;
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _imageWidget(),
          _tokenNameWidget(),
          SizedBox(height: Get.setPaddingSize(8)),
          _amountWidget(),
          SizedBox(height: Get.setPaddingSize(8)),
          _nameWidget(),
        ],
      ),
    );
  }

  Text _nameWidget() {
    return Text(
      model.collectionName ?? '',
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Get.theme.textSecondary,
        fontFamily: Get.setNumberFontFamily(),
        fontSize: Get.setFontSize(12),
      ),
    );
  }

  Row _amountWidget() {
    return Row(
      children: [
        ImageWidget(
          imageUrl: model.latestTradeSymbolImg ?? '',
          width: Get.setImageSize(14),
          height: Get.setImageSize(14),
          loadingWidget: ImageWidget(
            assetUrl: 'icon_chain_default',
            width: Get.setImageSize(14),
            height: Get.setImageSize(14),
          ),
          errorWidget: ImageWidget(
            assetUrl: 'icon_chain_default',
            width: Get.setImageSize(14),
            height: Get.setImageSize(14),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Flexible(
          child: Text(
            model.tradePriceValue,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontFamily: Get.setNumberFontFamily(),
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.semibold,
            ),
          ),
        ),
        SizedBox(width: Get.setPaddingSize(4)),
        Expanded(
          child: Text(
            model.amountBalance,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontFamily: Get.setNumberFontFamily(),
              fontSize: Get.setFontSize(14),
            ),
          ),
        ),
      ],
    );
  }

  Text _tokenNameWidget() {
    return Text(
      model.tokenNameValue,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Get.theme.textPrimary,
        fontFamily: Get.setNumberFontFamily(),
        fontSize: Get.setFontSize(16),
        fontWeight: FontWeightX.semibold,
      ),
    );
  }

  Stack _imageWidget() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(Get.setRadius(12)),
          child: ImageWidget(
            width: width,
            height: width,
            imageUrl: model.imageUri ?? '',
            loadingWidget: Container(
              color: Get.theme.colorFF6A16.withAlpha(1),
              child: ImageWidget(
                width: width,
                height: width,
                assetUrl: 'icon_nft_empty',
              ),
            ),
            errorWidget: ImageWidget(
              width: width,
              height: width,
              assetUrl: 'icon_nft_failed',
            ),
          ),
        ),
        Visibility(
          visible: model.isShowAmount,
          child: Positioned(
              left: Get.setPaddingSize(12),
              bottom: Get.setPaddingSize(16),
              child: Container(
                height: Get.setHeight(24),
                padding:
                    EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
                decoration: BoxDecoration(
                  color: Get.theme.black.withAlpha(150),
                  borderRadius: BorderRadius.circular(Get.setRadius(12)),
                ),
                child: Center(
                  child: Row(
                    children: [
                      ImageWidget(
                        assetUrl: 'nft_count',
                        width: Get.setImageSize(12),
                        height: Get.setImageSize(12),
                      ),
                      SizedBox(width: Get.setPaddingSize(4)),
                      Text(
                        'x${model.amountValue}',
                        style: TextStyle(
                          color: Get.theme.white,
                          fontSize: Get.setFontSize(12),
                          fontFamily: Get.setNumberFontFamily(),
                        ),
                      )
                    ],
                  ),
                ),
              )),
        )
      ],
    );
  }

  Future toDetails() async {
    await Get.toNamed(AppRoutes.assetsDetailPage, arguments: {
      GetArgumentsKey.walletModel: controller.walletModel,
      GetArgumentsKey.addressModel: controller.addressModel,
      GetArgumentsKey.coinModel: controller.coinModel,
      GetArgumentsKey.model: model,
    });
  }
}
