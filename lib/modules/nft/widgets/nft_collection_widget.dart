/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-08 13:36:43
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/nft/controllers/nft_collection_controller.dart';
import 'package:coinbag/modules/nft/models/nft_collection_item_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class NftCollectionWidget extends BaseStatelessWidget<NftCollectionController> {
  final double width;
  final NftCollectionItemModel model;
  const NftCollectionWidget({
    super.key,
    required this.width,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Get.setPaddingSize(16)),
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Get.theme.colorECECEC, width: 1),
      ),
      child: Column(
        children: [
          _imageWIdget(),
          Sized<PERSON>ox(height: Get.setPaddingSize(12)),
          _collectionNameWidget(),
          SizedBox(height: Get.setPaddingSize(4)),
          _holdWidget(),
          SizedBox(height: Get.setPaddingSize(12)),
          _amountWidget(),
        ],
      ),
    );
  }

  Text _collectionNameWidget() {
    return Text(
      model.collectionName ?? '',
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: NftCollectionWidget.collectionNameStyle,
    );
  }

  Text _holdWidget() {
    return Text(
      ID.stringHoldValue.trParams({'value': model.ownsTotal ?? ''}),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: NftCollectionWidget.styleSize12,
    );
  }

  Container _amountWidget() {
    return Container(
      width: Get.width,
      height: Get.setHeight(50),
      decoration: BoxDecoration(
        color: Get.theme.colorF9F9F9,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            ID.stringValuation.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: NftCollectionWidget.styleSize12,
          ),
          SizedBox(height: Get.setPaddingSize(2)),
          Text(
            model.amountValue,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.semibold,
              fontFamily: Get.setNumberFontFamily(),
            ),
          ),
        ],
      ),
    );
  }

  ClipRRect _imageWIdget() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(Get.setRadius(30)),
      child: ImageWidget(
        width: Get.setImageSize(60),
        height: Get.setImageSize(60),
        imageUrl: model.logoUrl ?? '',
        loadingWidget: ImageWidget(
          width: Get.setImageSize(60),
          height: Get.setImageSize(60),
          assetUrl: 'nft_collection_item',
        ),
        errorWidget: ImageWidget(
          width: Get.setImageSize(60),
          height: Get.setImageSize(60),
          assetUrl: 'nft_collection_item',
        ),
      ),
    );
  }

  static TextStyle get collectionNameStyle {
    return TextStyle(
      color: Get.theme.textPrimary,
      fontSize: Get.setFontSize(16),
      fontWeight: FontWeightX.semibold,
      fontFamily: Get.setNumberFontFamily(),
    );
  }

  static TextStyle get styleSize12 {
    return TextStyle(
      color: Get.theme.textSecondary,
      fontSize: Get.setFontSize(12),
      fontFamily: Get.setNumberFontFamily(),
    );
  }
}
