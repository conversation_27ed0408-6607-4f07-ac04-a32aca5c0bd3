/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-10-12 17:39:28
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/modules/nft/widgets/nft_body_widget.dart';
import 'package:coinbag/modules/nft/widgets/nft_chain_widget.dart';
import 'package:coinbag/modules/nft/widgets/nft_header_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:flutter/material.dart';

class NftPage extends BaseStatelessWidget<NftController> {
  const NftPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _appBarWidget(),
      body: Padding(
        padding: EdgeInsets.only(
          top: Get.setPaddingSize(16),
        ),
        child: const Column(
          children: [
            NftHeaderWidget(),
            Expanded(
                child: SizedB<PERSON>(
              child: NftBodyWidget(),
            ))
          ],
        ),
      ),
    );
  }

  AppBar _appBarWidget() {
    return baseAppBar(
      hideLeading: true,
      titleSpacing: -40,
      title: 'NFT',
      centerTitle: false,
      actionWidget: [
        Padding(
          padding: EdgeInsets.only(right: Get.setPaddingSize(16)),
          child: const NftChainWidget(),
        )
      ],
    );
  }
}
