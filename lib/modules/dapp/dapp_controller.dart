import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/controllers/base_refresh_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/modules/dapp/models/rpc_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:flutter/widgets.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class DappController extends BaseRefreshController<ApiService> {
  var dataList = <DappModels>[].obs;
  var recentDataList = <DappModels>[].obs;
  var favoritesDataList = <DappModels>[].obs;
  final ScrollController scrollController = ScrollController();
  var allRecentDataList = <DappModels>[];
  var allFavoritesDataList = <DappModels>[];

  late WalletModel? walletModel;
  Wallet? wallet;
  bool hasCache = false;
  bool isWalletSupportDapp = false;

  /// dapp 钱包类型请求头
  var headerParameter = "";
  @override
  void onInit() {
    super.onInit();
    initEverAction();
  }

  void initEverAction() {
    /// 切换语言刷新数据
    ever(AppController.languageModel, (_) => loadData());

    /// 绑定或切换钱包时
    ever(AppController.refreshHomeAssets, (refresh) async {
      if (!refresh) return;
      AppController.refreshHomeAssets.value = false;
      hasCache = false;
      loadData();
    });
  }

  @override
  void onReady() {
    super.onReady();
    loadCache();
    loadData();
    watchData();
  }

  @override
  void loadData() async {
    walletModel = await Get.database.walletDao.getCheckedWallets();
    if (walletModel != null) {
      wallet = Wallet.getWalletByBatch(walletModel!.batchId!);
      isWalletSupportDapp = wallet!.isSupportDapp;
      String dappParameterBase64 = wallet!.dappParameter;
      headerParameter = FileUtils.deCodeBase64(dappParameterBase64);
    } else {
      headerParameter = '';
    }
    if (!AppConfig.instance.enableDapp) {
      headerParameter = '';
      isWalletSupportDapp = false;
    }
    requestPageData();
  }

  void onRefresh() {
    showLoading();
    loadData();
  }

  void watchData() {
    Get.database.dappDao.watchAllData().listen((data) {
      // 从数据库获取所有 DappModel
      List<DappModel> watchAllData = data;
      recentDataList.clear();
      favoritesDataList.clear();
      allRecentDataList.clear();
      allFavoritesDataList.clear();
      // 遍历所有模型并根据类型分类
      for (var dappModel in watchAllData) {
        switch (dappModel.dappMode) {
          case DappMode.serrchMode:
            // 搜索类型可以在此处理
            break;
          case DappMode.rentMode:
            if (recentDataList.length < 4) {
              recentDataList.add(DappModels.fromDB(dappModel));
            } // 最近浏览
            allRecentDataList.add(DappModels.fromDB(dappModel));

            break;
          case DappMode.favoritesMode:
            if (favoritesDataList.length < 4) {
              favoritesDataList.add(DappModels.fromDB(dappModel));
            } // 收藏
            allFavoritesDataList.add(DappModels.fromDB(dappModel));

            break;
          default:
            // 处理未知类型
            break;
        }
      }
    });
  }

  Future<void> loadCache() async {
    // 从数据库获取所有 DappModels
    List<DappModels> allDappModelListCache =
        await Get.database.dappDao.getAllDappModels();
    // 清空现有的列表
    dataList.clear();
    recentDataList.clear();
    favoritesDataList.clear();
    allRecentDataList.clear();
    allFavoritesDataList.clear();
    // 遍历所有模型并根据类型分类
    for (var dappModel in allDappModelListCache) {
      switch (dappModel.dappMode) {
        case 0:
          dataList.add(dappModel); // 普通类型

          break;
        case 1:
          // 搜索类型可以在此处理
          break;
        case 2:
          if (recentDataList.length < 4) {
            recentDataList.add(dappModel);
          } // 最近浏览
          allRecentDataList.add(dappModel);

          break;
        case 3:
          if (favoritesDataList.length < 4) {
            favoritesDataList.add(dappModel); // 收藏
          }
          allFavoritesDataList.add(dappModel);

          break;
        default:
          // 处理未知类型
          break;
      }
    }

    hasCache = dataList.isNotEmpty;
    if (hasCache) {
      showSuccess();
    }
  }

  @override
  void requestPageData({Refresh refresh = Refresh.first}) {
    cancelRequest();
    List<Future<dynamic>> futures = [
      api.getDappList(
        cancelToken: cancelToken,
        version: 39,
        pageNum: "1",
        pageSize: "20",
        filter: '',
        deviceType: headerParameter,
      ),
    ];

    if (isWalletSupportDapp) {
      futures.add(api.getDappRpcList());
    }

    multiHttpRequest(
        futures,
        (value) async {
          if (value != null) {
            showSuccess();
            _handleDappData(value[0]);
            if (isWalletSupportDapp) {
              _handleRpcData(value[1]);
            }
          } else {
            if (!hasCache) {
              showError(); // 只有在没有缓存的情况下才显示错误
            } else {
              showSuccess();
            }
          }
          hideRefresh(refreshController);
        },
        handleError: false,
        error: (e) {
          if (!hasCache) {
            showError(); // 只有在没有缓存的情况下才显示错误
          } else {
            showSuccess();
          }
          hideRefresh(refreshController);
        });
  }

  void _handleDappData(dynamic dappData) {
    if (dappData != null) {
      final Map<String, dynamic> decoded =
          Map<String, dynamic>.from(dappData.data);

      dataList.value = decoded['data']
          .map<DappModels>((item) => DappModels.fromJson(item))
          .toList();

      /// batch_9002 批次之前去除 baby质押
      if (walletModel?.batchId != null &&
          walletModel!.batchId! < UltraPlusWallet.batch_9002) {
        dataList.value = dataList
            .where(
                (e) => !(e.dAppUrl == babylonchain || e.dAppUrl == babyAirDrop))
            .toList();
      }
      Get.database.dappDao.insertBatch(dataList);
    }
    if (dataList.isEmpty) {
      showEmpty();
    }
  }

  void _handleRpcData(dynamic rpcData) {
    if (rpcData != null && rpcData.data != null) {
      if (rpcData.data is RpcModel) {
        RpcModel rpcModel = rpcData.data;
        StorageManager.saveObject(key: StorageKey.rpcModel, obj: rpcModel);
      }
    }
  }

  bool showViewAll(int mode) {
    if (!isWalletSupportDapp) {
      return false;
    }

    return mode == DappMode.rentMode
        ? recentDataList.length > 3
        : favoritesDataList.length > 3;
  }

  bool showLabel(int mode) {
    if (!isWalletSupportDapp) {
      return false;
    }
    if (mode == DappMode.defaultModel) {
      return true;
    }
    return mode == DappMode.rentMode
        ? recentDataList.isNotEmpty
        : favoritesDataList.isNotEmpty;
  }

  bool showRecord(int mode) {
    if (!isWalletSupportDapp) {
      return false;
    }
    if (mode == DappMode.defaultModel) {
      return false;
    }
    return mode == DappMode.rentMode
        ? recentDataList.isNotEmpty
        : favoritesDataList.isNotEmpty;
  }

  void toDappManagerPage(int mode) {
    Get.toNamed(AppRoutes.dappManagerPage, arguments: {
      GetArgumentsKey.walletModel: walletModel,
      GetArgumentsKey.wallet: wallet,
      GetArgumentsKey.mode: mode,
      GetArgumentsKey.dataList:
          mode == DappMode.rentMode ? allRecentDataList : allFavoritesDataList,
    });
  }

  void scrollToTop() {
    if (scrollController.positions.isEmpty) return;
    scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}

class DappBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DappController());
  }
}
