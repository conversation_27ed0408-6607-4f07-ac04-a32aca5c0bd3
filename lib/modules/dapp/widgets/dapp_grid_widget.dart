/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-09-23 12:58:36
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/dapp/dapp_controller.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class DappGridWidget extends BaseStatelessWidget<DappController> {
  final int mode;
  final List<DappModels> dataList;
  const DappGridWidget({super.key, required this.mode, required this.dataList});

  @override
  Widget build(BuildContext context) {
    return !controller.showRecord(mode)
        ? const SliverToBoxAdapter(child: SizedBox.shrink())
        : SliverGrid(
            delegate: SliverChildBuilderDelegate(
              (_, index) => _itemBuilder(dataList[index]),
              childCount: dataList.length,
            ),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 0.0,
                mainAxisSpacing: 0.0,
                childAspectRatio: ((Get.width - 32) / 2) / 86),
          );
  }

  HighLightInkWell _itemBuilder(DappModels model) {
    return HighLightInkWell(
      onTap: () => Get.openDapp(
          dappModels: model,
          walletModel: controller.walletModel,
          wallet: controller.wallet),
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(14),
            horizontal: Get.setPaddingSize(16)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ImageWidget(
              imageUrl: model.dAppLogo ?? "",
              width: Get.setImageSize(48),
              height: Get.setImageSize(48),
              radius: Get.setRadius(12),
            ),
            SizedBox(
              width: Get.setWidth(12),
            ),
            _textDapp(model),
          ],
        ),
      ),
    );
  }

  Expanded _textDapp(DappModels model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            model.dAppName ?? '',
            maxLines: 1,
            style: style_Dapp_Item,
          ),
          SizedBox(
            height: Get.setHeight(2),
          ),
          Text(
            model.abs ?? '',
            maxLines: 1,
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeightX.regular,
                fontFamily: Get.setFontFamily(),
                color: Get.theme.textSecondary),
          ),
        ],
      ),
    );
  }
}
