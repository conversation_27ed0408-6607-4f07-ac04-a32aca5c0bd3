/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-09-19 17:32:11
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SearchEmptyWidget extends BaseStatelessWidget {
  const SearchEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        left: Get.setPaddingSize(16),
        right: Get.setPaddingSize(16),
        top: Get.setPaddingSize(50),
        bottom: Get.getSafetyBottomPadding(),
      ),
      child: SizedBox(
        width: Get.width,
        child: Column(
          children: [
            SizedBox(
              height: Get.setPaddingSize(30),
            ),
            ImageWidget(
              assetUrl: 'icon_Component',
              width: Get.setImageSize(88),
              height: Get.setImageSize(88),
            ),
            Sized<PERSON><PERSON>(
              height: Get.setPaddingSize(12),
            ),
            Text(
              ID.stringNoSearchResultsFound.tr,
              style: styleSecond_14,
            ),
          ],
        ),
      ),
    );
  }
}
