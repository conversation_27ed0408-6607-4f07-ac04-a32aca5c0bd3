// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rpc_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RpcModel _$RpcModelFromJson(Map<String, dynamic> json) => RpcModel(
      arb: json['arb'] as String?,
      btc: json['btc'] as String?,
      opt: json['opt'] as String?,
      bsc: json['bsc'] as String?,
      zks: json['zks'] as String?,
      polygon: json['polygon'] as String?,
      btcsignet: json['btcsignet'] as String?,
      eth: json['eth'] as String?,
      ethH<PERSON>sky: json['ethHolesky'] as String?,
      linea: json['linea'] as String?,
      base: json['base'] as String?,
      blast: json['blast'] as String?,
    );

Map<String, dynamic> _$RpcModelToJson(RpcModel instance) => <String, dynamic>{
      'arb': instance.arb,
      'btc': instance.btc,
      'opt': instance.opt,
      'bsc': instance.bsc,
      'zks': instance.zks,
      'polygon': instance.polygon,
      'btcsignet': instance.btcsignet,
      'eth': instance.eth,
      'ethHolesky': instance.ethHolesky,
      'linea': instance.linea,
      'base': instance.base,
      'blast': instance.blast,
    };
