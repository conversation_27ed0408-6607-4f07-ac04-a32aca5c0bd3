/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-02-18 14:10:31
 */
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/locale/locale_controller.dart';
import 'package:coinbag/res/resource.dart';

class DappModels {
  String? abs;
  String? chain;
  String? coinSymbol;
  String? dAppId;
  String? dAppLogo;
  String? dAppName;
  String? dAppUrl;
  dynamic js;
  String? label;
  dynamic network;
  String? slip44;
  int? dappMode;

  DappModels({
    this.abs,
    this.chain,
    this.coinSymbol,
    this.dAppId,
    this.dAppLogo,
    this.dAppName,
    this.dAppUrl,
    this.js,
    this.label,
    this.network,
    this.slip44,
    this.dappMode,
  });

  factory DappModels.fromJson(Map<String, dynamic> json) => DappModels(
        abs: json['abs'] as String?,
        chain: json['chain'] as String?,
        coinSymbol: json['coinSymbol'] as String?,
        dAppId: json['dAppId'] != null ? json['dAppId'].toString() : "",
        dAppLogo: json['dAppLogo'] as String?,
        dAppName: json['dAppName'] as String?,
        dAppUrl: json['dAppUrl'] as String?,
        js: json['js'] as String?,
        label: json['label'] as String?,
        network: json['network'] as dynamic,
        slip44: json['slip44'] as String?,
        dappMode: 0,
      );

  Map<String, dynamic> toJson() => {
        'abs': abs,
        'chain': chain,
        'coinSymbol': coinSymbol,
        'dAppId': dAppId,
        'dAppLogo': dAppLogo,
        'dAppName': dAppName,
        'dAppUrl': dAppUrl,
        'js': js,
        'label': label,
        'network': network,
        'slip44': slip44,
        'dappMode': dappMode,
      };

  factory DappModels.fromDB(DappModel model) {
    return DappModels(
      dAppId: model.dappId,
      dAppName: model.dappName,
      dAppLogo: model.dappLogoUrl,
      dAppUrl: model.dappUrl,
      chain: model.chain,
      coinSymbol: model.symbol,
      js: model.btcDappJs,
      label: model.dappLabel,
      abs: model.dappInfo,
      network: model.netWork,
      slip44: model.slip44,
      dappMode: model.dappMode,
    );
  }

  String getDappUrl() {
    if (dAppUrl == null || dAppUrl!.isEmpty) return "";

    Uri uri = Uri.parse(dAppUrl!);

    // 添加查询参数
    Map<String, String> queryParameters = {};

    // 清除特定查询参数
    queryParameters.remove(CommonConstant.locale);
    queryParameters.remove(CommonConstant.utmSource);
    queryParameters.remove(CommonConstant.language);

    // 根据条件添加查询参数
    if ((dAppId == "10000003" || dAppId == "10000002") &&
        LocaleController.isChinese()) {
      queryParameters[CommonConstant.language] = "zh_Hant";
    } else {
      queryParameters[CommonConstant.locale] =
          LocaleController.locale().toLanguageTag();
    }

    queryParameters[CommonConstant.utmSource] = ID.appName.tr;

    // 生成新的 URI
    Uri newUri = uri.replace(queryParameters: queryParameters);
    return newUri.toString();
  }

  static String getSearchDappUrl(String dAppUrl) {
    Uri uri = Uri.parse(dAppUrl);

    // 添加查询参数
    Map<String, String> queryParameters = {};

    // 清除特定查询参数
    queryParameters.remove(CommonConstant.locale);
    queryParameters.remove(CommonConstant.utmSource);
    queryParameters.remove(CommonConstant.language);

    // 根据条件添加查询参数
    queryParameters[CommonConstant.locale] =
        LocaleController.locale().toLanguageTag();

    queryParameters[CommonConstant.utmSource] = ID.appName.tr;

    // 生成新的 URI
    Uri newUri = uri.replace(queryParameters: queryParameters);
    return newUri.toString();
  }
}
