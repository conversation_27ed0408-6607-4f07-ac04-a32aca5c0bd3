/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-29 13:06:18
 */
/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-13 16:44:03
 */

import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/models/storage_base_model.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rpc_model.g.dart';

@JsonSerializable()
class RpcModel extends BaseStorageModel {
  String? arb;
  String? btc;
  String? opt;
  String? bsc;
  String? zks;
  String? polygon;
  String? btcsignet;
  String? eth;
  String? ethHolesky;
  String? linea;
  String? base;
  String? blast;

  RpcModel({
    this.arb,
    this.btc,
    this.opt,
    this.bsc,
    this.zks,
    this.polygon,
    this.btcsignet,
    this.eth,
    this.ethHolesky,
    this.linea,
    this.base,
    this.blast,
  });

  factory RpcModel.fromJson(Map<String, dynamic> json) =>
      _$RpcModelFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$RpcModelToJson(this);

  String getRpcUrl(String chain) {
    String url = "";

    try {
      RpcModel? model = StorageManager.getObject(
          key: StorageKey.rpcModel, fromJson: RpcModel.fromJson);
      if (model != null) {
        switch (chain) {
          case "bsc":
            url = model.bsc ?? "";
            break;
          case "eth":
            url = model.eth ?? "";
            break;
          case "polygon":
            url = model.polygon ?? "";
            break;
          case "arb":
            url = model.arb ?? "";
            break;
          case "opt":
            url = model.opt ?? "";
            break;
          case "base":
            url = model.base ?? "";
            break;
          case "blast":
            url = model.blast ?? "";
            break;
          case "linea":
            url = model.linea ?? "";
            break;
          case "zks":
            url = model.zks ?? "";
            break;
          case "btc":
            url = model.btc ?? "";
            break;
        }
      }
    } catch (e) {
      // 异常处理可以在这里进行
    }

    return url;
  }
}
