/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-10-09 11:11:13
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/checkbox/checkbox_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class AuthorizationWarningDialog extends BaseStatelessWidget {
  final WebController? webController;
  final VoidCallback? onPressed;
  final VoidCallback? onCancel;

  const AuthorizationWarningDialog(
      {super.key, this.webController, this.onPressed, this.onCancel});

  void showBottomSheet() {
    Get.showBottomSheet(
        onCancel: onCancel,
        barrierDismissible: false,
        enableDrag: false,
        disableBack: false,
        title: ID.stringWarning.tr,
        bodyWidget: this,
        bottomWidget: Obx(() => webController!.isPending.value
            ? const SizedBox.shrink()
            : Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
                child: ButtonWidget(
                    text: ID.stringTouchNextTitle.tr,
                    buttonSize: ButtonSize.full,
                    buttonStatus: webController!.authWarningCheckOne.value &&
                            webController!.authWarningCheckTwo.value
                        ? ButtonStatus.enable
                        : ButtonStatus.disable,
                    onPressed: onPressed),
              )));
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Obx(() =>
          Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
            ImageWidget(
              assetUrl: "icon_warning",
              width: Get.setImageSize(30),
              height: Get.setImageSize(30),
            ),
            SizedBox(height: Get.setPaddingSize(16)),
            Text(ID.stringAssetSecurityWarning1.tr,
                textAlign: TextAlign.center,
                maxLines: 1,
                style: TextStyle(
                  color: Get.theme.colorFF6A16,
                  fontSize: Get.setFontSize(14),
                  overflow: TextOverflow.ellipsis,
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            SizedBox(height: Get.setPaddingSize(10)),
            _buildCheckboxRow(ID.stringAssetSecurityWarning2.tr,
                webController!.authWarningCheckOne.value,
                onChanged: () => webController!.authWarningCheckOne.value =
                    !webController!.authWarningCheckOne.value),
            _buildTextWithDot(ID.stringAssetSecurityWarning3.tr),
            _buildTextWithDot(ID.stringAssetSecurityWarning4.tr),
            _buildTextWithDot(ID.stringAssetSecurityWarning5.tr),
            _buildTextWithDot(ID.stringAssetSecurityWarning6.tr),
            SizedBox(height: Get.setPaddingSize(4)),
            _buildCheckboxRow(ID.stringColdWalletAuthorizationWarning.tr,
                webController!.authWarningCheckTwo.value,
                onChanged: () => webController!.authWarningCheckTwo.value =
                    !webController!.authWarningCheckTwo.value)
          ])),
    );
  }

  Padding _buildCheckboxRow(String text, bool isCheck,
      {VoidCallback? onChanged}) {
    return Padding(
      padding: const EdgeInsets.only(top: 14.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: onChanged!,
            child: Padding(
              padding: const EdgeInsets.only(top: 2),
              child: CheckboxWidget(
                isCheck: isCheck,
                onChanged: (value) => onChanged.call(),
              ),
            ),
          ),
          SizedBox(width: Get.setPaddingSize(6)),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                color: Get.theme.textPrimary,
                fontSize: Get.setFontSize(14),
                fontFamily: Get.setFontFamily(),
                fontWeight: FontWeightX.regular,
              ), // 替换为实际颜色
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextWithDot(String text) {
    return Padding(
      padding: const EdgeInsets.only(left: 20.0, top: 16.0),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 4,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey, // 替换为实际颜色
            ),
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.regular,
            ),
          ),
        ],
      ),
    );
  }
}
