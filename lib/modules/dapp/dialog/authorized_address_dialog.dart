/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2024-10-02 12:40:31
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/dapp/widgets/dapp_logo_widget.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/action_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class AuthorizedAddressDalog extends StatelessWidget {
  final WebController? webController;
  final VoidCallback? onDeny;
  final VoidCallback? onConfirm;
  const AuthorizedAddressDalog(
      {super.key, this.webController, this.onDeny, this.onConfirm});

  void showBottomSheet() {
    Get.showBottomSheet(
        onCancel: onDeny,
        barrierDismissible: false,
        enableDrag: false,
        disableBack: false,
        title: ID.stringRequestAuthorization.tr,
        bodyWidget: this,
        bottomWidget: _actionButton());
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
        DappLogoWidget(
            dappUrl: webController!.url.value,
            dappModel: webController!.dappModel),
        const SizedBox(height: 16),
        Text(ID.stringRequestAccess.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.regular,
            )),
        const SizedBox(height: 16),
      ]),
    );
  }

  ActionButtonWidget _actionButton() => ActionButtonWidget(
      onLeftPressed: () => onDeny!(), onRightPressed: () => onConfirm!());
}
