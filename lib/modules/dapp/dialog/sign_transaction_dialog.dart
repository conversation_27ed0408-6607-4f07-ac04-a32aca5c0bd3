/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2025-02-14 09:53:04
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/dapp/browser/ethereum/etherum_dapp_controller.dart';
import 'package:coinbag/modules/dapp/widgets/dapp_logo_widget.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/image/symbol_widget.dart';
import 'package:coinbag/widgets/progress/image_progress_widget.dart';
import 'package:flutter/material.dart';

class SignTransactionDialog extends BaseStatelessWidget {
  final WebController? webController;
  final VoidCallback? onPressed;
  final VoidCallback? onCancel;

  const SignTransactionDialog({
    super.key,
    this.webController,
    this.onPressed,
    this.onCancel,
  });
  void showBottomSheet() {
    Get.showBottomSheet(
        onCancel: onCancel,
        barrierDismissible: false,
        enableDrag: false,
        disableBack: false,
        title: webController!.isApproveTransaction
            ? ID.stringAuthorizeSmartContract.tr
            : ID.stringSignTransaction.tr,
        bodyWidget: this,
        bottomWidget: Obx(() => webController!.isPending.value
            ? const SizedBox.shrink()
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ButtonWidget(
                    text: ID.stringConfirm.tr,
                    buttonSize: ButtonSize.full,
                    onPressed: onPressed),
              )));
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Obx(() => webController!.isPending.value
          ? Column(
              children: [
                SizedBox(height: Get.setPaddingSize(34)),
                const ImageProgressWidget(),
                SizedBox(height: Get.setPaddingSize(16)),
                Text(ID.loading.tr,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    style: TextStyle(
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      overflow: TextOverflow.ellipsis,
                      fontFamily: Get.setFontFamily(),
                      fontWeight: FontWeightX.regular,
                    )),
                SizedBox(height: Get.setPaddingSize(10)),
              ],
            )
          : Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
              DappLogoWidget(
                  showName: true,
                  dappUrl: webController!.url.value,
                  dappModel: webController!.dappModel),
              SizedBox(height: Get.setPaddingSize(12)),
              _dataItem(),
              _babyChainItem(),
              _rawDataItem()
            ])),
    );
  }

  Visibility _dataItem() => Visibility(
        visible: webController!.visibleData.value,
        child: !webController!.isApproveTransaction
            ? Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                _amountItem(),
                _transationTypeItem(ID.stringContractCall.tr),
                _addressItem(ID.stringSendAddress.tr, webController!.fromList!),
                _addressItem(ID.stringReciveAddress.tr, webController!.toList!),
                _feeItem()
              ])
            : Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                _transationTypeItem(ID.stringAuthorizeSmartContract.tr),
                _addressItem(ID.stringContractAddress.tr,
                    webController!.approveContractAddress),
                _addressItem(
                    ID.stringAuthorizedAddress.tr, webController!.fromList!),
                _feeItem()
              ]),
      );
  Visibility _babyChainItem() => Visibility(
      visible: webController!.visibleBabylonChainData.value,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(ID.coinTsTypeTitle.tr,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            Row(
              children: [
                Text(webController!.bbnAddressModel?.chain ?? "",
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setNumberFontFamily(),
                      fontWeight: FontWeightX.medium,
                    )),
                SizedBox(
                  width: Get.setPaddingSize(4),
                ),
                Text(ID.stringTransferSend.tr,
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontWeight: FontWeightX.medium,
                    )),
              ],
            )
          ],
        ),
      ));

  Container _amountItem() => Container(
        padding: const EdgeInsets.all(12),
        width: Get.width,
        margin: const EdgeInsets.symmetric(vertical: 8),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Get.theme.colorF9F9F9,
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text.rich(
                      TextSpan(children: [
                        WidgetSpan(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: SymbolWidget(
                                coinModel: webController!.coinModel!),
                          ),
                        ),
                        TextSpan(
                            text: webController!.amountText,
                            style: TextStyle(
                              overflow: TextOverflow.ellipsis,
                              color: Get.theme.textSecondary,
                              fontSize: Get.setFontSize(
                                  webController!.coinType!.isEthereumSeries
                                      ? 20
                                      : 28),
                              fontFamily: Get.setNumberFontFamily(),
                              fontWeight: FontWeightX.semibold,
                            )),
                      ]),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );

  Padding _transationTypeItem(String type) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(ID.coinTsTypeTitle.tr,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            Row(
              children: [
                SymbolWidget(
                  coinModel: webController!.coinModel!,
                  size: 20,
                ),
                SizedBox(
                  width: Get.setPaddingSize(4),
                ),
                Text(webController!.coinModel!.symbol ?? "",
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontFamily: Get.setNumberFontFamily(),
                      fontWeight: FontWeightX.medium,
                    )),
                SizedBox(
                  width: Get.setPaddingSize(4),
                ),
                Text(type,
                    maxLines: 1,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      fontWeight: FontWeightX.medium,
                    )),
              ],
            )
          ],
        ),
      );

  Padding _addressItem(String transferInfoItemType, List<String> addressList) =>
      Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(transferInfoItemType,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            SizedBox(
              width: Get.setPaddingSize(12),
            ),
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children:
                  addressList.map((item) => _addressDetailsItem(item)).toList(),
            )),
          ]));

  Widget _addressDetailsItem(String? address) => Padding(
        padding: const EdgeInsets.only(bottom: 2),
        child: Row(
          children: [
            Expanded(
              child: Text(
                AddressUtils.omitAddress(address),
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.semibold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
            ),
            GestureDetector(
              onTap: () => Get.copy(address),
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: ImageWidget(
                    assetUrl: 'copy',
                    width: Get.setPaddingSize(16),
                    height: Get.setPaddingSize(16)),
              ),
            ),
          ],
        ),
      );

  Padding _feeItem() => Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(ID.feeTitle.tr,
            maxLines: 1,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.regular,
            )),
        SizedBox(
          width: Get.setPaddingSize(12),
        ),
        Expanded(
            child: webController!.coinType!.isEthereumSeries
                ? _ethereumFeeItem()
                : _btiCoinFeeItem()),
      ]));

  GestureDetector _ethereumFeeItem() => GestureDetector(
        onTap: () {
          Get.toNamed(AppRoutes.feePage, arguments: {
            GetArgumentsKey.tsModel: webController!.tsModel,
            GetArgumentsKey.gearList: webController!.feeGearList,
            GetArgumentsKey.coinModel: webController!.coinModel,
          })?.then((result) {
            if (result != null) {
              webController!.tsModel = result[GetArgumentsKey.tsModel];
              webController!.gearModel = result[GetArgumentsKey.gearModel];
              webController!.handelFeeValue(isCustomize: true);
            }
          });
        },
        behavior: HitTestBehavior.translucent,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  webController!.fee.value,
                  style: TextStyle(
                    overflow: TextOverflow.ellipsis,
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(14),
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.semibold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
                const SizedBox(
                  height: 2,
                ),
                Text(
                  webController!.feePrice.value,
                  style: TextStyle(
                    overflow: TextOverflow.ellipsis,
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(12),
                    fontFamily: Get.setNumberFontFamily(),
                    fontWeight: FontWeightX.regular,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
              child: ImageWidget(
                  assetUrl: 'icon_new_arrow01',
                  width: Get.setPaddingSize(12),
                  height: Get.setPaddingSize(12)),
            ),
          ],
        ),
      );

  Column _btiCoinFeeItem() => Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            webController!.fee.value,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setNumberFontFamily(),
              fontWeight: FontWeightX.semibold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
          ),
          Text(
            webController!.feePrice.value,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setNumberFontFamily(),
              fontWeight: FontWeightX.semibold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
          ),
        ],
      );

  Visibility _rawDataItem() => Visibility(
      visible: webController!.visibleRawData.value,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(ID.stringData.tr,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            GestureDetector(
              onTap: () => Get.copy(webController!.rawData ?? ""),
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: Container(
                  height: Get.setFontSize(150), // 固定高度
                  width: Get.width,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Get.theme.colorF9F9F9,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Scrollbar(
                    child: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(webController!.rawData ?? "",
                                style: TextStyle(
                                  color: Get.theme.textSecondary,
                                  fontSize: Get.setFontSize(14),
                                  fontFamily: Get.setNumberFontFamily(),
                                  fontWeight: FontWeightX.regular,
                                ))
                          ]),
                    ),
                  ),
                ),
              ),
            ),
          ])));
}
