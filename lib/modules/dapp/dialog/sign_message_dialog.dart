/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:22:39
 * @LastEditTime: 2025-02-27 13:05:45
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/dapp/widgets/dapp_logo_widget.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/address_utils.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/progress/image_progress_widget.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';

class SignMessageDalog extends BaseStatelessWidget {
  final WebController? webController;
  final VoidCallback? onPressed;
  final VoidCallback? onCancel;

  const SignMessageDalog(
      {super.key, this.webController, this.onPressed, this.onCancel});
  void showBottomSheet() {
    Get.showBottomSheet(
        onCancel: onCancel,
        barrierDismissible: false,
        enableDrag: false,
        disableBack: false,
        title: ID.stringMessgaeSign.tr,
        bodyWidget: this,
        bottomWidget: Obx(() => webController!.isPending.value
            ? const SizedBox.shrink()
            : Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
                child: ButtonWidget(
                    text: ID.stringConfirm.tr,
                    buttonSize: ButtonSize.full,
                    onPressed: onPressed),
              )));
  }

  @override
  build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Obx(() => webController!.isPending.value
          ? Column(
              children: [
                SizedBox(height: Get.setPaddingSize(34)),
                const ImageProgressWidget(),
                SizedBox(height: Get.setPaddingSize(16)),
                Text(ID.loading.tr,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    style: TextStyle(
                      color: Get.theme.textSecondary,
                      fontSize: Get.setFontSize(14),
                      overflow: TextOverflow.ellipsis,
                      fontFamily: Get.setFontFamily(),
                      fontWeight: FontWeightX.regular,
                    )),
                SizedBox(height: Get.setPaddingSize(10)),
              ],
            )
          : Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
              DappLogoWidget(
                  showName: true,
                  dappUrl: webController!.url.value,
                  dappModel: webController!.dappModel),
              SizedBox(height: Get.setPaddingSize(16)),
              _dataItem(),
              _rawDataItem()
            ])),
    );
  }

  Visibility _dataItem() => Visibility(
        visible: webController!.visibleData.value,
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [_transationTypeItem(), _addressItem(), _signMethod()]),
      );

  Padding _transationTypeItem() => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(ID.coinTsTypeTitle.tr,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            SizedBox(
              width: Get.setPaddingSize(4),
            ),
            Text(ID.stringMessgaeSign.tr,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.medium,
                )),
          ],
        ),
      );

  Padding _addressItem() => Padding(
      padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(ID.stringAddress.tr,
            maxLines: 1,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              color: Get.theme.textSecondary,
              fontSize: Get.setFontSize(14),
              fontFamily: Get.setFontFamily(),
              fontWeight: FontWeightX.regular,
            )),
        SizedBox(
          width: Get.setPaddingSize(12),
        ),
        Expanded(
            child: _addressDetailsItem(
          webController!.qrCodeType != QRCodeType.cosmosSignMessage
              ? webController!.address
              : webController!.bbnAddressModel?.address ?? '',
        ))
      ]));

  Widget _addressDetailsItem(String? address) => Padding(
        padding: const EdgeInsets.only(bottom: 2),
        child: Row(
          children: [
            Expanded(
              child: Text(
                AddressUtils.omitAddress(address),
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setNumberFontFamily(),
                  fontWeight: FontWeightX.semibold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
            ),
            GestureDetector(
              onTap: () => Get.copy(address),
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: ImageWidget(
                    assetUrl: 'copy',
                    width: Get.setPaddingSize(16),
                    height: Get.setPaddingSize(16)),
              ),
            ),
          ],
        ),
      );

  SingleChildRenderObjectWidget _signMethod() => webController!.signMethod ==
          null
      ? SizedBox.shrink()
      : Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(ID.stringSignMethodTitle.tr,
                maxLines: 1,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            SizedBox(
              width: Get.setPaddingSize(12),
            ),
            Expanded(
              child: Text(
                webController!.signMethod ?? "",
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.semibold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
            ),
          ]));

  Visibility _rawDataItem() => Visibility(
      visible: webController!.visibleRawData.value,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(10)),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(ID.stringMessageContentTitle.tr,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            GestureDetector(
              onTap: () => Get.copy(webController!.uftMessage ?? ""),
              behavior: HitTestBehavior.translucent,
              child: Padding(
                padding: EdgeInsets.only(left: Get.setPaddingSize(4)),
                child: Container(
                  height: Get.height / 2, // 固定高度
                  width: Get.width,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Get.theme.colorF9F9F9,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Scrollbar(
                    child: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(webController!.uftMessage ?? "",
                                style: TextStyle(
                                  color: Get.theme.textSecondary,
                                  fontSize: Get.setFontSize(14),
                                  fontFamily: Get.setNumberFontFamily(),
                                  fontWeight: FontWeightX.regular,
                                ))
                          ]),
                    ),
                  ),
                ),
              ),
            ),
          ])));
}
