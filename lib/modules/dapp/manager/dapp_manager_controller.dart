/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-19 14:05:39
 */
/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:47:52
 * @LastEditTime: 2024-09-13 10:53:40
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/res/resource.dart';
import 'package:wallet_core/wallet/wallet.dart';

class DappMnagerontroller extends BaseController<AppDatabase> {
  var allDataList = <DappModels>[].obs;
  var mode = 0;
  var editMode = false.obs;
  var title = ''.obs;

  late WalletModel? walletModel;
  late Wallet? wallet;
  @override
  void onInit() {
    super.onInit();
    mode = Get.arguments?[GetArgumentsKey.mode] ?? '';
    allDataList.value =
        Get.arguments?[GetArgumentsKey.dataList] ?? <DappModels>[];
    if (mode != 0) {
      title.value = mode == DappMode.rentMode ? ID.recent.tr : ID.favorites.tr;
    }
    walletModel = Get.arguments?[GetArgumentsKey.walletModel];
    wallet = Get.arguments?[GetArgumentsKey.wallet];
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {}

  Future<void> deleteDapp(DappModels model) async {
    await api.dappDao.deleteDappRecord(model.dAppId, mode);
    allDataList.remove(model);
  }
}

class DappManagerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DappMnagerontroller());
  }
}
