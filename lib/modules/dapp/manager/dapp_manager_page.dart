/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2024-09-23 13:13:05
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/dapp/manager/dapp_manager_controller.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class DappManagerPage extends BaseStatelessWidget<DappMnagerontroller> {
  const DappManagerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: baseAppBar(
              titleWidget: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(controller.title.value, style: styleAppbarTitle),
              ),
              actionWidget: [
                GestureDetector(
                    onTap: () =>
                        controller.editMode.value = !controller.editMode.value,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: Get.setPaddingSize(6),
                          horizontal: Get.setPaddingSize(
                              12)), // paddingVertical 和 paddingStart
                      child: Text(
                        controller.editMode.value
                            ? ID.stringComplte.tr
                            : ID.stringEdit.tr,
                        style: TextStyle(
                            fontSize: Get.setFontSize(14),
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeightX.medium,
                            fontFamily: Get.setFontFamily(),
                            color: Get.theme.textPrimary),
                      ),
                    ))
              ]),
          body: _buildBodyView(),
        ));
  }

  ListView _buildBodyView() {
    return ListView.builder(
        itemCount: controller.allDataList.length,
        padding: EdgeInsets.zero,
        itemBuilder: (_, index) => _itemBuilder(controller.allDataList[index]));
  }

  HighLightInkWell _itemBuilder(DappModels model) {
    return HighLightInkWell(
      onTap: () => Get.openDapp(
          dappModels: model,
          walletModel: controller.walletModel,
          wallet: controller.wallet),
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(14),
            horizontal: Get.setPaddingSize(16)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageWidget(
              imageUrl: model.dAppLogo ?? "",
              width: Get.setImageSize(48),
              height: Get.setImageSize(48),
              radius: Get.setRadius(12),
            ),
            SizedBox(
              width: Get.setWidth(12),
            ),
            _textDapp(model),
            Visibility(
              visible: controller.editMode.value,
              child: GestureDetector(
                onTap: () async => controller.deleteDapp(model),
                child: Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: ImageWidget(
                    assetUrl: 'icon_delete',
                    width: Get.setImageSize(18),
                    height: Get.setImageSize(18),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Expanded _textDapp(DappModels model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            model.dAppName ?? '',
            style: style_Dapp_Item,
          ),
          SizedBox(
            height: Get.setHeight(2),
          ),
          Text(
            model.abs ?? '',
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeightX.regular,
                fontFamily: Get.setFontFamily(),
                color: Get.theme.textSecondary),
          ),
        ],
      ),
    );
  }
}
