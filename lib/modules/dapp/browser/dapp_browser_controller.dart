/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-03-06 10:12:31
 */

import 'dart:convert';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/dapp/browser/bitcoin/bitcoin_dapp_controller.dart';
import 'package:coinbag/modules/dapp/browser/ethereum/etherum_dapp_controller.dart';
import 'package:coinbag/modules/dapp/browser/models/js_call_back_model.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/dapp/dialog/authorization_warning_dialog.dart';
import 'package:coinbag/modules/dapp/dialog/authorized_address_dialog.dart';
import 'package:coinbag/modules/dapp/dialog/sign_message_dialog.dart';
import 'package:coinbag/modules/dapp/dialog/sign_transaction_dialog.dart';
import 'package:coinbag/modules/dapp/models/rpc_model.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/widgets/toast/toast.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/extension/string_to_int_list.dart';

extension DappBrowserController on WebController {
  // 初始化链地址相关
  Future<void> loadChainData({
    int selectChainId = -1,
    bool isSwitchWallet = false,
    String? selectChain,
    AddressModel? selectAddressModel,
    ChainManagerMode mode = ChainManagerMode.dapp,
  }) async {
    if (mode == ChainManagerMode.dappBaby) {
      bbnAddressModel = selectAddressModel;
      onSetBBNAddress();
      // 返回上一页
      if (isSwitchWallet) {
        Get.back();
      }
      return;
    }

    if (selectChainId != -1) {
      coinType = CoinBase.getCoinTypeByChainId(selectChainId);
    } else {
      if (selectChain != null && !Get.isEmptyString(selectChain)) {
        coinType = CoinBase.getCoinTypeByChain(selectChain);
      }
    }

    // 检查 coinType 是否有效
    if (!isSwitchWallet && coinType == null) {
      onSendError(ID.stringNotCurrentlySupporte.tr);
      _showToast(ID.stringNotCurrentlySupporte.tr);
      return;
    }

    Log.r("loadChainData coinType =${coinType!.chain}");

    // 如果是切换钱包，进行地址验证
    if (isSwitchWallet) {
      if (coinType?.isBitCoinNetWork == true && selectAddressModel != null) {
        if (!await validateBech32Address(selectAddressModel.address!)) {
          _showToast(ID.stringOnlySupportsTaproot.tr);
          return;
        }
      }
    }

    // 设置链 ID 和链
    chainId = coinType?.chainId ?? 1;
    chain = coinType?.chain ?? '';

    // 获取 coinModel 和 addressModel
    await _loadCoinModel();
    addressModel = selectAddressModel ?? await loadDefaultAddressModel(chain);

    // 设置地址
    if (addressModel != null) {
      address = addressModel!.address!;
    }

    // 设置 RPC URL 和处理比特币地址
    setRpcUrl();

    if (isSwitchWallet) {
      if (coinType!.isBitCoinNetWork) {
        onSetBitcoinAddress();
      } else if (coinType!.isEthereumSeries) {
        defaultEthereumAddress = address;
        onSwitchWallet();
      }
    }

    // 返回上一页
    if (isSwitchWallet) {
      Get.back();
    }
  }

// 显示 Toast 的辅助函数
  void _showToast(String message) {
    Get.showToast(message, toastMode: ToastMode.waring);
  }

// 加载 coinModel 的辅助函数
  Future<void> _loadCoinModel() async {
    coinModel = await Get.database.coinDao.getMainCoinModel(chain: chain);
  }

// 加载默认地址模型的辅助函数
  Future<AddressModel?> loadDefaultAddressModel(String c) async {
    return await Get.database.addressDao.getDefaultAddressModel(
      walletId: walletModel!.walletId!,
      deviceId: walletModel!.deviceId!,
      chain: c,
    );
  }

  // 获取RPC
  void setRpcUrl() {
    RpcModel? rpcModel = StorageManager.getObject(
        key: StorageKey.rpcModel, fromJson: RpcModel.fromJson);
    if (rpcModel != null) {
      rpcUrl = rpcModel.getRpcUrl(chain);
    }
  }

  Future<bool> isApprove() async {
    bool isApproveAddress = false;
    if (dappModel != null) {
      isApproveAddress = await Get.database.dappDao
          .isApprove(dappModel!.dAppId, DappMode.defaultModel);
    } else {
      isApproveAddress =
          await Get.database.dappDao.isApprove(url.value, DappMode.rentMode);
    }
    return isApproveAddress;
  }

  Future<void> approveAddress() async {
    if (!isApproveAddress) {
      if (dappModel != null) {
        await Get.database.dappDao
            .approveAddress(dappModel!.dAppId, DappMode.defaultModel);
      } else {
        await Get.database.dappDao.approveAddress(url.value, DappMode.rentMode);
      }
    }
  }

  String? decodeMessageData(String jsonString) {
    String rawJson = utf8.decode(jsonString.hexToBytes);
    Map? map = json.decode(rawJson);
    List? msgs = map?['msgs'];
    Map? msMap = msgs?.first;
    Map? valueMap = msMap?['value'];
    String? data = valueMap?['data'];
    if (Get.isEmptyString(data)) return null;
    return utf8.decode(base64Decode(data!));
  }

  Future<void> handleJsonData(String handlerName, var args) async {
    try {
      if (!isAlive.value) {
        return;
      }
      Log.r(handlerName);
      Log.logPrint(args);
      isSendResutling = false;
      String? jsonString;
      JsCallbackModel? jsData;
      bool isCosmosMessage = false;
      String? type;
      if (handlerName == EthereumJsCallBack.ethereumjs) {
        try {
          // 解析 JSON 字符串
          Log.r('handleJsonData=$args');
          final Map<String, dynamic> jsonData = args[0];
          jsData = JsCallbackModel.fromJson(jsonData);
          network = jsData.network!;
          callbackId = jsData.id!;
        } catch (e) {
          jsData == null;
        }
      } else {
        jsonString = args != null && args[0] != null ? args[0] : '';
        if (jsonString!.isNotEmpty) Log.r('handleJsonData=$jsonString');

        /// 是否可以解析
        if (args != null && args is List) {
          final last = args.last;
          if (last is String) {
            isCosmosMessage = last == CommonConstant.amino;
            type = last;
          }
        }
      }
      // 检查 jsData 是否为 null，使用默认的 handlerName
      DappMethod dappMethod = DappMethodManager.fromValue(
          (jsData != null && jsData.name != null) ? jsData.name! : handlerName);

      switch (dappMethod) {
        case DappMethod.requestAccounts:
          if (isOpenDappBrowserFromSearch) {
            loadChainData(selectChain: EthereumChain.get.chain);
          }
          if (isApproveAddress) {
            onSendAddress();
          } else {
            showAuthorizedAddressDalog();
          }
          break;
        case DappMethod.signTransaction:
          signTransaction(jsData!);
          break;
        case DappMethod.signMessage:
        case DappMethod.signPersonalMessage:
        case DappMethod.signTypedMessage:
          handleEthereumMessage(dappMethod, jsData!);
          break;

        case DappMethod.ecRecover:
          break;

        case DappMethod.watchAsset:
          break;
        case DappMethod.addEthereumChain:
          addEthereumChain(jsData!);
          break;
        case DappMethod.switchEthereumChain:
          switchEthereumChain(jsData!);
          break;

        case DappMethod.getBitcoinAddress:
          if (isOpenDappBrowserFromSearch) {
            loadChainData(selectChain: BitcoinChain.get.chain);
          }
          if (isApproveAddress) {
            showChainManagerDialog();
          } else {
            showAuthorizedAddressDalog();
          }

          break;
        case DappMethod.getBitcoinPublicKeyHex:
          onSetBitcoinPubKeyHex();
          break;
        case DappMethod.signPsbt:
          handlePsdbData(jsonString!);
          break;
        case DappMethod.signBitcoinMessage:
          handleBitCoinMessage(jsonString!, type);
          break;
        case DappMethod.bitcoinAccountChanged:
          break;
        case DappMethod.getBitcoinNetwork:
          break;
        case DappMethod.getBabyAddress:
          onSetBBNAddress();
          break;
        case DappMethod.getBabyPublicKeyHex:
          onSetBBNPubKeyHex();
          break;
        case DappMethod.signBaby:
          if (isCosmosMessage) {
            handleCosmosMessage(jsonString!, decodeMessageData(jsonString));
          } else {
            handleBabyData(jsonString!);
          }
          break;
        case DappMethod.unknown:
          break;
      }

      // 提取 BTC 地址
    } catch (e) {
      Log.r(e);
    }
  }

  void showChainManagerDialog({bool isAction = false}) {
    if (!isAlive.value) {
      return;
    }
    if (Get.isDialogOpen != null && Get.isDialogOpen!) {
      Get.back();
    }
    String selectChain = chain;
    if (isAction && (dappModel == null || dappModel!.chain == null)) {
      selectChain = 'all';
    }
    ChainManagerPage(
      wallet: walletModel,
      isFullPage: false,
      chain: selectChain,
      chainManagerMode: ChainManagerMode.dapp,
    ).showChainPage();
  }

  void showAuthorizedAddressDalog() {
    Log.r("showAuthorizedAddressDalog isAlive: $isAlive");
    if (!isAlive.value || isDialogShowing) {
      return;
    }
    isDialogShowing = true;

    AuthorizedAddressDalog(
        webController: this,
        onConfirm: () async {
          Get.back();
          if (_verifyUrl() == false) {
            isDialogShowing = false;
            onSendError("onDeny");
            return;
          }

          isDialogShowing = false;
          approveAddress();
          if (coinType!.isBitCoinNetWork) {
            showChainManagerDialog();
          } else {
            onSendAddress();
          }
        },
        onDeny: () {
          isDialogShowing = false;
          onSendError("onDeny");
          Get.back();
        }).showBottomSheet();
  }

  bool _verifyUrl() {
    String url = this.url.value;
    if (url.contains('?')) {
      List<String> data = url.split('?');
      if (data.isNotEmpty) {
        url = data.first;
        if (url.endsWith('/')) {
          url = url.replaceRange(url.length, null, '');
        }
      }
    }

    if (coinType?.isBitCoinNetWork == true) {
      if (url.endsWith(CommonConstant.babylonlabs) ||
          url.endsWith(CommonConstant.babylonchainlabs) ||
          url.endsWith(CommonConstant.babylonchain) ||
          url.endsWith(CommonConstant.babylonlabs2) ||
          url.contains(CommonConstant.airdropTest) ||
          url.contains(CommonConstant.airdropBabylon)) {
        return true;
      }
      Get.showAlertDialog(content: ID.stringBabylonError.tr);
      return false;
    }

    return true;
  }

  void showAuthorizationWarningDialog() {
    if (!isAlive.value) {
      return;
    }

    AuthorizationWarningDialog(
        webController: this,
        onPressed: () async {
          navigate();
        },
        onCancel: () {
          onSendError("onDeny");
          Get.back();
        }).showBottomSheet();
  }

  void showSignMessageDalog() {
    if (!isAlive.value) {
      return;
    }

    SignMessageDalog(
      webController: this,
      onPressed: () {
        AppController.isDappSignMessageFlag.value = true;
        navigateToQRPage();
      },
      onCancel: () async {
        onSendError('cancel');
        Get.back();
      },
    ).showBottomSheet();
  }

  void showSignTransactionDialog() {
    if (!isAlive.value) {
      return;
    }

    SignTransactionDialog(
      webController: this,
      onPressed: () => navigateToQRPage(isTransaction: true),
      onCancel: () async {
        onSendError('cancel');
        Get.back();
      },
    ).showBottomSheet();
  }

  Future<void> navigateToQRPage({bool isTransaction = false}) async {
    if (isEip1559Transaction && !wallet!.isSupportEIP1559 && isTransaction) {
      Get.showToast(ID.stringNotSupportEIP1559.tr);
      return;
    }

    if (coinType!.isEthereumSeries && isTransaction) {
      bool greaterThanDecStr =
          DecimalUtils.compare(mainChainBalance!, feeValue);

      if (!greaterThanDecStr) {
        Get.showToast(ID.insufficientBalanceFee.tr);

        return;
      }
    }
    if (isApproveTransaction) {
      Get.back();
      showAuthorizationWarningDialog();
      return;
    }

    if (Get.isAuthorizeTransaction()) {
      final result = await Get.toNamed(AppRoutes.securityPage,
          arguments: SecurityState.security);
      if (result == true) {
        navigate();
      }
    } else {
      navigate();
    }
  }

  void navigate() {
    Get.back();
    Future.delayed(const Duration(milliseconds: 300), () async {
      Get.toNamed(AppRoutes.walletQRCodePage, arguments: {
        GetArgumentsKey.transferModel: tsModel,
        GetArgumentsKey.qrType: qrCodeType,
      });
    });
  }
}
