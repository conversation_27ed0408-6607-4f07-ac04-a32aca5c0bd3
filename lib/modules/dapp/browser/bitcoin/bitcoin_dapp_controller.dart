/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-03-06 10:12:25
 */

import 'dart:convert';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/dapp/browser/dapp_browser_controller.dart';
import 'package:coinbag/modules/dapp/browser/models/psbt/psbt_model.dart';
import 'package:coinbag/modules/dapp/browser/models/psbt/psbt_raw_model/psbt_raw_model.dart';
import 'package:coinbag/modules/dapp/browser/models/psbt/psbt_raw_model/v_output.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:wallet_core/chain/bitcoin/btc.dart';
import 'package:wallet_core/chain/cosmos/baby.dart';
import 'package:wallet_core/chain/cosmos/tbaby.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';

extension BitcoinDappController on WebController {
// 验证 Bech32 地址的辅助函数
  Future<bool> validateBech32Address(String address) async {
    return await Get.walletCore.isBech32Address(toAddress: address) ?? false;
  }

  Future<void> onSetBBNAddress() async {
    if (webViewController == null) return;

    if (bbnAddressModel == null) {
      String babyChain = BabylonChain.get.chain;
      if (chain != BitcoinChain.get.chain) {
        babyChain = BabylonTestnetChain.get.chain;
      }
      Log.logPrint(babyChain);
      ChainManagerPage(
        wallet: walletModel,
        isFullPage: false,
        chain: babyChain,
        chainManagerMode: ChainManagerMode.dappBaby,
      ).showChainPage();
      return;
    }

    String initSrc = CallBackJs.jsSetBBNAddress;
    String callback = initSrc.replaceAll('%1\$s', bbnAddressModel!.address!);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  Future<void> onSetBBNPubKeyHex() async {
    if (webViewController == null) return;

    String initSrc = CallBackJs.jsSetBBNPublicKeyHex;
    String callback = initSrc.replaceAll('%1\$s', bbnAddressModel!.publickey!);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  Future<void> onSetBBNSignature(String hex) async {
    if (webViewController == null) return;

    List<int> bytes = [];
    for (int i = 0; i < hex.length; i += 2) {
      bytes.add(int.parse(hex.substring(i, i + 2), radix: 16));
    }
    String encodedText = base64Encode(bytes);

    String initSrc = CallBackJs.jsSetSignBBN;
    String callback = initSrc.replaceAll('%1\$s', encodedText);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  // 给btc地址
  Future<void> onSetBitcoinAddress() async {
    if (webViewController == null) return;

    String initSrc = CallBackJs.jsSetBtcAddress;
    String callback = initSrc.replaceAll('%1\$s', address);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  // 给btc地址
  Future<void> onSetBitcoinPubKeyHex() async {
    if (webViewController == null) return;

    String initSrc = CallBackJs.jsSetBtcPublicKeyHex;
    String callback = initSrc.replaceAll('%1\$s', addressModel!.publickey!);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  // 发送PSBT签名数据
  Future<void> onSendPsbtSignData(String signData) async {
    if (webViewController == null) return;

    try {
      // 创建一个 JSON 对象
      final jsonObject = {
        "id": callbackId,
        "psbts": [signData]
      };

      String initSrc = CallBackJs.jsSetPsbtList;
      String callback = initSrc.replaceAll('%1\$s', jsonEncode(jsonObject));

      Log.r(callback);
      await webViewController!.evaluateJavascript(source: callback);
    } catch (e) {
      // 捕获异常但忽略
    }
  }

  // 发送PSBT签名消息数据
  Future<void> onSendPsbtSignMessageData(String signData) async {
    if (webViewController == null) return;

    final jsonObject = {
      "id": callbackId,
      "message": signData,
    };

    String initSrc = CallBackJs.jsSetPsbtMessage;
    String callback = initSrc.replaceAll('%1\$s', jsonEncode(jsonObject));
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  /// 处理 PSBT 解析
  Future<void> handlePsdbData(String data) async {
    final Map<String, dynamic> obj = json.decode(data);
    callbackId = obj['id'];
    final List<dynamic> jsonArray = List<dynamic>.from(obj['psbts']);

    if (jsonArray.isNotEmpty) {
      final String pastHex = jsonArray[0].toString(); // 确保转换为 String
      if (pastHex.isEmpty) {
        return;
      }

      isPending.value = true;
      showSignTransactionDialog();
      rawData = pastHex;

      String? psbtModelJson =
          await Get.walletCore.parseRawPsbt(pastHex, coinType!.isTestNet);
      Log.r(psbtModelJson);
      PsbtRawModel psbtRawModel =
          PsbtRawModel.fromJson(json.decode(psbtModelJson!));
      _parseRawPsbtData(psbtRawModel);
    } else {
      Log.r("psbts array is empty");
    }
  }

  /// 处理 BitCoin签名消息
  Future<void> handleBitCoinMessage(String data, String? type) async {
    final Map<String, dynamic> obj = json.decode(data);
    if (obj.isNotEmpty) {
      isPending.value = true;
      callbackId = obj['id'];
      rawData = obj['message'];
      signMethod = type ?? (obj['type'] ?? SignMessageType.ecdsa);
      isPending.value = true;

      uftMessage = rawData;
      qrCodeType = QRCodeType.signMessage;

      showSignMessageDalog();

      visibleData.value = true;
      visibleRawData.value = true;
      if (Get.isEmptyString(rawData)) {
        return;
      }

      tsModel = TransferModel(
          chain: chain,
          addressPath: addressModel!.path,
          walletId: addressModel!.walletId,
          wallet: wallet,
          type: signMethod == SignMessageType.bip322Simple
              ? TransferType.bip322Simple
              : TransferType.ecdsa);

      tsModel!.signMessage = rawData;
      isPending.value = false;
    } else {
      Log.r("psbts message is empty");
    }
  }

  /// 处理 cosmos签名消息
  Future<void> handleCosmosMessage(String rawData, String? message) async {
    isPending.value = true;
    this.rawData = rawData;
    uftMessage = message ?? rawData;
    signMethod = null;
    qrCodeType = QRCodeType.cosmosSignMessage;

    showSignMessageDalog();

    visibleData.value = true;
    visibleRawData.value = true;

    tsModel = TransferModel(
      chain: bbnAddressModel!.chain!,
      addressPath: bbnAddressModel!.path,
      walletId: bbnAddressModel!.walletId,
      wallet: wallet,
      type: TransferType.cosmosSignMessage,
    );

    tsModel!.signMessage = rawData;

    isPending.value = false;
  }

  /// PSBT展示
  void _parseRawPsbtData(PsbtRawModel psbtRawModel) {
    bool isParseError = false;
    if (psbtRawModel.vInput!.isEmpty ||
        psbtRawModel.vInput![0].address == null ||
        psbtRawModel.vOutput == null ||
        psbtRawModel.vOutput!.isEmpty) {
      isParseError = true;
    } else {
      isParseError = false;
      List<FromModel> fromModelList = [];
      List<ToModel> toModelList = [];

      // 处理输入
      if (psbtRawModel.vInput != null && psbtRawModel.vInput!.isNotEmpty) {
        fromModelList = psbtRawModel.vInput!.map((firstInput) {
          FromModel fromModel = FromModel();
          fromModel.setAddress(firstInput.address ?? "");
          fromModel.setValue(firstInput.value ?? 0);
          return fromModel;
        }).toList();
      }

      // 处理输出
      List<VOutput> vOutputDTOS = psbtRawModel.vOutput!;
      toModelList = vOutputDTOS.map((vOutputDTO) {
        ToModel to = ToModel();
        String toAddress = vOutputDTO.address ?? "";
        to.setAddress(toAddress);
        to.setValue(vOutputDTO.value ?? 0);
        return to;
      }).toList();

      // 检查解析错误

      visibleData.value = !isParseError;
      visibleRawData.value = true;

      fromList!.clear();
      toList!.clear();

      // 计算 totalValue
      int totalValue = fromModelList.fold(0, (sum, fromModel) {
        if (fromModel.getAddress() != null) {
          fromList!.add(fromModel.getAddress()!);
        }
        return sum + (fromModel.getValue() ?? 0);
      });

      // 计算 sendValue
      int sendValue = toModelList.fold(0, (sum, toModel) {
        if (toModel.getAddress() != null) {
          toList!.add(toModel.getAddress()!);
        }
        return sum + toModel.getValue()!;
      });

      amount = BalanceManager.getBalance(sendValue.toString(), coinModel);
      amountText = "$amount ${coinModel!.symbol!}";

      // 计算费用
      int feeNum = totalValue - sendValue;
      if (feeNum < 0) {
        feeNum = 0;
      }

      String? feeStr = BalanceManager.getBalance(feeNum.toString(), coinModel);

      Log.r("feeStr=$feeStr");

      fee.value = '$feeStr ${coinModel!.symbol!}';
      feePrice.value = BalanceManager.calculateFiatValue(feeStr, coinModel);

      tsModel = TransferModel(
        chain: coinType!.chain,
        addressPath: addressModel!.path,
        walletId: addressModel!.walletId,
        wallet: wallet,
        type: TransferType.rawPsbt,
      );
      tsModel!.isDapp = true;
      tsModel!.signDataHash = rawData;
      qrCodeType = QRCodeType.transfer;

      isPending.value = false;
    }
  }

  /// babylon 签名
  Future<void> handleBabyData(String data) async {
    isPending.value = true;
    showSignTransactionDialog();
    rawData = data;
    visibleData.value = false;
    visibleRawData.value = true;
    visibleBabylonChainData.value = true;

    tsModel = TransferModel(
      chain: bbnAddressModel!.chain!,
      addressPath: bbnAddressModel!.path,
      walletId: addressModel!.walletId,
      wallet: wallet,
      type: TransferType.dApp,
    );
    tsModel!.isDapp = true;
    tsModel!.signDataHash = rawData;
    qrCodeType = QRCodeType.transfer;

    isPending.value = false;
  }
}
