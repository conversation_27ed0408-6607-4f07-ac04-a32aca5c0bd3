/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2024-10-11 11:26:57
 */

import 'dart:convert';
import 'dart:math';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/params/block_chain_params_manager.dart';
import 'package:coinbag/http/params/extra.dart';
import 'package:coinbag/http/params/request_params.dart';
import 'package:coinbag/modules/dapp/browser/dapp_browser_controller.dart';
import 'package:coinbag/modules/dapp/browser/models/js_call_back_model.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/send/models/fee_gear_model.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/balance_manager.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/services.dart';
import 'package:wallet_core/chain/bitcoin/usdt.dart';
import 'package:wallet_core/chain/coin_base.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/extension/string_to_hex.dart';
import 'package:wallet_core/model/transfer_model.dart';
import 'package:wallet_core/qrcode/qr_code_generator.dart';

extension EtherumDappController on WebController {
  // 获取注入setNetworkJs js
  static const String ethereumSetNetworkJs =
      'assets/dapp/ethereum_set_network.js';

  static const emptyAddress = "0000000000000000000000000000000000000000";

  // 发送地址
  Future<void> onSendAddress() async {
    if (webViewController == null || onLoadError.value) return;

    if (isClosed) {
      return;
    }
    String initSrc = CallBackJs.jsSetAddress;
    String callback =
        initSrc.replaceAll('%1\$s', network).replaceAll('%2\$s', address);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
    String initSrc2 = CallBackJs.jsSendResponse;

    String callbackSend = initSrc2
        .replaceAll('%1\$s', network)
        .replaceAll('%2\$s', callbackId.toString())
        .replaceAll('%3\$s', defaultEthereumAddress);
    Log.r(callbackSend);
    await webViewController!.evaluateJavascript(source: callbackSend);
  }

  // 切换网络
  Future<void> onSendNetWork() async {
    if (webViewController == null || onLoadError.value) return;

    if (isClosed) {
      return;
    }

    String initSrc2 = await rootBundle.loadString(ethereumSetNetworkJs);

    String callbackSend = initSrc2
        .replaceAll('%2\$s', rpcUrl)
        .replaceAll('%3\$s', chainId.toString());

    Log.r(callbackSend);
    await webViewController!.evaluateJavascript(source: callbackSend);
  }

  // 取消动作
  Future<void> onSendError(String message) async {
    if (webViewController == null || isSendResutling || onLoadError.value) {
      return;
    }
    String initSrc = CallBackJs.jsSendError;
    String callback = initSrc
        .replaceAll('%1\$s', CallBackJs.evmNetwork)
        .replaceAll('%2\$s', callbackId.toString())
        .replaceAll('%3\$s', message);
    Log.r(callback);
    try {
      await webViewController!.evaluateJavascript(source: callback);
    } catch (e) {
      Log.e(e.toString());
    }
  }

  // 发送签名数据
  Future<void> onSendResutl(String result) async {
    if (webViewController == null || onLoadError.value) return;
    isSendResutling = true;
    String initSrc = CallBackJs.jsSendResult;
    String callback = initSrc
        .replaceAll('%1\$s', CallBackJs.evmNetwork)
        .replaceAll('%2\$s', callbackId.toString())
        .replaceAll('%3\$s', result);
    Log.r(callback);
    await webViewController!.evaluateJavascript(source: callback);
  }

  /// 处理 Ethereum签名消息
  Future<void> handleEthereumMessage(
      DappMethod dappMethod, JsCallbackModel? jsDataModel) async {
    if (jsDataModel == null) return;
    if (!isAlive.value) {
      return;
    }

    try {
      isPending.value = true;
      callbackId = jsDataModel.id!;
      signMethod = jsDataModel.name;
      visibleData.value = true;
      visibleRawData.value = true;
      showSignMessageDalog();
      if (dappMethod == DappMethod.signMessage ||
          dappMethod == DappMethod.signPersonalMessage) {
        String message = jsDataModel.object!["data"];

        rawData = message;
        uftMessage = rawData!.hexToUtf8;

        if (Get.isEmptyString(rawData)) {
          return;
        }

        tsModel = TransferModel(
            chain: chain,
            addressPath: addressModel!.path,
            walletId: addressModel!.walletId,
            wallet: wallet,
            type: dappMethod == DappMethod.signPersonalMessage
                ? TransferType.signPersonalMessage
                : TransferType.signMessage);

        tsModel!.signMessage =
            await Get.walletCore.getSignPersonalMessage(message);
      } else if (dappMethod == DappMethod.signTypedMessage) {
        String signData = jsDataModel.object!["data"];
        String raw = jsDataModel.object!["raw"];

        // 将 raw 字符串解码为 Map
        Map<String, dynamic> rawObj = json.decode(raw);

        // 检查 messageObj 的类型
        dynamic message = rawObj['message'];
        Map<dynamic, dynamic>? messageObj;

        if (message is String) {
          // 如果 message 是字符串，则将其解码
          messageObj = json.decode(message);
        } else if (message is Map) {
          // 如果 message 已经是 Map，则直接使用
          messageObj = message;
        } else {
          // 处理不符合预期的情况
          messageObj = {};
        }

        // 将 messageObj 转换为 JSON 字符串（格式化输出）
        uftMessage = const JsonEncoder.withIndent('  ').convert(messageObj);

        rawData = signData;

        if (Get.isEmptyString(rawData)) {
          return;
        }

        tsModel = TransferModel(
            chain: chain,
            addressPath: addressModel!.path,
            walletId: addressModel!.walletId,
            wallet: wallet,
            type: TransferType.signTypedMessage);

        tsModel!.signMessage = signData;
      }
      qrCodeType = QRCodeType.signMessage;
      isPending.value = false;
    } catch (e) {
      onSendError(e.toString());
    }
  }

  Future<void> switchEthereumChain(JsCallbackModel? jsDataModel) async {
    if (jsDataModel == null) return;
    if (!isAlive.value) {
      return;
    }
    String chainIdHex = jsDataModel.object!["chainId"];
    callbackId = jsDataModel.id!;

    try {
      Log.r(
          "switchEthereumChain coinId =${DecimalUtils.toIntSafe(chainIdHex)}");

      await loadChainData(
        selectChainId: DecimalUtils.toIntSafe(chainIdHex),
      );
      await onSendNetWork();
      onSendAddress();
      reload();
    } catch (e) {
      onSendError(e.toString());
    }
  }

  Future<void> addEthereumChain(JsCallbackModel? jsDataModel) async {
    if (jsDataModel == null) return;
    if (!isAlive.value) {
      return;
    }
    String chainIdHex = jsDataModel.object!["chainId"];
    callbackId = jsDataModel.id!;

    try {
      Log.r(
          "switchEthereumChain coinId =${DecimalUtils.toIntSafe(chainIdHex)}");

      await loadChainData(
        selectChainId: DecimalUtils.toIntSafe(chainIdHex),
      );
      await onSendNetWork();
      onSendAddress();
    } catch (e) {
      onSendError(e.toString());
    }
  }

  Future<void> onSwitchWallet() async {
    try {
      await onSendNetWork();
      await onSendAddress();
      reload();
    } catch (e) {
      onSendError(e.toString());
    }
  }

  Future<void> signTransaction(JsCallbackModel? jsDataModel) async {
    if (jsDataModel == null) return;
    if (!isAlive.value) {
      return;
    }

    try {
      isPending.value = true;
      showSignTransactionDialog();

      var txObj = jsDataModel.object; // 假设 obj 是一个 Map<String, dynamic> 类型
      if (txObj == null) {
        return; // 或者根据需要处理返回
      }
      callbackId = jsDataModel.id!;

      String from = "";
      if (txObj.containsKey("from")) {
        from = (txObj["from"] == null ||
                (txObj["from"] is String && txObj["from"].isEmpty))
            ? ""
            : txObj["from"];

        fromList!.clear();
        fromList!.add(from);
      }

      String to = "";
      if (txObj.containsKey("to")) {
        to = (txObj["to"] == null ||
                (txObj["to"] is String && txObj["to"].isEmpty))
            ? ""
            : txObj["to"];

        if (to.isEmpty) {
          to = emptyAddress;
        }

        toList!.clear();
        toList!.add(to);
      }

      String? value; // 使用可空类型

      if (txObj.containsKey("value")) {
        value = (txObj["value"] == null ||
                (txObj["value"] is String && txObj["value"].isEmpty))
            ? null
            : txObj["value"];

        if (value != null) {
          String valueStr = value.hexToDecimal;
          if (DecimalUtils.isZeros(valueStr)) {
            amount = "0";
          } else {
            amount = DecimalUtils.divide(
                valueStr, pow(10, EthereumChain.get.decimals).toString(),
                scale: EthereumChain.get.decimals);
          }
          amountText = "$amount ${coinModel!.symbol!}";
        }
      }

      if (txObj.containsKey("data")) {
        rawData = (txObj["data"] == null ||
                (txObj["data"] is String && txObj["data"].isEmpty))
            ? ""
            : txObj["data"];
        isEip1559Transaction =
            await Get.walletCore.isEip1559Transaction(rawData!) ?? false;
      }

      tsModel = TransferModel(
        chain: coinType!.chain,
        addressPath: addressModel!.path,
        walletId: addressModel!.walletId,
        wallet: wallet,
        type: TransferType.dApp,
        contract: to,
        toAddress: to,
        amount: amount,
        isEip1559: isEip1559Transaction,
        fromAddress: from,
        coinSymbol: coinModel!.symbol,
        coinDecimal: CoinBase.usdtContractList.contains(to)
            ? UsdtMain.get.balanceDecimals
            : coinModel!.chainDecimal,
      );

      requestData(from: from, to: to, valueAmount: value!);
    } catch (e) {
      Get.back();
      onSendError(e.toString());
      isPending.value = false;
    }
  }

  Future<void> requestData({
    required String from,
    required String to,
    required String valueAmount,
  }) async {
    List<Future<dynamic>> futures = [];

    Extra extra = Extra();

    futures.add(blockChainService
        .isApproveTransaction(BlockChainParamsManager.createParams(
      method: BlockChainAPI.isApproveTransaction,
      requestParams: RequestParams()
          .put(APIConstant.chain, chain)
          .put(APIConstant.inputData, rawData)
          .getRequestBody(),
    )));

    futures.add(blockChainService.getGasLimit(
        BlockChainParamsManager.createParams(
            method: BlockChainAPI.gasLimit,
            requestParams: RequestParams()
                .put(APIConstant.chain, chain)
                .put(
                    APIConstant.params,
                    RequestParams()
                        .put(APIConstant.to, to)
                        .put(APIConstant.data, rawData)
                        .put(APIConstant.value, valueAmount)
                        .getRequestBody())
                .getRequestBody())));

    futures.add(blockChainService.getFeeGear(
        BlockChainParamsManager.createParams(
            method: isEip1559Transaction
                ? BlockChainAPI.feeGearEIP1559
                : BlockChainAPI.feeGear,
            requestParams: RequestParams()
                .put(APIConstant.chain, chain)
                .getRequestBody())));

    futures.add(
        blockChainService.getTokenBalance(BlockChainParamsManager.createParams(
      method: BlockChainAPI.getTokenBalance,
      requestParams: RequestParams()
          .put(APIConstant.chain, chain)
          .put(APIConstant.address, address)
          .put(APIConstant.type, '')
          .put(APIConstant.extra, extra.toJson())
          .getRequestBody(),
    )));

    multiHttpRequest(
        futures,
        showToast: true,
        (value) async {
          if (value != null) {
            showSuccess();
          }

          _handleApproveData(value[0].data);
          _handleGasLimit(value[1].data);
          _handleFee(value[2].data);
          _handleBalanceData(value[3].data);
          handelFeeValue();

          visibleData.value = true;
          visibleRawData.value = true;
          tsModel!.isDapp = true;
          tsModel!.signDataHash = rawData;
          qrCodeType = QRCodeType.transfer;

          isPending.value = false;
        },
        handleError: false,
        error: (e) {
          onSendError(e.toString());
          isPending.value = false;
          Get.back();
        });
  }

  void _handleApproveData(dynamic data) {
    // 获取字段
    isApproveTransaction = data['is_approve'] ?? false; // 使用 ?? 提供默认值
    approveContractAddress.clear();
    approveContractAddress.add(data['contract'] ?? '');
  }

  Future<void> _handleFee(dynamic data) async {
    if (data != null) {
      /// 更新档位矿工费
      updateGears(data);
    }
  }

  void _handleBalanceData(dynamic data) {
    balanceModel = BalanceModel.fromJson(data);
    if (balanceModel != null) {
      mainChainBalance =
          BalanceManager.getBalance(balanceModel!.balance!, coinModel);
      tsModel!.nonce = balanceModel!.nonce!;
    }
  }

  void _handleGasLimit(dynamic data) {
    // 默认
    gasLimit = coinType!.gasLimit ?? '';

    if (data != null && data is String) {
      gasLimit = data.hexToDecimal;
    }

    // 不是自定义才刷新
    if (tsModel!.feeType != FeeType.customize) {
      tsModel!.gasLimit = gasLimit;
    }

    /// 记录gas
    tsModel!.rawGasLimit = gasLimit;
  }

  void updateGears(List data, {FeeType? type}) {
    feeGearList = data.map((e) {
      FeeGearModel model = _gearModelFromJson(e, type: type);

      if (model.feeType == FeeType.normal) {
        gearModel = model;
      }

      return model;
    }).toList();
  }

  FeeGearModel _gearModelFromJson(Map<String, dynamic> json, {FeeType? type}) {
    FeeGearModel model = FeeGearModel.fromJson(json, coinType);
    if (Get.isEmptyString(model.fee)) {
      model.fee = '0';
    }

    if (model.fee! == 'null') {
      model.fee = '0';
    }

    model.fee = model.fee!.div('1000000000', scale: 2, roundMode: RoundMode.up);

    if (type != null) {
      if (type == FeeType.slow) {
        model.name = 'rear_block_fee';
      } else if (type == FeeType.normal) {
        model.name = 'estimatefee';
      } else if (type == FeeType.fast) {
        model.name = 'top_block_fee';
      }
      model.name = 'name';
    }
    return model;
  }

  void handelFeeValue({bool isCustomize = false}) {
    if (!isAlive()) {
      return;
    }
    if (isCustomize) {
      gasLimit = tsModel!.gasLimit!;
    } else {
      gasLimit = tsModel!.rawGasLimit!;
    }
    if (isEip1559Transaction) {
      if (!isCustomize) {
        tsModel!.maxFee = gearModel!.maxFee;
        tsModel!.baseFee = gearModel!.baseFee;
        tsModel!.maxPriorityFee = gearModel!.maxPriorityFee;
        tsModel!.minPriorityFee = gearModel!.minPriorityFee;
      }
      maxFee = tsModel!.maxFee!;
      feeValue = EthereumChain.get.calculateETHSeriesEip1559Fee(
              maxFee: maxFee, gasLimit: gasLimit) ??
          '';
      if (feeValue.isEmpty) {
        return;
      }

      minPriorityFee = gearModel!.minPriorityFee ?? "";
      maxPriorityFee = gearModel!.maxPriorityFee ?? "";
      baseFee = gearModel!.baseFee ?? "";
      String minFee = "";
      if (baseFee.isNotEmpty && minPriorityFee.isNotEmpty) {
        minFee = EthereumChain.get.calculateETHSeriesEip1559Fee(
                maxFee: baseFee.add(minPriorityFee), gasLimit: gasLimit) ??
            '';
      }

      bool isHasRange =
          minFee.isNotEmpty && DecimalUtils.compare(feeValue, minFee);
      String newFee2 = "";

      if (isHasRange) {
        newFee2 =
            "$minFee ${coinModel!.symbol!} ~ $feeValue ${coinModel!.symbol!}";
      } else {
        newFee2 = "$feeValue ${coinModel!.symbol!}";
      }

      fee.value = newFee2;

      StringBuffer stringBuilder = StringBuffer();
      stringBuilder
          .write("=Gas($gasLimit)*MaxFee(${maxFee.decimal(scale: 2)})");
      feePrice.value = stringBuilder.toString();
    } else {
      gasPrice = gearModel!.fee ?? "";
      if (!isCustomize) {
        tsModel!.gasPrice = gasPrice;
      }

      feeValue = EthereumChain.get
              .calculateETHSeriesFee(gasPrice: gasPrice, gasLimit: gasLimit) ??
          '';

      fee.value = '$feeValue ${coinModel!.symbol!}';

      StringBuffer stringBuilder = StringBuffer();
      stringBuilder
          .write("=Gas($gasLimit)*GasPrice($gasPrice${CommonConstant.gWEI})");
      feePrice.value = stringBuilder.toString();
    }
  }
}
