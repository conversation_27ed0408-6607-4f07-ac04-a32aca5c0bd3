/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-27 15:16:03
 */
class PsbtModel {
  bool isParseError = false;

  List<FromModel>? fromModelList;
  List<ToModel>? toModelList;

  List<FromModel>? getFromModelList() {
    return fromModelList;
  }

  void setFromModelList(List<FromModel> fromModelList) {
    this.fromModelList = fromModelList;
  }

  List<ToModel>? getToModelList() {
    return toModelList;
  }

  void setToModelList(List<ToModel> toModelList) {
    this.toModelList = toModelList;
  }

  bool isPsbtParseError() {
    return isParseError;
  }

  void setParseError(bool parseError) {
    isParseError = parseError;
  }
}

class FromModel {
  String? address;
  int? value;

  String? getAddress() {
    return address;
  }

  void setAddress(String address) {
    this.address = address;
  }

  int? getValue() {
    return value;
  }

  void setValue(int value) {
    this.value = value;
  }
}

class ToModel {
  String? address;
  int? value;

  String? getAddress() {
    return address;
  }

  void setAddress(String address) {
    this.address = address;
  }

  int? getValue() {
    return value;
  }

  void setValue(int value) {
    this.value = value;
  }
}
