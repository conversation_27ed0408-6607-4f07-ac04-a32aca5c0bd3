// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      inputs: (json['inputs'] as List<dynamic>?)
          ?.map((e) => Input.fromJson(e as Map<String, dynamic>))
          .toList(),
      globalMap: json['globalMap'] == null
          ? null
          : GlobalMap.fromJson(json['globalMap'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$<PERSON>o<PERSON>son(Data instance) => <String, dynamic>{
      'inputs': instance.inputs,
      'globalMap': instance.globalMap,
    };
