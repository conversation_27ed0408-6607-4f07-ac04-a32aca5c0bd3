/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-27 17:07:06
 */
import 'package:json_annotation/json_annotation.dart';

part 'v_output.g.dart';

@JsonSerializable()
class VOutput {
  String? address;
  int? value;

  VOutput({this.address, this.value});

  factory VOutput.fromJson(Map<String, dynamic> json) {
    return _$VOutputFromJson(json);
  }

  Map<String, dynamic> toJson() => _$VOutputToJson(this);
}
