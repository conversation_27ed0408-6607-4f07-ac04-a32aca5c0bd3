// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'psbt_raw_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PsbtRawModel _$PsbtRawModelFromJson(Map<String, dynamic> json) => PsbtRawModel(
      psbt: json['psbt'] == null
          ? null
          : Psbt.fromJson(json['psbt'] as Map<String, dynamic>),
      txInputs: (json['txInputs'] as List<dynamic>?)
          ?.map((e) => TxInput.fromJson(e as Map<String, dynamic>))
          .toList(),
      txOutputs: (json['txOutputs'] as List<dynamic>?)
          ?.map((e) => TxOutput.fromJson(e as Map<String, dynamic>))
          .toList(),
      vInput: (json['vInput'] as List<dynamic>?)
          ?.map((e) => VInput.fromJson(e as Map<String, dynamic>))
          .toList(),
      vOutput: (json['vOutput'] as List<dynamic>?)
          ?.map((e) => VOutput.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PsbtRawModelToJson(PsbtRawModel instance) =>
    <String, dynamic>{
      'psbt': instance.psbt,
      'txInputs': instance.txInputs,
      'txOutputs': instance.txOutputs,
      'vInput': instance.vInput,
      'vOutput': instance.vOutput,
    };
