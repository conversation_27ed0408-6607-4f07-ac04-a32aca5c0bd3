// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hash.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Hash _$Hash<PERSON>rom<PERSON>son(Map<String, dynamic> json) => Hash(
      type: json['type'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$HashToJson(Hash instance) => <String, dynamic>{
      'type': instance.type,
      'data': instance.data,
    };
