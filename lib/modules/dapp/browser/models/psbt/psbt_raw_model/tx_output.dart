import 'package:json_annotation/json_annotation.dart';

import 'script.dart';

part 'tx_output.g.dart';

@JsonSerializable()
class TxOutput {
  Script? script;
  int? value;
  String? address;

  TxOutput({this.script, this.value, this.address});

  factory TxOutput.fromJson(Map<String, dynamic> json) {
    return _$TxOutputFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TxOutputToJson(this);
}
