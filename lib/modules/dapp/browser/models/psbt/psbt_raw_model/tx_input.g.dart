// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tx_input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TxInput _$TxInputFromJson(Map<String, dynamic> json) => TxInput(
      hash: json['hash'] == null
          ? null
          : Hash.fromJson(json['hash'] as Map<String, dynamic>),
      index: (json['index'] as num?)?.toInt(),
      sequence: (json['sequence'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TxInputToJson(TxInput instance) => <String, dynamic>{
      'hash': instance.hash,
      'index': instance.index,
      'sequence': instance.sequence,
    };
