import 'package:json_annotation/json_annotation.dart';

import 'psbt.dart';
import 'tx_input.dart';
import 'tx_output.dart';
import 'v_input.dart';
import 'v_output.dart';

part 'psbt_raw_model.g.dart';

@JsonSerializable()
class PsbtRawModel {
  Psbt? psbt;
  List<TxInput>? txInputs;
  List<TxOutput>? txOutputs;
  List<VInput>? vInput;
  List<VOutput>? vOutput;

  PsbtRawModel({
    this.psbt,
    this.txInputs,
    this.txOutputs,
    this.vInput,
    this.vOutput,
  });

  factory PsbtRawModel.fromJson(Map<String, dynamic> json) {
    return _$PsbtRawModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PsbtRawModelToJson(this);
}
