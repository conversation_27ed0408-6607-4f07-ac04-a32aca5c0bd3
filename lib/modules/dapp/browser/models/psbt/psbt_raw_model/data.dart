/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-27 14:35:35
 */
import 'package:json_annotation/json_annotation.dart';

import 'global_map.dart';
import 'input.dart';

part 'data.g.dart';

@JsonSerializable()
class Data {
  List<Input>? inputs;
  GlobalMap? globalMap;

  Data({this.inputs, this.globalMap});

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}
