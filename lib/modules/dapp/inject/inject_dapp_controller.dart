import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/modules/dapp/browser/dapp_browser_controller.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';

extension InjectDappController on WebController {
  // 获取Bitcoin注入 init js
  static const String bitcoinInitJsFiles = 'assets/dapp/init_bitcoin.js';

  // 获取Bitcoin注入js
  static const List<String> bitcoinJsFiles = [
    'assets/dapp/bitcoin_wallet.js',
    'assets/dapp/bitcoin_psbt_callback.js',
  ];
  // 获取注入ethereum js
  static const String ethereumJsFiles = 'assets/dapp/ethereum_min.js';

  // 获取注入ethereum init js
  static const String ethereumInitJsFiles = 'assets/dapp/init_ethereum.js';

  // 获取注入ethereum init js no Address
  static const String ethereumInitJsFilesNoAddress =
      'assets/dapp/init_ethereum_no_address.js';

  //注入dapp js
  Future<void> injectjs() async {
    if (!showWalletAction()) {
      return;
    }
    if (webViewController == null) {
      return;
    }
    if (onLoadError.value) {
      return;
    }
    if (!isAlive.value) {
      return;
    }
    try {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Log.r('injectjs');
        _removeAllJsHandlers();
        _addJsHandlers();
        injectEthereumJs();
        injectBitcoinJs();
      });
    } catch (e) {
      Log.e("injectjs error ");
    }
  }

  Future<void> injectBitcoinJs() async {
    String? bitCoinDappModelsJs = await loadBitCoinDappModelsJs();
    if (bitCoinDappModelsJs != null &&
        !Get.isEmptyString(bitCoinDappModelsJs)) {
      Log.r('bitCoinDappModelsJs=$bitCoinDappModelsJs');
      if (!isClosed &&
          !onLoadError.value &&
          isAlive.value &&
          webViewController != null) {
        await webViewController!
            .evaluateJavascript(source: bitCoinDappModelsJs);
      }
    }
    String bitCoinInitJs = await loadBitCoinInitJs();
    if (!isClosed &&
        !onLoadError.value &&
        isAlive.value &&
        webViewController != null) {
      await webViewController!.evaluateJavascript(source: bitCoinInitJs);
    }
    // 循环注入 JavaScript 文件
    for (String jsFile in bitcoinJsFiles) {
      if (!isClosed &&
          !onLoadError.value &&
          isAlive.value &&
          webViewController != null) {
        await webViewController!
            .injectJavascriptFileFromAsset(assetFilePath: jsFile);
      }
    }
  }

  Future<void> injectEthereumJs() async {
    if (!isClosed &&
        !onLoadError.value &&
        isAlive.value &&
        webViewController != null) {
      await webViewController!
          .injectJavascriptFileFromAsset(assetFilePath: ethereumJsFiles);
    }
    String ethereumInitJs = await loadEthereumInitJs();
    Log.r('ethereumInitJs=$ethereumInitJs');
    if (!isClosed &&
        !onLoadError.value &&
        isAlive.value &&
        webViewController != null) {
      await webViewController!.evaluateJavascript(source: ethereumInitJs);
    }
  }

  //注入bitcoin 初始化 js
  Future<String> loadBitCoinInitJs() async {
    String network =
        (coinType != null && coinType!.isTestNet) ? "signet" : "mainnet";
    String mempoolApi = !Get.isEmptyString(rpcUrl) ? rpcUrl : bitCoinMempoolApi;
    String initSrc = await rootBundle.loadString(bitcoinInitJsFiles);
    String initJs =
        initSrc.replaceAll('%1\$s', mempoolApi).replaceAll('%2\$s', network);

    Log.r('Js=$initJs');
    return initJs;
  }

  Future<String?> loadBitCoinDappModelsJs() async {
    if (dappModel != null && dappModel!.js != null) {
      return dappModel!.js! as String;
    }
    return null;
  }

  //注入evm 初始化 js
  Future<String> loadEthereumInitJs() async {
    isApproveAddress = await isApprove();
    String formattedJs;
    if (rpcUrl.isEmpty) {
      setRpcUrl();
    }

    if (defaultEthereumAddress.isEmpty) {
      AddressModel? addressModel =
          await loadDefaultAddressModel(EthereumChain.get.chain);
      defaultEthereumAddress = addressModel!.address!;
    }

    String initSrc = await rootBundle.loadString(ethereumInitJsFiles);
    // 使用 %1$s, %2$s, %3$d 进行格式化
    formattedJs = initSrc
        .replaceAll('%1\$s', defaultEthereumAddress) // 钱包地址
        .replaceAll('%2\$s', rpcUrl) // RPC URL
        .replaceAll('%3\$s', chainId.toString()); // 链 ID
    return formattedJs;
  }

  // 添加 JavaScript 处理程序
  void setupJavaScriptHandlers(InAppWebViewController wController) async {
    Log.r('setupJavaScriptHandlers');
    isAlive.value = true;
    onWebViewCreated.value = false;
    webViewController = wController;
    if (!showWalletAction()) return;
  }

  void _removeAllJsHandlers() {
    webViewController!.removeAllUserScripts();
    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.getAddress);
    webViewController!.removeJavaScriptHandler(
        handlerName: BitCoinJsCallBack.getPublicKeyHex);

    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.getNetwork);
    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.signPsbt);
    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.signMessage);
    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.accountChanged);

    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.getBBNAddress);

    webViewController!.removeJavaScriptHandler(
        handlerName: BitCoinJsCallBack.getBBNPublicKeyHex);

    webViewController!
        .removeJavaScriptHandler(handlerName: BitCoinJsCallBack.signBBN);
    webViewController!
        .removeJavaScriptHandler(handlerName: EthereumJsCallBack.ethereumjs);
  }

  void _addJsHandlers() {
    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.getAddress,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.getAddress, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.getPublicKeyHex,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.getPublicKeyHex, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.signPsbt,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.signPsbt, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.signMessage,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.signMessage, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.accountChanged,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.accountChanged, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: EthereumJsCallBack.ethereumjs,
      callback: (arg) => handleJsonData(EthereumJsCallBack.ethereumjs, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.getBBNAddress,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.getBBNAddress, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.getBBNPublicKeyHex,
      callback: (arg) =>
          handleJsonData(BitCoinJsCallBack.getBBNPublicKeyHex, arg),
    );

    webViewController!.addJavaScriptHandler(
      handlerName: BitCoinJsCallBack.signBBN,
      callback: (arg) => handleJsonData(BitCoinJsCallBack.signBBN, arg),
    );
  }
}
