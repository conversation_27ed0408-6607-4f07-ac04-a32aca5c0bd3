/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-02-28 15:03:09
 */

const String bitCoinMempoolApi = "https://mempool.coldlar.com";
const String babylonchain = "https://btcstaking.btc-mainnet.babylonchain.io";
const String babyAirDrop = "https://airdrop.babylon.foundation";

class CallBackJs {
  static const String evmNetwork = "ethereum";
  static const String jsSetAddress = "window.%1\$s.setAddress('%2\$s');";
  static const String jsSendResponse =
      "window.%1\$s.sendResponse(%2\$s, ['%3\$s'])";
  static const String jsSendError = "window.%1\$s.sendError(%2\$s, '%3\$s')";
  static const String jsSendResult =
      "window.%1\$s.sendResponse(%2\$s, '%3\$s')";

  static const String jsSetBtcAddress = "window.btcwallet.setAddress('%1\$s');";
  static const String jsSetBtcPublicKeyHex =
      "window.btcwallet.setPublicKey('%1\$s')";
  static const String jsSetPsbtList = "window.btcwallet.setPsbtList('%1\$s')";
  static const String jsSetPsbtMessage =
      "window.btcwallet.setSignMessage('%1\$s')";
  static const String jsSetBBNAddress = "window.bbnwallet.setAddress('%1\$s');";
  static const String jsSetBBNPublicKeyHex =
      "window.bbnwallet.setPublicKey('%1\$s')";
  static const String jsSetSignBBN = "window.bbnwallet.setSignature('%1\$s')";
}

class DappMode {
  static const int defaultModel = 0;

  static const int serrchMode = 1;

  static const int rentMode = 2;

  static const int favoritesMode = 3;
}

class EthereumJsCallBack {
  static const String ethereumjs = "OrangeHandler";
  static const String requestAccounts = "requestAccounts";
  static const String switchEthereumChain = "switchEthereumChain";
  static const String signTransaction = "signTransaction";
  static const String signMessage = "signMessage";
  static const String signPersonalMessage = "signPersonalMessage";
  static const String signTypedMessage = "signTypedMessage";
}

class BitCoinJsCallBack {
  static const String getAddress = "_getAddress_";
  static const String getPublicKeyHex = "_getPublicKeyHex_";
  static const String signPsbt = "_signPsbt_";
  static const String signMessage = "_signMessage_";
  static const String accountChanged = "_accountChanged_";
  static const String getNetwork = "_getNetwork_";
  static const String getBBNAddress = "_getBBNAddress_";
  static const String getBBNPublicKeyHex = "_getBBNPublicKeyHex_";
  static const String signBBN = "_signBBN_";
}

// Enumeration for DApp methods
enum DappMethod {
  // Ethereum
  signTransaction,
  signPersonalMessage,
  signMessage,
  signTypedMessage,
  ecRecover,
  requestAccounts,
  watchAsset,
  addEthereumChain,
  switchEthereumChain,
  unknown,
  // Bitcoin-related methods as static constants
  getBitcoinAddress,
  getBitcoinPublicKeyHex,
  signPsbt,
  signBitcoinMessage,
  bitcoinAccountChanged,
  getBitcoinNetwork,
  getBabyAddress,
  getBabyPublicKeyHex,
  signBaby,
}

class DappMethodManager {
  static DappMethod fromValue(String value) {
    switch (value) {
      case 'signTransaction':
        return DappMethod.signTransaction;
      case 'signPersonalMessage':
        return DappMethod.signPersonalMessage;
      case 'signMessage':
        return DappMethod.signMessage;
      case 'signTypedMessage':
        return DappMethod.signTypedMessage;
      case 'ecRecover':
        return DappMethod.ecRecover;
      case 'requestAccounts':
        return DappMethod.requestAccounts;
      case 'watchAsset':
        return DappMethod.watchAsset;
      case 'addEthereumChain':
        return DappMethod.addEthereumChain;
      case 'switchEthereumChain':
        return DappMethod.switchEthereumChain;
      case '_getAddress_':
        return DappMethod.getBitcoinAddress;
      case '_getPublicKeyHex_':
        return DappMethod.getBitcoinPublicKeyHex;
      case '_signPsbt_':
        return DappMethod.signPsbt;
      case '_signMessage_':
        return DappMethod.signBitcoinMessage; // Renamed to avoid conflict
      case '_accountChanged_':
        return DappMethod.bitcoinAccountChanged;
      case '_getNetwork_':
        return DappMethod.getBitcoinNetwork;
      case '_getBBNAddress_':
        return DappMethod.getBabyAddress;
      case '_getBBNPublicKeyHex_':
        return DappMethod.getBabyPublicKeyHex;
      case '_signBBN_':
        return DappMethod.signBaby;
      default:
        return DappMethod.unknown;
    }
  }
}
