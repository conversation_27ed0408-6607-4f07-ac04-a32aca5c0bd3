/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-23 13:04:49
 */
/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:47:52
 * @LastEditTime: 2024-09-13 10:53:40
 */
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/response/base_response.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:flutter/material.dart';
import 'package:regexed_validator/regexed_validator.dart';
import 'package:wallet_core/wallet/wallet.dart';

class DappSearchController extends BaseController<ApiService> {
  var headerParameter = ''.obs;
  var dataList = <DappModels>[].obs;
  var isFocus = true.obs;
  var isSearching = false.obs;
  var isClearAll = false.obs;
  var textEditingController = TextEditingController();
  var searchHistoryList = <String>[].obs;

  late WalletModel? walletModel;
  late Wallet? wallet;
  var searchText = ''.obs;
  var isShowBrowser = false.obs;

  @override
  void onInit() {
    super.onInit();
    headerParameter.value =
        Get.arguments?[GetArgumentsKey.headerParameter] ?? "";
    walletModel = Get.arguments?[GetArgumentsKey.walletModel];
    wallet = Get.arguments?[GetArgumentsKey.wallet];
    debounce(searchText, (callback) => loadData());
  }

  @override
  void onReady() {
    super.onReady();
    loadHistory();
    loadData();
  }

  void loadHistory() {
    searchHistoryList.value =
        StorageManager.getStringReversedList(key: StorageKey.searchHistoryList);
  }

  @override
  void loadData() {
    if (Get.isEmptyString(searchText.value) || isShowBrowser.value) {
      dataList.clear();
      showSuccess();
      return;
    }
    isSearching.value = true;
    httpRequest<BaseResponse<dynamic>>(
        showToast: true,
        api.getDappList(
          version: 39,
          pageNum: "1",
          pageSize: "20",
          filter: searchText.value,
          deviceType: headerParameter.value,
        ),
        (value) {
          isSearching.value = false;
          if (value.data != null) {
            showSuccess();
            final Map<String, dynamic> decoded =
                Map<String, dynamic>.from(value.data);

            dataList.value = decoded['data']
                .map<DappModels>((item) => DappModels.fromJson(item))
                .toList();
          }
          if (dataList.isEmpty) {
            showEmpty();
          }
        },
        handleError: false,
        error: (e) {
          isSearching.value = false;
          showError();
        });
  }

  void clearAll() {
    searchHistoryList.clear();
    StorageManager.remove(key: StorageKey.searchHistoryList);
    isClearAll.value = false;
  }

  Future<void> saveHistory(String value) async {
    await StorageManager.addStringToListLimit(
        key: StorageKey.searchHistoryList, value: value);
    loadHistory();
  }

  void showBrowser() {
    isShowBrowser.value = validator
        .url(FileUtils.formatUrl(searchText.value, addHttpHeader: true));
  }

  void onClear() {
    textEditingController.clear();
    onValueChanged("");
  }

  void onValueChanged(String? value) {
    searchText.value = value ?? "";
    showBrowser();
  }

  void onFieldSubmitted(String? value) {
    saveHistory(value!);
    loadData();
  }

  void onActionSearch(String keyword) {
    searchText.value = keyword;
    showBrowser();
    textEditingController.text = keyword;
    loadData();
  }
}

class DappSearchBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DappSearchController());
  }
}
