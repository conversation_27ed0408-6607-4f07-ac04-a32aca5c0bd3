/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2025-02-18 13:57:53
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/modules/dapp/search/dapp_search_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/divider/divider_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SearchBrowserWidget extends BaseStatelessWidget<DappSearchController> {
  const SearchBrowserWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
        child: Obx(
      () => Visibility(
        visible: controller.isShowBrowser.value,
        child: HighLightInkWell(
          onTap: () => Get.openDapp(
              url: DappModels.getSearchDappUrl(FileUtils.formatUrl(
                  controller.searchText.value,
                  addHttpHeader: true)),
              walletModel: controller.walletModel,
              isOpenDappBrowserFromSearch: true,
              wallet: controller.wallet),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: Get.setPaddingSize(16),
                    vertical: Get.setPaddingSize(12)),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        controller.searchText.value,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: stylePrimary_16_m,
                      ),
                    ),
                    SizedBox(
                      width: Get.setPaddingSize(8),
                    ),
                    ImageWidget(
                      assetUrl: 'icon_new_arrow01',
                      width: Get.setImageSize(12),
                      height: Get.setImageSize(12),
                    )
                  ],
                ),
              ),
              const DividerWidget(),
            ],
          ),
        ),
      ),
    ));
  }
}
