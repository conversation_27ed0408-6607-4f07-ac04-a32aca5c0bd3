import 'package:coinbag/base/component/base_status_widget.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/modules/dapp/search/dapp_search_controller.dart';
import 'package:coinbag/modules/dapp/search/widgets/search_browser_widget.dart';
import 'package:coinbag/modules/dapp/search/widgets/search_chip_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SearchBodyWidget extends BaseStatusWidget<DappSearchController> {
  const SearchBodyWidget({super.key});

  @override
  buildContent(BuildContext context) {
    return Obx(() =>
        CustomScrollView(physics: const BouncingScrollPhysics(), slivers: [
          const SearchBrowserWidget(),
          const SearchChipWidget(),
          _labelWidget(ID.stringSearchResult.tr),
          SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
            return (controller.dataList.isNotEmpty)
                ? _itemBuilder(controller.dataList[index])
                : const SizedBox.shrink();
          }, childCount: controller.dataList.length)),
        ]));
  }

  SliverToBoxAdapter _labelWidget(String text) => SliverToBoxAdapter(
        child: Visibility(
          visible:
              controller.dataList.isNotEmpty && !controller.isShowBrowser.value,
          child: Padding(
            padding: EdgeInsets.fromLTRB(
                Get.setPaddingSize(16),
                Get.setPaddingSize(16),
                Get.setPaddingSize(16),
                Get.setPaddingSize(12)),
            child: Text(
              text,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: Get.setFontSize(14),
                  color: Get.theme.textSecondary),
            ),
          ),
        ),
      );

  HighLightInkWell _itemBuilder(DappModels model) {
    return HighLightInkWell(
      onTap: () => Get.openDapp(
          dappModels: model,
          walletModel: controller.walletModel,
          wallet: controller.wallet),
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(14),
            horizontal: Get.setPaddingSize(16)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageWidget(
              imageUrl: model.dAppLogo ?? "",
              width: Get.setImageSize(48),
              height: Get.setImageSize(48),
              radius: Get.setRadius(12),
            ),
            SizedBox(
              width: Get.setWidth(12),
            ),
            _textDapp(model),
          ],
        ),
      ),
    );
  }

  Expanded _textDapp(DappModels model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            model.dAppName ?? '',
            style: style_Dapp_Item,
          ),
          SizedBox(
            height: Get.setHeight(2),
          ),
          Text(
            model.abs ?? '',
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeightX.regular,
                fontFamily: Get.setFontFamily(),
                color: Get.theme.textSecondary),
          ),
        ],
      ),
    );
  }
}
