/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-23 11:21:12
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/modules/dapp/search/dapp_search_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class SearchChipWidget extends BaseStatelessWidget<DappSearchController> {
  const SearchChipWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
        child: Obx(
      () => Visibility(
        visible: controller.dataList.isEmpty &&
            controller.searchHistoryList.isNotEmpty &&
            !controller.isShowBrowser.value,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: Get.setPaddingSize(12)),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        ID.stringSearchHistory.tr,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: Get.setFontSize(14),
                            color: Get.theme.textSecondary),
                      ),
                    ),
                    !controller.isClearAll.value
                        ? GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () => controller.isClearAll.value = true,
                            child: Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: Get.setPaddingSize(2),
                                    horizontal: Get.setPaddingSize(4)),
                                color: Get.theme.bgColor,
                                child: ImageWidget(
                                  assetUrl: 'icon_del',
                                  width: Get.setImageSize(24),
                                  height: Get.setImageSize(24),
                                )),
                          )
                        : GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () => controller.clearAll(),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  vertical: Get.setPaddingSize(2),
                                  horizontal: Get.setPaddingSize(4)),
                              decoration: ShapeDecoration(
                                color: Get.theme.colorF3F3F5,
                                shape: RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.circular(Get.setRadius(32)),
                                ),
                              ),
                              child: Row(
                                children: [
                                  ImageWidget(
                                    assetUrl: 'icon_del',
                                    width: Get.setImageSize(24),
                                    height: Get.setImageSize(24),
                                  ),
                                  SizedBox(width: Get.setFontSize(4)),
                                  Text(
                                    ID.stringClearAll.tr,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: Get.setFontSize(12),
                                        color: Get.theme.textSecondary),
                                  ),
                                  SizedBox(width: Get.setFontSize(4)),
                                ],
                              ),
                            ),
                          )
                  ],
                ),
              ),
              Wrap(
                spacing: Get.setPaddingSize(8),
                runSpacing: Get.setPaddingSize(8),
                alignment: WrapAlignment.start,
                children: controller.searchHistoryList.map((String keyword) {
                  return ActionChip(
                      side: BorderSide.none, // 去除边线
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      key: ValueKey<String>(keyword),
                      backgroundColor: Get.theme.colorF3F3F5,
                      onPressed: () async => controller.onActionSearch(keyword),
                      labelStyle: TextStyle(
                          fontSize: Get.setFontSize(14),
                          color: Get.theme.textPrimary),
                      label: Text(
                        keyword,
                        style: TextStyle(
                            fontSize: Get.setFontSize(14),
                            color: Get.theme.textPrimary),
                      ));
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    ));
  }
}
