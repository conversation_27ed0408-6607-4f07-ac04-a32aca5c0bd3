import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/base/component/base_status_widget.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateless_widget.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/dapp/dapp_controller.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:coinbag/modules/dapp/widgets/dapp_grid_widget.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/widgets/appbar/base_app_bar.dart';
import 'package:coinbag/widgets/button/high_light_tab.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:coinbag/widgets/pull_refresh/pull_smart_refresher.dart';
import 'package:coinbag/widgets/skeleton/skeleton_list_page.dart';
import 'package:coinbag/widgets/status/app_empty_widget.dart';
import 'package:coinbag/widgets/status/app_error_widget.dart';
import 'package:flutter/material.dart';

class DappPage extends BaseStatelessWidget<DappController> {
  const DappPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: baseAppBar(title: ID.stringDiscover.tr, hideLeading: true),
        body: controller.obx((state) => const BodyWidget(),
            onLoading: const DappListSkeletonWidget(),
            onError: (error) => controller.hasCache
                ? const BodyWidget()
                : AppErrorWidget(onRefresh: () => controller.onRefresh()),
            onEmpty: const AppEmptyWidget()));
  }
}

class BodyWidget extends BaseStatusWidget<DappController> {
  const BodyWidget({super.key});

  @override
  buildContent(BuildContext context) {
    return Obx(() => RefreshWidget<DappController>(
        enablePullDown: true,
        enablePullUp: false,
        refreshController: controller.refreshController,
        child: CustomScrollView(
            controller: controller.scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              _searchWidget(),
              _labelWidget(ID.recent.tr, mode: DappMode.rentMode),
              DappGridWidget(
                  mode: DappMode.rentMode, dataList: controller.recentDataList),
              _seeAllButton(DappMode.rentMode),
              _labelWidget(ID.favorites.tr, mode: DappMode.favoritesMode),
              DappGridWidget(
                  mode: DappMode.favoritesMode,
                  dataList: controller.favoritesDataList),
              _seeAllButton(DappMode.favoritesMode),
              _labelWidget(ID.recommend.tr, mode: DappMode.defaultModel),
              SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                return (controller.dataList.isNotEmpty)
                    ? _itemBuilder(controller.dataList[index])
                    : const SizedBox.shrink();
              }, childCount: controller.dataList.length)),
            ])));
  }

  Widget _searchWidget() {
    return SliverPadding(
      padding: EdgeInsets.fromLTRB(
        Get.setPaddingSize(16),
        Get.setPaddingSize(16),
        Get.setPaddingSize(16),
        Get.setPaddingSize(8),
      ),
      sliver: SliverToBoxAdapter(
        child: Visibility(
          visible: controller.isWalletSupportDapp,
          child: GestureDetector(
            onTap: () => Get.toNamed(AppRoutes.dappSearchPage, arguments: {
              GetArgumentsKey.headerParameter: controller.headerParameter,
              GetArgumentsKey.walletModel: controller.walletModel,
              GetArgumentsKey.wallet: controller.wallet
            }),
            behavior: HitTestBehavior.translucent,
            child: Container(
              height: Get.setHeight(40),
              padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(12)),
              decoration: BoxDecoration(
                color: Get.theme.colorF3F3F5,
                borderRadius: BorderRadius.circular(34.0),
              ),
              child: Center(
                child: Row(
                  children: [
                    ImageWidget(
                      assetUrl: 'search_bar_search',
                      width: Get.setImageSize(16),
                      height: Get.setImageSize(16),
                    ),
                    SizedBox(
                      width: Get.setPaddingSize(6),
                    ),
                    Expanded(
                      child: Text(
                        ID.stringSearchDapp.tr,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: Get.setFontSize(14),
                          color: Get.theme.textTertiary,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _itemBuilder(DappModels model) {
    if (model.network != null &&
        model.network == "testnet" &&
        !AppConfig.instance.isTestNetWork) {
      return const SizedBox.shrink();
    }
    return HighLightInkWell(
      onTap: () => Get.openDapp(
          dappModels: model,
          walletModel: controller.walletModel,
          wallet: controller.wallet),
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: Get.setPaddingSize(14),
            horizontal: Get.setPaddingSize(16)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ImageWidget(
              imageUrl: model.dAppLogo ?? "",
              width: Get.setImageSize(48),
              height: Get.setImageSize(48),
              radius: Get.setRadius(12),
            ),
            SizedBox(
              width: Get.setWidth(12),
            ),
            _textDapp(model),
          ],
        ),
      ),
    );
  }

  SliverToBoxAdapter _labelWidget(String text, {int mode = 0}) =>
      SliverToBoxAdapter(
        child: Visibility(
          visible: controller.showLabel(mode),
          child: Padding(
            padding: EdgeInsets.fromLTRB(
                Get.setPaddingSize(16),
                Get.setPaddingSize(16),
                Get.setPaddingSize(16),
                Get.setPaddingSize(12)),
            child: Text(
              text,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: Get.setFontSize(20), color: Get.theme.textPrimary),
            ),
          ),
        ),
      );

  SliverToBoxAdapter _seeAllButton(int mode) => SliverToBoxAdapter(
          child: Visibility(
        visible: controller.showViewAll(mode),
        child: GestureDetector(
          onTap: () => controller.toDappManagerPage(mode),
          child: Container(
            height: Get.setHeight(44),
            margin: EdgeInsets.all(Get.setPaddingSize(12)),
            decoration: BoxDecoration(
              color: Get.theme.colorF9F9F9,
              borderRadius: BorderRadius.circular(22),
            ),
            alignment: Alignment.center,
            child: Text(
              ID.viewAll.tr,
              style: TextStyle(
                  fontSize: Get.setFontSize(14),
                  overflow: TextOverflow.ellipsis,
                  fontWeight: FontWeightX.medium,
                  fontFamily: Get.setFontFamily(),
                  color: Get.theme.textPrimary),
            ),
          ),
        ),
      ));

  Expanded _textDapp(DappModels model) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            model.dAppName ?? '',
            style: style_Dapp_Item,
          ),
          SizedBox(
            height: Get.setHeight(2),
          ),
          Text(
            model.abs ?? '',
            style: TextStyle(
                fontSize: Get.setFontSize(12),
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeightX.regular,
                fontFamily: Get.setFontFamily(),
                color: Get.theme.textSecondary),
          ),
        ],
      ),
    );
  }
}
