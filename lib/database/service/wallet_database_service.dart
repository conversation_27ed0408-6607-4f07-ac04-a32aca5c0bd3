/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-11-13 13:59:10
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:drift/drift.dart' as drift;
import 'package:get/get.dart';

class WalletDatabaseService {
  Future insertDatabase(
    AppDatabase appDatabase,
    WalletTableCompanion walletModel,
    List<MonitorTableCompanion> monitorList,
    List<AddressTableCompanion> addresList,
  ) async {
    await Future.wait([
      appDatabase.walletDao.insertOrUpdateWallet(walletModel),
      appDatabase.monitorDao.insertBatch(monitorList),
      appDatabase.addressDao.insertBatch(addresList),
      insertOnConflictUpdateAddressMapping(
          walletModel.deviceId.value!, walletModel.walletId.value!, addresList),
    ]);
  }

  Future deleteWallet(
    AppDatabase appDatabase,
    WalletModel oldWalletModel,
  ) async {
    String walletId = oldWalletModel.walletId!;
    await Future.wait([
      appDatabase.walletDao
          .deleteWalletAndUpdateChecked(walletId, oldWalletModel.deviceId!),
      appDatabase.monitorDao.deleteByWalletId(walletId),
      appDatabase.addressDao.deleteByWalletId(walletId),
      appDatabase.tokenDao.deleteByWalletId(walletId),
      appDatabase.transactionsActivityDao.deleteByWalletId(walletId),
      appDatabase.addressMappingDao.deleteByWalletIdOrDeviceId(
          walletId: walletId, deviceId: oldWalletModel.deviceId!),
    ]);
  }

  Future unBindWallet(
      {required String walletId, required String deviceId}) async {
    AppDatabase appDatabase = Get.find<AppDatabase>();

    appDatabase.addressMappingDao
        .deleteByWalletIdOrDeviceId(walletId: walletId, deviceId: deviceId);
    List<WalletModel> walletModeList =
        await appDatabase.walletDao.findAllWalletsByWalletId(walletId);

    ///只有一个钱包时才删除，防止多个相同钱包ID 不同设备时删除 其他钱包数据
    if (walletModeList.isEmpty) {
      await Future.wait([
        appDatabase.monitorDao.deleteByWalletId(walletId),
        appDatabase.addressDao.deleteByWalletId(walletId),
        appDatabase.tokenDao.deleteByWalletId(walletId),
        appDatabase.transactionsActivityDao.deleteByWalletId(walletId),
      ]);
    }
  }

  // 获取硬件钱包设备下某个链的所有地址，
  Future<List<String>> getWalletAddressList(
      {required String deviceId,
      required String walletId,
      required String chain,
      String? address}) async {
    // 获取地址映射
    List<AddressMappingModel> mappings = await Get.database.addressMappingDao
        .getAddressMappingList(deviceId, walletId, chain, address);

    // 从映射中提取地址列表
    List<String?> nullableAddresses =
        mappings.map((mapping) => mapping.address).toList();
    List<String> addresses = nullableAddresses
        .where((address) => address != null)
        .map((address) => address!)
        .toList();

    // 如果没有找到任何地址映射，返回空列表
    if (addresses.isEmpty) {
      return [];
    }

    return addresses;
  }

  // 批量插入关联地址和wallet
  Future insertOnConflictUpdateAddressMapping(String deviceId, String walletId,
      List<AddressTableCompanion> addresList) async {
    await Get.database.addressMappingDao.insertBatch(addresList.map((address) {
      return AddressMappingTableCompanion(
        deviceId: drift.Value(deviceId),
        walletId: drift.Value(walletId),
        chain: address.chain,
        address: address.address,
        addressIndex:
            address.addressIndex, // 确保 AddressTableCompanion 有 addressIndex 字段
      );
    }).toList());
  }
}
