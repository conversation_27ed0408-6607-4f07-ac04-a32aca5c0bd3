/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2025-03-13 13:47:10
 */
import 'package:coinbag/database/storage/models/storage_base_model.dart';
import 'package:get_storage/get_storage.dart';

class StorageManager {
  static final _box = GetStorage();

  /// 保存 value：String Int Double Map List
  static Future<T> saveValue<T>({required String key, required T value}) async {
    await _box.write(key, value);

    return value;
  }

  /// 获取
  static T? getValue<T>({required String key}) => _box.read(key);

  /// 保存对象
  static Future saveObject<T extends BaseStorageModel>(
      {required String key, required T obj}) async {
    await _box.write(key, obj.toJson());
    return obj;
  }

// 获取对象
  static T? getObject<T extends BaseStorageModel>({
    required String key,
    required T Function(Map<String, dynamic>) fromJson,
  }) {
    final dynamic jsonDynamic = _box.read(key);
    if (jsonDynamic == null) return null;
    if (jsonDynamic is! Map<String, dynamic>) {
      throw Exception(
          'Data retrieved from storage is not a Map<String, dynamic>');
    }
    return fromJson(jsonDynamic);
  }

  /// 删除
  static Future remove({required String key}) async => _box.remove(key);

  /// 添加字符串到列表
  static Future<void> saveStringList(
      {required String key, required String value}) async {
    List<String> currentList = getValue<List<String>>(key: key) ?? [];
    if (!currentList.contains(value)) {
      // 确保不重复
      currentList.add(value);
      await saveValue(key: key, value: currentList);
    }
  }

  /// 添加字符串到列表，最大存储 10 条
  static Future<void> addStringToListLimit(
      {required String key, required String value}) async {
    // Retrieve the list of dynamic type
    List<dynamic>? dynamicList = getValue<List<dynamic>>(key: key);

    // Initialize the current list, converting each item to String
    List<String> currentList = dynamicList?.map((item) {
          if (item is String) {
            return item;
          } else {
            // Handle the case where an item is not a String, e.g., throw an error or log it
            throw Exception('Item is not a String: $item');
          }
        }).toList() ??
        [];

    // 如果列表已满，移除最旧的条目
    if (currentList.length >= 10) {
      currentList.removeAt(0); // 移除第一条（最旧的）
    }

    // 添加新的条目（确保不重复）
    if (!currentList.contains(value)) {
      currentList.add(value);
      await saveValue(key: key, value: currentList);
    }
  }

  /// 获取字符串列表
  static List<String> getStringList({required String key}) {
    // Retrieve the list of dynamic type
    List<dynamic>? dynamicList = getValue<List<dynamic>>(key: key);

    // If the list is null, return an empty list
    if (dynamicList == null) {
      return [];
    }

    // Cast each element to String and create a new list
    List<String> stringList = dynamicList.map((item) {
      if (item is String) {
        return item;
      } else {
        // Handle the case where an item is not a String, e.g., throw an error or log it
        throw Exception('Item is not a String: $item');
      }
    }).toList();
    return stringList;
  }

  /// 获取字符串列表，返回倒序
  static List<String> getStringReversedList({required String key}) {
    // Return the reversed list
    return getStringList(key: key).reversed.toList();
  }

  /// 清空字符串列表
  static Future<void> clearStringList({required String key}) async {
    await _box.remove(key);
  }
}
