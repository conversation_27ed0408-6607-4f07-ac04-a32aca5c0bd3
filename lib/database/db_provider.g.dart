// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'db_provider.dart';

// ignore_for_file: type=lint
class $AddressBookTableTable extends AddressBookTable
    with TableInfo<$AddressBookTableTable, AddressBookModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AddressBookTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  @override
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _constantNameMeta =
      const VerificationMeta('constantName');
  @override
  late final GeneratedColumn<String> constantName = GeneratedColumn<String>(
      'constant_name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [id, chain, address, constantName];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'address_book_table';
  @override
  VerificationContext validateIntegrity(Insertable<AddressBookModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    } else if (isInserting) {
      context.missing(_chainMeta);
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    } else if (isInserting) {
      context.missing(_addressMeta);
    }
    if (data.containsKey('constant_name')) {
      context.handle(
          _constantNameMeta,
          constantName.isAcceptableOrUnknown(
              data['constant_name']!, _constantNameMeta));
    } else if (isInserting) {
      context.missing(_constantNameMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AddressBookModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AddressBookModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain'])!,
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address'])!,
      constantName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}constant_name'])!,
    );
  }

  @override
  $AddressBookTableTable createAlias(String alias) {
    return $AddressBookTableTable(attachedDatabase, alias);
  }
}

class AddressBookModel extends DataClass
    implements Insertable<AddressBookModel> {
  final int id;
  final String chain;
  final String address;
  final String constantName;
  const AddressBookModel(
      {required this.id,
      required this.chain,
      required this.address,
      required this.constantName});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['chain'] = Variable<String>(chain);
    map['address'] = Variable<String>(address);
    map['constant_name'] = Variable<String>(constantName);
    return map;
  }

  AddressBookTableCompanion toCompanion(bool nullToAbsent) {
    return AddressBookTableCompanion(
      id: Value(id),
      chain: Value(chain),
      address: Value(address),
      constantName: Value(constantName),
    );
  }

  factory AddressBookModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AddressBookModel(
      id: serializer.fromJson<int>(json['id']),
      chain: serializer.fromJson<String>(json['chain']),
      address: serializer.fromJson<String>(json['address']),
      constantName: serializer.fromJson<String>(json['constantName']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'chain': serializer.toJson<String>(chain),
      'address': serializer.toJson<String>(address),
      'constantName': serializer.toJson<String>(constantName),
    };
  }

  AddressBookModel copyWith(
          {int? id, String? chain, String? address, String? constantName}) =>
      AddressBookModel(
        id: id ?? this.id,
        chain: chain ?? this.chain,
        address: address ?? this.address,
        constantName: constantName ?? this.constantName,
      );
  AddressBookModel copyWithCompanion(AddressBookTableCompanion data) {
    return AddressBookModel(
      id: data.id.present ? data.id.value : this.id,
      chain: data.chain.present ? data.chain.value : this.chain,
      address: data.address.present ? data.address.value : this.address,
      constantName: data.constantName.present
          ? data.constantName.value
          : this.constantName,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AddressBookModel(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('address: $address, ')
          ..write('constantName: $constantName')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, chain, address, constantName);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AddressBookModel &&
          other.id == this.id &&
          other.chain == this.chain &&
          other.address == this.address &&
          other.constantName == this.constantName);
}

class AddressBookTableCompanion extends UpdateCompanion<AddressBookModel> {
  final Value<int> id;
  final Value<String> chain;
  final Value<String> address;
  final Value<String> constantName;
  const AddressBookTableCompanion({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.address = const Value.absent(),
    this.constantName = const Value.absent(),
  });
  AddressBookTableCompanion.insert({
    this.id = const Value.absent(),
    required String chain,
    required String address,
    required String constantName,
  })  : chain = Value(chain),
        address = Value(address),
        constantName = Value(constantName);
  static Insertable<AddressBookModel> custom({
    Expression<int>? id,
    Expression<String>? chain,
    Expression<String>? address,
    Expression<String>? constantName,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chain != null) 'chain': chain,
      if (address != null) 'address': address,
      if (constantName != null) 'constant_name': constantName,
    });
  }

  AddressBookTableCompanion copyWith(
      {Value<int>? id,
      Value<String>? chain,
      Value<String>? address,
      Value<String>? constantName}) {
    return AddressBookTableCompanion(
      id: id ?? this.id,
      chain: chain ?? this.chain,
      address: address ?? this.address,
      constantName: constantName ?? this.constantName,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (constantName.present) {
      map['constant_name'] = Variable<String>(constantName.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AddressBookTableCompanion(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('address: $address, ')
          ..write('constantName: $constantName')
          ..write(')'))
        .toString();
  }
}

class $MonitorTableTable extends MonitorTable
    with TableInfo<$MonitorTableTable, MonitorModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $MonitorTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainCodeMeta =
      const VerificationMeta('chainCode');
  @override
  late final GeneratedColumn<String> chainCode = GeneratedColumn<String>(
      'chain_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _publickeyMeta =
      const VerificationMeta('publickey');
  @override
  late final GeneratedColumn<String> publickey = GeneratedColumn<String>(
      'publickey', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _batchIdMeta =
      const VerificationMeta('batchId');
  @override
  late final GeneratedColumn<int> batchId = GeneratedColumn<int>(
      'batch_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _walletIdMeta =
      const VerificationMeta('walletId');
  @override
  late final GeneratedColumn<String> walletId = GeneratedColumn<String>(
      'wallet_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _slip44IdMeta =
      const VerificationMeta('slip44Id');
  @override
  late final GeneratedColumn<int> slip44Id = GeneratedColumn<int>(
      'slip44_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _addressNumMeta =
      const VerificationMeta('addressNum');
  @override
  late final GeneratedColumn<int> addressNum = GeneratedColumn<int>(
      'address_num', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _segwitTypeMeta =
      const VerificationMeta('segwitType');
  @override
  late final GeneratedColumn<int> segwitType = GeneratedColumn<int>(
      'segwit_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _pathMeta = const VerificationMeta('path');
  @override
  late final GeneratedColumn<String> path = GeneratedColumn<String>(
      'path', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _keyTypekeyTypeMeta =
      const VerificationMeta('keyTypekeyType');
  @override
  late final GeneratedColumn<int> keyTypekeyType = GeneratedColumn<int>(
      'key_typekey_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _isXPubMeta = const VerificationMeta('isXPub');
  @override
  late final GeneratedColumn<bool> isXPub = GeneratedColumn<bool>(
      'is_x_pub', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_x_pub" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        chain,
        chainCode,
        publickey,
        batchId,
        walletId,
        slip44Id,
        addressNum,
        segwitType,
        path,
        keyTypekeyType,
        isXPub
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'monitor_table';
  @override
  VerificationContext validateIntegrity(Insertable<MonitorModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('chain_code')) {
      context.handle(_chainCodeMeta,
          chainCode.isAcceptableOrUnknown(data['chain_code']!, _chainCodeMeta));
    }
    if (data.containsKey('publickey')) {
      context.handle(_publickeyMeta,
          publickey.isAcceptableOrUnknown(data['publickey']!, _publickeyMeta));
    }
    if (data.containsKey('batch_id')) {
      context.handle(_batchIdMeta,
          batchId.isAcceptableOrUnknown(data['batch_id']!, _batchIdMeta));
    }
    if (data.containsKey('wallet_id')) {
      context.handle(_walletIdMeta,
          walletId.isAcceptableOrUnknown(data['wallet_id']!, _walletIdMeta));
    }
    if (data.containsKey('slip44_id')) {
      context.handle(_slip44IdMeta,
          slip44Id.isAcceptableOrUnknown(data['slip44_id']!, _slip44IdMeta));
    }
    if (data.containsKey('address_num')) {
      context.handle(
          _addressNumMeta,
          addressNum.isAcceptableOrUnknown(
              data['address_num']!, _addressNumMeta));
    }
    if (data.containsKey('segwit_type')) {
      context.handle(
          _segwitTypeMeta,
          segwitType.isAcceptableOrUnknown(
              data['segwit_type']!, _segwitTypeMeta));
    }
    if (data.containsKey('path')) {
      context.handle(
          _pathMeta, path.isAcceptableOrUnknown(data['path']!, _pathMeta));
    }
    if (data.containsKey('key_typekey_type')) {
      context.handle(
          _keyTypekeyTypeMeta,
          keyTypekeyType.isAcceptableOrUnknown(
              data['key_typekey_type']!, _keyTypekeyTypeMeta));
    }
    if (data.containsKey('is_x_pub')) {
      context.handle(_isXPubMeta,
          isXPub.isAcceptableOrUnknown(data['is_x_pub']!, _isXPubMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MonitorModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MonitorModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      chainCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_code']),
      publickey: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}publickey']),
      batchId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}batch_id']),
      walletId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_id']),
      slip44Id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}slip44_id']),
      addressNum: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}address_num']),
      segwitType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}segwit_type']),
      path: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}path']),
      keyTypekeyType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}key_typekey_type']),
      isXPub: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_x_pub']),
    );
  }

  @override
  $MonitorTableTable createAlias(String alias) {
    return $MonitorTableTable(attachedDatabase, alias);
  }
}

class MonitorModel extends DataClass implements Insertable<MonitorModel> {
  final int id;
  final String? chain;
  final String? chainCode;
  final String? publickey;
  final int? batchId;
  final String? walletId;
  final int? slip44Id;
  final int? addressNum;
  final int? segwitType;
  final String? path;
  final int? keyTypekeyType;
  final bool? isXPub;
  const MonitorModel(
      {required this.id,
      this.chain,
      this.chainCode,
      this.publickey,
      this.batchId,
      this.walletId,
      this.slip44Id,
      this.addressNum,
      this.segwitType,
      this.path,
      this.keyTypekeyType,
      this.isXPub});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || chainCode != null) {
      map['chain_code'] = Variable<String>(chainCode);
    }
    if (!nullToAbsent || publickey != null) {
      map['publickey'] = Variable<String>(publickey);
    }
    if (!nullToAbsent || batchId != null) {
      map['batch_id'] = Variable<int>(batchId);
    }
    if (!nullToAbsent || walletId != null) {
      map['wallet_id'] = Variable<String>(walletId);
    }
    if (!nullToAbsent || slip44Id != null) {
      map['slip44_id'] = Variable<int>(slip44Id);
    }
    if (!nullToAbsent || addressNum != null) {
      map['address_num'] = Variable<int>(addressNum);
    }
    if (!nullToAbsent || segwitType != null) {
      map['segwit_type'] = Variable<int>(segwitType);
    }
    if (!nullToAbsent || path != null) {
      map['path'] = Variable<String>(path);
    }
    if (!nullToAbsent || keyTypekeyType != null) {
      map['key_typekey_type'] = Variable<int>(keyTypekeyType);
    }
    if (!nullToAbsent || isXPub != null) {
      map['is_x_pub'] = Variable<bool>(isXPub);
    }
    return map;
  }

  MonitorTableCompanion toCompanion(bool nullToAbsent) {
    return MonitorTableCompanion(
      id: Value(id),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      chainCode: chainCode == null && nullToAbsent
          ? const Value.absent()
          : Value(chainCode),
      publickey: publickey == null && nullToAbsent
          ? const Value.absent()
          : Value(publickey),
      batchId: batchId == null && nullToAbsent
          ? const Value.absent()
          : Value(batchId),
      walletId: walletId == null && nullToAbsent
          ? const Value.absent()
          : Value(walletId),
      slip44Id: slip44Id == null && nullToAbsent
          ? const Value.absent()
          : Value(slip44Id),
      addressNum: addressNum == null && nullToAbsent
          ? const Value.absent()
          : Value(addressNum),
      segwitType: segwitType == null && nullToAbsent
          ? const Value.absent()
          : Value(segwitType),
      path: path == null && nullToAbsent ? const Value.absent() : Value(path),
      keyTypekeyType: keyTypekeyType == null && nullToAbsent
          ? const Value.absent()
          : Value(keyTypekeyType),
      isXPub:
          isXPub == null && nullToAbsent ? const Value.absent() : Value(isXPub),
    );
  }

  factory MonitorModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MonitorModel(
      id: serializer.fromJson<int>(json['id']),
      chain: serializer.fromJson<String?>(json['chain']),
      chainCode: serializer.fromJson<String?>(json['chainCode']),
      publickey: serializer.fromJson<String?>(json['publickey']),
      batchId: serializer.fromJson<int?>(json['batchId']),
      walletId: serializer.fromJson<String?>(json['walletId']),
      slip44Id: serializer.fromJson<int?>(json['slip44Id']),
      addressNum: serializer.fromJson<int?>(json['addressNum']),
      segwitType: serializer.fromJson<int?>(json['segwitType']),
      path: serializer.fromJson<String?>(json['path']),
      keyTypekeyType: serializer.fromJson<int?>(json['keyTypekeyType']),
      isXPub: serializer.fromJson<bool?>(json['isXPub']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'chain': serializer.toJson<String?>(chain),
      'chainCode': serializer.toJson<String?>(chainCode),
      'publickey': serializer.toJson<String?>(publickey),
      'batchId': serializer.toJson<int?>(batchId),
      'walletId': serializer.toJson<String?>(walletId),
      'slip44Id': serializer.toJson<int?>(slip44Id),
      'addressNum': serializer.toJson<int?>(addressNum),
      'segwitType': serializer.toJson<int?>(segwitType),
      'path': serializer.toJson<String?>(path),
      'keyTypekeyType': serializer.toJson<int?>(keyTypekeyType),
      'isXPub': serializer.toJson<bool?>(isXPub),
    };
  }

  MonitorModel copyWith(
          {int? id,
          Value<String?> chain = const Value.absent(),
          Value<String?> chainCode = const Value.absent(),
          Value<String?> publickey = const Value.absent(),
          Value<int?> batchId = const Value.absent(),
          Value<String?> walletId = const Value.absent(),
          Value<int?> slip44Id = const Value.absent(),
          Value<int?> addressNum = const Value.absent(),
          Value<int?> segwitType = const Value.absent(),
          Value<String?> path = const Value.absent(),
          Value<int?> keyTypekeyType = const Value.absent(),
          Value<bool?> isXPub = const Value.absent()}) =>
      MonitorModel(
        id: id ?? this.id,
        chain: chain.present ? chain.value : this.chain,
        chainCode: chainCode.present ? chainCode.value : this.chainCode,
        publickey: publickey.present ? publickey.value : this.publickey,
        batchId: batchId.present ? batchId.value : this.batchId,
        walletId: walletId.present ? walletId.value : this.walletId,
        slip44Id: slip44Id.present ? slip44Id.value : this.slip44Id,
        addressNum: addressNum.present ? addressNum.value : this.addressNum,
        segwitType: segwitType.present ? segwitType.value : this.segwitType,
        path: path.present ? path.value : this.path,
        keyTypekeyType:
            keyTypekeyType.present ? keyTypekeyType.value : this.keyTypekeyType,
        isXPub: isXPub.present ? isXPub.value : this.isXPub,
      );
  MonitorModel copyWithCompanion(MonitorTableCompanion data) {
    return MonitorModel(
      id: data.id.present ? data.id.value : this.id,
      chain: data.chain.present ? data.chain.value : this.chain,
      chainCode: data.chainCode.present ? data.chainCode.value : this.chainCode,
      publickey: data.publickey.present ? data.publickey.value : this.publickey,
      batchId: data.batchId.present ? data.batchId.value : this.batchId,
      walletId: data.walletId.present ? data.walletId.value : this.walletId,
      slip44Id: data.slip44Id.present ? data.slip44Id.value : this.slip44Id,
      addressNum:
          data.addressNum.present ? data.addressNum.value : this.addressNum,
      segwitType:
          data.segwitType.present ? data.segwitType.value : this.segwitType,
      path: data.path.present ? data.path.value : this.path,
      keyTypekeyType: data.keyTypekeyType.present
          ? data.keyTypekeyType.value
          : this.keyTypekeyType,
      isXPub: data.isXPub.present ? data.isXPub.value : this.isXPub,
    );
  }

  @override
  String toString() {
    return (StringBuffer('MonitorModel(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('publickey: $publickey, ')
          ..write('batchId: $batchId, ')
          ..write('walletId: $walletId, ')
          ..write('slip44Id: $slip44Id, ')
          ..write('addressNum: $addressNum, ')
          ..write('segwitType: $segwitType, ')
          ..write('path: $path, ')
          ..write('keyTypekeyType: $keyTypekeyType, ')
          ..write('isXPub: $isXPub')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, chain, chainCode, publickey, batchId,
      walletId, slip44Id, addressNum, segwitType, path, keyTypekeyType, isXPub);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MonitorModel &&
          other.id == this.id &&
          other.chain == this.chain &&
          other.chainCode == this.chainCode &&
          other.publickey == this.publickey &&
          other.batchId == this.batchId &&
          other.walletId == this.walletId &&
          other.slip44Id == this.slip44Id &&
          other.addressNum == this.addressNum &&
          other.segwitType == this.segwitType &&
          other.path == this.path &&
          other.keyTypekeyType == this.keyTypekeyType &&
          other.isXPub == this.isXPub);
}

class MonitorTableCompanion extends UpdateCompanion<MonitorModel> {
  final Value<int> id;
  final Value<String?> chain;
  final Value<String?> chainCode;
  final Value<String?> publickey;
  final Value<int?> batchId;
  final Value<String?> walletId;
  final Value<int?> slip44Id;
  final Value<int?> addressNum;
  final Value<int?> segwitType;
  final Value<String?> path;
  final Value<int?> keyTypekeyType;
  final Value<bool?> isXPub;
  const MonitorTableCompanion({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.publickey = const Value.absent(),
    this.batchId = const Value.absent(),
    this.walletId = const Value.absent(),
    this.slip44Id = const Value.absent(),
    this.addressNum = const Value.absent(),
    this.segwitType = const Value.absent(),
    this.path = const Value.absent(),
    this.keyTypekeyType = const Value.absent(),
    this.isXPub = const Value.absent(),
  });
  MonitorTableCompanion.insert({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.publickey = const Value.absent(),
    this.batchId = const Value.absent(),
    this.walletId = const Value.absent(),
    this.slip44Id = const Value.absent(),
    this.addressNum = const Value.absent(),
    this.segwitType = const Value.absent(),
    this.path = const Value.absent(),
    this.keyTypekeyType = const Value.absent(),
    this.isXPub = const Value.absent(),
  });
  static Insertable<MonitorModel> custom({
    Expression<int>? id,
    Expression<String>? chain,
    Expression<String>? chainCode,
    Expression<String>? publickey,
    Expression<int>? batchId,
    Expression<String>? walletId,
    Expression<int>? slip44Id,
    Expression<int>? addressNum,
    Expression<int>? segwitType,
    Expression<String>? path,
    Expression<int>? keyTypekeyType,
    Expression<bool>? isXPub,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chain != null) 'chain': chain,
      if (chainCode != null) 'chain_code': chainCode,
      if (publickey != null) 'publickey': publickey,
      if (batchId != null) 'batch_id': batchId,
      if (walletId != null) 'wallet_id': walletId,
      if (slip44Id != null) 'slip44_id': slip44Id,
      if (addressNum != null) 'address_num': addressNum,
      if (segwitType != null) 'segwit_type': segwitType,
      if (path != null) 'path': path,
      if (keyTypekeyType != null) 'key_typekey_type': keyTypekeyType,
      if (isXPub != null) 'is_x_pub': isXPub,
    });
  }

  MonitorTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? chain,
      Value<String?>? chainCode,
      Value<String?>? publickey,
      Value<int?>? batchId,
      Value<String?>? walletId,
      Value<int?>? slip44Id,
      Value<int?>? addressNum,
      Value<int?>? segwitType,
      Value<String?>? path,
      Value<int?>? keyTypekeyType,
      Value<bool?>? isXPub}) {
    return MonitorTableCompanion(
      id: id ?? this.id,
      chain: chain ?? this.chain,
      chainCode: chainCode ?? this.chainCode,
      publickey: publickey ?? this.publickey,
      batchId: batchId ?? this.batchId,
      walletId: walletId ?? this.walletId,
      slip44Id: slip44Id ?? this.slip44Id,
      addressNum: addressNum ?? this.addressNum,
      segwitType: segwitType ?? this.segwitType,
      path: path ?? this.path,
      keyTypekeyType: keyTypekeyType ?? this.keyTypekeyType,
      isXPub: isXPub ?? this.isXPub,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (chainCode.present) {
      map['chain_code'] = Variable<String>(chainCode.value);
    }
    if (publickey.present) {
      map['publickey'] = Variable<String>(publickey.value);
    }
    if (batchId.present) {
      map['batch_id'] = Variable<int>(batchId.value);
    }
    if (walletId.present) {
      map['wallet_id'] = Variable<String>(walletId.value);
    }
    if (slip44Id.present) {
      map['slip44_id'] = Variable<int>(slip44Id.value);
    }
    if (addressNum.present) {
      map['address_num'] = Variable<int>(addressNum.value);
    }
    if (segwitType.present) {
      map['segwit_type'] = Variable<int>(segwitType.value);
    }
    if (path.present) {
      map['path'] = Variable<String>(path.value);
    }
    if (keyTypekeyType.present) {
      map['key_typekey_type'] = Variable<int>(keyTypekeyType.value);
    }
    if (isXPub.present) {
      map['is_x_pub'] = Variable<bool>(isXPub.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MonitorTableCompanion(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('publickey: $publickey, ')
          ..write('batchId: $batchId, ')
          ..write('walletId: $walletId, ')
          ..write('slip44Id: $slip44Id, ')
          ..write('addressNum: $addressNum, ')
          ..write('segwitType: $segwitType, ')
          ..write('path: $path, ')
          ..write('keyTypekeyType: $keyTypekeyType, ')
          ..write('isXPub: $isXPub')
          ..write(')'))
        .toString();
  }
}

class $CoinTableTable extends CoinTable
    with TableInfo<$CoinTableTable, CoinModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CoinTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainCodeMeta =
      const VerificationMeta('chainCode');
  @override
  late final GeneratedColumn<String> chainCode = GeneratedColumn<String>(
      'chain_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainIdMeta =
      const VerificationMeta('chainId');
  @override
  late final GeneratedColumn<int> chainId = GeneratedColumn<int>(
      'chain_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _chainNameMeta =
      const VerificationMeta('chainName');
  @override
  late final GeneratedColumn<String> chainName = GeneratedColumn<String>(
      'chain_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _cnNameMeta = const VerificationMeta('cnName');
  @override
  late final GeneratedColumn<String> cnName = GeneratedColumn<String>(
      'cn_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _symbolMeta = const VerificationMeta('symbol');
  @override
  late final GeneratedColumn<String> symbol = GeneratedColumn<String>(
      'symbol', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _contractMeta =
      const VerificationMeta('contract');
  @override
  late final GeneratedColumn<String> contract = GeneratedColumn<String>(
      'contract', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainDecimalMeta =
      const VerificationMeta('chainDecimal');
  @override
  late final GeneratedColumn<int> chainDecimal = GeneratedColumn<int>(
      'chain_decimal', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _balanceDecimalMeta =
      const VerificationMeta('balanceDecimal');
  @override
  late final GeneratedColumn<int> balanceDecimal = GeneratedColumn<int>(
      'balance_decimal', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _tokenTypeMeta =
      const VerificationMeta('tokenType');
  @override
  late final GeneratedColumn<int> tokenType = GeneratedColumn<int>(
      'token_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _priceMeta = const VerificationMeta('price');
  @override
  late final GeneratedColumn<String> price = GeneratedColumn<String>(
      'price', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isSupportTokenMeta =
      const VerificationMeta('isSupportToken');
  @override
  late final GeneratedColumn<bool> isSupportToken = GeneratedColumn<bool>(
      'is_support_token', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_support_token" IN (0, 1))'));
  static const VerificationMeta _isCoinSupportedMeta =
      const VerificationMeta('isCoinSupported');
  @override
  late final GeneratedColumn<bool> isCoinSupported = GeneratedColumn<bool>(
      'is_coin_supported', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_coin_supported" IN (0, 1))'));
  static const VerificationMeta _isTokenMeta =
      const VerificationMeta('isToken');
  @override
  late final GeneratedColumn<bool> isToken = GeneratedColumn<bool>(
      'is_token', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_token" IN (0, 1))'));
  static const VerificationMeta _symbolIconMeta =
      const VerificationMeta('symbolIcon');
  @override
  late final GeneratedColumn<String> symbolIcon = GeneratedColumn<String>(
      'symbol_icon', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainIconMeta =
      const VerificationMeta('chainIcon');
  @override
  late final GeneratedColumn<String> chainIcon = GeneratedColumn<String>(
      'chain_icon', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _disabledIconMeta =
      const VerificationMeta('disabledIcon');
  @override
  late final GeneratedColumn<String> disabledIcon = GeneratedColumn<String>(
      'disabled_icon', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _coinFeeTierMeta =
      const VerificationMeta('coinFeeTier');
  @override
  late final GeneratedColumn<String> coinFeeTier = GeneratedColumn<String>(
      'coin_fee_tier', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _feeCacheMeta =
      const VerificationMeta('feeCache');
  @override
  late final GeneratedColumn<String> feeCache = GeneratedColumn<String>(
      'fee_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _gasLimitCacheMeta =
      const VerificationMeta('gasLimitCache');
  @override
  late final GeneratedColumn<String> gasLimitCache = GeneratedColumn<String>(
      'gas_limit_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftGasLimitCacheMeta =
      const VerificationMeta('nftGasLimitCache');
  @override
  late final GeneratedColumn<String> nftGasLimitCache = GeneratedColumn<String>(
      'nft_gas_limit_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _sortedIdMeta =
      const VerificationMeta('sortedId');
  @override
  late final GeneratedColumn<double> sortedId = GeneratedColumn<double>(
      'sorted_id', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _filGasJsonStrMeta =
      const VerificationMeta('filGasJsonStr');
  @override
  late final GeneratedColumn<String> filGasJsonStr = GeneratedColumn<String>(
      'fil_gas_json_str', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _hiddenDateTimeMeta =
      const VerificationMeta('hiddenDateTime');
  @override
  late final GeneratedColumn<DateTime> hiddenDateTime =
      GeneratedColumn<DateTime>('hidden_date_time', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        chain,
        chainCode,
        chainId,
        chainName,
        cnName,
        symbol,
        contract,
        chainDecimal,
        balanceDecimal,
        tokenType,
        price,
        isSupportToken,
        isCoinSupported,
        isToken,
        symbolIcon,
        chainIcon,
        disabledIcon,
        coinFeeTier,
        feeCache,
        gasLimitCache,
        nftGasLimitCache,
        sortedId,
        filGasJsonStr,
        hiddenDateTime
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'coin_table';
  @override
  VerificationContext validateIntegrity(Insertable<CoinModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('chain_code')) {
      context.handle(_chainCodeMeta,
          chainCode.isAcceptableOrUnknown(data['chain_code']!, _chainCodeMeta));
    }
    if (data.containsKey('chain_id')) {
      context.handle(_chainIdMeta,
          chainId.isAcceptableOrUnknown(data['chain_id']!, _chainIdMeta));
    }
    if (data.containsKey('chain_name')) {
      context.handle(_chainNameMeta,
          chainName.isAcceptableOrUnknown(data['chain_name']!, _chainNameMeta));
    }
    if (data.containsKey('cn_name')) {
      context.handle(_cnNameMeta,
          cnName.isAcceptableOrUnknown(data['cn_name']!, _cnNameMeta));
    }
    if (data.containsKey('symbol')) {
      context.handle(_symbolMeta,
          symbol.isAcceptableOrUnknown(data['symbol']!, _symbolMeta));
    }
    if (data.containsKey('contract')) {
      context.handle(_contractMeta,
          contract.isAcceptableOrUnknown(data['contract']!, _contractMeta));
    }
    if (data.containsKey('chain_decimal')) {
      context.handle(
          _chainDecimalMeta,
          chainDecimal.isAcceptableOrUnknown(
              data['chain_decimal']!, _chainDecimalMeta));
    }
    if (data.containsKey('balance_decimal')) {
      context.handle(
          _balanceDecimalMeta,
          balanceDecimal.isAcceptableOrUnknown(
              data['balance_decimal']!, _balanceDecimalMeta));
    }
    if (data.containsKey('token_type')) {
      context.handle(_tokenTypeMeta,
          tokenType.isAcceptableOrUnknown(data['token_type']!, _tokenTypeMeta));
    }
    if (data.containsKey('price')) {
      context.handle(
          _priceMeta, price.isAcceptableOrUnknown(data['price']!, _priceMeta));
    }
    if (data.containsKey('is_support_token')) {
      context.handle(
          _isSupportTokenMeta,
          isSupportToken.isAcceptableOrUnknown(
              data['is_support_token']!, _isSupportTokenMeta));
    }
    if (data.containsKey('is_coin_supported')) {
      context.handle(
          _isCoinSupportedMeta,
          isCoinSupported.isAcceptableOrUnknown(
              data['is_coin_supported']!, _isCoinSupportedMeta));
    }
    if (data.containsKey('is_token')) {
      context.handle(_isTokenMeta,
          isToken.isAcceptableOrUnknown(data['is_token']!, _isTokenMeta));
    }
    if (data.containsKey('symbol_icon')) {
      context.handle(
          _symbolIconMeta,
          symbolIcon.isAcceptableOrUnknown(
              data['symbol_icon']!, _symbolIconMeta));
    }
    if (data.containsKey('chain_icon')) {
      context.handle(_chainIconMeta,
          chainIcon.isAcceptableOrUnknown(data['chain_icon']!, _chainIconMeta));
    }
    if (data.containsKey('disabled_icon')) {
      context.handle(
          _disabledIconMeta,
          disabledIcon.isAcceptableOrUnknown(
              data['disabled_icon']!, _disabledIconMeta));
    }
    if (data.containsKey('coin_fee_tier')) {
      context.handle(
          _coinFeeTierMeta,
          coinFeeTier.isAcceptableOrUnknown(
              data['coin_fee_tier']!, _coinFeeTierMeta));
    }
    if (data.containsKey('fee_cache')) {
      context.handle(_feeCacheMeta,
          feeCache.isAcceptableOrUnknown(data['fee_cache']!, _feeCacheMeta));
    }
    if (data.containsKey('gas_limit_cache')) {
      context.handle(
          _gasLimitCacheMeta,
          gasLimitCache.isAcceptableOrUnknown(
              data['gas_limit_cache']!, _gasLimitCacheMeta));
    }
    if (data.containsKey('nft_gas_limit_cache')) {
      context.handle(
          _nftGasLimitCacheMeta,
          nftGasLimitCache.isAcceptableOrUnknown(
              data['nft_gas_limit_cache']!, _nftGasLimitCacheMeta));
    }
    if (data.containsKey('sorted_id')) {
      context.handle(_sortedIdMeta,
          sortedId.isAcceptableOrUnknown(data['sorted_id']!, _sortedIdMeta));
    }
    if (data.containsKey('fil_gas_json_str')) {
      context.handle(
          _filGasJsonStrMeta,
          filGasJsonStr.isAcceptableOrUnknown(
              data['fil_gas_json_str']!, _filGasJsonStrMeta));
    }
    if (data.containsKey('hidden_date_time')) {
      context.handle(
          _hiddenDateTimeMeta,
          hiddenDateTime.isAcceptableOrUnknown(
              data['hidden_date_time']!, _hiddenDateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CoinModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CoinModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      chainCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_code']),
      chainId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_id']),
      chainName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_name']),
      cnName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}cn_name']),
      symbol: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}symbol']),
      contract: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contract']),
      chainDecimal: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_decimal']),
      balanceDecimal: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}balance_decimal']),
      tokenType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}token_type']),
      price: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}price']),
      isSupportToken: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_support_token']),
      isCoinSupported: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_coin_supported']),
      isToken: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_token']),
      symbolIcon: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}symbol_icon']),
      chainIcon: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_icon']),
      disabledIcon: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}disabled_icon']),
      coinFeeTier: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}coin_fee_tier']),
      feeCache: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}fee_cache']),
      gasLimitCache: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}gas_limit_cache']),
      nftGasLimitCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}nft_gas_limit_cache']),
      sortedId: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}sorted_id']),
      filGasJsonStr: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}fil_gas_json_str']),
      hiddenDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}hidden_date_time']),
    );
  }

  @override
  $CoinTableTable createAlias(String alias) {
    return $CoinTableTable(attachedDatabase, alias);
  }
}

class CoinModel extends DataClass implements Insertable<CoinModel> {
  final int id;
  final String? chain;
  final String? chainCode;
  final int? chainId;
  final String? chainName;
  final String? cnName;
  final String? symbol;
  final String? contract;
  final int? chainDecimal;
  final int? balanceDecimal;
  final int? tokenType;
  final String? price;
  final bool? isSupportToken;
  final bool? isCoinSupported;
  final bool? isToken;
  final String? symbolIcon;
  final String? chainIcon;
  final String? disabledIcon;
  final String? coinFeeTier;
  final String? feeCache;
  final String? gasLimitCache;
  final String? nftGasLimitCache;
  final double? sortedId;
  final String? filGasJsonStr;
  final DateTime? hiddenDateTime;
  const CoinModel(
      {required this.id,
      this.chain,
      this.chainCode,
      this.chainId,
      this.chainName,
      this.cnName,
      this.symbol,
      this.contract,
      this.chainDecimal,
      this.balanceDecimal,
      this.tokenType,
      this.price,
      this.isSupportToken,
      this.isCoinSupported,
      this.isToken,
      this.symbolIcon,
      this.chainIcon,
      this.disabledIcon,
      this.coinFeeTier,
      this.feeCache,
      this.gasLimitCache,
      this.nftGasLimitCache,
      this.sortedId,
      this.filGasJsonStr,
      this.hiddenDateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || chainCode != null) {
      map['chain_code'] = Variable<String>(chainCode);
    }
    if (!nullToAbsent || chainId != null) {
      map['chain_id'] = Variable<int>(chainId);
    }
    if (!nullToAbsent || chainName != null) {
      map['chain_name'] = Variable<String>(chainName);
    }
    if (!nullToAbsent || cnName != null) {
      map['cn_name'] = Variable<String>(cnName);
    }
    if (!nullToAbsent || symbol != null) {
      map['symbol'] = Variable<String>(symbol);
    }
    if (!nullToAbsent || contract != null) {
      map['contract'] = Variable<String>(contract);
    }
    if (!nullToAbsent || chainDecimal != null) {
      map['chain_decimal'] = Variable<int>(chainDecimal);
    }
    if (!nullToAbsent || balanceDecimal != null) {
      map['balance_decimal'] = Variable<int>(balanceDecimal);
    }
    if (!nullToAbsent || tokenType != null) {
      map['token_type'] = Variable<int>(tokenType);
    }
    if (!nullToAbsent || price != null) {
      map['price'] = Variable<String>(price);
    }
    if (!nullToAbsent || isSupportToken != null) {
      map['is_support_token'] = Variable<bool>(isSupportToken);
    }
    if (!nullToAbsent || isCoinSupported != null) {
      map['is_coin_supported'] = Variable<bool>(isCoinSupported);
    }
    if (!nullToAbsent || isToken != null) {
      map['is_token'] = Variable<bool>(isToken);
    }
    if (!nullToAbsent || symbolIcon != null) {
      map['symbol_icon'] = Variable<String>(symbolIcon);
    }
    if (!nullToAbsent || chainIcon != null) {
      map['chain_icon'] = Variable<String>(chainIcon);
    }
    if (!nullToAbsent || disabledIcon != null) {
      map['disabled_icon'] = Variable<String>(disabledIcon);
    }
    if (!nullToAbsent || coinFeeTier != null) {
      map['coin_fee_tier'] = Variable<String>(coinFeeTier);
    }
    if (!nullToAbsent || feeCache != null) {
      map['fee_cache'] = Variable<String>(feeCache);
    }
    if (!nullToAbsent || gasLimitCache != null) {
      map['gas_limit_cache'] = Variable<String>(gasLimitCache);
    }
    if (!nullToAbsent || nftGasLimitCache != null) {
      map['nft_gas_limit_cache'] = Variable<String>(nftGasLimitCache);
    }
    if (!nullToAbsent || sortedId != null) {
      map['sorted_id'] = Variable<double>(sortedId);
    }
    if (!nullToAbsent || filGasJsonStr != null) {
      map['fil_gas_json_str'] = Variable<String>(filGasJsonStr);
    }
    if (!nullToAbsent || hiddenDateTime != null) {
      map['hidden_date_time'] = Variable<DateTime>(hiddenDateTime);
    }
    return map;
  }

  CoinTableCompanion toCompanion(bool nullToAbsent) {
    return CoinTableCompanion(
      id: Value(id),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      chainCode: chainCode == null && nullToAbsent
          ? const Value.absent()
          : Value(chainCode),
      chainId: chainId == null && nullToAbsent
          ? const Value.absent()
          : Value(chainId),
      chainName: chainName == null && nullToAbsent
          ? const Value.absent()
          : Value(chainName),
      cnName:
          cnName == null && nullToAbsent ? const Value.absent() : Value(cnName),
      symbol:
          symbol == null && nullToAbsent ? const Value.absent() : Value(symbol),
      contract: contract == null && nullToAbsent
          ? const Value.absent()
          : Value(contract),
      chainDecimal: chainDecimal == null && nullToAbsent
          ? const Value.absent()
          : Value(chainDecimal),
      balanceDecimal: balanceDecimal == null && nullToAbsent
          ? const Value.absent()
          : Value(balanceDecimal),
      tokenType: tokenType == null && nullToAbsent
          ? const Value.absent()
          : Value(tokenType),
      price:
          price == null && nullToAbsent ? const Value.absent() : Value(price),
      isSupportToken: isSupportToken == null && nullToAbsent
          ? const Value.absent()
          : Value(isSupportToken),
      isCoinSupported: isCoinSupported == null && nullToAbsent
          ? const Value.absent()
          : Value(isCoinSupported),
      isToken: isToken == null && nullToAbsent
          ? const Value.absent()
          : Value(isToken),
      symbolIcon: symbolIcon == null && nullToAbsent
          ? const Value.absent()
          : Value(symbolIcon),
      chainIcon: chainIcon == null && nullToAbsent
          ? const Value.absent()
          : Value(chainIcon),
      disabledIcon: disabledIcon == null && nullToAbsent
          ? const Value.absent()
          : Value(disabledIcon),
      coinFeeTier: coinFeeTier == null && nullToAbsent
          ? const Value.absent()
          : Value(coinFeeTier),
      feeCache: feeCache == null && nullToAbsent
          ? const Value.absent()
          : Value(feeCache),
      gasLimitCache: gasLimitCache == null && nullToAbsent
          ? const Value.absent()
          : Value(gasLimitCache),
      nftGasLimitCache: nftGasLimitCache == null && nullToAbsent
          ? const Value.absent()
          : Value(nftGasLimitCache),
      sortedId: sortedId == null && nullToAbsent
          ? const Value.absent()
          : Value(sortedId),
      filGasJsonStr: filGasJsonStr == null && nullToAbsent
          ? const Value.absent()
          : Value(filGasJsonStr),
      hiddenDateTime: hiddenDateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(hiddenDateTime),
    );
  }

  factory CoinModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CoinModel(
      id: serializer.fromJson<int>(json['id']),
      chain: serializer.fromJson<String?>(json['chain']),
      chainCode: serializer.fromJson<String?>(json['chainCode']),
      chainId: serializer.fromJson<int?>(json['chainId']),
      chainName: serializer.fromJson<String?>(json['chainName']),
      cnName: serializer.fromJson<String?>(json['cnName']),
      symbol: serializer.fromJson<String?>(json['symbol']),
      contract: serializer.fromJson<String?>(json['contract']),
      chainDecimal: serializer.fromJson<int?>(json['chainDecimal']),
      balanceDecimal: serializer.fromJson<int?>(json['balanceDecimal']),
      tokenType: serializer.fromJson<int?>(json['tokenType']),
      price: serializer.fromJson<String?>(json['price']),
      isSupportToken: serializer.fromJson<bool?>(json['isSupportToken']),
      isCoinSupported: serializer.fromJson<bool?>(json['isCoinSupported']),
      isToken: serializer.fromJson<bool?>(json['isToken']),
      symbolIcon: serializer.fromJson<String?>(json['symbolIcon']),
      chainIcon: serializer.fromJson<String?>(json['chainIcon']),
      disabledIcon: serializer.fromJson<String?>(json['disabledIcon']),
      coinFeeTier: serializer.fromJson<String?>(json['coinFeeTier']),
      feeCache: serializer.fromJson<String?>(json['feeCache']),
      gasLimitCache: serializer.fromJson<String?>(json['gasLimitCache']),
      nftGasLimitCache: serializer.fromJson<String?>(json['nftGasLimitCache']),
      sortedId: serializer.fromJson<double?>(json['sortedId']),
      filGasJsonStr: serializer.fromJson<String?>(json['filGasJsonStr']),
      hiddenDateTime: serializer.fromJson<DateTime?>(json['hiddenDateTime']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'chain': serializer.toJson<String?>(chain),
      'chainCode': serializer.toJson<String?>(chainCode),
      'chainId': serializer.toJson<int?>(chainId),
      'chainName': serializer.toJson<String?>(chainName),
      'cnName': serializer.toJson<String?>(cnName),
      'symbol': serializer.toJson<String?>(symbol),
      'contract': serializer.toJson<String?>(contract),
      'chainDecimal': serializer.toJson<int?>(chainDecimal),
      'balanceDecimal': serializer.toJson<int?>(balanceDecimal),
      'tokenType': serializer.toJson<int?>(tokenType),
      'price': serializer.toJson<String?>(price),
      'isSupportToken': serializer.toJson<bool?>(isSupportToken),
      'isCoinSupported': serializer.toJson<bool?>(isCoinSupported),
      'isToken': serializer.toJson<bool?>(isToken),
      'symbolIcon': serializer.toJson<String?>(symbolIcon),
      'chainIcon': serializer.toJson<String?>(chainIcon),
      'disabledIcon': serializer.toJson<String?>(disabledIcon),
      'coinFeeTier': serializer.toJson<String?>(coinFeeTier),
      'feeCache': serializer.toJson<String?>(feeCache),
      'gasLimitCache': serializer.toJson<String?>(gasLimitCache),
      'nftGasLimitCache': serializer.toJson<String?>(nftGasLimitCache),
      'sortedId': serializer.toJson<double?>(sortedId),
      'filGasJsonStr': serializer.toJson<String?>(filGasJsonStr),
      'hiddenDateTime': serializer.toJson<DateTime?>(hiddenDateTime),
    };
  }

  CoinModel copyWith(
          {int? id,
          Value<String?> chain = const Value.absent(),
          Value<String?> chainCode = const Value.absent(),
          Value<int?> chainId = const Value.absent(),
          Value<String?> chainName = const Value.absent(),
          Value<String?> cnName = const Value.absent(),
          Value<String?> symbol = const Value.absent(),
          Value<String?> contract = const Value.absent(),
          Value<int?> chainDecimal = const Value.absent(),
          Value<int?> balanceDecimal = const Value.absent(),
          Value<int?> tokenType = const Value.absent(),
          Value<String?> price = const Value.absent(),
          Value<bool?> isSupportToken = const Value.absent(),
          Value<bool?> isCoinSupported = const Value.absent(),
          Value<bool?> isToken = const Value.absent(),
          Value<String?> symbolIcon = const Value.absent(),
          Value<String?> chainIcon = const Value.absent(),
          Value<String?> disabledIcon = const Value.absent(),
          Value<String?> coinFeeTier = const Value.absent(),
          Value<String?> feeCache = const Value.absent(),
          Value<String?> gasLimitCache = const Value.absent(),
          Value<String?> nftGasLimitCache = const Value.absent(),
          Value<double?> sortedId = const Value.absent(),
          Value<String?> filGasJsonStr = const Value.absent(),
          Value<DateTime?> hiddenDateTime = const Value.absent()}) =>
      CoinModel(
        id: id ?? this.id,
        chain: chain.present ? chain.value : this.chain,
        chainCode: chainCode.present ? chainCode.value : this.chainCode,
        chainId: chainId.present ? chainId.value : this.chainId,
        chainName: chainName.present ? chainName.value : this.chainName,
        cnName: cnName.present ? cnName.value : this.cnName,
        symbol: symbol.present ? symbol.value : this.symbol,
        contract: contract.present ? contract.value : this.contract,
        chainDecimal:
            chainDecimal.present ? chainDecimal.value : this.chainDecimal,
        balanceDecimal:
            balanceDecimal.present ? balanceDecimal.value : this.balanceDecimal,
        tokenType: tokenType.present ? tokenType.value : this.tokenType,
        price: price.present ? price.value : this.price,
        isSupportToken:
            isSupportToken.present ? isSupportToken.value : this.isSupportToken,
        isCoinSupported: isCoinSupported.present
            ? isCoinSupported.value
            : this.isCoinSupported,
        isToken: isToken.present ? isToken.value : this.isToken,
        symbolIcon: symbolIcon.present ? symbolIcon.value : this.symbolIcon,
        chainIcon: chainIcon.present ? chainIcon.value : this.chainIcon,
        disabledIcon:
            disabledIcon.present ? disabledIcon.value : this.disabledIcon,
        coinFeeTier: coinFeeTier.present ? coinFeeTier.value : this.coinFeeTier,
        feeCache: feeCache.present ? feeCache.value : this.feeCache,
        gasLimitCache:
            gasLimitCache.present ? gasLimitCache.value : this.gasLimitCache,
        nftGasLimitCache: nftGasLimitCache.present
            ? nftGasLimitCache.value
            : this.nftGasLimitCache,
        sortedId: sortedId.present ? sortedId.value : this.sortedId,
        filGasJsonStr:
            filGasJsonStr.present ? filGasJsonStr.value : this.filGasJsonStr,
        hiddenDateTime:
            hiddenDateTime.present ? hiddenDateTime.value : this.hiddenDateTime,
      );
  CoinModel copyWithCompanion(CoinTableCompanion data) {
    return CoinModel(
      id: data.id.present ? data.id.value : this.id,
      chain: data.chain.present ? data.chain.value : this.chain,
      chainCode: data.chainCode.present ? data.chainCode.value : this.chainCode,
      chainId: data.chainId.present ? data.chainId.value : this.chainId,
      chainName: data.chainName.present ? data.chainName.value : this.chainName,
      cnName: data.cnName.present ? data.cnName.value : this.cnName,
      symbol: data.symbol.present ? data.symbol.value : this.symbol,
      contract: data.contract.present ? data.contract.value : this.contract,
      chainDecimal: data.chainDecimal.present
          ? data.chainDecimal.value
          : this.chainDecimal,
      balanceDecimal: data.balanceDecimal.present
          ? data.balanceDecimal.value
          : this.balanceDecimal,
      tokenType: data.tokenType.present ? data.tokenType.value : this.tokenType,
      price: data.price.present ? data.price.value : this.price,
      isSupportToken: data.isSupportToken.present
          ? data.isSupportToken.value
          : this.isSupportToken,
      isCoinSupported: data.isCoinSupported.present
          ? data.isCoinSupported.value
          : this.isCoinSupported,
      isToken: data.isToken.present ? data.isToken.value : this.isToken,
      symbolIcon:
          data.symbolIcon.present ? data.symbolIcon.value : this.symbolIcon,
      chainIcon: data.chainIcon.present ? data.chainIcon.value : this.chainIcon,
      disabledIcon: data.disabledIcon.present
          ? data.disabledIcon.value
          : this.disabledIcon,
      coinFeeTier:
          data.coinFeeTier.present ? data.coinFeeTier.value : this.coinFeeTier,
      feeCache: data.feeCache.present ? data.feeCache.value : this.feeCache,
      gasLimitCache: data.gasLimitCache.present
          ? data.gasLimitCache.value
          : this.gasLimitCache,
      nftGasLimitCache: data.nftGasLimitCache.present
          ? data.nftGasLimitCache.value
          : this.nftGasLimitCache,
      sortedId: data.sortedId.present ? data.sortedId.value : this.sortedId,
      filGasJsonStr: data.filGasJsonStr.present
          ? data.filGasJsonStr.value
          : this.filGasJsonStr,
      hiddenDateTime: data.hiddenDateTime.present
          ? data.hiddenDateTime.value
          : this.hiddenDateTime,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CoinModel(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('chainId: $chainId, ')
          ..write('chainName: $chainName, ')
          ..write('cnName: $cnName, ')
          ..write('symbol: $symbol, ')
          ..write('contract: $contract, ')
          ..write('chainDecimal: $chainDecimal, ')
          ..write('balanceDecimal: $balanceDecimal, ')
          ..write('tokenType: $tokenType, ')
          ..write('price: $price, ')
          ..write('isSupportToken: $isSupportToken, ')
          ..write('isCoinSupported: $isCoinSupported, ')
          ..write('isToken: $isToken, ')
          ..write('symbolIcon: $symbolIcon, ')
          ..write('chainIcon: $chainIcon, ')
          ..write('disabledIcon: $disabledIcon, ')
          ..write('coinFeeTier: $coinFeeTier, ')
          ..write('feeCache: $feeCache, ')
          ..write('gasLimitCache: $gasLimitCache, ')
          ..write('nftGasLimitCache: $nftGasLimitCache, ')
          ..write('sortedId: $sortedId, ')
          ..write('filGasJsonStr: $filGasJsonStr, ')
          ..write('hiddenDateTime: $hiddenDateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        chain,
        chainCode,
        chainId,
        chainName,
        cnName,
        symbol,
        contract,
        chainDecimal,
        balanceDecimal,
        tokenType,
        price,
        isSupportToken,
        isCoinSupported,
        isToken,
        symbolIcon,
        chainIcon,
        disabledIcon,
        coinFeeTier,
        feeCache,
        gasLimitCache,
        nftGasLimitCache,
        sortedId,
        filGasJsonStr,
        hiddenDateTime
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CoinModel &&
          other.id == this.id &&
          other.chain == this.chain &&
          other.chainCode == this.chainCode &&
          other.chainId == this.chainId &&
          other.chainName == this.chainName &&
          other.cnName == this.cnName &&
          other.symbol == this.symbol &&
          other.contract == this.contract &&
          other.chainDecimal == this.chainDecimal &&
          other.balanceDecimal == this.balanceDecimal &&
          other.tokenType == this.tokenType &&
          other.price == this.price &&
          other.isSupportToken == this.isSupportToken &&
          other.isCoinSupported == this.isCoinSupported &&
          other.isToken == this.isToken &&
          other.symbolIcon == this.symbolIcon &&
          other.chainIcon == this.chainIcon &&
          other.disabledIcon == this.disabledIcon &&
          other.coinFeeTier == this.coinFeeTier &&
          other.feeCache == this.feeCache &&
          other.gasLimitCache == this.gasLimitCache &&
          other.nftGasLimitCache == this.nftGasLimitCache &&
          other.sortedId == this.sortedId &&
          other.filGasJsonStr == this.filGasJsonStr &&
          other.hiddenDateTime == this.hiddenDateTime);
}

class CoinTableCompanion extends UpdateCompanion<CoinModel> {
  final Value<int> id;
  final Value<String?> chain;
  final Value<String?> chainCode;
  final Value<int?> chainId;
  final Value<String?> chainName;
  final Value<String?> cnName;
  final Value<String?> symbol;
  final Value<String?> contract;
  final Value<int?> chainDecimal;
  final Value<int?> balanceDecimal;
  final Value<int?> tokenType;
  final Value<String?> price;
  final Value<bool?> isSupportToken;
  final Value<bool?> isCoinSupported;
  final Value<bool?> isToken;
  final Value<String?> symbolIcon;
  final Value<String?> chainIcon;
  final Value<String?> disabledIcon;
  final Value<String?> coinFeeTier;
  final Value<String?> feeCache;
  final Value<String?> gasLimitCache;
  final Value<String?> nftGasLimitCache;
  final Value<double?> sortedId;
  final Value<String?> filGasJsonStr;
  final Value<DateTime?> hiddenDateTime;
  const CoinTableCompanion({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.chainId = const Value.absent(),
    this.chainName = const Value.absent(),
    this.cnName = const Value.absent(),
    this.symbol = const Value.absent(),
    this.contract = const Value.absent(),
    this.chainDecimal = const Value.absent(),
    this.balanceDecimal = const Value.absent(),
    this.tokenType = const Value.absent(),
    this.price = const Value.absent(),
    this.isSupportToken = const Value.absent(),
    this.isCoinSupported = const Value.absent(),
    this.isToken = const Value.absent(),
    this.symbolIcon = const Value.absent(),
    this.chainIcon = const Value.absent(),
    this.disabledIcon = const Value.absent(),
    this.coinFeeTier = const Value.absent(),
    this.feeCache = const Value.absent(),
    this.gasLimitCache = const Value.absent(),
    this.nftGasLimitCache = const Value.absent(),
    this.sortedId = const Value.absent(),
    this.filGasJsonStr = const Value.absent(),
    this.hiddenDateTime = const Value.absent(),
  });
  CoinTableCompanion.insert({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.chainId = const Value.absent(),
    this.chainName = const Value.absent(),
    this.cnName = const Value.absent(),
    this.symbol = const Value.absent(),
    this.contract = const Value.absent(),
    this.chainDecimal = const Value.absent(),
    this.balanceDecimal = const Value.absent(),
    this.tokenType = const Value.absent(),
    this.price = const Value.absent(),
    this.isSupportToken = const Value.absent(),
    this.isCoinSupported = const Value.absent(),
    this.isToken = const Value.absent(),
    this.symbolIcon = const Value.absent(),
    this.chainIcon = const Value.absent(),
    this.disabledIcon = const Value.absent(),
    this.coinFeeTier = const Value.absent(),
    this.feeCache = const Value.absent(),
    this.gasLimitCache = const Value.absent(),
    this.nftGasLimitCache = const Value.absent(),
    this.sortedId = const Value.absent(),
    this.filGasJsonStr = const Value.absent(),
    this.hiddenDateTime = const Value.absent(),
  });
  static Insertable<CoinModel> custom({
    Expression<int>? id,
    Expression<String>? chain,
    Expression<String>? chainCode,
    Expression<int>? chainId,
    Expression<String>? chainName,
    Expression<String>? cnName,
    Expression<String>? symbol,
    Expression<String>? contract,
    Expression<int>? chainDecimal,
    Expression<int>? balanceDecimal,
    Expression<int>? tokenType,
    Expression<String>? price,
    Expression<bool>? isSupportToken,
    Expression<bool>? isCoinSupported,
    Expression<bool>? isToken,
    Expression<String>? symbolIcon,
    Expression<String>? chainIcon,
    Expression<String>? disabledIcon,
    Expression<String>? coinFeeTier,
    Expression<String>? feeCache,
    Expression<String>? gasLimitCache,
    Expression<String>? nftGasLimitCache,
    Expression<double>? sortedId,
    Expression<String>? filGasJsonStr,
    Expression<DateTime>? hiddenDateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chain != null) 'chain': chain,
      if (chainCode != null) 'chain_code': chainCode,
      if (chainId != null) 'chain_id': chainId,
      if (chainName != null) 'chain_name': chainName,
      if (cnName != null) 'cn_name': cnName,
      if (symbol != null) 'symbol': symbol,
      if (contract != null) 'contract': contract,
      if (chainDecimal != null) 'chain_decimal': chainDecimal,
      if (balanceDecimal != null) 'balance_decimal': balanceDecimal,
      if (tokenType != null) 'token_type': tokenType,
      if (price != null) 'price': price,
      if (isSupportToken != null) 'is_support_token': isSupportToken,
      if (isCoinSupported != null) 'is_coin_supported': isCoinSupported,
      if (isToken != null) 'is_token': isToken,
      if (symbolIcon != null) 'symbol_icon': symbolIcon,
      if (chainIcon != null) 'chain_icon': chainIcon,
      if (disabledIcon != null) 'disabled_icon': disabledIcon,
      if (coinFeeTier != null) 'coin_fee_tier': coinFeeTier,
      if (feeCache != null) 'fee_cache': feeCache,
      if (gasLimitCache != null) 'gas_limit_cache': gasLimitCache,
      if (nftGasLimitCache != null) 'nft_gas_limit_cache': nftGasLimitCache,
      if (sortedId != null) 'sorted_id': sortedId,
      if (filGasJsonStr != null) 'fil_gas_json_str': filGasJsonStr,
      if (hiddenDateTime != null) 'hidden_date_time': hiddenDateTime,
    });
  }

  CoinTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? chain,
      Value<String?>? chainCode,
      Value<int?>? chainId,
      Value<String?>? chainName,
      Value<String?>? cnName,
      Value<String?>? symbol,
      Value<String?>? contract,
      Value<int?>? chainDecimal,
      Value<int?>? balanceDecimal,
      Value<int?>? tokenType,
      Value<String?>? price,
      Value<bool?>? isSupportToken,
      Value<bool?>? isCoinSupported,
      Value<bool?>? isToken,
      Value<String?>? symbolIcon,
      Value<String?>? chainIcon,
      Value<String?>? disabledIcon,
      Value<String?>? coinFeeTier,
      Value<String?>? feeCache,
      Value<String?>? gasLimitCache,
      Value<String?>? nftGasLimitCache,
      Value<double?>? sortedId,
      Value<String?>? filGasJsonStr,
      Value<DateTime?>? hiddenDateTime}) {
    return CoinTableCompanion(
      id: id ?? this.id,
      chain: chain ?? this.chain,
      chainCode: chainCode ?? this.chainCode,
      chainId: chainId ?? this.chainId,
      chainName: chainName ?? this.chainName,
      cnName: cnName ?? this.cnName,
      symbol: symbol ?? this.symbol,
      contract: contract ?? this.contract,
      chainDecimal: chainDecimal ?? this.chainDecimal,
      balanceDecimal: balanceDecimal ?? this.balanceDecimal,
      tokenType: tokenType ?? this.tokenType,
      price: price ?? this.price,
      isSupportToken: isSupportToken ?? this.isSupportToken,
      isCoinSupported: isCoinSupported ?? this.isCoinSupported,
      isToken: isToken ?? this.isToken,
      symbolIcon: symbolIcon ?? this.symbolIcon,
      chainIcon: chainIcon ?? this.chainIcon,
      disabledIcon: disabledIcon ?? this.disabledIcon,
      coinFeeTier: coinFeeTier ?? this.coinFeeTier,
      feeCache: feeCache ?? this.feeCache,
      gasLimitCache: gasLimitCache ?? this.gasLimitCache,
      nftGasLimitCache: nftGasLimitCache ?? this.nftGasLimitCache,
      sortedId: sortedId ?? this.sortedId,
      filGasJsonStr: filGasJsonStr ?? this.filGasJsonStr,
      hiddenDateTime: hiddenDateTime ?? this.hiddenDateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (chainCode.present) {
      map['chain_code'] = Variable<String>(chainCode.value);
    }
    if (chainId.present) {
      map['chain_id'] = Variable<int>(chainId.value);
    }
    if (chainName.present) {
      map['chain_name'] = Variable<String>(chainName.value);
    }
    if (cnName.present) {
      map['cn_name'] = Variable<String>(cnName.value);
    }
    if (symbol.present) {
      map['symbol'] = Variable<String>(symbol.value);
    }
    if (contract.present) {
      map['contract'] = Variable<String>(contract.value);
    }
    if (chainDecimal.present) {
      map['chain_decimal'] = Variable<int>(chainDecimal.value);
    }
    if (balanceDecimal.present) {
      map['balance_decimal'] = Variable<int>(balanceDecimal.value);
    }
    if (tokenType.present) {
      map['token_type'] = Variable<int>(tokenType.value);
    }
    if (price.present) {
      map['price'] = Variable<String>(price.value);
    }
    if (isSupportToken.present) {
      map['is_support_token'] = Variable<bool>(isSupportToken.value);
    }
    if (isCoinSupported.present) {
      map['is_coin_supported'] = Variable<bool>(isCoinSupported.value);
    }
    if (isToken.present) {
      map['is_token'] = Variable<bool>(isToken.value);
    }
    if (symbolIcon.present) {
      map['symbol_icon'] = Variable<String>(symbolIcon.value);
    }
    if (chainIcon.present) {
      map['chain_icon'] = Variable<String>(chainIcon.value);
    }
    if (disabledIcon.present) {
      map['disabled_icon'] = Variable<String>(disabledIcon.value);
    }
    if (coinFeeTier.present) {
      map['coin_fee_tier'] = Variable<String>(coinFeeTier.value);
    }
    if (feeCache.present) {
      map['fee_cache'] = Variable<String>(feeCache.value);
    }
    if (gasLimitCache.present) {
      map['gas_limit_cache'] = Variable<String>(gasLimitCache.value);
    }
    if (nftGasLimitCache.present) {
      map['nft_gas_limit_cache'] = Variable<String>(nftGasLimitCache.value);
    }
    if (sortedId.present) {
      map['sorted_id'] = Variable<double>(sortedId.value);
    }
    if (filGasJsonStr.present) {
      map['fil_gas_json_str'] = Variable<String>(filGasJsonStr.value);
    }
    if (hiddenDateTime.present) {
      map['hidden_date_time'] = Variable<DateTime>(hiddenDateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CoinTableCompanion(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('chainId: $chainId, ')
          ..write('chainName: $chainName, ')
          ..write('cnName: $cnName, ')
          ..write('symbol: $symbol, ')
          ..write('contract: $contract, ')
          ..write('chainDecimal: $chainDecimal, ')
          ..write('balanceDecimal: $balanceDecimal, ')
          ..write('tokenType: $tokenType, ')
          ..write('price: $price, ')
          ..write('isSupportToken: $isSupportToken, ')
          ..write('isCoinSupported: $isCoinSupported, ')
          ..write('isToken: $isToken, ')
          ..write('symbolIcon: $symbolIcon, ')
          ..write('chainIcon: $chainIcon, ')
          ..write('disabledIcon: $disabledIcon, ')
          ..write('coinFeeTier: $coinFeeTier, ')
          ..write('feeCache: $feeCache, ')
          ..write('gasLimitCache: $gasLimitCache, ')
          ..write('nftGasLimitCache: $nftGasLimitCache, ')
          ..write('sortedId: $sortedId, ')
          ..write('filGasJsonStr: $filGasJsonStr, ')
          ..write('hiddenDateTime: $hiddenDateTime')
          ..write(')'))
        .toString();
  }
}

class $WalletTableTable extends WalletTable
    with TableInfo<$WalletTableTable, WalletModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $WalletTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _deviceIdMeta =
      const VerificationMeta('deviceId');
  @override
  late final GeneratedColumn<String> deviceId = GeneratedColumn<String>(
      'device_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _deviceTypeMeta =
      const VerificationMeta('deviceType');
  @override
  late final GeneratedColumn<String> deviceType = GeneratedColumn<String>(
      'device_type', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chipVersionMeta =
      const VerificationMeta('chipVersion');
  @override
  late final GeneratedColumn<String> chipVersion = GeneratedColumn<String>(
      'chip_version', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _appVersionMeta =
      const VerificationMeta('appVersion');
  @override
  late final GeneratedColumn<String> appVersion = GeneratedColumn<String>(
      'app_version', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _firmwareVersionMeta =
      const VerificationMeta('firmwareVersion');
  @override
  late final GeneratedColumn<String> firmwareVersion = GeneratedColumn<String>(
      'firmware_version', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _batchIdMeta =
      const VerificationMeta('batchId');
  @override
  late final GeneratedColumn<int> batchId = GeneratedColumn<int>(
      'batch_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _walletIdMeta =
      const VerificationMeta('walletId');
  @override
  late final GeneratedColumn<String> walletId = GeneratedColumn<String>(
      'wallet_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _walletNameMeta =
      const VerificationMeta('walletName');
  @override
  late final GeneratedColumn<String> walletName = GeneratedColumn<String>(
      'wallet_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _checkedMeta =
      const VerificationMeta('checked');
  @override
  late final GeneratedColumn<bool> checked = GeneratedColumn<bool>(
      'checked', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("checked" IN (0, 1))'));
  static const VerificationMeta _isPassphraseWalletMeta =
      const VerificationMeta('isPassphraseWallet');
  @override
  late final GeneratedColumn<bool> isPassphraseWallet = GeneratedColumn<bool>(
      'is_passphrase_wallet', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_passphrase_wallet" IN (0, 1))'));
  static const VerificationMeta _bleMacIdMeta =
      const VerificationMeta('bleMacId');
  @override
  late final GeneratedColumn<String> bleMacId = GeneratedColumn<String>(
      'ble_mac_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _seedTypeMeta =
      const VerificationMeta('seedType');
  @override
  late final GeneratedColumn<int> seedType = GeneratedColumn<int>(
      'seed_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _walletTypeMeta =
      const VerificationMeta('walletType');
  @override
  late final GeneratedColumn<int> walletType = GeneratedColumn<int>(
      'wallet_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _monitorTimeMeta =
      const VerificationMeta('monitorTime');
  @override
  late final GeneratedColumn<String> monitorTime = GeneratedColumn<String>(
      'monitor_time', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _vipLevelMeta =
      const VerificationMeta('vipLevel');
  @override
  late final GeneratedColumn<int> vipLevel = GeneratedColumn<int>(
      'vip_level', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  @override
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressLabelMeta =
      const VerificationMeta('addressLabel');
  @override
  late final GeneratedColumn<String> addressLabel = GeneratedColumn<String>(
      'address_label', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftChainMeta =
      const VerificationMeta('nftChain');
  @override
  late final GeneratedColumn<String> nftChain = GeneratedColumn<String>(
      'nft_chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftAddressMeta =
      const VerificationMeta('nftAddress');
  @override
  late final GeneratedColumn<String> nftAddress = GeneratedColumn<String>(
      'nft_address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftAddressLabelMeta =
      const VerificationMeta('nftAddressLabel');
  @override
  late final GeneratedColumn<String> nftAddressLabel = GeneratedColumn<String>(
      'nft_address_label', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappChainListMeta =
      const VerificationMeta('dappChainList');
  @override
  late final GeneratedColumn<String> dappChainList = GeneratedColumn<String>(
      'dapp_chain_list', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _totalAssetsMeta =
      const VerificationMeta('totalAssets');
  @override
  late final GeneratedColumn<String> totalAssets = GeneratedColumn<String>(
      'total_assets', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        deviceId,
        deviceType,
        chipVersion,
        appVersion,
        firmwareVersion,
        batchId,
        walletId,
        walletName,
        checked,
        isPassphraseWallet,
        bleMacId,
        seedType,
        walletType,
        monitorTime,
        vipLevel,
        chain,
        address,
        addressLabel,
        nftChain,
        nftAddress,
        nftAddressLabel,
        dappChainList,
        totalAssets
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'wallet_table';
  @override
  VerificationContext validateIntegrity(Insertable<WalletModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('device_id')) {
      context.handle(_deviceIdMeta,
          deviceId.isAcceptableOrUnknown(data['device_id']!, _deviceIdMeta));
    }
    if (data.containsKey('device_type')) {
      context.handle(
          _deviceTypeMeta,
          deviceType.isAcceptableOrUnknown(
              data['device_type']!, _deviceTypeMeta));
    }
    if (data.containsKey('chip_version')) {
      context.handle(
          _chipVersionMeta,
          chipVersion.isAcceptableOrUnknown(
              data['chip_version']!, _chipVersionMeta));
    }
    if (data.containsKey('app_version')) {
      context.handle(
          _appVersionMeta,
          appVersion.isAcceptableOrUnknown(
              data['app_version']!, _appVersionMeta));
    }
    if (data.containsKey('firmware_version')) {
      context.handle(
          _firmwareVersionMeta,
          firmwareVersion.isAcceptableOrUnknown(
              data['firmware_version']!, _firmwareVersionMeta));
    }
    if (data.containsKey('batch_id')) {
      context.handle(_batchIdMeta,
          batchId.isAcceptableOrUnknown(data['batch_id']!, _batchIdMeta));
    }
    if (data.containsKey('wallet_id')) {
      context.handle(_walletIdMeta,
          walletId.isAcceptableOrUnknown(data['wallet_id']!, _walletIdMeta));
    }
    if (data.containsKey('wallet_name')) {
      context.handle(
          _walletNameMeta,
          walletName.isAcceptableOrUnknown(
              data['wallet_name']!, _walletNameMeta));
    }
    if (data.containsKey('checked')) {
      context.handle(_checkedMeta,
          checked.isAcceptableOrUnknown(data['checked']!, _checkedMeta));
    }
    if (data.containsKey('is_passphrase_wallet')) {
      context.handle(
          _isPassphraseWalletMeta,
          isPassphraseWallet.isAcceptableOrUnknown(
              data['is_passphrase_wallet']!, _isPassphraseWalletMeta));
    }
    if (data.containsKey('ble_mac_id')) {
      context.handle(_bleMacIdMeta,
          bleMacId.isAcceptableOrUnknown(data['ble_mac_id']!, _bleMacIdMeta));
    }
    if (data.containsKey('seed_type')) {
      context.handle(_seedTypeMeta,
          seedType.isAcceptableOrUnknown(data['seed_type']!, _seedTypeMeta));
    }
    if (data.containsKey('wallet_type')) {
      context.handle(
          _walletTypeMeta,
          walletType.isAcceptableOrUnknown(
              data['wallet_type']!, _walletTypeMeta));
    }
    if (data.containsKey('monitor_time')) {
      context.handle(
          _monitorTimeMeta,
          monitorTime.isAcceptableOrUnknown(
              data['monitor_time']!, _monitorTimeMeta));
    }
    if (data.containsKey('vip_level')) {
      context.handle(_vipLevelMeta,
          vipLevel.isAcceptableOrUnknown(data['vip_level']!, _vipLevelMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    }
    if (data.containsKey('address_label')) {
      context.handle(
          _addressLabelMeta,
          addressLabel.isAcceptableOrUnknown(
              data['address_label']!, _addressLabelMeta));
    }
    if (data.containsKey('nft_chain')) {
      context.handle(_nftChainMeta,
          nftChain.isAcceptableOrUnknown(data['nft_chain']!, _nftChainMeta));
    }
    if (data.containsKey('nft_address')) {
      context.handle(
          _nftAddressMeta,
          nftAddress.isAcceptableOrUnknown(
              data['nft_address']!, _nftAddressMeta));
    }
    if (data.containsKey('nft_address_label')) {
      context.handle(
          _nftAddressLabelMeta,
          nftAddressLabel.isAcceptableOrUnknown(
              data['nft_address_label']!, _nftAddressLabelMeta));
    }
    if (data.containsKey('dapp_chain_list')) {
      context.handle(
          _dappChainListMeta,
          dappChainList.isAcceptableOrUnknown(
              data['dapp_chain_list']!, _dappChainListMeta));
    }
    if (data.containsKey('total_assets')) {
      context.handle(
          _totalAssetsMeta,
          totalAssets.isAcceptableOrUnknown(
              data['total_assets']!, _totalAssetsMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  WalletModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return WalletModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      deviceId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}device_id']),
      deviceType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}device_type']),
      chipVersion: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chip_version']),
      appVersion: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}app_version']),
      firmwareVersion: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}firmware_version']),
      batchId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}batch_id']),
      walletId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_id']),
      walletName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_name']),
      checked: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}checked']),
      isPassphraseWallet: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}is_passphrase_wallet']),
      bleMacId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ble_mac_id']),
      seedType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}seed_type']),
      walletType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}wallet_type']),
      monitorTime: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}monitor_time']),
      vipLevel: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}vip_level']),
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address']),
      addressLabel: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address_label']),
      nftChain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_chain']),
      nftAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_address']),
      nftAddressLabel: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}nft_address_label']),
      dappChainList: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_chain_list']),
      totalAssets: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}total_assets']),
    );
  }

  @override
  $WalletTableTable createAlias(String alias) {
    return $WalletTableTable(attachedDatabase, alias);
  }
}

class WalletModel extends DataClass implements Insertable<WalletModel> {
  final int id;
  final String? deviceId;
  final String? deviceType;
  final String? chipVersion;
  final String? appVersion;
  final String? firmwareVersion;
  final int? batchId;
  final String? walletId;
  final String? walletName;
  final bool? checked;
  final bool? isPassphraseWallet;
  final String? bleMacId;
  final int? seedType;
  final int? walletType;
  final String? monitorTime;
  final int? vipLevel;
  final String? chain;
  final String? address;
  final String? addressLabel;
  final String? nftChain;
  final String? nftAddress;
  final String? nftAddressLabel;
  final String? dappChainList;
  final String? totalAssets;
  const WalletModel(
      {required this.id,
      this.deviceId,
      this.deviceType,
      this.chipVersion,
      this.appVersion,
      this.firmwareVersion,
      this.batchId,
      this.walletId,
      this.walletName,
      this.checked,
      this.isPassphraseWallet,
      this.bleMacId,
      this.seedType,
      this.walletType,
      this.monitorTime,
      this.vipLevel,
      this.chain,
      this.address,
      this.addressLabel,
      this.nftChain,
      this.nftAddress,
      this.nftAddressLabel,
      this.dappChainList,
      this.totalAssets});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || deviceId != null) {
      map['device_id'] = Variable<String>(deviceId);
    }
    if (!nullToAbsent || deviceType != null) {
      map['device_type'] = Variable<String>(deviceType);
    }
    if (!nullToAbsent || chipVersion != null) {
      map['chip_version'] = Variable<String>(chipVersion);
    }
    if (!nullToAbsent || appVersion != null) {
      map['app_version'] = Variable<String>(appVersion);
    }
    if (!nullToAbsent || firmwareVersion != null) {
      map['firmware_version'] = Variable<String>(firmwareVersion);
    }
    if (!nullToAbsent || batchId != null) {
      map['batch_id'] = Variable<int>(batchId);
    }
    if (!nullToAbsent || walletId != null) {
      map['wallet_id'] = Variable<String>(walletId);
    }
    if (!nullToAbsent || walletName != null) {
      map['wallet_name'] = Variable<String>(walletName);
    }
    if (!nullToAbsent || checked != null) {
      map['checked'] = Variable<bool>(checked);
    }
    if (!nullToAbsent || isPassphraseWallet != null) {
      map['is_passphrase_wallet'] = Variable<bool>(isPassphraseWallet);
    }
    if (!nullToAbsent || bleMacId != null) {
      map['ble_mac_id'] = Variable<String>(bleMacId);
    }
    if (!nullToAbsent || seedType != null) {
      map['seed_type'] = Variable<int>(seedType);
    }
    if (!nullToAbsent || walletType != null) {
      map['wallet_type'] = Variable<int>(walletType);
    }
    if (!nullToAbsent || monitorTime != null) {
      map['monitor_time'] = Variable<String>(monitorTime);
    }
    if (!nullToAbsent || vipLevel != null) {
      map['vip_level'] = Variable<int>(vipLevel);
    }
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || address != null) {
      map['address'] = Variable<String>(address);
    }
    if (!nullToAbsent || addressLabel != null) {
      map['address_label'] = Variable<String>(addressLabel);
    }
    if (!nullToAbsent || nftChain != null) {
      map['nft_chain'] = Variable<String>(nftChain);
    }
    if (!nullToAbsent || nftAddress != null) {
      map['nft_address'] = Variable<String>(nftAddress);
    }
    if (!nullToAbsent || nftAddressLabel != null) {
      map['nft_address_label'] = Variable<String>(nftAddressLabel);
    }
    if (!nullToAbsent || dappChainList != null) {
      map['dapp_chain_list'] = Variable<String>(dappChainList);
    }
    if (!nullToAbsent || totalAssets != null) {
      map['total_assets'] = Variable<String>(totalAssets);
    }
    return map;
  }

  WalletTableCompanion toCompanion(bool nullToAbsent) {
    return WalletTableCompanion(
      id: Value(id),
      deviceId: deviceId == null && nullToAbsent
          ? const Value.absent()
          : Value(deviceId),
      deviceType: deviceType == null && nullToAbsent
          ? const Value.absent()
          : Value(deviceType),
      chipVersion: chipVersion == null && nullToAbsent
          ? const Value.absent()
          : Value(chipVersion),
      appVersion: appVersion == null && nullToAbsent
          ? const Value.absent()
          : Value(appVersion),
      firmwareVersion: firmwareVersion == null && nullToAbsent
          ? const Value.absent()
          : Value(firmwareVersion),
      batchId: batchId == null && nullToAbsent
          ? const Value.absent()
          : Value(batchId),
      walletId: walletId == null && nullToAbsent
          ? const Value.absent()
          : Value(walletId),
      walletName: walletName == null && nullToAbsent
          ? const Value.absent()
          : Value(walletName),
      checked: checked == null && nullToAbsent
          ? const Value.absent()
          : Value(checked),
      isPassphraseWallet: isPassphraseWallet == null && nullToAbsent
          ? const Value.absent()
          : Value(isPassphraseWallet),
      bleMacId: bleMacId == null && nullToAbsent
          ? const Value.absent()
          : Value(bleMacId),
      seedType: seedType == null && nullToAbsent
          ? const Value.absent()
          : Value(seedType),
      walletType: walletType == null && nullToAbsent
          ? const Value.absent()
          : Value(walletType),
      monitorTime: monitorTime == null && nullToAbsent
          ? const Value.absent()
          : Value(monitorTime),
      vipLevel: vipLevel == null && nullToAbsent
          ? const Value.absent()
          : Value(vipLevel),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      address: address == null && nullToAbsent
          ? const Value.absent()
          : Value(address),
      addressLabel: addressLabel == null && nullToAbsent
          ? const Value.absent()
          : Value(addressLabel),
      nftChain: nftChain == null && nullToAbsent
          ? const Value.absent()
          : Value(nftChain),
      nftAddress: nftAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(nftAddress),
      nftAddressLabel: nftAddressLabel == null && nullToAbsent
          ? const Value.absent()
          : Value(nftAddressLabel),
      dappChainList: dappChainList == null && nullToAbsent
          ? const Value.absent()
          : Value(dappChainList),
      totalAssets: totalAssets == null && nullToAbsent
          ? const Value.absent()
          : Value(totalAssets),
    );
  }

  factory WalletModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return WalletModel(
      id: serializer.fromJson<int>(json['id']),
      deviceId: serializer.fromJson<String?>(json['deviceId']),
      deviceType: serializer.fromJson<String?>(json['deviceType']),
      chipVersion: serializer.fromJson<String?>(json['chipVersion']),
      appVersion: serializer.fromJson<String?>(json['appVersion']),
      firmwareVersion: serializer.fromJson<String?>(json['firmwareVersion']),
      batchId: serializer.fromJson<int?>(json['batchId']),
      walletId: serializer.fromJson<String?>(json['walletId']),
      walletName: serializer.fromJson<String?>(json['walletName']),
      checked: serializer.fromJson<bool?>(json['checked']),
      isPassphraseWallet:
          serializer.fromJson<bool?>(json['isPassphraseWallet']),
      bleMacId: serializer.fromJson<String?>(json['bleMacId']),
      seedType: serializer.fromJson<int?>(json['seedType']),
      walletType: serializer.fromJson<int?>(json['walletType']),
      monitorTime: serializer.fromJson<String?>(json['monitorTime']),
      vipLevel: serializer.fromJson<int?>(json['vipLevel']),
      chain: serializer.fromJson<String?>(json['chain']),
      address: serializer.fromJson<String?>(json['address']),
      addressLabel: serializer.fromJson<String?>(json['addressLabel']),
      nftChain: serializer.fromJson<String?>(json['nftChain']),
      nftAddress: serializer.fromJson<String?>(json['nftAddress']),
      nftAddressLabel: serializer.fromJson<String?>(json['nftAddressLabel']),
      dappChainList: serializer.fromJson<String?>(json['dappChainList']),
      totalAssets: serializer.fromJson<String?>(json['totalAssets']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'deviceId': serializer.toJson<String?>(deviceId),
      'deviceType': serializer.toJson<String?>(deviceType),
      'chipVersion': serializer.toJson<String?>(chipVersion),
      'appVersion': serializer.toJson<String?>(appVersion),
      'firmwareVersion': serializer.toJson<String?>(firmwareVersion),
      'batchId': serializer.toJson<int?>(batchId),
      'walletId': serializer.toJson<String?>(walletId),
      'walletName': serializer.toJson<String?>(walletName),
      'checked': serializer.toJson<bool?>(checked),
      'isPassphraseWallet': serializer.toJson<bool?>(isPassphraseWallet),
      'bleMacId': serializer.toJson<String?>(bleMacId),
      'seedType': serializer.toJson<int?>(seedType),
      'walletType': serializer.toJson<int?>(walletType),
      'monitorTime': serializer.toJson<String?>(monitorTime),
      'vipLevel': serializer.toJson<int?>(vipLevel),
      'chain': serializer.toJson<String?>(chain),
      'address': serializer.toJson<String?>(address),
      'addressLabel': serializer.toJson<String?>(addressLabel),
      'nftChain': serializer.toJson<String?>(nftChain),
      'nftAddress': serializer.toJson<String?>(nftAddress),
      'nftAddressLabel': serializer.toJson<String?>(nftAddressLabel),
      'dappChainList': serializer.toJson<String?>(dappChainList),
      'totalAssets': serializer.toJson<String?>(totalAssets),
    };
  }

  WalletModel copyWith(
          {int? id,
          Value<String?> deviceId = const Value.absent(),
          Value<String?> deviceType = const Value.absent(),
          Value<String?> chipVersion = const Value.absent(),
          Value<String?> appVersion = const Value.absent(),
          Value<String?> firmwareVersion = const Value.absent(),
          Value<int?> batchId = const Value.absent(),
          Value<String?> walletId = const Value.absent(),
          Value<String?> walletName = const Value.absent(),
          Value<bool?> checked = const Value.absent(),
          Value<bool?> isPassphraseWallet = const Value.absent(),
          Value<String?> bleMacId = const Value.absent(),
          Value<int?> seedType = const Value.absent(),
          Value<int?> walletType = const Value.absent(),
          Value<String?> monitorTime = const Value.absent(),
          Value<int?> vipLevel = const Value.absent(),
          Value<String?> chain = const Value.absent(),
          Value<String?> address = const Value.absent(),
          Value<String?> addressLabel = const Value.absent(),
          Value<String?> nftChain = const Value.absent(),
          Value<String?> nftAddress = const Value.absent(),
          Value<String?> nftAddressLabel = const Value.absent(),
          Value<String?> dappChainList = const Value.absent(),
          Value<String?> totalAssets = const Value.absent()}) =>
      WalletModel(
        id: id ?? this.id,
        deviceId: deviceId.present ? deviceId.value : this.deviceId,
        deviceType: deviceType.present ? deviceType.value : this.deviceType,
        chipVersion: chipVersion.present ? chipVersion.value : this.chipVersion,
        appVersion: appVersion.present ? appVersion.value : this.appVersion,
        firmwareVersion: firmwareVersion.present
            ? firmwareVersion.value
            : this.firmwareVersion,
        batchId: batchId.present ? batchId.value : this.batchId,
        walletId: walletId.present ? walletId.value : this.walletId,
        walletName: walletName.present ? walletName.value : this.walletName,
        checked: checked.present ? checked.value : this.checked,
        isPassphraseWallet: isPassphraseWallet.present
            ? isPassphraseWallet.value
            : this.isPassphraseWallet,
        bleMacId: bleMacId.present ? bleMacId.value : this.bleMacId,
        seedType: seedType.present ? seedType.value : this.seedType,
        walletType: walletType.present ? walletType.value : this.walletType,
        monitorTime: monitorTime.present ? monitorTime.value : this.monitorTime,
        vipLevel: vipLevel.present ? vipLevel.value : this.vipLevel,
        chain: chain.present ? chain.value : this.chain,
        address: address.present ? address.value : this.address,
        addressLabel:
            addressLabel.present ? addressLabel.value : this.addressLabel,
        nftChain: nftChain.present ? nftChain.value : this.nftChain,
        nftAddress: nftAddress.present ? nftAddress.value : this.nftAddress,
        nftAddressLabel: nftAddressLabel.present
            ? nftAddressLabel.value
            : this.nftAddressLabel,
        dappChainList:
            dappChainList.present ? dappChainList.value : this.dappChainList,
        totalAssets: totalAssets.present ? totalAssets.value : this.totalAssets,
      );
  WalletModel copyWithCompanion(WalletTableCompanion data) {
    return WalletModel(
      id: data.id.present ? data.id.value : this.id,
      deviceId: data.deviceId.present ? data.deviceId.value : this.deviceId,
      deviceType:
          data.deviceType.present ? data.deviceType.value : this.deviceType,
      chipVersion:
          data.chipVersion.present ? data.chipVersion.value : this.chipVersion,
      appVersion:
          data.appVersion.present ? data.appVersion.value : this.appVersion,
      firmwareVersion: data.firmwareVersion.present
          ? data.firmwareVersion.value
          : this.firmwareVersion,
      batchId: data.batchId.present ? data.batchId.value : this.batchId,
      walletId: data.walletId.present ? data.walletId.value : this.walletId,
      walletName:
          data.walletName.present ? data.walletName.value : this.walletName,
      checked: data.checked.present ? data.checked.value : this.checked,
      isPassphraseWallet: data.isPassphraseWallet.present
          ? data.isPassphraseWallet.value
          : this.isPassphraseWallet,
      bleMacId: data.bleMacId.present ? data.bleMacId.value : this.bleMacId,
      seedType: data.seedType.present ? data.seedType.value : this.seedType,
      walletType:
          data.walletType.present ? data.walletType.value : this.walletType,
      monitorTime:
          data.monitorTime.present ? data.monitorTime.value : this.monitorTime,
      vipLevel: data.vipLevel.present ? data.vipLevel.value : this.vipLevel,
      chain: data.chain.present ? data.chain.value : this.chain,
      address: data.address.present ? data.address.value : this.address,
      addressLabel: data.addressLabel.present
          ? data.addressLabel.value
          : this.addressLabel,
      nftChain: data.nftChain.present ? data.nftChain.value : this.nftChain,
      nftAddress:
          data.nftAddress.present ? data.nftAddress.value : this.nftAddress,
      nftAddressLabel: data.nftAddressLabel.present
          ? data.nftAddressLabel.value
          : this.nftAddressLabel,
      dappChainList: data.dappChainList.present
          ? data.dappChainList.value
          : this.dappChainList,
      totalAssets:
          data.totalAssets.present ? data.totalAssets.value : this.totalAssets,
    );
  }

  @override
  String toString() {
    return (StringBuffer('WalletModel(')
          ..write('id: $id, ')
          ..write('deviceId: $deviceId, ')
          ..write('deviceType: $deviceType, ')
          ..write('chipVersion: $chipVersion, ')
          ..write('appVersion: $appVersion, ')
          ..write('firmwareVersion: $firmwareVersion, ')
          ..write('batchId: $batchId, ')
          ..write('walletId: $walletId, ')
          ..write('walletName: $walletName, ')
          ..write('checked: $checked, ')
          ..write('isPassphraseWallet: $isPassphraseWallet, ')
          ..write('bleMacId: $bleMacId, ')
          ..write('seedType: $seedType, ')
          ..write('walletType: $walletType, ')
          ..write('monitorTime: $monitorTime, ')
          ..write('vipLevel: $vipLevel, ')
          ..write('chain: $chain, ')
          ..write('address: $address, ')
          ..write('addressLabel: $addressLabel, ')
          ..write('nftChain: $nftChain, ')
          ..write('nftAddress: $nftAddress, ')
          ..write('nftAddressLabel: $nftAddressLabel, ')
          ..write('dappChainList: $dappChainList, ')
          ..write('totalAssets: $totalAssets')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        deviceId,
        deviceType,
        chipVersion,
        appVersion,
        firmwareVersion,
        batchId,
        walletId,
        walletName,
        checked,
        isPassphraseWallet,
        bleMacId,
        seedType,
        walletType,
        monitorTime,
        vipLevel,
        chain,
        address,
        addressLabel,
        nftChain,
        nftAddress,
        nftAddressLabel,
        dappChainList,
        totalAssets
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is WalletModel &&
          other.id == this.id &&
          other.deviceId == this.deviceId &&
          other.deviceType == this.deviceType &&
          other.chipVersion == this.chipVersion &&
          other.appVersion == this.appVersion &&
          other.firmwareVersion == this.firmwareVersion &&
          other.batchId == this.batchId &&
          other.walletId == this.walletId &&
          other.walletName == this.walletName &&
          other.checked == this.checked &&
          other.isPassphraseWallet == this.isPassphraseWallet &&
          other.bleMacId == this.bleMacId &&
          other.seedType == this.seedType &&
          other.walletType == this.walletType &&
          other.monitorTime == this.monitorTime &&
          other.vipLevel == this.vipLevel &&
          other.chain == this.chain &&
          other.address == this.address &&
          other.addressLabel == this.addressLabel &&
          other.nftChain == this.nftChain &&
          other.nftAddress == this.nftAddress &&
          other.nftAddressLabel == this.nftAddressLabel &&
          other.dappChainList == this.dappChainList &&
          other.totalAssets == this.totalAssets);
}

class WalletTableCompanion extends UpdateCompanion<WalletModel> {
  final Value<int> id;
  final Value<String?> deviceId;
  final Value<String?> deviceType;
  final Value<String?> chipVersion;
  final Value<String?> appVersion;
  final Value<String?> firmwareVersion;
  final Value<int?> batchId;
  final Value<String?> walletId;
  final Value<String?> walletName;
  final Value<bool?> checked;
  final Value<bool?> isPassphraseWallet;
  final Value<String?> bleMacId;
  final Value<int?> seedType;
  final Value<int?> walletType;
  final Value<String?> monitorTime;
  final Value<int?> vipLevel;
  final Value<String?> chain;
  final Value<String?> address;
  final Value<String?> addressLabel;
  final Value<String?> nftChain;
  final Value<String?> nftAddress;
  final Value<String?> nftAddressLabel;
  final Value<String?> dappChainList;
  final Value<String?> totalAssets;
  const WalletTableCompanion({
    this.id = const Value.absent(),
    this.deviceId = const Value.absent(),
    this.deviceType = const Value.absent(),
    this.chipVersion = const Value.absent(),
    this.appVersion = const Value.absent(),
    this.firmwareVersion = const Value.absent(),
    this.batchId = const Value.absent(),
    this.walletId = const Value.absent(),
    this.walletName = const Value.absent(),
    this.checked = const Value.absent(),
    this.isPassphraseWallet = const Value.absent(),
    this.bleMacId = const Value.absent(),
    this.seedType = const Value.absent(),
    this.walletType = const Value.absent(),
    this.monitorTime = const Value.absent(),
    this.vipLevel = const Value.absent(),
    this.chain = const Value.absent(),
    this.address = const Value.absent(),
    this.addressLabel = const Value.absent(),
    this.nftChain = const Value.absent(),
    this.nftAddress = const Value.absent(),
    this.nftAddressLabel = const Value.absent(),
    this.dappChainList = const Value.absent(),
    this.totalAssets = const Value.absent(),
  });
  WalletTableCompanion.insert({
    this.id = const Value.absent(),
    this.deviceId = const Value.absent(),
    this.deviceType = const Value.absent(),
    this.chipVersion = const Value.absent(),
    this.appVersion = const Value.absent(),
    this.firmwareVersion = const Value.absent(),
    this.batchId = const Value.absent(),
    this.walletId = const Value.absent(),
    this.walletName = const Value.absent(),
    this.checked = const Value.absent(),
    this.isPassphraseWallet = const Value.absent(),
    this.bleMacId = const Value.absent(),
    this.seedType = const Value.absent(),
    this.walletType = const Value.absent(),
    this.monitorTime = const Value.absent(),
    this.vipLevel = const Value.absent(),
    this.chain = const Value.absent(),
    this.address = const Value.absent(),
    this.addressLabel = const Value.absent(),
    this.nftChain = const Value.absent(),
    this.nftAddress = const Value.absent(),
    this.nftAddressLabel = const Value.absent(),
    this.dappChainList = const Value.absent(),
    this.totalAssets = const Value.absent(),
  });
  static Insertable<WalletModel> custom({
    Expression<int>? id,
    Expression<String>? deviceId,
    Expression<String>? deviceType,
    Expression<String>? chipVersion,
    Expression<String>? appVersion,
    Expression<String>? firmwareVersion,
    Expression<int>? batchId,
    Expression<String>? walletId,
    Expression<String>? walletName,
    Expression<bool>? checked,
    Expression<bool>? isPassphraseWallet,
    Expression<String>? bleMacId,
    Expression<int>? seedType,
    Expression<int>? walletType,
    Expression<String>? monitorTime,
    Expression<int>? vipLevel,
    Expression<String>? chain,
    Expression<String>? address,
    Expression<String>? addressLabel,
    Expression<String>? nftChain,
    Expression<String>? nftAddress,
    Expression<String>? nftAddressLabel,
    Expression<String>? dappChainList,
    Expression<String>? totalAssets,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (deviceId != null) 'device_id': deviceId,
      if (deviceType != null) 'device_type': deviceType,
      if (chipVersion != null) 'chip_version': chipVersion,
      if (appVersion != null) 'app_version': appVersion,
      if (firmwareVersion != null) 'firmware_version': firmwareVersion,
      if (batchId != null) 'batch_id': batchId,
      if (walletId != null) 'wallet_id': walletId,
      if (walletName != null) 'wallet_name': walletName,
      if (checked != null) 'checked': checked,
      if (isPassphraseWallet != null)
        'is_passphrase_wallet': isPassphraseWallet,
      if (bleMacId != null) 'ble_mac_id': bleMacId,
      if (seedType != null) 'seed_type': seedType,
      if (walletType != null) 'wallet_type': walletType,
      if (monitorTime != null) 'monitor_time': monitorTime,
      if (vipLevel != null) 'vip_level': vipLevel,
      if (chain != null) 'chain': chain,
      if (address != null) 'address': address,
      if (addressLabel != null) 'address_label': addressLabel,
      if (nftChain != null) 'nft_chain': nftChain,
      if (nftAddress != null) 'nft_address': nftAddress,
      if (nftAddressLabel != null) 'nft_address_label': nftAddressLabel,
      if (dappChainList != null) 'dapp_chain_list': dappChainList,
      if (totalAssets != null) 'total_assets': totalAssets,
    });
  }

  WalletTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? deviceId,
      Value<String?>? deviceType,
      Value<String?>? chipVersion,
      Value<String?>? appVersion,
      Value<String?>? firmwareVersion,
      Value<int?>? batchId,
      Value<String?>? walletId,
      Value<String?>? walletName,
      Value<bool?>? checked,
      Value<bool?>? isPassphraseWallet,
      Value<String?>? bleMacId,
      Value<int?>? seedType,
      Value<int?>? walletType,
      Value<String?>? monitorTime,
      Value<int?>? vipLevel,
      Value<String?>? chain,
      Value<String?>? address,
      Value<String?>? addressLabel,
      Value<String?>? nftChain,
      Value<String?>? nftAddress,
      Value<String?>? nftAddressLabel,
      Value<String?>? dappChainList,
      Value<String?>? totalAssets}) {
    return WalletTableCompanion(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      deviceType: deviceType ?? this.deviceType,
      chipVersion: chipVersion ?? this.chipVersion,
      appVersion: appVersion ?? this.appVersion,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
      batchId: batchId ?? this.batchId,
      walletId: walletId ?? this.walletId,
      walletName: walletName ?? this.walletName,
      checked: checked ?? this.checked,
      isPassphraseWallet: isPassphraseWallet ?? this.isPassphraseWallet,
      bleMacId: bleMacId ?? this.bleMacId,
      seedType: seedType ?? this.seedType,
      walletType: walletType ?? this.walletType,
      monitorTime: monitorTime ?? this.monitorTime,
      vipLevel: vipLevel ?? this.vipLevel,
      chain: chain ?? this.chain,
      address: address ?? this.address,
      addressLabel: addressLabel ?? this.addressLabel,
      nftChain: nftChain ?? this.nftChain,
      nftAddress: nftAddress ?? this.nftAddress,
      nftAddressLabel: nftAddressLabel ?? this.nftAddressLabel,
      dappChainList: dappChainList ?? this.dappChainList,
      totalAssets: totalAssets ?? this.totalAssets,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (deviceId.present) {
      map['device_id'] = Variable<String>(deviceId.value);
    }
    if (deviceType.present) {
      map['device_type'] = Variable<String>(deviceType.value);
    }
    if (chipVersion.present) {
      map['chip_version'] = Variable<String>(chipVersion.value);
    }
    if (appVersion.present) {
      map['app_version'] = Variable<String>(appVersion.value);
    }
    if (firmwareVersion.present) {
      map['firmware_version'] = Variable<String>(firmwareVersion.value);
    }
    if (batchId.present) {
      map['batch_id'] = Variable<int>(batchId.value);
    }
    if (walletId.present) {
      map['wallet_id'] = Variable<String>(walletId.value);
    }
    if (walletName.present) {
      map['wallet_name'] = Variable<String>(walletName.value);
    }
    if (checked.present) {
      map['checked'] = Variable<bool>(checked.value);
    }
    if (isPassphraseWallet.present) {
      map['is_passphrase_wallet'] = Variable<bool>(isPassphraseWallet.value);
    }
    if (bleMacId.present) {
      map['ble_mac_id'] = Variable<String>(bleMacId.value);
    }
    if (seedType.present) {
      map['seed_type'] = Variable<int>(seedType.value);
    }
    if (walletType.present) {
      map['wallet_type'] = Variable<int>(walletType.value);
    }
    if (monitorTime.present) {
      map['monitor_time'] = Variable<String>(monitorTime.value);
    }
    if (vipLevel.present) {
      map['vip_level'] = Variable<int>(vipLevel.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (addressLabel.present) {
      map['address_label'] = Variable<String>(addressLabel.value);
    }
    if (nftChain.present) {
      map['nft_chain'] = Variable<String>(nftChain.value);
    }
    if (nftAddress.present) {
      map['nft_address'] = Variable<String>(nftAddress.value);
    }
    if (nftAddressLabel.present) {
      map['nft_address_label'] = Variable<String>(nftAddressLabel.value);
    }
    if (dappChainList.present) {
      map['dapp_chain_list'] = Variable<String>(dappChainList.value);
    }
    if (totalAssets.present) {
      map['total_assets'] = Variable<String>(totalAssets.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('WalletTableCompanion(')
          ..write('id: $id, ')
          ..write('deviceId: $deviceId, ')
          ..write('deviceType: $deviceType, ')
          ..write('chipVersion: $chipVersion, ')
          ..write('appVersion: $appVersion, ')
          ..write('firmwareVersion: $firmwareVersion, ')
          ..write('batchId: $batchId, ')
          ..write('walletId: $walletId, ')
          ..write('walletName: $walletName, ')
          ..write('checked: $checked, ')
          ..write('isPassphraseWallet: $isPassphraseWallet, ')
          ..write('bleMacId: $bleMacId, ')
          ..write('seedType: $seedType, ')
          ..write('walletType: $walletType, ')
          ..write('monitorTime: $monitorTime, ')
          ..write('vipLevel: $vipLevel, ')
          ..write('chain: $chain, ')
          ..write('address: $address, ')
          ..write('addressLabel: $addressLabel, ')
          ..write('nftChain: $nftChain, ')
          ..write('nftAddress: $nftAddress, ')
          ..write('nftAddressLabel: $nftAddressLabel, ')
          ..write('dappChainList: $dappChainList, ')
          ..write('totalAssets: $totalAssets')
          ..write(')'))
        .toString();
  }
}

class $AddressTableTable extends AddressTable
    with TableInfo<$AddressTableTable, AddressModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AddressTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _walletIdMeta =
      const VerificationMeta('walletId');
  @override
  late final GeneratedColumn<String> walletId = GeneratedColumn<String>(
      'wallet_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainCodeMeta =
      const VerificationMeta('chainCode');
  @override
  late final GeneratedColumn<String> chainCode = GeneratedColumn<String>(
      'chain_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  @override
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _publickeyMeta =
      const VerificationMeta('publickey');
  @override
  late final GeneratedColumn<String> publickey = GeneratedColumn<String>(
      'publickey', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _xpubDataMeta =
      const VerificationMeta('xpubData');
  @override
  late final GeneratedColumn<String> xpubData = GeneratedColumn<String>(
      'xpub_data', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _pathMeta = const VerificationMeta('path');
  @override
  late final GeneratedColumn<String> path = GeneratedColumn<String>(
      'path', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _balanceMeta =
      const VerificationMeta('balance');
  @override
  late final GeneratedColumn<String> balance = GeneratedColumn<String>(
      'balance', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _sortedIdMeta =
      const VerificationMeta('sortedId');
  @override
  late final GeneratedColumn<int> sortedId = GeneratedColumn<int>(
      'sorted_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _addressIndexMeta =
      const VerificationMeta('addressIndex');
  @override
  late final GeneratedColumn<int> addressIndex = GeneratedColumn<int>(
      'address_index', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _addressLabelMeta =
      const VerificationMeta('addressLabel');
  @override
  late final GeneratedColumn<String> addressLabel = GeneratedColumn<String>(
      'address_label', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isSelectedMeta =
      const VerificationMeta('isSelected');
  @override
  late final GeneratedColumn<bool> isSelected = GeneratedColumn<bool>(
      'is_selected', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_selected" IN (0, 1))'));
  static const VerificationMeta _isUploadedMeta =
      const VerificationMeta('isUploaded');
  @override
  late final GeneratedColumn<bool> isUploaded = GeneratedColumn<bool>(
      'is_uploaded', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_uploaded" IN (0, 1))'));
  static const VerificationMeta _slip44IdMeta =
      const VerificationMeta('slip44Id');
  @override
  late final GeneratedColumn<int> slip44Id = GeneratedColumn<int>(
      'slip44_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _segwitTypeMeta =
      const VerificationMeta('segwitType');
  @override
  late final GeneratedColumn<int> segwitType = GeneratedColumn<int>(
      'segwit_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _walletTypeMeta =
      const VerificationMeta('walletType');
  @override
  late final GeneratedColumn<int> walletType = GeneratedColumn<int>(
      'wallet_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _nftAmountCacheMeta =
      const VerificationMeta('nftAmountCache');
  @override
  late final GeneratedColumn<String> nftAmountCache = GeneratedColumn<String>(
      'nft_amount_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftAssetsCacheMeta =
      const VerificationMeta('nftAssetsCache');
  @override
  late final GeneratedColumn<String> nftAssetsCache = GeneratedColumn<String>(
      'nft_assets_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftCollectionCacheMeta =
      const VerificationMeta('nftCollectionCache');
  @override
  late final GeneratedColumn<String> nftCollectionCache =
      GeneratedColumn<String>('nft_collection_cache', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nftUsdCacheMeta =
      const VerificationMeta('nftUsdCache');
  @override
  late final GeneratedColumn<String> nftUsdCache = GeneratedColumn<String>(
      'nft_usd_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _totalBalanceMeta =
      const VerificationMeta('totalBalance');
  @override
  late final GeneratedColumn<String> totalBalance = GeneratedColumn<String>(
      'total_balance', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _availableBalanceMeta =
      const VerificationMeta('availableBalance');
  @override
  late final GeneratedColumn<String> availableBalance = GeneratedColumn<String>(
      'available_balance', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _unavailableBalanceMeta =
      const VerificationMeta('unavailableBalance');
  @override
  late final GeneratedColumn<String> unavailableBalance =
      GeneratedColumn<String>('unavailable_balance', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _eosAccountInfoCacheMeta =
      const VerificationMeta('eosAccountInfoCache');
  @override
  late final GeneratedColumn<String> eosAccountInfoCache =
      GeneratedColumn<String>('eos_account_info_cache', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _tronAccountInfoCacheMeta =
      const VerificationMeta('tronAccountInfoCache');
  @override
  late final GeneratedColumn<String> tronAccountInfoCache =
      GeneratedColumn<String>('tron_account_info_cache', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _tronResourceCacheMeta =
      const VerificationMeta('tronResourceCache');
  @override
  late final GeneratedColumn<String> tronResourceCache =
      GeneratedColumn<String>('tron_resource_cache', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _utxoCacheMeta =
      const VerificationMeta('utxoCache');
  @override
  late final GeneratedColumn<String> utxoCache = GeneratedColumn<String>(
      'utxo_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _solMinimumRentMeta =
      const VerificationMeta('solMinimumRent');
  @override
  late final GeneratedColumn<String> solMinimumRent = GeneratedColumn<String>(
      'sol_minimum_rent', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        walletId,
        chain,
        chainCode,
        address,
        publickey,
        xpubData,
        path,
        balance,
        sortedId,
        addressIndex,
        addressLabel,
        isSelected,
        isUploaded,
        slip44Id,
        segwitType,
        walletType,
        nftAmountCache,
        nftAssetsCache,
        nftCollectionCache,
        nftUsdCache,
        totalBalance,
        availableBalance,
        unavailableBalance,
        eosAccountInfoCache,
        tronAccountInfoCache,
        tronResourceCache,
        utxoCache,
        solMinimumRent
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'address_table';
  @override
  VerificationContext validateIntegrity(Insertable<AddressModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('wallet_id')) {
      context.handle(_walletIdMeta,
          walletId.isAcceptableOrUnknown(data['wallet_id']!, _walletIdMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('chain_code')) {
      context.handle(_chainCodeMeta,
          chainCode.isAcceptableOrUnknown(data['chain_code']!, _chainCodeMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    }
    if (data.containsKey('publickey')) {
      context.handle(_publickeyMeta,
          publickey.isAcceptableOrUnknown(data['publickey']!, _publickeyMeta));
    }
    if (data.containsKey('xpub_data')) {
      context.handle(_xpubDataMeta,
          xpubData.isAcceptableOrUnknown(data['xpub_data']!, _xpubDataMeta));
    }
    if (data.containsKey('path')) {
      context.handle(
          _pathMeta, path.isAcceptableOrUnknown(data['path']!, _pathMeta));
    }
    if (data.containsKey('balance')) {
      context.handle(_balanceMeta,
          balance.isAcceptableOrUnknown(data['balance']!, _balanceMeta));
    }
    if (data.containsKey('sorted_id')) {
      context.handle(_sortedIdMeta,
          sortedId.isAcceptableOrUnknown(data['sorted_id']!, _sortedIdMeta));
    }
    if (data.containsKey('address_index')) {
      context.handle(
          _addressIndexMeta,
          addressIndex.isAcceptableOrUnknown(
              data['address_index']!, _addressIndexMeta));
    }
    if (data.containsKey('address_label')) {
      context.handle(
          _addressLabelMeta,
          addressLabel.isAcceptableOrUnknown(
              data['address_label']!, _addressLabelMeta));
    }
    if (data.containsKey('is_selected')) {
      context.handle(
          _isSelectedMeta,
          isSelected.isAcceptableOrUnknown(
              data['is_selected']!, _isSelectedMeta));
    }
    if (data.containsKey('is_uploaded')) {
      context.handle(
          _isUploadedMeta,
          isUploaded.isAcceptableOrUnknown(
              data['is_uploaded']!, _isUploadedMeta));
    }
    if (data.containsKey('slip44_id')) {
      context.handle(_slip44IdMeta,
          slip44Id.isAcceptableOrUnknown(data['slip44_id']!, _slip44IdMeta));
    }
    if (data.containsKey('segwit_type')) {
      context.handle(
          _segwitTypeMeta,
          segwitType.isAcceptableOrUnknown(
              data['segwit_type']!, _segwitTypeMeta));
    }
    if (data.containsKey('wallet_type')) {
      context.handle(
          _walletTypeMeta,
          walletType.isAcceptableOrUnknown(
              data['wallet_type']!, _walletTypeMeta));
    }
    if (data.containsKey('nft_amount_cache')) {
      context.handle(
          _nftAmountCacheMeta,
          nftAmountCache.isAcceptableOrUnknown(
              data['nft_amount_cache']!, _nftAmountCacheMeta));
    }
    if (data.containsKey('nft_assets_cache')) {
      context.handle(
          _nftAssetsCacheMeta,
          nftAssetsCache.isAcceptableOrUnknown(
              data['nft_assets_cache']!, _nftAssetsCacheMeta));
    }
    if (data.containsKey('nft_collection_cache')) {
      context.handle(
          _nftCollectionCacheMeta,
          nftCollectionCache.isAcceptableOrUnknown(
              data['nft_collection_cache']!, _nftCollectionCacheMeta));
    }
    if (data.containsKey('nft_usd_cache')) {
      context.handle(
          _nftUsdCacheMeta,
          nftUsdCache.isAcceptableOrUnknown(
              data['nft_usd_cache']!, _nftUsdCacheMeta));
    }
    if (data.containsKey('total_balance')) {
      context.handle(
          _totalBalanceMeta,
          totalBalance.isAcceptableOrUnknown(
              data['total_balance']!, _totalBalanceMeta));
    }
    if (data.containsKey('available_balance')) {
      context.handle(
          _availableBalanceMeta,
          availableBalance.isAcceptableOrUnknown(
              data['available_balance']!, _availableBalanceMeta));
    }
    if (data.containsKey('unavailable_balance')) {
      context.handle(
          _unavailableBalanceMeta,
          unavailableBalance.isAcceptableOrUnknown(
              data['unavailable_balance']!, _unavailableBalanceMeta));
    }
    if (data.containsKey('eos_account_info_cache')) {
      context.handle(
          _eosAccountInfoCacheMeta,
          eosAccountInfoCache.isAcceptableOrUnknown(
              data['eos_account_info_cache']!, _eosAccountInfoCacheMeta));
    }
    if (data.containsKey('tron_account_info_cache')) {
      context.handle(
          _tronAccountInfoCacheMeta,
          tronAccountInfoCache.isAcceptableOrUnknown(
              data['tron_account_info_cache']!, _tronAccountInfoCacheMeta));
    }
    if (data.containsKey('tron_resource_cache')) {
      context.handle(
          _tronResourceCacheMeta,
          tronResourceCache.isAcceptableOrUnknown(
              data['tron_resource_cache']!, _tronResourceCacheMeta));
    }
    if (data.containsKey('utxo_cache')) {
      context.handle(_utxoCacheMeta,
          utxoCache.isAcceptableOrUnknown(data['utxo_cache']!, _utxoCacheMeta));
    }
    if (data.containsKey('sol_minimum_rent')) {
      context.handle(
          _solMinimumRentMeta,
          solMinimumRent.isAcceptableOrUnknown(
              data['sol_minimum_rent']!, _solMinimumRentMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AddressModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AddressModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      walletId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_id']),
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      chainCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_code']),
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address']),
      publickey: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}publickey']),
      xpubData: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}xpub_data']),
      path: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}path']),
      balance: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}balance']),
      sortedId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}sorted_id']),
      addressIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}address_index']),
      addressLabel: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address_label']),
      isSelected: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_selected']),
      isUploaded: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_uploaded']),
      slip44Id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}slip44_id']),
      segwitType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}segwit_type']),
      walletType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}wallet_type']),
      nftAmountCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}nft_amount_cache']),
      nftAssetsCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}nft_assets_cache']),
      nftCollectionCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}nft_collection_cache']),
      nftUsdCache: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_usd_cache']),
      totalBalance: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}total_balance']),
      availableBalance: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}available_balance']),
      unavailableBalance: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}unavailable_balance']),
      eosAccountInfoCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}eos_account_info_cache']),
      tronAccountInfoCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}tron_account_info_cache']),
      tronResourceCache: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}tron_resource_cache']),
      utxoCache: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}utxo_cache']),
      solMinimumRent: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}sol_minimum_rent']),
    );
  }

  @override
  $AddressTableTable createAlias(String alias) {
    return $AddressTableTable(attachedDatabase, alias);
  }
}

class AddressModel extends DataClass implements Insertable<AddressModel> {
  final int id;
  final String? walletId;
  final String? chain;
  final String? chainCode;
  final String? address;
  final String? publickey;
  final String? xpubData;
  final String? path;
  final String? balance;
  final int? sortedId;
  final int? addressIndex;
  final String? addressLabel;
  final bool? isSelected;
  final bool? isUploaded;
  final int? slip44Id;
  final int? segwitType;
  final int? walletType;
  final String? nftAmountCache;
  final String? nftAssetsCache;
  final String? nftCollectionCache;
  final String? nftUsdCache;

  /// --- btc 系列 ----
  final String? totalBalance;
  final String? availableBalance;
  final String? unavailableBalance;
  final String? eosAccountInfoCache;
  final String? tronAccountInfoCache;
  final String? tronResourceCache;
  final String? utxoCache;
  final String? solMinimumRent;
  const AddressModel(
      {required this.id,
      this.walletId,
      this.chain,
      this.chainCode,
      this.address,
      this.publickey,
      this.xpubData,
      this.path,
      this.balance,
      this.sortedId,
      this.addressIndex,
      this.addressLabel,
      this.isSelected,
      this.isUploaded,
      this.slip44Id,
      this.segwitType,
      this.walletType,
      this.nftAmountCache,
      this.nftAssetsCache,
      this.nftCollectionCache,
      this.nftUsdCache,
      this.totalBalance,
      this.availableBalance,
      this.unavailableBalance,
      this.eosAccountInfoCache,
      this.tronAccountInfoCache,
      this.tronResourceCache,
      this.utxoCache,
      this.solMinimumRent});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || walletId != null) {
      map['wallet_id'] = Variable<String>(walletId);
    }
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || chainCode != null) {
      map['chain_code'] = Variable<String>(chainCode);
    }
    if (!nullToAbsent || address != null) {
      map['address'] = Variable<String>(address);
    }
    if (!nullToAbsent || publickey != null) {
      map['publickey'] = Variable<String>(publickey);
    }
    if (!nullToAbsent || xpubData != null) {
      map['xpub_data'] = Variable<String>(xpubData);
    }
    if (!nullToAbsent || path != null) {
      map['path'] = Variable<String>(path);
    }
    if (!nullToAbsent || balance != null) {
      map['balance'] = Variable<String>(balance);
    }
    if (!nullToAbsent || sortedId != null) {
      map['sorted_id'] = Variable<int>(sortedId);
    }
    if (!nullToAbsent || addressIndex != null) {
      map['address_index'] = Variable<int>(addressIndex);
    }
    if (!nullToAbsent || addressLabel != null) {
      map['address_label'] = Variable<String>(addressLabel);
    }
    if (!nullToAbsent || isSelected != null) {
      map['is_selected'] = Variable<bool>(isSelected);
    }
    if (!nullToAbsent || isUploaded != null) {
      map['is_uploaded'] = Variable<bool>(isUploaded);
    }
    if (!nullToAbsent || slip44Id != null) {
      map['slip44_id'] = Variable<int>(slip44Id);
    }
    if (!nullToAbsent || segwitType != null) {
      map['segwit_type'] = Variable<int>(segwitType);
    }
    if (!nullToAbsent || walletType != null) {
      map['wallet_type'] = Variable<int>(walletType);
    }
    if (!nullToAbsent || nftAmountCache != null) {
      map['nft_amount_cache'] = Variable<String>(nftAmountCache);
    }
    if (!nullToAbsent || nftAssetsCache != null) {
      map['nft_assets_cache'] = Variable<String>(nftAssetsCache);
    }
    if (!nullToAbsent || nftCollectionCache != null) {
      map['nft_collection_cache'] = Variable<String>(nftCollectionCache);
    }
    if (!nullToAbsent || nftUsdCache != null) {
      map['nft_usd_cache'] = Variable<String>(nftUsdCache);
    }
    if (!nullToAbsent || totalBalance != null) {
      map['total_balance'] = Variable<String>(totalBalance);
    }
    if (!nullToAbsent || availableBalance != null) {
      map['available_balance'] = Variable<String>(availableBalance);
    }
    if (!nullToAbsent || unavailableBalance != null) {
      map['unavailable_balance'] = Variable<String>(unavailableBalance);
    }
    if (!nullToAbsent || eosAccountInfoCache != null) {
      map['eos_account_info_cache'] = Variable<String>(eosAccountInfoCache);
    }
    if (!nullToAbsent || tronAccountInfoCache != null) {
      map['tron_account_info_cache'] = Variable<String>(tronAccountInfoCache);
    }
    if (!nullToAbsent || tronResourceCache != null) {
      map['tron_resource_cache'] = Variable<String>(tronResourceCache);
    }
    if (!nullToAbsent || utxoCache != null) {
      map['utxo_cache'] = Variable<String>(utxoCache);
    }
    if (!nullToAbsent || solMinimumRent != null) {
      map['sol_minimum_rent'] = Variable<String>(solMinimumRent);
    }
    return map;
  }

  AddressTableCompanion toCompanion(bool nullToAbsent) {
    return AddressTableCompanion(
      id: Value(id),
      walletId: walletId == null && nullToAbsent
          ? const Value.absent()
          : Value(walletId),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      chainCode: chainCode == null && nullToAbsent
          ? const Value.absent()
          : Value(chainCode),
      address: address == null && nullToAbsent
          ? const Value.absent()
          : Value(address),
      publickey: publickey == null && nullToAbsent
          ? const Value.absent()
          : Value(publickey),
      xpubData: xpubData == null && nullToAbsent
          ? const Value.absent()
          : Value(xpubData),
      path: path == null && nullToAbsent ? const Value.absent() : Value(path),
      balance: balance == null && nullToAbsent
          ? const Value.absent()
          : Value(balance),
      sortedId: sortedId == null && nullToAbsent
          ? const Value.absent()
          : Value(sortedId),
      addressIndex: addressIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(addressIndex),
      addressLabel: addressLabel == null && nullToAbsent
          ? const Value.absent()
          : Value(addressLabel),
      isSelected: isSelected == null && nullToAbsent
          ? const Value.absent()
          : Value(isSelected),
      isUploaded: isUploaded == null && nullToAbsent
          ? const Value.absent()
          : Value(isUploaded),
      slip44Id: slip44Id == null && nullToAbsent
          ? const Value.absent()
          : Value(slip44Id),
      segwitType: segwitType == null && nullToAbsent
          ? const Value.absent()
          : Value(segwitType),
      walletType: walletType == null && nullToAbsent
          ? const Value.absent()
          : Value(walletType),
      nftAmountCache: nftAmountCache == null && nullToAbsent
          ? const Value.absent()
          : Value(nftAmountCache),
      nftAssetsCache: nftAssetsCache == null && nullToAbsent
          ? const Value.absent()
          : Value(nftAssetsCache),
      nftCollectionCache: nftCollectionCache == null && nullToAbsent
          ? const Value.absent()
          : Value(nftCollectionCache),
      nftUsdCache: nftUsdCache == null && nullToAbsent
          ? const Value.absent()
          : Value(nftUsdCache),
      totalBalance: totalBalance == null && nullToAbsent
          ? const Value.absent()
          : Value(totalBalance),
      availableBalance: availableBalance == null && nullToAbsent
          ? const Value.absent()
          : Value(availableBalance),
      unavailableBalance: unavailableBalance == null && nullToAbsent
          ? const Value.absent()
          : Value(unavailableBalance),
      eosAccountInfoCache: eosAccountInfoCache == null && nullToAbsent
          ? const Value.absent()
          : Value(eosAccountInfoCache),
      tronAccountInfoCache: tronAccountInfoCache == null && nullToAbsent
          ? const Value.absent()
          : Value(tronAccountInfoCache),
      tronResourceCache: tronResourceCache == null && nullToAbsent
          ? const Value.absent()
          : Value(tronResourceCache),
      utxoCache: utxoCache == null && nullToAbsent
          ? const Value.absent()
          : Value(utxoCache),
      solMinimumRent: solMinimumRent == null && nullToAbsent
          ? const Value.absent()
          : Value(solMinimumRent),
    );
  }

  factory AddressModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AddressModel(
      id: serializer.fromJson<int>(json['id']),
      walletId: serializer.fromJson<String?>(json['walletId']),
      chain: serializer.fromJson<String?>(json['chain']),
      chainCode: serializer.fromJson<String?>(json['chainCode']),
      address: serializer.fromJson<String?>(json['address']),
      publickey: serializer.fromJson<String?>(json['publickey']),
      xpubData: serializer.fromJson<String?>(json['xpubData']),
      path: serializer.fromJson<String?>(json['path']),
      balance: serializer.fromJson<String?>(json['balance']),
      sortedId: serializer.fromJson<int?>(json['sortedId']),
      addressIndex: serializer.fromJson<int?>(json['addressIndex']),
      addressLabel: serializer.fromJson<String?>(json['addressLabel']),
      isSelected: serializer.fromJson<bool?>(json['isSelected']),
      isUploaded: serializer.fromJson<bool?>(json['isUploaded']),
      slip44Id: serializer.fromJson<int?>(json['slip44Id']),
      segwitType: serializer.fromJson<int?>(json['segwitType']),
      walletType: serializer.fromJson<int?>(json['walletType']),
      nftAmountCache: serializer.fromJson<String?>(json['nftAmountCache']),
      nftAssetsCache: serializer.fromJson<String?>(json['nftAssetsCache']),
      nftCollectionCache:
          serializer.fromJson<String?>(json['nftCollectionCache']),
      nftUsdCache: serializer.fromJson<String?>(json['nftUsdCache']),
      totalBalance: serializer.fromJson<String?>(json['totalBalance']),
      availableBalance: serializer.fromJson<String?>(json['availableBalance']),
      unavailableBalance:
          serializer.fromJson<String?>(json['unavailableBalance']),
      eosAccountInfoCache:
          serializer.fromJson<String?>(json['eosAccountInfoCache']),
      tronAccountInfoCache:
          serializer.fromJson<String?>(json['tronAccountInfoCache']),
      tronResourceCache:
          serializer.fromJson<String?>(json['tronResourceCache']),
      utxoCache: serializer.fromJson<String?>(json['utxoCache']),
      solMinimumRent: serializer.fromJson<String?>(json['solMinimumRent']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'walletId': serializer.toJson<String?>(walletId),
      'chain': serializer.toJson<String?>(chain),
      'chainCode': serializer.toJson<String?>(chainCode),
      'address': serializer.toJson<String?>(address),
      'publickey': serializer.toJson<String?>(publickey),
      'xpubData': serializer.toJson<String?>(xpubData),
      'path': serializer.toJson<String?>(path),
      'balance': serializer.toJson<String?>(balance),
      'sortedId': serializer.toJson<int?>(sortedId),
      'addressIndex': serializer.toJson<int?>(addressIndex),
      'addressLabel': serializer.toJson<String?>(addressLabel),
      'isSelected': serializer.toJson<bool?>(isSelected),
      'isUploaded': serializer.toJson<bool?>(isUploaded),
      'slip44Id': serializer.toJson<int?>(slip44Id),
      'segwitType': serializer.toJson<int?>(segwitType),
      'walletType': serializer.toJson<int?>(walletType),
      'nftAmountCache': serializer.toJson<String?>(nftAmountCache),
      'nftAssetsCache': serializer.toJson<String?>(nftAssetsCache),
      'nftCollectionCache': serializer.toJson<String?>(nftCollectionCache),
      'nftUsdCache': serializer.toJson<String?>(nftUsdCache),
      'totalBalance': serializer.toJson<String?>(totalBalance),
      'availableBalance': serializer.toJson<String?>(availableBalance),
      'unavailableBalance': serializer.toJson<String?>(unavailableBalance),
      'eosAccountInfoCache': serializer.toJson<String?>(eosAccountInfoCache),
      'tronAccountInfoCache': serializer.toJson<String?>(tronAccountInfoCache),
      'tronResourceCache': serializer.toJson<String?>(tronResourceCache),
      'utxoCache': serializer.toJson<String?>(utxoCache),
      'solMinimumRent': serializer.toJson<String?>(solMinimumRent),
    };
  }

  AddressModel copyWith(
          {int? id,
          Value<String?> walletId = const Value.absent(),
          Value<String?> chain = const Value.absent(),
          Value<String?> chainCode = const Value.absent(),
          Value<String?> address = const Value.absent(),
          Value<String?> publickey = const Value.absent(),
          Value<String?> xpubData = const Value.absent(),
          Value<String?> path = const Value.absent(),
          Value<String?> balance = const Value.absent(),
          Value<int?> sortedId = const Value.absent(),
          Value<int?> addressIndex = const Value.absent(),
          Value<String?> addressLabel = const Value.absent(),
          Value<bool?> isSelected = const Value.absent(),
          Value<bool?> isUploaded = const Value.absent(),
          Value<int?> slip44Id = const Value.absent(),
          Value<int?> segwitType = const Value.absent(),
          Value<int?> walletType = const Value.absent(),
          Value<String?> nftAmountCache = const Value.absent(),
          Value<String?> nftAssetsCache = const Value.absent(),
          Value<String?> nftCollectionCache = const Value.absent(),
          Value<String?> nftUsdCache = const Value.absent(),
          Value<String?> totalBalance = const Value.absent(),
          Value<String?> availableBalance = const Value.absent(),
          Value<String?> unavailableBalance = const Value.absent(),
          Value<String?> eosAccountInfoCache = const Value.absent(),
          Value<String?> tronAccountInfoCache = const Value.absent(),
          Value<String?> tronResourceCache = const Value.absent(),
          Value<String?> utxoCache = const Value.absent(),
          Value<String?> solMinimumRent = const Value.absent()}) =>
      AddressModel(
        id: id ?? this.id,
        walletId: walletId.present ? walletId.value : this.walletId,
        chain: chain.present ? chain.value : this.chain,
        chainCode: chainCode.present ? chainCode.value : this.chainCode,
        address: address.present ? address.value : this.address,
        publickey: publickey.present ? publickey.value : this.publickey,
        xpubData: xpubData.present ? xpubData.value : this.xpubData,
        path: path.present ? path.value : this.path,
        balance: balance.present ? balance.value : this.balance,
        sortedId: sortedId.present ? sortedId.value : this.sortedId,
        addressIndex:
            addressIndex.present ? addressIndex.value : this.addressIndex,
        addressLabel:
            addressLabel.present ? addressLabel.value : this.addressLabel,
        isSelected: isSelected.present ? isSelected.value : this.isSelected,
        isUploaded: isUploaded.present ? isUploaded.value : this.isUploaded,
        slip44Id: slip44Id.present ? slip44Id.value : this.slip44Id,
        segwitType: segwitType.present ? segwitType.value : this.segwitType,
        walletType: walletType.present ? walletType.value : this.walletType,
        nftAmountCache:
            nftAmountCache.present ? nftAmountCache.value : this.nftAmountCache,
        nftAssetsCache:
            nftAssetsCache.present ? nftAssetsCache.value : this.nftAssetsCache,
        nftCollectionCache: nftCollectionCache.present
            ? nftCollectionCache.value
            : this.nftCollectionCache,
        nftUsdCache: nftUsdCache.present ? nftUsdCache.value : this.nftUsdCache,
        totalBalance:
            totalBalance.present ? totalBalance.value : this.totalBalance,
        availableBalance: availableBalance.present
            ? availableBalance.value
            : this.availableBalance,
        unavailableBalance: unavailableBalance.present
            ? unavailableBalance.value
            : this.unavailableBalance,
        eosAccountInfoCache: eosAccountInfoCache.present
            ? eosAccountInfoCache.value
            : this.eosAccountInfoCache,
        tronAccountInfoCache: tronAccountInfoCache.present
            ? tronAccountInfoCache.value
            : this.tronAccountInfoCache,
        tronResourceCache: tronResourceCache.present
            ? tronResourceCache.value
            : this.tronResourceCache,
        utxoCache: utxoCache.present ? utxoCache.value : this.utxoCache,
        solMinimumRent:
            solMinimumRent.present ? solMinimumRent.value : this.solMinimumRent,
      );
  AddressModel copyWithCompanion(AddressTableCompanion data) {
    return AddressModel(
      id: data.id.present ? data.id.value : this.id,
      walletId: data.walletId.present ? data.walletId.value : this.walletId,
      chain: data.chain.present ? data.chain.value : this.chain,
      chainCode: data.chainCode.present ? data.chainCode.value : this.chainCode,
      address: data.address.present ? data.address.value : this.address,
      publickey: data.publickey.present ? data.publickey.value : this.publickey,
      xpubData: data.xpubData.present ? data.xpubData.value : this.xpubData,
      path: data.path.present ? data.path.value : this.path,
      balance: data.balance.present ? data.balance.value : this.balance,
      sortedId: data.sortedId.present ? data.sortedId.value : this.sortedId,
      addressIndex: data.addressIndex.present
          ? data.addressIndex.value
          : this.addressIndex,
      addressLabel: data.addressLabel.present
          ? data.addressLabel.value
          : this.addressLabel,
      isSelected:
          data.isSelected.present ? data.isSelected.value : this.isSelected,
      isUploaded:
          data.isUploaded.present ? data.isUploaded.value : this.isUploaded,
      slip44Id: data.slip44Id.present ? data.slip44Id.value : this.slip44Id,
      segwitType:
          data.segwitType.present ? data.segwitType.value : this.segwitType,
      walletType:
          data.walletType.present ? data.walletType.value : this.walletType,
      nftAmountCache: data.nftAmountCache.present
          ? data.nftAmountCache.value
          : this.nftAmountCache,
      nftAssetsCache: data.nftAssetsCache.present
          ? data.nftAssetsCache.value
          : this.nftAssetsCache,
      nftCollectionCache: data.nftCollectionCache.present
          ? data.nftCollectionCache.value
          : this.nftCollectionCache,
      nftUsdCache:
          data.nftUsdCache.present ? data.nftUsdCache.value : this.nftUsdCache,
      totalBalance: data.totalBalance.present
          ? data.totalBalance.value
          : this.totalBalance,
      availableBalance: data.availableBalance.present
          ? data.availableBalance.value
          : this.availableBalance,
      unavailableBalance: data.unavailableBalance.present
          ? data.unavailableBalance.value
          : this.unavailableBalance,
      eosAccountInfoCache: data.eosAccountInfoCache.present
          ? data.eosAccountInfoCache.value
          : this.eosAccountInfoCache,
      tronAccountInfoCache: data.tronAccountInfoCache.present
          ? data.tronAccountInfoCache.value
          : this.tronAccountInfoCache,
      tronResourceCache: data.tronResourceCache.present
          ? data.tronResourceCache.value
          : this.tronResourceCache,
      utxoCache: data.utxoCache.present ? data.utxoCache.value : this.utxoCache,
      solMinimumRent: data.solMinimumRent.present
          ? data.solMinimumRent.value
          : this.solMinimumRent,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AddressModel(')
          ..write('id: $id, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('address: $address, ')
          ..write('publickey: $publickey, ')
          ..write('xpubData: $xpubData, ')
          ..write('path: $path, ')
          ..write('balance: $balance, ')
          ..write('sortedId: $sortedId, ')
          ..write('addressIndex: $addressIndex, ')
          ..write('addressLabel: $addressLabel, ')
          ..write('isSelected: $isSelected, ')
          ..write('isUploaded: $isUploaded, ')
          ..write('slip44Id: $slip44Id, ')
          ..write('segwitType: $segwitType, ')
          ..write('walletType: $walletType, ')
          ..write('nftAmountCache: $nftAmountCache, ')
          ..write('nftAssetsCache: $nftAssetsCache, ')
          ..write('nftCollectionCache: $nftCollectionCache, ')
          ..write('nftUsdCache: $nftUsdCache, ')
          ..write('totalBalance: $totalBalance, ')
          ..write('availableBalance: $availableBalance, ')
          ..write('unavailableBalance: $unavailableBalance, ')
          ..write('eosAccountInfoCache: $eosAccountInfoCache, ')
          ..write('tronAccountInfoCache: $tronAccountInfoCache, ')
          ..write('tronResourceCache: $tronResourceCache, ')
          ..write('utxoCache: $utxoCache, ')
          ..write('solMinimumRent: $solMinimumRent')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        walletId,
        chain,
        chainCode,
        address,
        publickey,
        xpubData,
        path,
        balance,
        sortedId,
        addressIndex,
        addressLabel,
        isSelected,
        isUploaded,
        slip44Id,
        segwitType,
        walletType,
        nftAmountCache,
        nftAssetsCache,
        nftCollectionCache,
        nftUsdCache,
        totalBalance,
        availableBalance,
        unavailableBalance,
        eosAccountInfoCache,
        tronAccountInfoCache,
        tronResourceCache,
        utxoCache,
        solMinimumRent
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AddressModel &&
          other.id == this.id &&
          other.walletId == this.walletId &&
          other.chain == this.chain &&
          other.chainCode == this.chainCode &&
          other.address == this.address &&
          other.publickey == this.publickey &&
          other.xpubData == this.xpubData &&
          other.path == this.path &&
          other.balance == this.balance &&
          other.sortedId == this.sortedId &&
          other.addressIndex == this.addressIndex &&
          other.addressLabel == this.addressLabel &&
          other.isSelected == this.isSelected &&
          other.isUploaded == this.isUploaded &&
          other.slip44Id == this.slip44Id &&
          other.segwitType == this.segwitType &&
          other.walletType == this.walletType &&
          other.nftAmountCache == this.nftAmountCache &&
          other.nftAssetsCache == this.nftAssetsCache &&
          other.nftCollectionCache == this.nftCollectionCache &&
          other.nftUsdCache == this.nftUsdCache &&
          other.totalBalance == this.totalBalance &&
          other.availableBalance == this.availableBalance &&
          other.unavailableBalance == this.unavailableBalance &&
          other.eosAccountInfoCache == this.eosAccountInfoCache &&
          other.tronAccountInfoCache == this.tronAccountInfoCache &&
          other.tronResourceCache == this.tronResourceCache &&
          other.utxoCache == this.utxoCache &&
          other.solMinimumRent == this.solMinimumRent);
}

class AddressTableCompanion extends UpdateCompanion<AddressModel> {
  final Value<int> id;
  final Value<String?> walletId;
  final Value<String?> chain;
  final Value<String?> chainCode;
  final Value<String?> address;
  final Value<String?> publickey;
  final Value<String?> xpubData;
  final Value<String?> path;
  final Value<String?> balance;
  final Value<int?> sortedId;
  final Value<int?> addressIndex;
  final Value<String?> addressLabel;
  final Value<bool?> isSelected;
  final Value<bool?> isUploaded;
  final Value<int?> slip44Id;
  final Value<int?> segwitType;
  final Value<int?> walletType;
  final Value<String?> nftAmountCache;
  final Value<String?> nftAssetsCache;
  final Value<String?> nftCollectionCache;
  final Value<String?> nftUsdCache;
  final Value<String?> totalBalance;
  final Value<String?> availableBalance;
  final Value<String?> unavailableBalance;
  final Value<String?> eosAccountInfoCache;
  final Value<String?> tronAccountInfoCache;
  final Value<String?> tronResourceCache;
  final Value<String?> utxoCache;
  final Value<String?> solMinimumRent;
  const AddressTableCompanion({
    this.id = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.address = const Value.absent(),
    this.publickey = const Value.absent(),
    this.xpubData = const Value.absent(),
    this.path = const Value.absent(),
    this.balance = const Value.absent(),
    this.sortedId = const Value.absent(),
    this.addressIndex = const Value.absent(),
    this.addressLabel = const Value.absent(),
    this.isSelected = const Value.absent(),
    this.isUploaded = const Value.absent(),
    this.slip44Id = const Value.absent(),
    this.segwitType = const Value.absent(),
    this.walletType = const Value.absent(),
    this.nftAmountCache = const Value.absent(),
    this.nftAssetsCache = const Value.absent(),
    this.nftCollectionCache = const Value.absent(),
    this.nftUsdCache = const Value.absent(),
    this.totalBalance = const Value.absent(),
    this.availableBalance = const Value.absent(),
    this.unavailableBalance = const Value.absent(),
    this.eosAccountInfoCache = const Value.absent(),
    this.tronAccountInfoCache = const Value.absent(),
    this.tronResourceCache = const Value.absent(),
    this.utxoCache = const Value.absent(),
    this.solMinimumRent = const Value.absent(),
  });
  AddressTableCompanion.insert({
    this.id = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.address = const Value.absent(),
    this.publickey = const Value.absent(),
    this.xpubData = const Value.absent(),
    this.path = const Value.absent(),
    this.balance = const Value.absent(),
    this.sortedId = const Value.absent(),
    this.addressIndex = const Value.absent(),
    this.addressLabel = const Value.absent(),
    this.isSelected = const Value.absent(),
    this.isUploaded = const Value.absent(),
    this.slip44Id = const Value.absent(),
    this.segwitType = const Value.absent(),
    this.walletType = const Value.absent(),
    this.nftAmountCache = const Value.absent(),
    this.nftAssetsCache = const Value.absent(),
    this.nftCollectionCache = const Value.absent(),
    this.nftUsdCache = const Value.absent(),
    this.totalBalance = const Value.absent(),
    this.availableBalance = const Value.absent(),
    this.unavailableBalance = const Value.absent(),
    this.eosAccountInfoCache = const Value.absent(),
    this.tronAccountInfoCache = const Value.absent(),
    this.tronResourceCache = const Value.absent(),
    this.utxoCache = const Value.absent(),
    this.solMinimumRent = const Value.absent(),
  });
  static Insertable<AddressModel> custom({
    Expression<int>? id,
    Expression<String>? walletId,
    Expression<String>? chain,
    Expression<String>? chainCode,
    Expression<String>? address,
    Expression<String>? publickey,
    Expression<String>? xpubData,
    Expression<String>? path,
    Expression<String>? balance,
    Expression<int>? sortedId,
    Expression<int>? addressIndex,
    Expression<String>? addressLabel,
    Expression<bool>? isSelected,
    Expression<bool>? isUploaded,
    Expression<int>? slip44Id,
    Expression<int>? segwitType,
    Expression<int>? walletType,
    Expression<String>? nftAmountCache,
    Expression<String>? nftAssetsCache,
    Expression<String>? nftCollectionCache,
    Expression<String>? nftUsdCache,
    Expression<String>? totalBalance,
    Expression<String>? availableBalance,
    Expression<String>? unavailableBalance,
    Expression<String>? eosAccountInfoCache,
    Expression<String>? tronAccountInfoCache,
    Expression<String>? tronResourceCache,
    Expression<String>? utxoCache,
    Expression<String>? solMinimumRent,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (walletId != null) 'wallet_id': walletId,
      if (chain != null) 'chain': chain,
      if (chainCode != null) 'chain_code': chainCode,
      if (address != null) 'address': address,
      if (publickey != null) 'publickey': publickey,
      if (xpubData != null) 'xpub_data': xpubData,
      if (path != null) 'path': path,
      if (balance != null) 'balance': balance,
      if (sortedId != null) 'sorted_id': sortedId,
      if (addressIndex != null) 'address_index': addressIndex,
      if (addressLabel != null) 'address_label': addressLabel,
      if (isSelected != null) 'is_selected': isSelected,
      if (isUploaded != null) 'is_uploaded': isUploaded,
      if (slip44Id != null) 'slip44_id': slip44Id,
      if (segwitType != null) 'segwit_type': segwitType,
      if (walletType != null) 'wallet_type': walletType,
      if (nftAmountCache != null) 'nft_amount_cache': nftAmountCache,
      if (nftAssetsCache != null) 'nft_assets_cache': nftAssetsCache,
      if (nftCollectionCache != null)
        'nft_collection_cache': nftCollectionCache,
      if (nftUsdCache != null) 'nft_usd_cache': nftUsdCache,
      if (totalBalance != null) 'total_balance': totalBalance,
      if (availableBalance != null) 'available_balance': availableBalance,
      if (unavailableBalance != null) 'unavailable_balance': unavailableBalance,
      if (eosAccountInfoCache != null)
        'eos_account_info_cache': eosAccountInfoCache,
      if (tronAccountInfoCache != null)
        'tron_account_info_cache': tronAccountInfoCache,
      if (tronResourceCache != null) 'tron_resource_cache': tronResourceCache,
      if (utxoCache != null) 'utxo_cache': utxoCache,
      if (solMinimumRent != null) 'sol_minimum_rent': solMinimumRent,
    });
  }

  AddressTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? walletId,
      Value<String?>? chain,
      Value<String?>? chainCode,
      Value<String?>? address,
      Value<String?>? publickey,
      Value<String?>? xpubData,
      Value<String?>? path,
      Value<String?>? balance,
      Value<int?>? sortedId,
      Value<int?>? addressIndex,
      Value<String?>? addressLabel,
      Value<bool?>? isSelected,
      Value<bool?>? isUploaded,
      Value<int?>? slip44Id,
      Value<int?>? segwitType,
      Value<int?>? walletType,
      Value<String?>? nftAmountCache,
      Value<String?>? nftAssetsCache,
      Value<String?>? nftCollectionCache,
      Value<String?>? nftUsdCache,
      Value<String?>? totalBalance,
      Value<String?>? availableBalance,
      Value<String?>? unavailableBalance,
      Value<String?>? eosAccountInfoCache,
      Value<String?>? tronAccountInfoCache,
      Value<String?>? tronResourceCache,
      Value<String?>? utxoCache,
      Value<String?>? solMinimumRent}) {
    return AddressTableCompanion(
      id: id ?? this.id,
      walletId: walletId ?? this.walletId,
      chain: chain ?? this.chain,
      chainCode: chainCode ?? this.chainCode,
      address: address ?? this.address,
      publickey: publickey ?? this.publickey,
      xpubData: xpubData ?? this.xpubData,
      path: path ?? this.path,
      balance: balance ?? this.balance,
      sortedId: sortedId ?? this.sortedId,
      addressIndex: addressIndex ?? this.addressIndex,
      addressLabel: addressLabel ?? this.addressLabel,
      isSelected: isSelected ?? this.isSelected,
      isUploaded: isUploaded ?? this.isUploaded,
      slip44Id: slip44Id ?? this.slip44Id,
      segwitType: segwitType ?? this.segwitType,
      walletType: walletType ?? this.walletType,
      nftAmountCache: nftAmountCache ?? this.nftAmountCache,
      nftAssetsCache: nftAssetsCache ?? this.nftAssetsCache,
      nftCollectionCache: nftCollectionCache ?? this.nftCollectionCache,
      nftUsdCache: nftUsdCache ?? this.nftUsdCache,
      totalBalance: totalBalance ?? this.totalBalance,
      availableBalance: availableBalance ?? this.availableBalance,
      unavailableBalance: unavailableBalance ?? this.unavailableBalance,
      eosAccountInfoCache: eosAccountInfoCache ?? this.eosAccountInfoCache,
      tronAccountInfoCache: tronAccountInfoCache ?? this.tronAccountInfoCache,
      tronResourceCache: tronResourceCache ?? this.tronResourceCache,
      utxoCache: utxoCache ?? this.utxoCache,
      solMinimumRent: solMinimumRent ?? this.solMinimumRent,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (walletId.present) {
      map['wallet_id'] = Variable<String>(walletId.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (chainCode.present) {
      map['chain_code'] = Variable<String>(chainCode.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (publickey.present) {
      map['publickey'] = Variable<String>(publickey.value);
    }
    if (xpubData.present) {
      map['xpub_data'] = Variable<String>(xpubData.value);
    }
    if (path.present) {
      map['path'] = Variable<String>(path.value);
    }
    if (balance.present) {
      map['balance'] = Variable<String>(balance.value);
    }
    if (sortedId.present) {
      map['sorted_id'] = Variable<int>(sortedId.value);
    }
    if (addressIndex.present) {
      map['address_index'] = Variable<int>(addressIndex.value);
    }
    if (addressLabel.present) {
      map['address_label'] = Variable<String>(addressLabel.value);
    }
    if (isSelected.present) {
      map['is_selected'] = Variable<bool>(isSelected.value);
    }
    if (isUploaded.present) {
      map['is_uploaded'] = Variable<bool>(isUploaded.value);
    }
    if (slip44Id.present) {
      map['slip44_id'] = Variable<int>(slip44Id.value);
    }
    if (segwitType.present) {
      map['segwit_type'] = Variable<int>(segwitType.value);
    }
    if (walletType.present) {
      map['wallet_type'] = Variable<int>(walletType.value);
    }
    if (nftAmountCache.present) {
      map['nft_amount_cache'] = Variable<String>(nftAmountCache.value);
    }
    if (nftAssetsCache.present) {
      map['nft_assets_cache'] = Variable<String>(nftAssetsCache.value);
    }
    if (nftCollectionCache.present) {
      map['nft_collection_cache'] = Variable<String>(nftCollectionCache.value);
    }
    if (nftUsdCache.present) {
      map['nft_usd_cache'] = Variable<String>(nftUsdCache.value);
    }
    if (totalBalance.present) {
      map['total_balance'] = Variable<String>(totalBalance.value);
    }
    if (availableBalance.present) {
      map['available_balance'] = Variable<String>(availableBalance.value);
    }
    if (unavailableBalance.present) {
      map['unavailable_balance'] = Variable<String>(unavailableBalance.value);
    }
    if (eosAccountInfoCache.present) {
      map['eos_account_info_cache'] =
          Variable<String>(eosAccountInfoCache.value);
    }
    if (tronAccountInfoCache.present) {
      map['tron_account_info_cache'] =
          Variable<String>(tronAccountInfoCache.value);
    }
    if (tronResourceCache.present) {
      map['tron_resource_cache'] = Variable<String>(tronResourceCache.value);
    }
    if (utxoCache.present) {
      map['utxo_cache'] = Variable<String>(utxoCache.value);
    }
    if (solMinimumRent.present) {
      map['sol_minimum_rent'] = Variable<String>(solMinimumRent.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AddressTableCompanion(')
          ..write('id: $id, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('address: $address, ')
          ..write('publickey: $publickey, ')
          ..write('xpubData: $xpubData, ')
          ..write('path: $path, ')
          ..write('balance: $balance, ')
          ..write('sortedId: $sortedId, ')
          ..write('addressIndex: $addressIndex, ')
          ..write('addressLabel: $addressLabel, ')
          ..write('isSelected: $isSelected, ')
          ..write('isUploaded: $isUploaded, ')
          ..write('slip44Id: $slip44Id, ')
          ..write('segwitType: $segwitType, ')
          ..write('walletType: $walletType, ')
          ..write('nftAmountCache: $nftAmountCache, ')
          ..write('nftAssetsCache: $nftAssetsCache, ')
          ..write('nftCollectionCache: $nftCollectionCache, ')
          ..write('nftUsdCache: $nftUsdCache, ')
          ..write('totalBalance: $totalBalance, ')
          ..write('availableBalance: $availableBalance, ')
          ..write('unavailableBalance: $unavailableBalance, ')
          ..write('eosAccountInfoCache: $eosAccountInfoCache, ')
          ..write('tronAccountInfoCache: $tronAccountInfoCache, ')
          ..write('tronResourceCache: $tronResourceCache, ')
          ..write('utxoCache: $utxoCache, ')
          ..write('solMinimumRent: $solMinimumRent')
          ..write(')'))
        .toString();
  }
}

class $TokenTableTable extends TokenTable
    with TableInfo<$TokenTableTable, TokenModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TokenTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _walletIdMeta =
      const VerificationMeta('walletId');
  @override
  late final GeneratedColumn<String> walletId = GeneratedColumn<String>(
      'wallet_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainCodeMeta =
      const VerificationMeta('chainCode');
  @override
  late final GeneratedColumn<String> chainCode = GeneratedColumn<String>(
      'chain_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  @override
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _contractMeta =
      const VerificationMeta('contract');
  @override
  late final GeneratedColumn<String> contract = GeneratedColumn<String>(
      'contract', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _balanceMeta =
      const VerificationMeta('balance');
  @override
  late final GeneratedColumn<String> balance = GeneratedColumn<String>(
      'balance', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressLabelMeta =
      const VerificationMeta('addressLabel');
  @override
  late final GeneratedColumn<String> addressLabel = GeneratedColumn<String>(
      'address_label', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _tokenTypeMeta =
      const VerificationMeta('tokenType');
  @override
  late final GeneratedColumn<int> tokenType = GeneratedColumn<int>(
      'token_type', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _derivedAddressesMeta =
      const VerificationMeta('derivedAddresses');
  @override
  late final GeneratedColumn<String> derivedAddresses = GeneratedColumn<String>(
      'derived_addresses', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        walletId,
        chain,
        chainCode,
        address,
        contract,
        balance,
        addressLabel,
        tokenType,
        derivedAddresses
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'token_table';
  @override
  VerificationContext validateIntegrity(Insertable<TokenModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('wallet_id')) {
      context.handle(_walletIdMeta,
          walletId.isAcceptableOrUnknown(data['wallet_id']!, _walletIdMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('chain_code')) {
      context.handle(_chainCodeMeta,
          chainCode.isAcceptableOrUnknown(data['chain_code']!, _chainCodeMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    }
    if (data.containsKey('contract')) {
      context.handle(_contractMeta,
          contract.isAcceptableOrUnknown(data['contract']!, _contractMeta));
    }
    if (data.containsKey('balance')) {
      context.handle(_balanceMeta,
          balance.isAcceptableOrUnknown(data['balance']!, _balanceMeta));
    }
    if (data.containsKey('address_label')) {
      context.handle(
          _addressLabelMeta,
          addressLabel.isAcceptableOrUnknown(
              data['address_label']!, _addressLabelMeta));
    }
    if (data.containsKey('token_type')) {
      context.handle(_tokenTypeMeta,
          tokenType.isAcceptableOrUnknown(data['token_type']!, _tokenTypeMeta));
    }
    if (data.containsKey('derived_addresses')) {
      context.handle(
          _derivedAddressesMeta,
          derivedAddresses.isAcceptableOrUnknown(
              data['derived_addresses']!, _derivedAddressesMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TokenModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TokenModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      walletId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_id']),
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      chainCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_code']),
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address']),
      contract: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contract']),
      balance: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}balance']),
      addressLabel: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address_label']),
      tokenType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}token_type']),
      derivedAddresses: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}derived_addresses']),
    );
  }

  @override
  $TokenTableTable createAlias(String alias) {
    return $TokenTableTable(attachedDatabase, alias);
  }
}

class TokenModel extends DataClass implements Insertable<TokenModel> {
  final int id;
  final String? walletId;
  final String? chain;
  final String? chainCode;
  final String? address;
  final String? contract;
  final String? balance;
  final String? addressLabel;
  final int? tokenType;
  final String? derivedAddresses;
  const TokenModel(
      {required this.id,
      this.walletId,
      this.chain,
      this.chainCode,
      this.address,
      this.contract,
      this.balance,
      this.addressLabel,
      this.tokenType,
      this.derivedAddresses});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || walletId != null) {
      map['wallet_id'] = Variable<String>(walletId);
    }
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || chainCode != null) {
      map['chain_code'] = Variable<String>(chainCode);
    }
    if (!nullToAbsent || address != null) {
      map['address'] = Variable<String>(address);
    }
    if (!nullToAbsent || contract != null) {
      map['contract'] = Variable<String>(contract);
    }
    if (!nullToAbsent || balance != null) {
      map['balance'] = Variable<String>(balance);
    }
    if (!nullToAbsent || addressLabel != null) {
      map['address_label'] = Variable<String>(addressLabel);
    }
    if (!nullToAbsent || tokenType != null) {
      map['token_type'] = Variable<int>(tokenType);
    }
    if (!nullToAbsent || derivedAddresses != null) {
      map['derived_addresses'] = Variable<String>(derivedAddresses);
    }
    return map;
  }

  TokenTableCompanion toCompanion(bool nullToAbsent) {
    return TokenTableCompanion(
      id: Value(id),
      walletId: walletId == null && nullToAbsent
          ? const Value.absent()
          : Value(walletId),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      chainCode: chainCode == null && nullToAbsent
          ? const Value.absent()
          : Value(chainCode),
      address: address == null && nullToAbsent
          ? const Value.absent()
          : Value(address),
      contract: contract == null && nullToAbsent
          ? const Value.absent()
          : Value(contract),
      balance: balance == null && nullToAbsent
          ? const Value.absent()
          : Value(balance),
      addressLabel: addressLabel == null && nullToAbsent
          ? const Value.absent()
          : Value(addressLabel),
      tokenType: tokenType == null && nullToAbsent
          ? const Value.absent()
          : Value(tokenType),
      derivedAddresses: derivedAddresses == null && nullToAbsent
          ? const Value.absent()
          : Value(derivedAddresses),
    );
  }

  factory TokenModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TokenModel(
      id: serializer.fromJson<int>(json['id']),
      walletId: serializer.fromJson<String?>(json['walletId']),
      chain: serializer.fromJson<String?>(json['chain']),
      chainCode: serializer.fromJson<String?>(json['chainCode']),
      address: serializer.fromJson<String?>(json['address']),
      contract: serializer.fromJson<String?>(json['contract']),
      balance: serializer.fromJson<String?>(json['balance']),
      addressLabel: serializer.fromJson<String?>(json['addressLabel']),
      tokenType: serializer.fromJson<int?>(json['tokenType']),
      derivedAddresses: serializer.fromJson<String?>(json['derivedAddresses']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'walletId': serializer.toJson<String?>(walletId),
      'chain': serializer.toJson<String?>(chain),
      'chainCode': serializer.toJson<String?>(chainCode),
      'address': serializer.toJson<String?>(address),
      'contract': serializer.toJson<String?>(contract),
      'balance': serializer.toJson<String?>(balance),
      'addressLabel': serializer.toJson<String?>(addressLabel),
      'tokenType': serializer.toJson<int?>(tokenType),
      'derivedAddresses': serializer.toJson<String?>(derivedAddresses),
    };
  }

  TokenModel copyWith(
          {int? id,
          Value<String?> walletId = const Value.absent(),
          Value<String?> chain = const Value.absent(),
          Value<String?> chainCode = const Value.absent(),
          Value<String?> address = const Value.absent(),
          Value<String?> contract = const Value.absent(),
          Value<String?> balance = const Value.absent(),
          Value<String?> addressLabel = const Value.absent(),
          Value<int?> tokenType = const Value.absent(),
          Value<String?> derivedAddresses = const Value.absent()}) =>
      TokenModel(
        id: id ?? this.id,
        walletId: walletId.present ? walletId.value : this.walletId,
        chain: chain.present ? chain.value : this.chain,
        chainCode: chainCode.present ? chainCode.value : this.chainCode,
        address: address.present ? address.value : this.address,
        contract: contract.present ? contract.value : this.contract,
        balance: balance.present ? balance.value : this.balance,
        addressLabel:
            addressLabel.present ? addressLabel.value : this.addressLabel,
        tokenType: tokenType.present ? tokenType.value : this.tokenType,
        derivedAddresses: derivedAddresses.present
            ? derivedAddresses.value
            : this.derivedAddresses,
      );
  TokenModel copyWithCompanion(TokenTableCompanion data) {
    return TokenModel(
      id: data.id.present ? data.id.value : this.id,
      walletId: data.walletId.present ? data.walletId.value : this.walletId,
      chain: data.chain.present ? data.chain.value : this.chain,
      chainCode: data.chainCode.present ? data.chainCode.value : this.chainCode,
      address: data.address.present ? data.address.value : this.address,
      contract: data.contract.present ? data.contract.value : this.contract,
      balance: data.balance.present ? data.balance.value : this.balance,
      addressLabel: data.addressLabel.present
          ? data.addressLabel.value
          : this.addressLabel,
      tokenType: data.tokenType.present ? data.tokenType.value : this.tokenType,
      derivedAddresses: data.derivedAddresses.present
          ? data.derivedAddresses.value
          : this.derivedAddresses,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TokenModel(')
          ..write('id: $id, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('address: $address, ')
          ..write('contract: $contract, ')
          ..write('balance: $balance, ')
          ..write('addressLabel: $addressLabel, ')
          ..write('tokenType: $tokenType, ')
          ..write('derivedAddresses: $derivedAddresses')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, walletId, chain, chainCode, address,
      contract, balance, addressLabel, tokenType, derivedAddresses);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TokenModel &&
          other.id == this.id &&
          other.walletId == this.walletId &&
          other.chain == this.chain &&
          other.chainCode == this.chainCode &&
          other.address == this.address &&
          other.contract == this.contract &&
          other.balance == this.balance &&
          other.addressLabel == this.addressLabel &&
          other.tokenType == this.tokenType &&
          other.derivedAddresses == this.derivedAddresses);
}

class TokenTableCompanion extends UpdateCompanion<TokenModel> {
  final Value<int> id;
  final Value<String?> walletId;
  final Value<String?> chain;
  final Value<String?> chainCode;
  final Value<String?> address;
  final Value<String?> contract;
  final Value<String?> balance;
  final Value<String?> addressLabel;
  final Value<int?> tokenType;
  final Value<String?> derivedAddresses;
  const TokenTableCompanion({
    this.id = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.address = const Value.absent(),
    this.contract = const Value.absent(),
    this.balance = const Value.absent(),
    this.addressLabel = const Value.absent(),
    this.tokenType = const Value.absent(),
    this.derivedAddresses = const Value.absent(),
  });
  TokenTableCompanion.insert({
    this.id = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.address = const Value.absent(),
    this.contract = const Value.absent(),
    this.balance = const Value.absent(),
    this.addressLabel = const Value.absent(),
    this.tokenType = const Value.absent(),
    this.derivedAddresses = const Value.absent(),
  });
  static Insertable<TokenModel> custom({
    Expression<int>? id,
    Expression<String>? walletId,
    Expression<String>? chain,
    Expression<String>? chainCode,
    Expression<String>? address,
    Expression<String>? contract,
    Expression<String>? balance,
    Expression<String>? addressLabel,
    Expression<int>? tokenType,
    Expression<String>? derivedAddresses,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (walletId != null) 'wallet_id': walletId,
      if (chain != null) 'chain': chain,
      if (chainCode != null) 'chain_code': chainCode,
      if (address != null) 'address': address,
      if (contract != null) 'contract': contract,
      if (balance != null) 'balance': balance,
      if (addressLabel != null) 'address_label': addressLabel,
      if (tokenType != null) 'token_type': tokenType,
      if (derivedAddresses != null) 'derived_addresses': derivedAddresses,
    });
  }

  TokenTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? walletId,
      Value<String?>? chain,
      Value<String?>? chainCode,
      Value<String?>? address,
      Value<String?>? contract,
      Value<String?>? balance,
      Value<String?>? addressLabel,
      Value<int?>? tokenType,
      Value<String?>? derivedAddresses}) {
    return TokenTableCompanion(
      id: id ?? this.id,
      walletId: walletId ?? this.walletId,
      chain: chain ?? this.chain,
      chainCode: chainCode ?? this.chainCode,
      address: address ?? this.address,
      contract: contract ?? this.contract,
      balance: balance ?? this.balance,
      addressLabel: addressLabel ?? this.addressLabel,
      tokenType: tokenType ?? this.tokenType,
      derivedAddresses: derivedAddresses ?? this.derivedAddresses,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (walletId.present) {
      map['wallet_id'] = Variable<String>(walletId.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (chainCode.present) {
      map['chain_code'] = Variable<String>(chainCode.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (contract.present) {
      map['contract'] = Variable<String>(contract.value);
    }
    if (balance.present) {
      map['balance'] = Variable<String>(balance.value);
    }
    if (addressLabel.present) {
      map['address_label'] = Variable<String>(addressLabel.value);
    }
    if (tokenType.present) {
      map['token_type'] = Variable<int>(tokenType.value);
    }
    if (derivedAddresses.present) {
      map['derived_addresses'] = Variable<String>(derivedAddresses.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TokenTableCompanion(')
          ..write('id: $id, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('chainCode: $chainCode, ')
          ..write('address: $address, ')
          ..write('contract: $contract, ')
          ..write('balance: $balance, ')
          ..write('addressLabel: $addressLabel, ')
          ..write('tokenType: $tokenType, ')
          ..write('derivedAddresses: $derivedAddresses')
          ..write(')'))
        .toString();
  }
}

class $DappTableTable extends DappTable
    with TableInfo<$DappTableTable, DappModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $DappTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappIdMeta = const VerificationMeta('dappId');
  @override
  late final GeneratedColumn<String> dappId = GeneratedColumn<String>(
      'dapp_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappNameMeta =
      const VerificationMeta('dappName');
  @override
  late final GeneratedColumn<String> dappName = GeneratedColumn<String>(
      'dapp_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappLabelMeta =
      const VerificationMeta('dappLabel');
  @override
  late final GeneratedColumn<String> dappLabel = GeneratedColumn<String>(
      'dapp_label', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappUrlMeta =
      const VerificationMeta('dappUrl');
  @override
  late final GeneratedColumn<String> dappUrl = GeneratedColumn<String>(
      'dapp_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappInfoMeta =
      const VerificationMeta('dappInfo');
  @override
  late final GeneratedColumn<String> dappInfo = GeneratedColumn<String>(
      'dapp_info', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _symbolMeta = const VerificationMeta('symbol');
  @override
  late final GeneratedColumn<String> symbol = GeneratedColumn<String>(
      'symbol', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappLogoUrlMeta =
      const VerificationMeta('dappLogoUrl');
  @override
  late final GeneratedColumn<String> dappLogoUrl = GeneratedColumn<String>(
      'dapp_logo_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _btcDappJsMeta =
      const VerificationMeta('btcDappJs');
  @override
  late final GeneratedColumn<String> btcDappJs = GeneratedColumn<String>(
      'btc_dapp_js', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _netWorkMeta =
      const VerificationMeta('netWork');
  @override
  late final GeneratedColumn<String> netWork = GeneratedColumn<String>(
      'net_work', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _slip44Meta = const VerificationMeta('slip44');
  @override
  late final GeneratedColumn<String> slip44 = GeneratedColumn<String>(
      'slip44', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _dappModeMeta =
      const VerificationMeta('dappMode');
  @override
  late final GeneratedColumn<int> dappMode = GeneratedColumn<int>(
      'dapp_mode', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _lastTimeMeta =
      const VerificationMeta('lastTime');
  @override
  late final GeneratedColumn<DateTime> lastTime = GeneratedColumn<DateTime>(
      'last_time', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _sortIndexMeta =
      const VerificationMeta('sortIndex');
  @override
  late final GeneratedColumn<int> sortIndex = GeneratedColumn<int>(
      'sort_index', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _isApproveMeta =
      const VerificationMeta('isApprove');
  @override
  late final GeneratedColumn<bool> isApprove = GeneratedColumn<bool>(
      'is_approve', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_approve" IN (0, 1))'));
  static const VerificationMeta _isBanMeta = const VerificationMeta('isBan');
  @override
  late final GeneratedColumn<bool> isBan = GeneratedColumn<bool>(
      'is_ban', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_ban" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        chain,
        dappId,
        dappName,
        dappLabel,
        dappUrl,
        dappInfo,
        symbol,
        dappLogoUrl,
        btcDappJs,
        netWork,
        slip44,
        dappMode,
        lastTime,
        sortIndex,
        isApprove,
        isBan
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'dapp_table';
  @override
  VerificationContext validateIntegrity(Insertable<DappModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('dapp_id')) {
      context.handle(_dappIdMeta,
          dappId.isAcceptableOrUnknown(data['dapp_id']!, _dappIdMeta));
    }
    if (data.containsKey('dapp_name')) {
      context.handle(_dappNameMeta,
          dappName.isAcceptableOrUnknown(data['dapp_name']!, _dappNameMeta));
    }
    if (data.containsKey('dapp_label')) {
      context.handle(_dappLabelMeta,
          dappLabel.isAcceptableOrUnknown(data['dapp_label']!, _dappLabelMeta));
    }
    if (data.containsKey('dapp_url')) {
      context.handle(_dappUrlMeta,
          dappUrl.isAcceptableOrUnknown(data['dapp_url']!, _dappUrlMeta));
    }
    if (data.containsKey('dapp_info')) {
      context.handle(_dappInfoMeta,
          dappInfo.isAcceptableOrUnknown(data['dapp_info']!, _dappInfoMeta));
    }
    if (data.containsKey('symbol')) {
      context.handle(_symbolMeta,
          symbol.isAcceptableOrUnknown(data['symbol']!, _symbolMeta));
    }
    if (data.containsKey('dapp_logo_url')) {
      context.handle(
          _dappLogoUrlMeta,
          dappLogoUrl.isAcceptableOrUnknown(
              data['dapp_logo_url']!, _dappLogoUrlMeta));
    }
    if (data.containsKey('btc_dapp_js')) {
      context.handle(
          _btcDappJsMeta,
          btcDappJs.isAcceptableOrUnknown(
              data['btc_dapp_js']!, _btcDappJsMeta));
    }
    if (data.containsKey('net_work')) {
      context.handle(_netWorkMeta,
          netWork.isAcceptableOrUnknown(data['net_work']!, _netWorkMeta));
    }
    if (data.containsKey('slip44')) {
      context.handle(_slip44Meta,
          slip44.isAcceptableOrUnknown(data['slip44']!, _slip44Meta));
    }
    if (data.containsKey('dapp_mode')) {
      context.handle(_dappModeMeta,
          dappMode.isAcceptableOrUnknown(data['dapp_mode']!, _dappModeMeta));
    }
    if (data.containsKey('last_time')) {
      context.handle(_lastTimeMeta,
          lastTime.isAcceptableOrUnknown(data['last_time']!, _lastTimeMeta));
    }
    if (data.containsKey('sort_index')) {
      context.handle(_sortIndexMeta,
          sortIndex.isAcceptableOrUnknown(data['sort_index']!, _sortIndexMeta));
    }
    if (data.containsKey('is_approve')) {
      context.handle(_isApproveMeta,
          isApprove.isAcceptableOrUnknown(data['is_approve']!, _isApproveMeta));
    }
    if (data.containsKey('is_ban')) {
      context.handle(
          _isBanMeta, isBan.isAcceptableOrUnknown(data['is_ban']!, _isBanMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DappModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DappModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      dappId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_id']),
      dappName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_name']),
      dappLabel: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_label']),
      dappUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_url']),
      dappInfo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_info']),
      symbol: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}symbol']),
      dappLogoUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}dapp_logo_url']),
      btcDappJs: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}btc_dapp_js']),
      netWork: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}net_work']),
      slip44: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}slip44']),
      dappMode: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}dapp_mode']),
      lastTime: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_time']),
      sortIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}sort_index']),
      isApprove: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_approve']),
      isBan: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_ban']),
    );
  }

  @override
  $DappTableTable createAlias(String alias) {
    return $DappTableTable(attachedDatabase, alias);
  }
}

class DappModel extends DataClass implements Insertable<DappModel> {
  final int id;
  final String? chain;
  final String? dappId;
  final String? dappName;
  final String? dappLabel;
  final String? dappUrl;
  final String? dappInfo;
  final String? symbol;
  final String? dappLogoUrl;
  final String? btcDappJs;
  final String? netWork;
  final String? slip44;
  final int? dappMode;
  final DateTime? lastTime;
  final int? sortIndex;
  final bool? isApprove;
  final bool? isBan;
  const DappModel(
      {required this.id,
      this.chain,
      this.dappId,
      this.dappName,
      this.dappLabel,
      this.dappUrl,
      this.dappInfo,
      this.symbol,
      this.dappLogoUrl,
      this.btcDappJs,
      this.netWork,
      this.slip44,
      this.dappMode,
      this.lastTime,
      this.sortIndex,
      this.isApprove,
      this.isBan});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || dappId != null) {
      map['dapp_id'] = Variable<String>(dappId);
    }
    if (!nullToAbsent || dappName != null) {
      map['dapp_name'] = Variable<String>(dappName);
    }
    if (!nullToAbsent || dappLabel != null) {
      map['dapp_label'] = Variable<String>(dappLabel);
    }
    if (!nullToAbsent || dappUrl != null) {
      map['dapp_url'] = Variable<String>(dappUrl);
    }
    if (!nullToAbsent || dappInfo != null) {
      map['dapp_info'] = Variable<String>(dappInfo);
    }
    if (!nullToAbsent || symbol != null) {
      map['symbol'] = Variable<String>(symbol);
    }
    if (!nullToAbsent || dappLogoUrl != null) {
      map['dapp_logo_url'] = Variable<String>(dappLogoUrl);
    }
    if (!nullToAbsent || btcDappJs != null) {
      map['btc_dapp_js'] = Variable<String>(btcDappJs);
    }
    if (!nullToAbsent || netWork != null) {
      map['net_work'] = Variable<String>(netWork);
    }
    if (!nullToAbsent || slip44 != null) {
      map['slip44'] = Variable<String>(slip44);
    }
    if (!nullToAbsent || dappMode != null) {
      map['dapp_mode'] = Variable<int>(dappMode);
    }
    if (!nullToAbsent || lastTime != null) {
      map['last_time'] = Variable<DateTime>(lastTime);
    }
    if (!nullToAbsent || sortIndex != null) {
      map['sort_index'] = Variable<int>(sortIndex);
    }
    if (!nullToAbsent || isApprove != null) {
      map['is_approve'] = Variable<bool>(isApprove);
    }
    if (!nullToAbsent || isBan != null) {
      map['is_ban'] = Variable<bool>(isBan);
    }
    return map;
  }

  DappTableCompanion toCompanion(bool nullToAbsent) {
    return DappTableCompanion(
      id: Value(id),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      dappId:
          dappId == null && nullToAbsent ? const Value.absent() : Value(dappId),
      dappName: dappName == null && nullToAbsent
          ? const Value.absent()
          : Value(dappName),
      dappLabel: dappLabel == null && nullToAbsent
          ? const Value.absent()
          : Value(dappLabel),
      dappUrl: dappUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(dappUrl),
      dappInfo: dappInfo == null && nullToAbsent
          ? const Value.absent()
          : Value(dappInfo),
      symbol:
          symbol == null && nullToAbsent ? const Value.absent() : Value(symbol),
      dappLogoUrl: dappLogoUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(dappLogoUrl),
      btcDappJs: btcDappJs == null && nullToAbsent
          ? const Value.absent()
          : Value(btcDappJs),
      netWork: netWork == null && nullToAbsent
          ? const Value.absent()
          : Value(netWork),
      slip44:
          slip44 == null && nullToAbsent ? const Value.absent() : Value(slip44),
      dappMode: dappMode == null && nullToAbsent
          ? const Value.absent()
          : Value(dappMode),
      lastTime: lastTime == null && nullToAbsent
          ? const Value.absent()
          : Value(lastTime),
      sortIndex: sortIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(sortIndex),
      isApprove: isApprove == null && nullToAbsent
          ? const Value.absent()
          : Value(isApprove),
      isBan:
          isBan == null && nullToAbsent ? const Value.absent() : Value(isBan),
    );
  }

  factory DappModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DappModel(
      id: serializer.fromJson<int>(json['id']),
      chain: serializer.fromJson<String?>(json['chain']),
      dappId: serializer.fromJson<String?>(json['dappId']),
      dappName: serializer.fromJson<String?>(json['dappName']),
      dappLabel: serializer.fromJson<String?>(json['dappLabel']),
      dappUrl: serializer.fromJson<String?>(json['dappUrl']),
      dappInfo: serializer.fromJson<String?>(json['dappInfo']),
      symbol: serializer.fromJson<String?>(json['symbol']),
      dappLogoUrl: serializer.fromJson<String?>(json['dappLogoUrl']),
      btcDappJs: serializer.fromJson<String?>(json['btcDappJs']),
      netWork: serializer.fromJson<String?>(json['netWork']),
      slip44: serializer.fromJson<String?>(json['slip44']),
      dappMode: serializer.fromJson<int?>(json['dappMode']),
      lastTime: serializer.fromJson<DateTime?>(json['lastTime']),
      sortIndex: serializer.fromJson<int?>(json['sortIndex']),
      isApprove: serializer.fromJson<bool?>(json['isApprove']),
      isBan: serializer.fromJson<bool?>(json['isBan']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'chain': serializer.toJson<String?>(chain),
      'dappId': serializer.toJson<String?>(dappId),
      'dappName': serializer.toJson<String?>(dappName),
      'dappLabel': serializer.toJson<String?>(dappLabel),
      'dappUrl': serializer.toJson<String?>(dappUrl),
      'dappInfo': serializer.toJson<String?>(dappInfo),
      'symbol': serializer.toJson<String?>(symbol),
      'dappLogoUrl': serializer.toJson<String?>(dappLogoUrl),
      'btcDappJs': serializer.toJson<String?>(btcDappJs),
      'netWork': serializer.toJson<String?>(netWork),
      'slip44': serializer.toJson<String?>(slip44),
      'dappMode': serializer.toJson<int?>(dappMode),
      'lastTime': serializer.toJson<DateTime?>(lastTime),
      'sortIndex': serializer.toJson<int?>(sortIndex),
      'isApprove': serializer.toJson<bool?>(isApprove),
      'isBan': serializer.toJson<bool?>(isBan),
    };
  }

  DappModel copyWith(
          {int? id,
          Value<String?> chain = const Value.absent(),
          Value<String?> dappId = const Value.absent(),
          Value<String?> dappName = const Value.absent(),
          Value<String?> dappLabel = const Value.absent(),
          Value<String?> dappUrl = const Value.absent(),
          Value<String?> dappInfo = const Value.absent(),
          Value<String?> symbol = const Value.absent(),
          Value<String?> dappLogoUrl = const Value.absent(),
          Value<String?> btcDappJs = const Value.absent(),
          Value<String?> netWork = const Value.absent(),
          Value<String?> slip44 = const Value.absent(),
          Value<int?> dappMode = const Value.absent(),
          Value<DateTime?> lastTime = const Value.absent(),
          Value<int?> sortIndex = const Value.absent(),
          Value<bool?> isApprove = const Value.absent(),
          Value<bool?> isBan = const Value.absent()}) =>
      DappModel(
        id: id ?? this.id,
        chain: chain.present ? chain.value : this.chain,
        dappId: dappId.present ? dappId.value : this.dappId,
        dappName: dappName.present ? dappName.value : this.dappName,
        dappLabel: dappLabel.present ? dappLabel.value : this.dappLabel,
        dappUrl: dappUrl.present ? dappUrl.value : this.dappUrl,
        dappInfo: dappInfo.present ? dappInfo.value : this.dappInfo,
        symbol: symbol.present ? symbol.value : this.symbol,
        dappLogoUrl: dappLogoUrl.present ? dappLogoUrl.value : this.dappLogoUrl,
        btcDappJs: btcDappJs.present ? btcDappJs.value : this.btcDappJs,
        netWork: netWork.present ? netWork.value : this.netWork,
        slip44: slip44.present ? slip44.value : this.slip44,
        dappMode: dappMode.present ? dappMode.value : this.dappMode,
        lastTime: lastTime.present ? lastTime.value : this.lastTime,
        sortIndex: sortIndex.present ? sortIndex.value : this.sortIndex,
        isApprove: isApprove.present ? isApprove.value : this.isApprove,
        isBan: isBan.present ? isBan.value : this.isBan,
      );
  DappModel copyWithCompanion(DappTableCompanion data) {
    return DappModel(
      id: data.id.present ? data.id.value : this.id,
      chain: data.chain.present ? data.chain.value : this.chain,
      dappId: data.dappId.present ? data.dappId.value : this.dappId,
      dappName: data.dappName.present ? data.dappName.value : this.dappName,
      dappLabel: data.dappLabel.present ? data.dappLabel.value : this.dappLabel,
      dappUrl: data.dappUrl.present ? data.dappUrl.value : this.dappUrl,
      dappInfo: data.dappInfo.present ? data.dappInfo.value : this.dappInfo,
      symbol: data.symbol.present ? data.symbol.value : this.symbol,
      dappLogoUrl:
          data.dappLogoUrl.present ? data.dappLogoUrl.value : this.dappLogoUrl,
      btcDappJs: data.btcDappJs.present ? data.btcDappJs.value : this.btcDappJs,
      netWork: data.netWork.present ? data.netWork.value : this.netWork,
      slip44: data.slip44.present ? data.slip44.value : this.slip44,
      dappMode: data.dappMode.present ? data.dappMode.value : this.dappMode,
      lastTime: data.lastTime.present ? data.lastTime.value : this.lastTime,
      sortIndex: data.sortIndex.present ? data.sortIndex.value : this.sortIndex,
      isApprove: data.isApprove.present ? data.isApprove.value : this.isApprove,
      isBan: data.isBan.present ? data.isBan.value : this.isBan,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DappModel(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('dappId: $dappId, ')
          ..write('dappName: $dappName, ')
          ..write('dappLabel: $dappLabel, ')
          ..write('dappUrl: $dappUrl, ')
          ..write('dappInfo: $dappInfo, ')
          ..write('symbol: $symbol, ')
          ..write('dappLogoUrl: $dappLogoUrl, ')
          ..write('btcDappJs: $btcDappJs, ')
          ..write('netWork: $netWork, ')
          ..write('slip44: $slip44, ')
          ..write('dappMode: $dappMode, ')
          ..write('lastTime: $lastTime, ')
          ..write('sortIndex: $sortIndex, ')
          ..write('isApprove: $isApprove, ')
          ..write('isBan: $isBan')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      chain,
      dappId,
      dappName,
      dappLabel,
      dappUrl,
      dappInfo,
      symbol,
      dappLogoUrl,
      btcDappJs,
      netWork,
      slip44,
      dappMode,
      lastTime,
      sortIndex,
      isApprove,
      isBan);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DappModel &&
          other.id == this.id &&
          other.chain == this.chain &&
          other.dappId == this.dappId &&
          other.dappName == this.dappName &&
          other.dappLabel == this.dappLabel &&
          other.dappUrl == this.dappUrl &&
          other.dappInfo == this.dappInfo &&
          other.symbol == this.symbol &&
          other.dappLogoUrl == this.dappLogoUrl &&
          other.btcDappJs == this.btcDappJs &&
          other.netWork == this.netWork &&
          other.slip44 == this.slip44 &&
          other.dappMode == this.dappMode &&
          other.lastTime == this.lastTime &&
          other.sortIndex == this.sortIndex &&
          other.isApprove == this.isApprove &&
          other.isBan == this.isBan);
}

class DappTableCompanion extends UpdateCompanion<DappModel> {
  final Value<int> id;
  final Value<String?> chain;
  final Value<String?> dappId;
  final Value<String?> dappName;
  final Value<String?> dappLabel;
  final Value<String?> dappUrl;
  final Value<String?> dappInfo;
  final Value<String?> symbol;
  final Value<String?> dappLogoUrl;
  final Value<String?> btcDappJs;
  final Value<String?> netWork;
  final Value<String?> slip44;
  final Value<int?> dappMode;
  final Value<DateTime?> lastTime;
  final Value<int?> sortIndex;
  final Value<bool?> isApprove;
  final Value<bool?> isBan;
  const DappTableCompanion({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.dappId = const Value.absent(),
    this.dappName = const Value.absent(),
    this.dappLabel = const Value.absent(),
    this.dappUrl = const Value.absent(),
    this.dappInfo = const Value.absent(),
    this.symbol = const Value.absent(),
    this.dappLogoUrl = const Value.absent(),
    this.btcDappJs = const Value.absent(),
    this.netWork = const Value.absent(),
    this.slip44 = const Value.absent(),
    this.dappMode = const Value.absent(),
    this.lastTime = const Value.absent(),
    this.sortIndex = const Value.absent(),
    this.isApprove = const Value.absent(),
    this.isBan = const Value.absent(),
  });
  DappTableCompanion.insert({
    this.id = const Value.absent(),
    this.chain = const Value.absent(),
    this.dappId = const Value.absent(),
    this.dappName = const Value.absent(),
    this.dappLabel = const Value.absent(),
    this.dappUrl = const Value.absent(),
    this.dappInfo = const Value.absent(),
    this.symbol = const Value.absent(),
    this.dappLogoUrl = const Value.absent(),
    this.btcDappJs = const Value.absent(),
    this.netWork = const Value.absent(),
    this.slip44 = const Value.absent(),
    this.dappMode = const Value.absent(),
    this.lastTime = const Value.absent(),
    this.sortIndex = const Value.absent(),
    this.isApprove = const Value.absent(),
    this.isBan = const Value.absent(),
  });
  static Insertable<DappModel> custom({
    Expression<int>? id,
    Expression<String>? chain,
    Expression<String>? dappId,
    Expression<String>? dappName,
    Expression<String>? dappLabel,
    Expression<String>? dappUrl,
    Expression<String>? dappInfo,
    Expression<String>? symbol,
    Expression<String>? dappLogoUrl,
    Expression<String>? btcDappJs,
    Expression<String>? netWork,
    Expression<String>? slip44,
    Expression<int>? dappMode,
    Expression<DateTime>? lastTime,
    Expression<int>? sortIndex,
    Expression<bool>? isApprove,
    Expression<bool>? isBan,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chain != null) 'chain': chain,
      if (dappId != null) 'dapp_id': dappId,
      if (dappName != null) 'dapp_name': dappName,
      if (dappLabel != null) 'dapp_label': dappLabel,
      if (dappUrl != null) 'dapp_url': dappUrl,
      if (dappInfo != null) 'dapp_info': dappInfo,
      if (symbol != null) 'symbol': symbol,
      if (dappLogoUrl != null) 'dapp_logo_url': dappLogoUrl,
      if (btcDappJs != null) 'btc_dapp_js': btcDappJs,
      if (netWork != null) 'net_work': netWork,
      if (slip44 != null) 'slip44': slip44,
      if (dappMode != null) 'dapp_mode': dappMode,
      if (lastTime != null) 'last_time': lastTime,
      if (sortIndex != null) 'sort_index': sortIndex,
      if (isApprove != null) 'is_approve': isApprove,
      if (isBan != null) 'is_ban': isBan,
    });
  }

  DappTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? chain,
      Value<String?>? dappId,
      Value<String?>? dappName,
      Value<String?>? dappLabel,
      Value<String?>? dappUrl,
      Value<String?>? dappInfo,
      Value<String?>? symbol,
      Value<String?>? dappLogoUrl,
      Value<String?>? btcDappJs,
      Value<String?>? netWork,
      Value<String?>? slip44,
      Value<int?>? dappMode,
      Value<DateTime?>? lastTime,
      Value<int?>? sortIndex,
      Value<bool?>? isApprove,
      Value<bool?>? isBan}) {
    return DappTableCompanion(
      id: id ?? this.id,
      chain: chain ?? this.chain,
      dappId: dappId ?? this.dappId,
      dappName: dappName ?? this.dappName,
      dappLabel: dappLabel ?? this.dappLabel,
      dappUrl: dappUrl ?? this.dappUrl,
      dappInfo: dappInfo ?? this.dappInfo,
      symbol: symbol ?? this.symbol,
      dappLogoUrl: dappLogoUrl ?? this.dappLogoUrl,
      btcDappJs: btcDappJs ?? this.btcDappJs,
      netWork: netWork ?? this.netWork,
      slip44: slip44 ?? this.slip44,
      dappMode: dappMode ?? this.dappMode,
      lastTime: lastTime ?? this.lastTime,
      sortIndex: sortIndex ?? this.sortIndex,
      isApprove: isApprove ?? this.isApprove,
      isBan: isBan ?? this.isBan,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (dappId.present) {
      map['dapp_id'] = Variable<String>(dappId.value);
    }
    if (dappName.present) {
      map['dapp_name'] = Variable<String>(dappName.value);
    }
    if (dappLabel.present) {
      map['dapp_label'] = Variable<String>(dappLabel.value);
    }
    if (dappUrl.present) {
      map['dapp_url'] = Variable<String>(dappUrl.value);
    }
    if (dappInfo.present) {
      map['dapp_info'] = Variable<String>(dappInfo.value);
    }
    if (symbol.present) {
      map['symbol'] = Variable<String>(symbol.value);
    }
    if (dappLogoUrl.present) {
      map['dapp_logo_url'] = Variable<String>(dappLogoUrl.value);
    }
    if (btcDappJs.present) {
      map['btc_dapp_js'] = Variable<String>(btcDappJs.value);
    }
    if (netWork.present) {
      map['net_work'] = Variable<String>(netWork.value);
    }
    if (slip44.present) {
      map['slip44'] = Variable<String>(slip44.value);
    }
    if (dappMode.present) {
      map['dapp_mode'] = Variable<int>(dappMode.value);
    }
    if (lastTime.present) {
      map['last_time'] = Variable<DateTime>(lastTime.value);
    }
    if (sortIndex.present) {
      map['sort_index'] = Variable<int>(sortIndex.value);
    }
    if (isApprove.present) {
      map['is_approve'] = Variable<bool>(isApprove.value);
    }
    if (isBan.present) {
      map['is_ban'] = Variable<bool>(isBan.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('DappTableCompanion(')
          ..write('id: $id, ')
          ..write('chain: $chain, ')
          ..write('dappId: $dappId, ')
          ..write('dappName: $dappName, ')
          ..write('dappLabel: $dappLabel, ')
          ..write('dappUrl: $dappUrl, ')
          ..write('dappInfo: $dappInfo, ')
          ..write('symbol: $symbol, ')
          ..write('dappLogoUrl: $dappLogoUrl, ')
          ..write('btcDappJs: $btcDappJs, ')
          ..write('netWork: $netWork, ')
          ..write('slip44: $slip44, ')
          ..write('dappMode: $dappMode, ')
          ..write('lastTime: $lastTime, ')
          ..write('sortIndex: $sortIndex, ')
          ..write('isApprove: $isApprove, ')
          ..write('isBan: $isBan')
          ..write(')'))
        .toString();
  }
}

class $TransactionsActivityTableTable extends TransactionsActivityTable
    with TableInfo<$TransactionsActivityTableTable, TransactionsActivityModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TransactionsActivityTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _txIdMeta = const VerificationMeta('txId');
  @override
  late final GeneratedColumn<String> txId = GeneratedColumn<String>(
      'tx_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _txTimeMeta = const VerificationMeta('txTime');
  @override
  late final GeneratedColumn<int> txTime = GeneratedColumn<int>(
      'tx_time', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _actionMeta = const VerificationMeta('action');
  @override
  late final GeneratedColumn<String> action = GeneratedColumn<String>(
      'action', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _walletIdMeta =
      const VerificationMeta('walletId');
  @override
  late final GeneratedColumn<String> walletId = GeneratedColumn<String>(
      'wallet_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _contractMeta =
      const VerificationMeta('contract');
  @override
  late final GeneratedColumn<String> contract = GeneratedColumn<String>(
      'contract', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _chainCodeMeta =
      const VerificationMeta('chainCode');
  @override
  late final GeneratedColumn<String> chainCode = GeneratedColumn<String>(
      'chain_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isTokenMeta =
      const VerificationMeta('isToken');
  @override
  late final GeneratedColumn<bool> isToken = GeneratedColumn<bool>(
      'is_token', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_token" IN (0, 1))'));
  static const VerificationMeta _coinNameMeta =
      const VerificationMeta('coinName');
  @override
  late final GeneratedColumn<String> coinName = GeneratedColumn<String>(
      'coin_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _symbolMeta = const VerificationMeta('symbol');
  @override
  late final GeneratedColumn<String> symbol = GeneratedColumn<String>(
      'symbol', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  @override
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _rawTxMeta = const VerificationMeta('rawTx');
  @override
  late final GeneratedColumn<String> rawTx = GeneratedColumn<String>(
      'raw_tx', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _blockNumberMeta =
      const VerificationMeta('blockNumber');
  @override
  late final GeneratedColumn<int> blockNumber = GeneratedColumn<int>(
      'block_number', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _blockTimeMeta =
      const VerificationMeta('blockTime');
  @override
  late final GeneratedColumn<int> blockTime = GeneratedColumn<int>(
      'block_time', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _amountMeta = const VerificationMeta('amount');
  @override
  late final GeneratedColumn<String> amount = GeneratedColumn<String>(
      'amount', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _feeMeta = const VerificationMeta('fee');
  @override
  late final GeneratedColumn<String> fee = GeneratedColumn<String>(
      'fee', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _memoMeta = const VerificationMeta('memo');
  @override
  late final GeneratedColumn<String> memo = GeneratedColumn<String>(
      'memo', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _fromAddressMeta =
      const VerificationMeta('fromAddress');
  @override
  late final GeneratedColumn<String> fromAddress = GeneratedColumn<String>(
      'from_address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _receiveAddressMeta =
      const VerificationMeta('receiveAddress');
  @override
  late final GeneratedColumn<String> receiveAddress = GeneratedColumn<String>(
      'receive_address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nonceMeta = const VerificationMeta('nonce');
  @override
  late final GeneratedColumn<int> nonce = GeneratedColumn<int>(
      'nonce', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _gasPriceMeta =
      const VerificationMeta('gasPrice');
  @override
  late final GeneratedColumn<String> gasPrice = GeneratedColumn<String>(
      'gas_price', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _gasLimitMeta =
      const VerificationMeta('gasLimit');
  @override
  late final GeneratedColumn<String> gasLimit = GeneratedColumn<String>(
      'gas_limit', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _gasUsedMeta =
      const VerificationMeta('gasUsed');
  @override
  late final GeneratedColumn<String> gasUsed = GeneratedColumn<String>(
      'gas_used', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _xrpLedgerMeta =
      const VerificationMeta('xrpLedger');
  @override
  late final GeneratedColumn<int> xrpLedger = GeneratedColumn<int>(
      'xrp_ledger', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _xrpSequenceMeta =
      const VerificationMeta('xrpSequence');
  @override
  late final GeneratedColumn<int> xrpSequence = GeneratedColumn<int>(
      'xrp_sequence', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _txSourceMeta =
      const VerificationMeta('txSource');
  @override
  late final GeneratedColumn<String> txSource = GeneratedColumn<String>(
      'tx_source', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _indataMeta = const VerificationMeta('indata');
  @override
  late final GeneratedColumn<String> indata = GeneratedColumn<String>(
      'indata', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _outDataMeta =
      const VerificationMeta('outData');
  @override
  late final GeneratedColumn<String> outData = GeneratedColumn<String>(
      'out_data', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _txSequenceMeta =
      const VerificationMeta('txSequence');
  @override
  late final GeneratedColumn<String> txSequence = GeneratedColumn<String>(
      'tx_sequence', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _txLedgerMeta =
      const VerificationMeta('txLedger');
  @override
  late final GeneratedColumn<String> txLedger = GeneratedColumn<String>(
      'tx_ledger', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _txStatusMeta =
      const VerificationMeta('txStatus');
  @override
  late final GeneratedColumn<int> txStatus = GeneratedColumn<int>(
      'tx_status', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _dataSizeMeta =
      const VerificationMeta('dataSize');
  @override
  late final GeneratedColumn<int> dataSize = GeneratedColumn<int>(
      'data_size', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _isPackingMeta =
      const VerificationMeta('isPacking');
  @override
  late final GeneratedColumn<bool> isPacking = GeneratedColumn<bool>(
      'is_packing', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_packing" IN (0, 1))'));
  static const VerificationMeta _extraMeta = const VerificationMeta('extra');
  @override
  late final GeneratedColumn<String> extra = GeneratedColumn<String>(
      'extra', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _validMeta = const VerificationMeta('valid');
  @override
  late final GeneratedColumn<bool> valid = GeneratedColumn<bool>(
      'valid', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("valid" IN (0, 1))'));
  static const VerificationMeta _detailsCacheMeta =
      const VerificationMeta('detailsCache');
  @override
  late final GeneratedColumn<String> detailsCache = GeneratedColumn<String>(
      'details_cache', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        txId,
        txTime,
        action,
        type,
        walletId,
        chain,
        contract,
        chainCode,
        isToken,
        coinName,
        symbol,
        address,
        rawTx,
        blockNumber,
        blockTime,
        amount,
        fee,
        memo,
        fromAddress,
        receiveAddress,
        nonce,
        gasPrice,
        gasLimit,
        gasUsed,
        xrpLedger,
        xrpSequence,
        txSource,
        indata,
        outData,
        txSequence,
        txLedger,
        txStatus,
        dataSize,
        isPacking,
        extra,
        valid,
        detailsCache
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'transactions_activity_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<TransactionsActivityModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('tx_id')) {
      context.handle(
          _txIdMeta, txId.isAcceptableOrUnknown(data['tx_id']!, _txIdMeta));
    }
    if (data.containsKey('tx_time')) {
      context.handle(_txTimeMeta,
          txTime.isAcceptableOrUnknown(data['tx_time']!, _txTimeMeta));
    }
    if (data.containsKey('action')) {
      context.handle(_actionMeta,
          action.isAcceptableOrUnknown(data['action']!, _actionMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('wallet_id')) {
      context.handle(_walletIdMeta,
          walletId.isAcceptableOrUnknown(data['wallet_id']!, _walletIdMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('contract')) {
      context.handle(_contractMeta,
          contract.isAcceptableOrUnknown(data['contract']!, _contractMeta));
    }
    if (data.containsKey('chain_code')) {
      context.handle(_chainCodeMeta,
          chainCode.isAcceptableOrUnknown(data['chain_code']!, _chainCodeMeta));
    }
    if (data.containsKey('is_token')) {
      context.handle(_isTokenMeta,
          isToken.isAcceptableOrUnknown(data['is_token']!, _isTokenMeta));
    }
    if (data.containsKey('coin_name')) {
      context.handle(_coinNameMeta,
          coinName.isAcceptableOrUnknown(data['coin_name']!, _coinNameMeta));
    }
    if (data.containsKey('symbol')) {
      context.handle(_symbolMeta,
          symbol.isAcceptableOrUnknown(data['symbol']!, _symbolMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    }
    if (data.containsKey('raw_tx')) {
      context.handle(
          _rawTxMeta, rawTx.isAcceptableOrUnknown(data['raw_tx']!, _rawTxMeta));
    }
    if (data.containsKey('block_number')) {
      context.handle(
          _blockNumberMeta,
          blockNumber.isAcceptableOrUnknown(
              data['block_number']!, _blockNumberMeta));
    }
    if (data.containsKey('block_time')) {
      context.handle(_blockTimeMeta,
          blockTime.isAcceptableOrUnknown(data['block_time']!, _blockTimeMeta));
    }
    if (data.containsKey('amount')) {
      context.handle(_amountMeta,
          amount.isAcceptableOrUnknown(data['amount']!, _amountMeta));
    }
    if (data.containsKey('fee')) {
      context.handle(
          _feeMeta, fee.isAcceptableOrUnknown(data['fee']!, _feeMeta));
    }
    if (data.containsKey('memo')) {
      context.handle(
          _memoMeta, memo.isAcceptableOrUnknown(data['memo']!, _memoMeta));
    }
    if (data.containsKey('from_address')) {
      context.handle(
          _fromAddressMeta,
          fromAddress.isAcceptableOrUnknown(
              data['from_address']!, _fromAddressMeta));
    }
    if (data.containsKey('receive_address')) {
      context.handle(
          _receiveAddressMeta,
          receiveAddress.isAcceptableOrUnknown(
              data['receive_address']!, _receiveAddressMeta));
    }
    if (data.containsKey('nonce')) {
      context.handle(
          _nonceMeta, nonce.isAcceptableOrUnknown(data['nonce']!, _nonceMeta));
    }
    if (data.containsKey('gas_price')) {
      context.handle(_gasPriceMeta,
          gasPrice.isAcceptableOrUnknown(data['gas_price']!, _gasPriceMeta));
    }
    if (data.containsKey('gas_limit')) {
      context.handle(_gasLimitMeta,
          gasLimit.isAcceptableOrUnknown(data['gas_limit']!, _gasLimitMeta));
    }
    if (data.containsKey('gas_used')) {
      context.handle(_gasUsedMeta,
          gasUsed.isAcceptableOrUnknown(data['gas_used']!, _gasUsedMeta));
    }
    if (data.containsKey('xrp_ledger')) {
      context.handle(_xrpLedgerMeta,
          xrpLedger.isAcceptableOrUnknown(data['xrp_ledger']!, _xrpLedgerMeta));
    }
    if (data.containsKey('xrp_sequence')) {
      context.handle(
          _xrpSequenceMeta,
          xrpSequence.isAcceptableOrUnknown(
              data['xrp_sequence']!, _xrpSequenceMeta));
    }
    if (data.containsKey('tx_source')) {
      context.handle(_txSourceMeta,
          txSource.isAcceptableOrUnknown(data['tx_source']!, _txSourceMeta));
    }
    if (data.containsKey('indata')) {
      context.handle(_indataMeta,
          indata.isAcceptableOrUnknown(data['indata']!, _indataMeta));
    }
    if (data.containsKey('out_data')) {
      context.handle(_outDataMeta,
          outData.isAcceptableOrUnknown(data['out_data']!, _outDataMeta));
    }
    if (data.containsKey('tx_sequence')) {
      context.handle(
          _txSequenceMeta,
          txSequence.isAcceptableOrUnknown(
              data['tx_sequence']!, _txSequenceMeta));
    }
    if (data.containsKey('tx_ledger')) {
      context.handle(_txLedgerMeta,
          txLedger.isAcceptableOrUnknown(data['tx_ledger']!, _txLedgerMeta));
    }
    if (data.containsKey('tx_status')) {
      context.handle(_txStatusMeta,
          txStatus.isAcceptableOrUnknown(data['tx_status']!, _txStatusMeta));
    }
    if (data.containsKey('data_size')) {
      context.handle(_dataSizeMeta,
          dataSize.isAcceptableOrUnknown(data['data_size']!, _dataSizeMeta));
    }
    if (data.containsKey('is_packing')) {
      context.handle(_isPackingMeta,
          isPacking.isAcceptableOrUnknown(data['is_packing']!, _isPackingMeta));
    }
    if (data.containsKey('extra')) {
      context.handle(
          _extraMeta, extra.isAcceptableOrUnknown(data['extra']!, _extraMeta));
    }
    if (data.containsKey('valid')) {
      context.handle(
          _validMeta, valid.isAcceptableOrUnknown(data['valid']!, _validMeta));
    }
    if (data.containsKey('details_cache')) {
      context.handle(
          _detailsCacheMeta,
          detailsCache.isAcceptableOrUnknown(
              data['details_cache']!, _detailsCacheMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TransactionsActivityModel map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TransactionsActivityModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      txId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tx_id']),
      txTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}tx_time']),
      action: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}action']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type']),
      walletId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_id']),
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      contract: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contract']),
      chainCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_code']),
      isToken: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_token']),
      coinName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}coin_name']),
      symbol: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}symbol']),
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address']),
      rawTx: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}raw_tx']),
      blockNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}block_number']),
      blockTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}block_time']),
      amount: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}amount']),
      fee: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}fee']),
      memo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}memo']),
      fromAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}from_address']),
      receiveAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}receive_address']),
      nonce: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}nonce']),
      gasPrice: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}gas_price']),
      gasLimit: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}gas_limit']),
      gasUsed: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}gas_used']),
      xrpLedger: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}xrp_ledger']),
      xrpSequence: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}xrp_sequence']),
      txSource: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tx_source']),
      indata: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}indata']),
      outData: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}out_data']),
      txSequence: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tx_sequence']),
      txLedger: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tx_ledger']),
      txStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}tx_status']),
      dataSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}data_size']),
      isPacking: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_packing']),
      extra: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}extra']),
      valid: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}valid']),
      detailsCache: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}details_cache']),
    );
  }

  @override
  $TransactionsActivityTableTable createAlias(String alias) {
    return $TransactionsActivityTableTable(attachedDatabase, alias);
  }
}

class TransactionsActivityModel extends DataClass
    implements Insertable<TransactionsActivityModel> {
  final int id;
  final String? txId;
  final int? txTime;
  final String? action;
  final String? type;
  final String? walletId;
  final String? chain;
  final String? contract;
  final String? chainCode;
  final bool? isToken;
  final String? coinName;
  final String? symbol;
  final String? address;
  final String? rawTx;
  final int? blockNumber;
  final int? blockTime;
  final String? amount;
  final String? fee;
  final String? memo;
  final String? fromAddress;
  final String? receiveAddress;
  final int? nonce;
  final String? gasPrice;
  final String? gasLimit;
  final String? gasUsed;
  final int? xrpLedger;
  final int? xrpSequence;
  final String? txSource;
  final String? indata;
  final String? outData;
  final String? txSequence;
  final String? txLedger;
  final int? txStatus;
  final int? dataSize;

  /// 是否是手动添加的缓存 状态为打包中
  final bool? isPacking;

  /// extra
  final String? extra;

  /// valid
  final bool? valid;

  /// 交易详情Cache
  final String? detailsCache;
  const TransactionsActivityModel(
      {required this.id,
      this.txId,
      this.txTime,
      this.action,
      this.type,
      this.walletId,
      this.chain,
      this.contract,
      this.chainCode,
      this.isToken,
      this.coinName,
      this.symbol,
      this.address,
      this.rawTx,
      this.blockNumber,
      this.blockTime,
      this.amount,
      this.fee,
      this.memo,
      this.fromAddress,
      this.receiveAddress,
      this.nonce,
      this.gasPrice,
      this.gasLimit,
      this.gasUsed,
      this.xrpLedger,
      this.xrpSequence,
      this.txSource,
      this.indata,
      this.outData,
      this.txSequence,
      this.txLedger,
      this.txStatus,
      this.dataSize,
      this.isPacking,
      this.extra,
      this.valid,
      this.detailsCache});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || txId != null) {
      map['tx_id'] = Variable<String>(txId);
    }
    if (!nullToAbsent || txTime != null) {
      map['tx_time'] = Variable<int>(txTime);
    }
    if (!nullToAbsent || action != null) {
      map['action'] = Variable<String>(action);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<String>(type);
    }
    if (!nullToAbsent || walletId != null) {
      map['wallet_id'] = Variable<String>(walletId);
    }
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || contract != null) {
      map['contract'] = Variable<String>(contract);
    }
    if (!nullToAbsent || chainCode != null) {
      map['chain_code'] = Variable<String>(chainCode);
    }
    if (!nullToAbsent || isToken != null) {
      map['is_token'] = Variable<bool>(isToken);
    }
    if (!nullToAbsent || coinName != null) {
      map['coin_name'] = Variable<String>(coinName);
    }
    if (!nullToAbsent || symbol != null) {
      map['symbol'] = Variable<String>(symbol);
    }
    if (!nullToAbsent || address != null) {
      map['address'] = Variable<String>(address);
    }
    if (!nullToAbsent || rawTx != null) {
      map['raw_tx'] = Variable<String>(rawTx);
    }
    if (!nullToAbsent || blockNumber != null) {
      map['block_number'] = Variable<int>(blockNumber);
    }
    if (!nullToAbsent || blockTime != null) {
      map['block_time'] = Variable<int>(blockTime);
    }
    if (!nullToAbsent || amount != null) {
      map['amount'] = Variable<String>(amount);
    }
    if (!nullToAbsent || fee != null) {
      map['fee'] = Variable<String>(fee);
    }
    if (!nullToAbsent || memo != null) {
      map['memo'] = Variable<String>(memo);
    }
    if (!nullToAbsent || fromAddress != null) {
      map['from_address'] = Variable<String>(fromAddress);
    }
    if (!nullToAbsent || receiveAddress != null) {
      map['receive_address'] = Variable<String>(receiveAddress);
    }
    if (!nullToAbsent || nonce != null) {
      map['nonce'] = Variable<int>(nonce);
    }
    if (!nullToAbsent || gasPrice != null) {
      map['gas_price'] = Variable<String>(gasPrice);
    }
    if (!nullToAbsent || gasLimit != null) {
      map['gas_limit'] = Variable<String>(gasLimit);
    }
    if (!nullToAbsent || gasUsed != null) {
      map['gas_used'] = Variable<String>(gasUsed);
    }
    if (!nullToAbsent || xrpLedger != null) {
      map['xrp_ledger'] = Variable<int>(xrpLedger);
    }
    if (!nullToAbsent || xrpSequence != null) {
      map['xrp_sequence'] = Variable<int>(xrpSequence);
    }
    if (!nullToAbsent || txSource != null) {
      map['tx_source'] = Variable<String>(txSource);
    }
    if (!nullToAbsent || indata != null) {
      map['indata'] = Variable<String>(indata);
    }
    if (!nullToAbsent || outData != null) {
      map['out_data'] = Variable<String>(outData);
    }
    if (!nullToAbsent || txSequence != null) {
      map['tx_sequence'] = Variable<String>(txSequence);
    }
    if (!nullToAbsent || txLedger != null) {
      map['tx_ledger'] = Variable<String>(txLedger);
    }
    if (!nullToAbsent || txStatus != null) {
      map['tx_status'] = Variable<int>(txStatus);
    }
    if (!nullToAbsent || dataSize != null) {
      map['data_size'] = Variable<int>(dataSize);
    }
    if (!nullToAbsent || isPacking != null) {
      map['is_packing'] = Variable<bool>(isPacking);
    }
    if (!nullToAbsent || extra != null) {
      map['extra'] = Variable<String>(extra);
    }
    if (!nullToAbsent || valid != null) {
      map['valid'] = Variable<bool>(valid);
    }
    if (!nullToAbsent || detailsCache != null) {
      map['details_cache'] = Variable<String>(detailsCache);
    }
    return map;
  }

  TransactionsActivityTableCompanion toCompanion(bool nullToAbsent) {
    return TransactionsActivityTableCompanion(
      id: Value(id),
      txId: txId == null && nullToAbsent ? const Value.absent() : Value(txId),
      txTime:
          txTime == null && nullToAbsent ? const Value.absent() : Value(txTime),
      action:
          action == null && nullToAbsent ? const Value.absent() : Value(action),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      walletId: walletId == null && nullToAbsent
          ? const Value.absent()
          : Value(walletId),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      contract: contract == null && nullToAbsent
          ? const Value.absent()
          : Value(contract),
      chainCode: chainCode == null && nullToAbsent
          ? const Value.absent()
          : Value(chainCode),
      isToken: isToken == null && nullToAbsent
          ? const Value.absent()
          : Value(isToken),
      coinName: coinName == null && nullToAbsent
          ? const Value.absent()
          : Value(coinName),
      symbol:
          symbol == null && nullToAbsent ? const Value.absent() : Value(symbol),
      address: address == null && nullToAbsent
          ? const Value.absent()
          : Value(address),
      rawTx:
          rawTx == null && nullToAbsent ? const Value.absent() : Value(rawTx),
      blockNumber: blockNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(blockNumber),
      blockTime: blockTime == null && nullToAbsent
          ? const Value.absent()
          : Value(blockTime),
      amount:
          amount == null && nullToAbsent ? const Value.absent() : Value(amount),
      fee: fee == null && nullToAbsent ? const Value.absent() : Value(fee),
      memo: memo == null && nullToAbsent ? const Value.absent() : Value(memo),
      fromAddress: fromAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(fromAddress),
      receiveAddress: receiveAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(receiveAddress),
      nonce:
          nonce == null && nullToAbsent ? const Value.absent() : Value(nonce),
      gasPrice: gasPrice == null && nullToAbsent
          ? const Value.absent()
          : Value(gasPrice),
      gasLimit: gasLimit == null && nullToAbsent
          ? const Value.absent()
          : Value(gasLimit),
      gasUsed: gasUsed == null && nullToAbsent
          ? const Value.absent()
          : Value(gasUsed),
      xrpLedger: xrpLedger == null && nullToAbsent
          ? const Value.absent()
          : Value(xrpLedger),
      xrpSequence: xrpSequence == null && nullToAbsent
          ? const Value.absent()
          : Value(xrpSequence),
      txSource: txSource == null && nullToAbsent
          ? const Value.absent()
          : Value(txSource),
      indata:
          indata == null && nullToAbsent ? const Value.absent() : Value(indata),
      outData: outData == null && nullToAbsent
          ? const Value.absent()
          : Value(outData),
      txSequence: txSequence == null && nullToAbsent
          ? const Value.absent()
          : Value(txSequence),
      txLedger: txLedger == null && nullToAbsent
          ? const Value.absent()
          : Value(txLedger),
      txStatus: txStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(txStatus),
      dataSize: dataSize == null && nullToAbsent
          ? const Value.absent()
          : Value(dataSize),
      isPacking: isPacking == null && nullToAbsent
          ? const Value.absent()
          : Value(isPacking),
      extra:
          extra == null && nullToAbsent ? const Value.absent() : Value(extra),
      valid:
          valid == null && nullToAbsent ? const Value.absent() : Value(valid),
      detailsCache: detailsCache == null && nullToAbsent
          ? const Value.absent()
          : Value(detailsCache),
    );
  }

  factory TransactionsActivityModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TransactionsActivityModel(
      id: serializer.fromJson<int>(json['id']),
      txId: serializer.fromJson<String?>(json['txId']),
      txTime: serializer.fromJson<int?>(json['txTime']),
      action: serializer.fromJson<String?>(json['action']),
      type: serializer.fromJson<String?>(json['type']),
      walletId: serializer.fromJson<String?>(json['walletId']),
      chain: serializer.fromJson<String?>(json['chain']),
      contract: serializer.fromJson<String?>(json['contract']),
      chainCode: serializer.fromJson<String?>(json['chainCode']),
      isToken: serializer.fromJson<bool?>(json['isToken']),
      coinName: serializer.fromJson<String?>(json['coinName']),
      symbol: serializer.fromJson<String?>(json['symbol']),
      address: serializer.fromJson<String?>(json['address']),
      rawTx: serializer.fromJson<String?>(json['rawTx']),
      blockNumber: serializer.fromJson<int?>(json['blockNumber']),
      blockTime: serializer.fromJson<int?>(json['blockTime']),
      amount: serializer.fromJson<String?>(json['amount']),
      fee: serializer.fromJson<String?>(json['fee']),
      memo: serializer.fromJson<String?>(json['memo']),
      fromAddress: serializer.fromJson<String?>(json['fromAddress']),
      receiveAddress: serializer.fromJson<String?>(json['receiveAddress']),
      nonce: serializer.fromJson<int?>(json['nonce']),
      gasPrice: serializer.fromJson<String?>(json['gasPrice']),
      gasLimit: serializer.fromJson<String?>(json['gasLimit']),
      gasUsed: serializer.fromJson<String?>(json['gasUsed']),
      xrpLedger: serializer.fromJson<int?>(json['xrpLedger']),
      xrpSequence: serializer.fromJson<int?>(json['xrpSequence']),
      txSource: serializer.fromJson<String?>(json['txSource']),
      indata: serializer.fromJson<String?>(json['indata']),
      outData: serializer.fromJson<String?>(json['outData']),
      txSequence: serializer.fromJson<String?>(json['txSequence']),
      txLedger: serializer.fromJson<String?>(json['txLedger']),
      txStatus: serializer.fromJson<int?>(json['txStatus']),
      dataSize: serializer.fromJson<int?>(json['dataSize']),
      isPacking: serializer.fromJson<bool?>(json['isPacking']),
      extra: serializer.fromJson<String?>(json['extra']),
      valid: serializer.fromJson<bool?>(json['valid']),
      detailsCache: serializer.fromJson<String?>(json['detailsCache']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'txId': serializer.toJson<String?>(txId),
      'txTime': serializer.toJson<int?>(txTime),
      'action': serializer.toJson<String?>(action),
      'type': serializer.toJson<String?>(type),
      'walletId': serializer.toJson<String?>(walletId),
      'chain': serializer.toJson<String?>(chain),
      'contract': serializer.toJson<String?>(contract),
      'chainCode': serializer.toJson<String?>(chainCode),
      'isToken': serializer.toJson<bool?>(isToken),
      'coinName': serializer.toJson<String?>(coinName),
      'symbol': serializer.toJson<String?>(symbol),
      'address': serializer.toJson<String?>(address),
      'rawTx': serializer.toJson<String?>(rawTx),
      'blockNumber': serializer.toJson<int?>(blockNumber),
      'blockTime': serializer.toJson<int?>(blockTime),
      'amount': serializer.toJson<String?>(amount),
      'fee': serializer.toJson<String?>(fee),
      'memo': serializer.toJson<String?>(memo),
      'fromAddress': serializer.toJson<String?>(fromAddress),
      'receiveAddress': serializer.toJson<String?>(receiveAddress),
      'nonce': serializer.toJson<int?>(nonce),
      'gasPrice': serializer.toJson<String?>(gasPrice),
      'gasLimit': serializer.toJson<String?>(gasLimit),
      'gasUsed': serializer.toJson<String?>(gasUsed),
      'xrpLedger': serializer.toJson<int?>(xrpLedger),
      'xrpSequence': serializer.toJson<int?>(xrpSequence),
      'txSource': serializer.toJson<String?>(txSource),
      'indata': serializer.toJson<String?>(indata),
      'outData': serializer.toJson<String?>(outData),
      'txSequence': serializer.toJson<String?>(txSequence),
      'txLedger': serializer.toJson<String?>(txLedger),
      'txStatus': serializer.toJson<int?>(txStatus),
      'dataSize': serializer.toJson<int?>(dataSize),
      'isPacking': serializer.toJson<bool?>(isPacking),
      'extra': serializer.toJson<String?>(extra),
      'valid': serializer.toJson<bool?>(valid),
      'detailsCache': serializer.toJson<String?>(detailsCache),
    };
  }

  TransactionsActivityModel copyWith(
          {int? id,
          Value<String?> txId = const Value.absent(),
          Value<int?> txTime = const Value.absent(),
          Value<String?> action = const Value.absent(),
          Value<String?> type = const Value.absent(),
          Value<String?> walletId = const Value.absent(),
          Value<String?> chain = const Value.absent(),
          Value<String?> contract = const Value.absent(),
          Value<String?> chainCode = const Value.absent(),
          Value<bool?> isToken = const Value.absent(),
          Value<String?> coinName = const Value.absent(),
          Value<String?> symbol = const Value.absent(),
          Value<String?> address = const Value.absent(),
          Value<String?> rawTx = const Value.absent(),
          Value<int?> blockNumber = const Value.absent(),
          Value<int?> blockTime = const Value.absent(),
          Value<String?> amount = const Value.absent(),
          Value<String?> fee = const Value.absent(),
          Value<String?> memo = const Value.absent(),
          Value<String?> fromAddress = const Value.absent(),
          Value<String?> receiveAddress = const Value.absent(),
          Value<int?> nonce = const Value.absent(),
          Value<String?> gasPrice = const Value.absent(),
          Value<String?> gasLimit = const Value.absent(),
          Value<String?> gasUsed = const Value.absent(),
          Value<int?> xrpLedger = const Value.absent(),
          Value<int?> xrpSequence = const Value.absent(),
          Value<String?> txSource = const Value.absent(),
          Value<String?> indata = const Value.absent(),
          Value<String?> outData = const Value.absent(),
          Value<String?> txSequence = const Value.absent(),
          Value<String?> txLedger = const Value.absent(),
          Value<int?> txStatus = const Value.absent(),
          Value<int?> dataSize = const Value.absent(),
          Value<bool?> isPacking = const Value.absent(),
          Value<String?> extra = const Value.absent(),
          Value<bool?> valid = const Value.absent(),
          Value<String?> detailsCache = const Value.absent()}) =>
      TransactionsActivityModel(
        id: id ?? this.id,
        txId: txId.present ? txId.value : this.txId,
        txTime: txTime.present ? txTime.value : this.txTime,
        action: action.present ? action.value : this.action,
        type: type.present ? type.value : this.type,
        walletId: walletId.present ? walletId.value : this.walletId,
        chain: chain.present ? chain.value : this.chain,
        contract: contract.present ? contract.value : this.contract,
        chainCode: chainCode.present ? chainCode.value : this.chainCode,
        isToken: isToken.present ? isToken.value : this.isToken,
        coinName: coinName.present ? coinName.value : this.coinName,
        symbol: symbol.present ? symbol.value : this.symbol,
        address: address.present ? address.value : this.address,
        rawTx: rawTx.present ? rawTx.value : this.rawTx,
        blockNumber: blockNumber.present ? blockNumber.value : this.blockNumber,
        blockTime: blockTime.present ? blockTime.value : this.blockTime,
        amount: amount.present ? amount.value : this.amount,
        fee: fee.present ? fee.value : this.fee,
        memo: memo.present ? memo.value : this.memo,
        fromAddress: fromAddress.present ? fromAddress.value : this.fromAddress,
        receiveAddress:
            receiveAddress.present ? receiveAddress.value : this.receiveAddress,
        nonce: nonce.present ? nonce.value : this.nonce,
        gasPrice: gasPrice.present ? gasPrice.value : this.gasPrice,
        gasLimit: gasLimit.present ? gasLimit.value : this.gasLimit,
        gasUsed: gasUsed.present ? gasUsed.value : this.gasUsed,
        xrpLedger: xrpLedger.present ? xrpLedger.value : this.xrpLedger,
        xrpSequence: xrpSequence.present ? xrpSequence.value : this.xrpSequence,
        txSource: txSource.present ? txSource.value : this.txSource,
        indata: indata.present ? indata.value : this.indata,
        outData: outData.present ? outData.value : this.outData,
        txSequence: txSequence.present ? txSequence.value : this.txSequence,
        txLedger: txLedger.present ? txLedger.value : this.txLedger,
        txStatus: txStatus.present ? txStatus.value : this.txStatus,
        dataSize: dataSize.present ? dataSize.value : this.dataSize,
        isPacking: isPacking.present ? isPacking.value : this.isPacking,
        extra: extra.present ? extra.value : this.extra,
        valid: valid.present ? valid.value : this.valid,
        detailsCache:
            detailsCache.present ? detailsCache.value : this.detailsCache,
      );
  TransactionsActivityModel copyWithCompanion(
      TransactionsActivityTableCompanion data) {
    return TransactionsActivityModel(
      id: data.id.present ? data.id.value : this.id,
      txId: data.txId.present ? data.txId.value : this.txId,
      txTime: data.txTime.present ? data.txTime.value : this.txTime,
      action: data.action.present ? data.action.value : this.action,
      type: data.type.present ? data.type.value : this.type,
      walletId: data.walletId.present ? data.walletId.value : this.walletId,
      chain: data.chain.present ? data.chain.value : this.chain,
      contract: data.contract.present ? data.contract.value : this.contract,
      chainCode: data.chainCode.present ? data.chainCode.value : this.chainCode,
      isToken: data.isToken.present ? data.isToken.value : this.isToken,
      coinName: data.coinName.present ? data.coinName.value : this.coinName,
      symbol: data.symbol.present ? data.symbol.value : this.symbol,
      address: data.address.present ? data.address.value : this.address,
      rawTx: data.rawTx.present ? data.rawTx.value : this.rawTx,
      blockNumber:
          data.blockNumber.present ? data.blockNumber.value : this.blockNumber,
      blockTime: data.blockTime.present ? data.blockTime.value : this.blockTime,
      amount: data.amount.present ? data.amount.value : this.amount,
      fee: data.fee.present ? data.fee.value : this.fee,
      memo: data.memo.present ? data.memo.value : this.memo,
      fromAddress:
          data.fromAddress.present ? data.fromAddress.value : this.fromAddress,
      receiveAddress: data.receiveAddress.present
          ? data.receiveAddress.value
          : this.receiveAddress,
      nonce: data.nonce.present ? data.nonce.value : this.nonce,
      gasPrice: data.gasPrice.present ? data.gasPrice.value : this.gasPrice,
      gasLimit: data.gasLimit.present ? data.gasLimit.value : this.gasLimit,
      gasUsed: data.gasUsed.present ? data.gasUsed.value : this.gasUsed,
      xrpLedger: data.xrpLedger.present ? data.xrpLedger.value : this.xrpLedger,
      xrpSequence:
          data.xrpSequence.present ? data.xrpSequence.value : this.xrpSequence,
      txSource: data.txSource.present ? data.txSource.value : this.txSource,
      indata: data.indata.present ? data.indata.value : this.indata,
      outData: data.outData.present ? data.outData.value : this.outData,
      txSequence:
          data.txSequence.present ? data.txSequence.value : this.txSequence,
      txLedger: data.txLedger.present ? data.txLedger.value : this.txLedger,
      txStatus: data.txStatus.present ? data.txStatus.value : this.txStatus,
      dataSize: data.dataSize.present ? data.dataSize.value : this.dataSize,
      isPacking: data.isPacking.present ? data.isPacking.value : this.isPacking,
      extra: data.extra.present ? data.extra.value : this.extra,
      valid: data.valid.present ? data.valid.value : this.valid,
      detailsCache: data.detailsCache.present
          ? data.detailsCache.value
          : this.detailsCache,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TransactionsActivityModel(')
          ..write('id: $id, ')
          ..write('txId: $txId, ')
          ..write('txTime: $txTime, ')
          ..write('action: $action, ')
          ..write('type: $type, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('contract: $contract, ')
          ..write('chainCode: $chainCode, ')
          ..write('isToken: $isToken, ')
          ..write('coinName: $coinName, ')
          ..write('symbol: $symbol, ')
          ..write('address: $address, ')
          ..write('rawTx: $rawTx, ')
          ..write('blockNumber: $blockNumber, ')
          ..write('blockTime: $blockTime, ')
          ..write('amount: $amount, ')
          ..write('fee: $fee, ')
          ..write('memo: $memo, ')
          ..write('fromAddress: $fromAddress, ')
          ..write('receiveAddress: $receiveAddress, ')
          ..write('nonce: $nonce, ')
          ..write('gasPrice: $gasPrice, ')
          ..write('gasLimit: $gasLimit, ')
          ..write('gasUsed: $gasUsed, ')
          ..write('xrpLedger: $xrpLedger, ')
          ..write('xrpSequence: $xrpSequence, ')
          ..write('txSource: $txSource, ')
          ..write('indata: $indata, ')
          ..write('outData: $outData, ')
          ..write('txSequence: $txSequence, ')
          ..write('txLedger: $txLedger, ')
          ..write('txStatus: $txStatus, ')
          ..write('dataSize: $dataSize, ')
          ..write('isPacking: $isPacking, ')
          ..write('extra: $extra, ')
          ..write('valid: $valid, ')
          ..write('detailsCache: $detailsCache')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        txId,
        txTime,
        action,
        type,
        walletId,
        chain,
        contract,
        chainCode,
        isToken,
        coinName,
        symbol,
        address,
        rawTx,
        blockNumber,
        blockTime,
        amount,
        fee,
        memo,
        fromAddress,
        receiveAddress,
        nonce,
        gasPrice,
        gasLimit,
        gasUsed,
        xrpLedger,
        xrpSequence,
        txSource,
        indata,
        outData,
        txSequence,
        txLedger,
        txStatus,
        dataSize,
        isPacking,
        extra,
        valid,
        detailsCache
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TransactionsActivityModel &&
          other.id == this.id &&
          other.txId == this.txId &&
          other.txTime == this.txTime &&
          other.action == this.action &&
          other.type == this.type &&
          other.walletId == this.walletId &&
          other.chain == this.chain &&
          other.contract == this.contract &&
          other.chainCode == this.chainCode &&
          other.isToken == this.isToken &&
          other.coinName == this.coinName &&
          other.symbol == this.symbol &&
          other.address == this.address &&
          other.rawTx == this.rawTx &&
          other.blockNumber == this.blockNumber &&
          other.blockTime == this.blockTime &&
          other.amount == this.amount &&
          other.fee == this.fee &&
          other.memo == this.memo &&
          other.fromAddress == this.fromAddress &&
          other.receiveAddress == this.receiveAddress &&
          other.nonce == this.nonce &&
          other.gasPrice == this.gasPrice &&
          other.gasLimit == this.gasLimit &&
          other.gasUsed == this.gasUsed &&
          other.xrpLedger == this.xrpLedger &&
          other.xrpSequence == this.xrpSequence &&
          other.txSource == this.txSource &&
          other.indata == this.indata &&
          other.outData == this.outData &&
          other.txSequence == this.txSequence &&
          other.txLedger == this.txLedger &&
          other.txStatus == this.txStatus &&
          other.dataSize == this.dataSize &&
          other.isPacking == this.isPacking &&
          other.extra == this.extra &&
          other.valid == this.valid &&
          other.detailsCache == this.detailsCache);
}

class TransactionsActivityTableCompanion
    extends UpdateCompanion<TransactionsActivityModel> {
  final Value<int> id;
  final Value<String?> txId;
  final Value<int?> txTime;
  final Value<String?> action;
  final Value<String?> type;
  final Value<String?> walletId;
  final Value<String?> chain;
  final Value<String?> contract;
  final Value<String?> chainCode;
  final Value<bool?> isToken;
  final Value<String?> coinName;
  final Value<String?> symbol;
  final Value<String?> address;
  final Value<String?> rawTx;
  final Value<int?> blockNumber;
  final Value<int?> blockTime;
  final Value<String?> amount;
  final Value<String?> fee;
  final Value<String?> memo;
  final Value<String?> fromAddress;
  final Value<String?> receiveAddress;
  final Value<int?> nonce;
  final Value<String?> gasPrice;
  final Value<String?> gasLimit;
  final Value<String?> gasUsed;
  final Value<int?> xrpLedger;
  final Value<int?> xrpSequence;
  final Value<String?> txSource;
  final Value<String?> indata;
  final Value<String?> outData;
  final Value<String?> txSequence;
  final Value<String?> txLedger;
  final Value<int?> txStatus;
  final Value<int?> dataSize;
  final Value<bool?> isPacking;
  final Value<String?> extra;
  final Value<bool?> valid;
  final Value<String?> detailsCache;
  const TransactionsActivityTableCompanion({
    this.id = const Value.absent(),
    this.txId = const Value.absent(),
    this.txTime = const Value.absent(),
    this.action = const Value.absent(),
    this.type = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.contract = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.isToken = const Value.absent(),
    this.coinName = const Value.absent(),
    this.symbol = const Value.absent(),
    this.address = const Value.absent(),
    this.rawTx = const Value.absent(),
    this.blockNumber = const Value.absent(),
    this.blockTime = const Value.absent(),
    this.amount = const Value.absent(),
    this.fee = const Value.absent(),
    this.memo = const Value.absent(),
    this.fromAddress = const Value.absent(),
    this.receiveAddress = const Value.absent(),
    this.nonce = const Value.absent(),
    this.gasPrice = const Value.absent(),
    this.gasLimit = const Value.absent(),
    this.gasUsed = const Value.absent(),
    this.xrpLedger = const Value.absent(),
    this.xrpSequence = const Value.absent(),
    this.txSource = const Value.absent(),
    this.indata = const Value.absent(),
    this.outData = const Value.absent(),
    this.txSequence = const Value.absent(),
    this.txLedger = const Value.absent(),
    this.txStatus = const Value.absent(),
    this.dataSize = const Value.absent(),
    this.isPacking = const Value.absent(),
    this.extra = const Value.absent(),
    this.valid = const Value.absent(),
    this.detailsCache = const Value.absent(),
  });
  TransactionsActivityTableCompanion.insert({
    this.id = const Value.absent(),
    this.txId = const Value.absent(),
    this.txTime = const Value.absent(),
    this.action = const Value.absent(),
    this.type = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.contract = const Value.absent(),
    this.chainCode = const Value.absent(),
    this.isToken = const Value.absent(),
    this.coinName = const Value.absent(),
    this.symbol = const Value.absent(),
    this.address = const Value.absent(),
    this.rawTx = const Value.absent(),
    this.blockNumber = const Value.absent(),
    this.blockTime = const Value.absent(),
    this.amount = const Value.absent(),
    this.fee = const Value.absent(),
    this.memo = const Value.absent(),
    this.fromAddress = const Value.absent(),
    this.receiveAddress = const Value.absent(),
    this.nonce = const Value.absent(),
    this.gasPrice = const Value.absent(),
    this.gasLimit = const Value.absent(),
    this.gasUsed = const Value.absent(),
    this.xrpLedger = const Value.absent(),
    this.xrpSequence = const Value.absent(),
    this.txSource = const Value.absent(),
    this.indata = const Value.absent(),
    this.outData = const Value.absent(),
    this.txSequence = const Value.absent(),
    this.txLedger = const Value.absent(),
    this.txStatus = const Value.absent(),
    this.dataSize = const Value.absent(),
    this.isPacking = const Value.absent(),
    this.extra = const Value.absent(),
    this.valid = const Value.absent(),
    this.detailsCache = const Value.absent(),
  });
  static Insertable<TransactionsActivityModel> custom({
    Expression<int>? id,
    Expression<String>? txId,
    Expression<int>? txTime,
    Expression<String>? action,
    Expression<String>? type,
    Expression<String>? walletId,
    Expression<String>? chain,
    Expression<String>? contract,
    Expression<String>? chainCode,
    Expression<bool>? isToken,
    Expression<String>? coinName,
    Expression<String>? symbol,
    Expression<String>? address,
    Expression<String>? rawTx,
    Expression<int>? blockNumber,
    Expression<int>? blockTime,
    Expression<String>? amount,
    Expression<String>? fee,
    Expression<String>? memo,
    Expression<String>? fromAddress,
    Expression<String>? receiveAddress,
    Expression<int>? nonce,
    Expression<String>? gasPrice,
    Expression<String>? gasLimit,
    Expression<String>? gasUsed,
    Expression<int>? xrpLedger,
    Expression<int>? xrpSequence,
    Expression<String>? txSource,
    Expression<String>? indata,
    Expression<String>? outData,
    Expression<String>? txSequence,
    Expression<String>? txLedger,
    Expression<int>? txStatus,
    Expression<int>? dataSize,
    Expression<bool>? isPacking,
    Expression<String>? extra,
    Expression<bool>? valid,
    Expression<String>? detailsCache,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (txId != null) 'tx_id': txId,
      if (txTime != null) 'tx_time': txTime,
      if (action != null) 'action': action,
      if (type != null) 'type': type,
      if (walletId != null) 'wallet_id': walletId,
      if (chain != null) 'chain': chain,
      if (contract != null) 'contract': contract,
      if (chainCode != null) 'chain_code': chainCode,
      if (isToken != null) 'is_token': isToken,
      if (coinName != null) 'coin_name': coinName,
      if (symbol != null) 'symbol': symbol,
      if (address != null) 'address': address,
      if (rawTx != null) 'raw_tx': rawTx,
      if (blockNumber != null) 'block_number': blockNumber,
      if (blockTime != null) 'block_time': blockTime,
      if (amount != null) 'amount': amount,
      if (fee != null) 'fee': fee,
      if (memo != null) 'memo': memo,
      if (fromAddress != null) 'from_address': fromAddress,
      if (receiveAddress != null) 'receive_address': receiveAddress,
      if (nonce != null) 'nonce': nonce,
      if (gasPrice != null) 'gas_price': gasPrice,
      if (gasLimit != null) 'gas_limit': gasLimit,
      if (gasUsed != null) 'gas_used': gasUsed,
      if (xrpLedger != null) 'xrp_ledger': xrpLedger,
      if (xrpSequence != null) 'xrp_sequence': xrpSequence,
      if (txSource != null) 'tx_source': txSource,
      if (indata != null) 'indata': indata,
      if (outData != null) 'out_data': outData,
      if (txSequence != null) 'tx_sequence': txSequence,
      if (txLedger != null) 'tx_ledger': txLedger,
      if (txStatus != null) 'tx_status': txStatus,
      if (dataSize != null) 'data_size': dataSize,
      if (isPacking != null) 'is_packing': isPacking,
      if (extra != null) 'extra': extra,
      if (valid != null) 'valid': valid,
      if (detailsCache != null) 'details_cache': detailsCache,
    });
  }

  TransactionsActivityTableCompanion copyWith(
      {Value<int>? id,
      Value<String?>? txId,
      Value<int?>? txTime,
      Value<String?>? action,
      Value<String?>? type,
      Value<String?>? walletId,
      Value<String?>? chain,
      Value<String?>? contract,
      Value<String?>? chainCode,
      Value<bool?>? isToken,
      Value<String?>? coinName,
      Value<String?>? symbol,
      Value<String?>? address,
      Value<String?>? rawTx,
      Value<int?>? blockNumber,
      Value<int?>? blockTime,
      Value<String?>? amount,
      Value<String?>? fee,
      Value<String?>? memo,
      Value<String?>? fromAddress,
      Value<String?>? receiveAddress,
      Value<int?>? nonce,
      Value<String?>? gasPrice,
      Value<String?>? gasLimit,
      Value<String?>? gasUsed,
      Value<int?>? xrpLedger,
      Value<int?>? xrpSequence,
      Value<String?>? txSource,
      Value<String?>? indata,
      Value<String?>? outData,
      Value<String?>? txSequence,
      Value<String?>? txLedger,
      Value<int?>? txStatus,
      Value<int?>? dataSize,
      Value<bool?>? isPacking,
      Value<String?>? extra,
      Value<bool?>? valid,
      Value<String?>? detailsCache}) {
    return TransactionsActivityTableCompanion(
      id: id ?? this.id,
      txId: txId ?? this.txId,
      txTime: txTime ?? this.txTime,
      action: action ?? this.action,
      type: type ?? this.type,
      walletId: walletId ?? this.walletId,
      chain: chain ?? this.chain,
      contract: contract ?? this.contract,
      chainCode: chainCode ?? this.chainCode,
      isToken: isToken ?? this.isToken,
      coinName: coinName ?? this.coinName,
      symbol: symbol ?? this.symbol,
      address: address ?? this.address,
      rawTx: rawTx ?? this.rawTx,
      blockNumber: blockNumber ?? this.blockNumber,
      blockTime: blockTime ?? this.blockTime,
      amount: amount ?? this.amount,
      fee: fee ?? this.fee,
      memo: memo ?? this.memo,
      fromAddress: fromAddress ?? this.fromAddress,
      receiveAddress: receiveAddress ?? this.receiveAddress,
      nonce: nonce ?? this.nonce,
      gasPrice: gasPrice ?? this.gasPrice,
      gasLimit: gasLimit ?? this.gasLimit,
      gasUsed: gasUsed ?? this.gasUsed,
      xrpLedger: xrpLedger ?? this.xrpLedger,
      xrpSequence: xrpSequence ?? this.xrpSequence,
      txSource: txSource ?? this.txSource,
      indata: indata ?? this.indata,
      outData: outData ?? this.outData,
      txSequence: txSequence ?? this.txSequence,
      txLedger: txLedger ?? this.txLedger,
      txStatus: txStatus ?? this.txStatus,
      dataSize: dataSize ?? this.dataSize,
      isPacking: isPacking ?? this.isPacking,
      extra: extra ?? this.extra,
      valid: valid ?? this.valid,
      detailsCache: detailsCache ?? this.detailsCache,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (txId.present) {
      map['tx_id'] = Variable<String>(txId.value);
    }
    if (txTime.present) {
      map['tx_time'] = Variable<int>(txTime.value);
    }
    if (action.present) {
      map['action'] = Variable<String>(action.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (walletId.present) {
      map['wallet_id'] = Variable<String>(walletId.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (contract.present) {
      map['contract'] = Variable<String>(contract.value);
    }
    if (chainCode.present) {
      map['chain_code'] = Variable<String>(chainCode.value);
    }
    if (isToken.present) {
      map['is_token'] = Variable<bool>(isToken.value);
    }
    if (coinName.present) {
      map['coin_name'] = Variable<String>(coinName.value);
    }
    if (symbol.present) {
      map['symbol'] = Variable<String>(symbol.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (rawTx.present) {
      map['raw_tx'] = Variable<String>(rawTx.value);
    }
    if (blockNumber.present) {
      map['block_number'] = Variable<int>(blockNumber.value);
    }
    if (blockTime.present) {
      map['block_time'] = Variable<int>(blockTime.value);
    }
    if (amount.present) {
      map['amount'] = Variable<String>(amount.value);
    }
    if (fee.present) {
      map['fee'] = Variable<String>(fee.value);
    }
    if (memo.present) {
      map['memo'] = Variable<String>(memo.value);
    }
    if (fromAddress.present) {
      map['from_address'] = Variable<String>(fromAddress.value);
    }
    if (receiveAddress.present) {
      map['receive_address'] = Variable<String>(receiveAddress.value);
    }
    if (nonce.present) {
      map['nonce'] = Variable<int>(nonce.value);
    }
    if (gasPrice.present) {
      map['gas_price'] = Variable<String>(gasPrice.value);
    }
    if (gasLimit.present) {
      map['gas_limit'] = Variable<String>(gasLimit.value);
    }
    if (gasUsed.present) {
      map['gas_used'] = Variable<String>(gasUsed.value);
    }
    if (xrpLedger.present) {
      map['xrp_ledger'] = Variable<int>(xrpLedger.value);
    }
    if (xrpSequence.present) {
      map['xrp_sequence'] = Variable<int>(xrpSequence.value);
    }
    if (txSource.present) {
      map['tx_source'] = Variable<String>(txSource.value);
    }
    if (indata.present) {
      map['indata'] = Variable<String>(indata.value);
    }
    if (outData.present) {
      map['out_data'] = Variable<String>(outData.value);
    }
    if (txSequence.present) {
      map['tx_sequence'] = Variable<String>(txSequence.value);
    }
    if (txLedger.present) {
      map['tx_ledger'] = Variable<String>(txLedger.value);
    }
    if (txStatus.present) {
      map['tx_status'] = Variable<int>(txStatus.value);
    }
    if (dataSize.present) {
      map['data_size'] = Variable<int>(dataSize.value);
    }
    if (isPacking.present) {
      map['is_packing'] = Variable<bool>(isPacking.value);
    }
    if (extra.present) {
      map['extra'] = Variable<String>(extra.value);
    }
    if (valid.present) {
      map['valid'] = Variable<bool>(valid.value);
    }
    if (detailsCache.present) {
      map['details_cache'] = Variable<String>(detailsCache.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TransactionsActivityTableCompanion(')
          ..write('id: $id, ')
          ..write('txId: $txId, ')
          ..write('txTime: $txTime, ')
          ..write('action: $action, ')
          ..write('type: $type, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('contract: $contract, ')
          ..write('chainCode: $chainCode, ')
          ..write('isToken: $isToken, ')
          ..write('coinName: $coinName, ')
          ..write('symbol: $symbol, ')
          ..write('address: $address, ')
          ..write('rawTx: $rawTx, ')
          ..write('blockNumber: $blockNumber, ')
          ..write('blockTime: $blockTime, ')
          ..write('amount: $amount, ')
          ..write('fee: $fee, ')
          ..write('memo: $memo, ')
          ..write('fromAddress: $fromAddress, ')
          ..write('receiveAddress: $receiveAddress, ')
          ..write('nonce: $nonce, ')
          ..write('gasPrice: $gasPrice, ')
          ..write('gasLimit: $gasLimit, ')
          ..write('gasUsed: $gasUsed, ')
          ..write('xrpLedger: $xrpLedger, ')
          ..write('xrpSequence: $xrpSequence, ')
          ..write('txSource: $txSource, ')
          ..write('indata: $indata, ')
          ..write('outData: $outData, ')
          ..write('txSequence: $txSequence, ')
          ..write('txLedger: $txLedger, ')
          ..write('txStatus: $txStatus, ')
          ..write('dataSize: $dataSize, ')
          ..write('isPacking: $isPacking, ')
          ..write('extra: $extra, ')
          ..write('valid: $valid, ')
          ..write('detailsCache: $detailsCache')
          ..write(')'))
        .toString();
  }
}

class $AddressMappingTableTable extends AddressMappingTable
    with TableInfo<$AddressMappingTableTable, AddressMappingModel> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AddressMappingTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _deviceIdMeta =
      const VerificationMeta('deviceId');
  @override
  late final GeneratedColumn<String> deviceId = GeneratedColumn<String>(
      'device_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _walletIdMeta =
      const VerificationMeta('walletId');
  @override
  late final GeneratedColumn<String> walletId = GeneratedColumn<String>(
      'wallet_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  @override
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  @override
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _addressIndexMeta =
      const VerificationMeta('addressIndex');
  @override
  late final GeneratedColumn<int> addressIndex = GeneratedColumn<int>(
      'address_index', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns =>
      [id, deviceId, walletId, chain, address, addressIndex];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'address_mapping_table';
  @override
  VerificationContext validateIntegrity(
      Insertable<AddressMappingModel> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('device_id')) {
      context.handle(_deviceIdMeta,
          deviceId.isAcceptableOrUnknown(data['device_id']!, _deviceIdMeta));
    } else if (isInserting) {
      context.missing(_deviceIdMeta);
    }
    if (data.containsKey('wallet_id')) {
      context.handle(_walletIdMeta,
          walletId.isAcceptableOrUnknown(data['wallet_id']!, _walletIdMeta));
    } else if (isInserting) {
      context.missing(_walletIdMeta);
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    }
    if (data.containsKey('address_index')) {
      context.handle(
          _addressIndexMeta,
          addressIndex.isAcceptableOrUnknown(
              data['address_index']!, _addressIndexMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AddressMappingModel map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AddressMappingModel(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      deviceId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}device_id'])!,
      walletId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_id'])!,
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address']),
      addressIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}address_index']),
    );
  }

  @override
  $AddressMappingTableTable createAlias(String alias) {
    return $AddressMappingTableTable(attachedDatabase, alias);
  }
}

class AddressMappingModel extends DataClass
    implements Insertable<AddressMappingModel> {
  final int id;
  final String deviceId;
  final String walletId;
  final String? chain;
  final String? address;
  final int? addressIndex;
  const AddressMappingModel(
      {required this.id,
      required this.deviceId,
      required this.walletId,
      this.chain,
      this.address,
      this.addressIndex});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['device_id'] = Variable<String>(deviceId);
    map['wallet_id'] = Variable<String>(walletId);
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || address != null) {
      map['address'] = Variable<String>(address);
    }
    if (!nullToAbsent || addressIndex != null) {
      map['address_index'] = Variable<int>(addressIndex);
    }
    return map;
  }

  AddressMappingTableCompanion toCompanion(bool nullToAbsent) {
    return AddressMappingTableCompanion(
      id: Value(id),
      deviceId: Value(deviceId),
      walletId: Value(walletId),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      address: address == null && nullToAbsent
          ? const Value.absent()
          : Value(address),
      addressIndex: addressIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(addressIndex),
    );
  }

  factory AddressMappingModel.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AddressMappingModel(
      id: serializer.fromJson<int>(json['id']),
      deviceId: serializer.fromJson<String>(json['deviceId']),
      walletId: serializer.fromJson<String>(json['walletId']),
      chain: serializer.fromJson<String?>(json['chain']),
      address: serializer.fromJson<String?>(json['address']),
      addressIndex: serializer.fromJson<int?>(json['addressIndex']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'deviceId': serializer.toJson<String>(deviceId),
      'walletId': serializer.toJson<String>(walletId),
      'chain': serializer.toJson<String?>(chain),
      'address': serializer.toJson<String?>(address),
      'addressIndex': serializer.toJson<int?>(addressIndex),
    };
  }

  AddressMappingModel copyWith(
          {int? id,
          String? deviceId,
          String? walletId,
          Value<String?> chain = const Value.absent(),
          Value<String?> address = const Value.absent(),
          Value<int?> addressIndex = const Value.absent()}) =>
      AddressMappingModel(
        id: id ?? this.id,
        deviceId: deviceId ?? this.deviceId,
        walletId: walletId ?? this.walletId,
        chain: chain.present ? chain.value : this.chain,
        address: address.present ? address.value : this.address,
        addressIndex:
            addressIndex.present ? addressIndex.value : this.addressIndex,
      );
  AddressMappingModel copyWithCompanion(AddressMappingTableCompanion data) {
    return AddressMappingModel(
      id: data.id.present ? data.id.value : this.id,
      deviceId: data.deviceId.present ? data.deviceId.value : this.deviceId,
      walletId: data.walletId.present ? data.walletId.value : this.walletId,
      chain: data.chain.present ? data.chain.value : this.chain,
      address: data.address.present ? data.address.value : this.address,
      addressIndex: data.addressIndex.present
          ? data.addressIndex.value
          : this.addressIndex,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AddressMappingModel(')
          ..write('id: $id, ')
          ..write('deviceId: $deviceId, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('address: $address, ')
          ..write('addressIndex: $addressIndex')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, deviceId, walletId, chain, address, addressIndex);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AddressMappingModel &&
          other.id == this.id &&
          other.deviceId == this.deviceId &&
          other.walletId == this.walletId &&
          other.chain == this.chain &&
          other.address == this.address &&
          other.addressIndex == this.addressIndex);
}

class AddressMappingTableCompanion
    extends UpdateCompanion<AddressMappingModel> {
  final Value<int> id;
  final Value<String> deviceId;
  final Value<String> walletId;
  final Value<String?> chain;
  final Value<String?> address;
  final Value<int?> addressIndex;
  const AddressMappingTableCompanion({
    this.id = const Value.absent(),
    this.deviceId = const Value.absent(),
    this.walletId = const Value.absent(),
    this.chain = const Value.absent(),
    this.address = const Value.absent(),
    this.addressIndex = const Value.absent(),
  });
  AddressMappingTableCompanion.insert({
    this.id = const Value.absent(),
    required String deviceId,
    required String walletId,
    this.chain = const Value.absent(),
    this.address = const Value.absent(),
    this.addressIndex = const Value.absent(),
  })  : deviceId = Value(deviceId),
        walletId = Value(walletId);
  static Insertable<AddressMappingModel> custom({
    Expression<int>? id,
    Expression<String>? deviceId,
    Expression<String>? walletId,
    Expression<String>? chain,
    Expression<String>? address,
    Expression<int>? addressIndex,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (deviceId != null) 'device_id': deviceId,
      if (walletId != null) 'wallet_id': walletId,
      if (chain != null) 'chain': chain,
      if (address != null) 'address': address,
      if (addressIndex != null) 'address_index': addressIndex,
    });
  }

  AddressMappingTableCompanion copyWith(
      {Value<int>? id,
      Value<String>? deviceId,
      Value<String>? walletId,
      Value<String?>? chain,
      Value<String?>? address,
      Value<int?>? addressIndex}) {
    return AddressMappingTableCompanion(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      walletId: walletId ?? this.walletId,
      chain: chain ?? this.chain,
      address: address ?? this.address,
      addressIndex: addressIndex ?? this.addressIndex,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (deviceId.present) {
      map['device_id'] = Variable<String>(deviceId.value);
    }
    if (walletId.present) {
      map['wallet_id'] = Variable<String>(walletId.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (addressIndex.present) {
      map['address_index'] = Variable<int>(addressIndex.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AddressMappingTableCompanion(')
          ..write('id: $id, ')
          ..write('deviceId: $deviceId, ')
          ..write('walletId: $walletId, ')
          ..write('chain: $chain, ')
          ..write('address: $address, ')
          ..write('addressIndex: $addressIndex')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $AddressBookTableTable addressBookTable =
      $AddressBookTableTable(this);
  late final $MonitorTableTable monitorTable = $MonitorTableTable(this);
  late final $CoinTableTable coinTable = $CoinTableTable(this);
  late final $WalletTableTable walletTable = $WalletTableTable(this);
  late final $AddressTableTable addressTable = $AddressTableTable(this);
  late final $TokenTableTable tokenTable = $TokenTableTable(this);
  late final $DappTableTable dappTable = $DappTableTable(this);
  late final $TransactionsActivityTableTable transactionsActivityTable =
      $TransactionsActivityTableTable(this);
  late final $AddressMappingTableTable addressMappingTable =
      $AddressMappingTableTable(this);
  late final AddressBookDao addressBookDao =
      AddressBookDao(this as AppDatabase);
  late final MonitorDao monitorDao = MonitorDao(this as AppDatabase);
  late final CoinDao coinDao = CoinDao(this as AppDatabase);
  late final WalletDao walletDao = WalletDao(this as AppDatabase);
  late final AddressDao addressDao = AddressDao(this as AppDatabase);
  late final TokenDao tokenDao = TokenDao(this as AppDatabase);
  late final DappDao dappDao = DappDao(this as AppDatabase);
  late final TransactionsActivityDao transactionsActivityDao =
      TransactionsActivityDao(this as AppDatabase);
  late final AddressMappingDao addressMappingDao =
      AddressMappingDao(this as AppDatabase);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        addressBookTable,
        monitorTable,
        coinTable,
        walletTable,
        addressTable,
        tokenTable,
        dappTable,
        transactionsActivityTable,
        addressMappingTable
      ];
}

typedef $$AddressBookTableTableCreateCompanionBuilder
    = AddressBookTableCompanion Function({
  Value<int> id,
  required String chain,
  required String address,
  required String constantName,
});
typedef $$AddressBookTableTableUpdateCompanionBuilder
    = AddressBookTableCompanion Function({
  Value<int> id,
  Value<String> chain,
  Value<String> address,
  Value<String> constantName,
});

class $$AddressBookTableTableFilterComposer
    extends Composer<_$AppDatabase, $AddressBookTableTable> {
  $$AddressBookTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get constantName => $composableBuilder(
      column: $table.constantName, builder: (column) => ColumnFilters(column));
}

class $$AddressBookTableTableOrderingComposer
    extends Composer<_$AppDatabase, $AddressBookTableTable> {
  $$AddressBookTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get constantName => $composableBuilder(
      column: $table.constantName,
      builder: (column) => ColumnOrderings(column));
}

class $$AddressBookTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $AddressBookTableTable> {
  $$AddressBookTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get address =>
      $composableBuilder(column: $table.address, builder: (column) => column);

  GeneratedColumn<String> get constantName => $composableBuilder(
      column: $table.constantName, builder: (column) => column);
}

class $$AddressBookTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AddressBookTableTable,
    AddressBookModel,
    $$AddressBookTableTableFilterComposer,
    $$AddressBookTableTableOrderingComposer,
    $$AddressBookTableTableAnnotationComposer,
    $$AddressBookTableTableCreateCompanionBuilder,
    $$AddressBookTableTableUpdateCompanionBuilder,
    (
      AddressBookModel,
      BaseReferences<_$AppDatabase, $AddressBookTableTable, AddressBookModel>
    ),
    AddressBookModel,
    PrefetchHooks Function()> {
  $$AddressBookTableTableTableManager(
      _$AppDatabase db, $AddressBookTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AddressBookTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AddressBookTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AddressBookTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> chain = const Value.absent(),
            Value<String> address = const Value.absent(),
            Value<String> constantName = const Value.absent(),
          }) =>
              AddressBookTableCompanion(
            id: id,
            chain: chain,
            address: address,
            constantName: constantName,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String chain,
            required String address,
            required String constantName,
          }) =>
              AddressBookTableCompanion.insert(
            id: id,
            chain: chain,
            address: address,
            constantName: constantName,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AddressBookTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AddressBookTableTable,
    AddressBookModel,
    $$AddressBookTableTableFilterComposer,
    $$AddressBookTableTableOrderingComposer,
    $$AddressBookTableTableAnnotationComposer,
    $$AddressBookTableTableCreateCompanionBuilder,
    $$AddressBookTableTableUpdateCompanionBuilder,
    (
      AddressBookModel,
      BaseReferences<_$AppDatabase, $AddressBookTableTable, AddressBookModel>
    ),
    AddressBookModel,
    PrefetchHooks Function()>;
typedef $$MonitorTableTableCreateCompanionBuilder = MonitorTableCompanion
    Function({
  Value<int> id,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<String?> publickey,
  Value<int?> batchId,
  Value<String?> walletId,
  Value<int?> slip44Id,
  Value<int?> addressNum,
  Value<int?> segwitType,
  Value<String?> path,
  Value<int?> keyTypekeyType,
  Value<bool?> isXPub,
});
typedef $$MonitorTableTableUpdateCompanionBuilder = MonitorTableCompanion
    Function({
  Value<int> id,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<String?> publickey,
  Value<int?> batchId,
  Value<String?> walletId,
  Value<int?> slip44Id,
  Value<int?> addressNum,
  Value<int?> segwitType,
  Value<String?> path,
  Value<int?> keyTypekeyType,
  Value<bool?> isXPub,
});

class $$MonitorTableTableFilterComposer
    extends Composer<_$AppDatabase, $MonitorTableTable> {
  $$MonitorTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get publickey => $composableBuilder(
      column: $table.publickey, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get batchId => $composableBuilder(
      column: $table.batchId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get slip44Id => $composableBuilder(
      column: $table.slip44Id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get addressNum => $composableBuilder(
      column: $table.addressNum, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get segwitType => $composableBuilder(
      column: $table.segwitType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get path => $composableBuilder(
      column: $table.path, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get keyTypekeyType => $composableBuilder(
      column: $table.keyTypekeyType,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isXPub => $composableBuilder(
      column: $table.isXPub, builder: (column) => ColumnFilters(column));
}

class $$MonitorTableTableOrderingComposer
    extends Composer<_$AppDatabase, $MonitorTableTable> {
  $$MonitorTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get publickey => $composableBuilder(
      column: $table.publickey, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get batchId => $composableBuilder(
      column: $table.batchId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get slip44Id => $composableBuilder(
      column: $table.slip44Id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get addressNum => $composableBuilder(
      column: $table.addressNum, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get segwitType => $composableBuilder(
      column: $table.segwitType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get path => $composableBuilder(
      column: $table.path, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get keyTypekeyType => $composableBuilder(
      column: $table.keyTypekeyType,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isXPub => $composableBuilder(
      column: $table.isXPub, builder: (column) => ColumnOrderings(column));
}

class $$MonitorTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $MonitorTableTable> {
  $$MonitorTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get chainCode =>
      $composableBuilder(column: $table.chainCode, builder: (column) => column);

  GeneratedColumn<String> get publickey =>
      $composableBuilder(column: $table.publickey, builder: (column) => column);

  GeneratedColumn<int> get batchId =>
      $composableBuilder(column: $table.batchId, builder: (column) => column);

  GeneratedColumn<String> get walletId =>
      $composableBuilder(column: $table.walletId, builder: (column) => column);

  GeneratedColumn<int> get slip44Id =>
      $composableBuilder(column: $table.slip44Id, builder: (column) => column);

  GeneratedColumn<int> get addressNum => $composableBuilder(
      column: $table.addressNum, builder: (column) => column);

  GeneratedColumn<int> get segwitType => $composableBuilder(
      column: $table.segwitType, builder: (column) => column);

  GeneratedColumn<String> get path =>
      $composableBuilder(column: $table.path, builder: (column) => column);

  GeneratedColumn<int> get keyTypekeyType => $composableBuilder(
      column: $table.keyTypekeyType, builder: (column) => column);

  GeneratedColumn<bool> get isXPub =>
      $composableBuilder(column: $table.isXPub, builder: (column) => column);
}

class $$MonitorTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $MonitorTableTable,
    MonitorModel,
    $$MonitorTableTableFilterComposer,
    $$MonitorTableTableOrderingComposer,
    $$MonitorTableTableAnnotationComposer,
    $$MonitorTableTableCreateCompanionBuilder,
    $$MonitorTableTableUpdateCompanionBuilder,
    (
      MonitorModel,
      BaseReferences<_$AppDatabase, $MonitorTableTable, MonitorModel>
    ),
    MonitorModel,
    PrefetchHooks Function()> {
  $$MonitorTableTableTableManager(_$AppDatabase db, $MonitorTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$MonitorTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$MonitorTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$MonitorTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<String?> publickey = const Value.absent(),
            Value<int?> batchId = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<int?> slip44Id = const Value.absent(),
            Value<int?> addressNum = const Value.absent(),
            Value<int?> segwitType = const Value.absent(),
            Value<String?> path = const Value.absent(),
            Value<int?> keyTypekeyType = const Value.absent(),
            Value<bool?> isXPub = const Value.absent(),
          }) =>
              MonitorTableCompanion(
            id: id,
            chain: chain,
            chainCode: chainCode,
            publickey: publickey,
            batchId: batchId,
            walletId: walletId,
            slip44Id: slip44Id,
            addressNum: addressNum,
            segwitType: segwitType,
            path: path,
            keyTypekeyType: keyTypekeyType,
            isXPub: isXPub,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<String?> publickey = const Value.absent(),
            Value<int?> batchId = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<int?> slip44Id = const Value.absent(),
            Value<int?> addressNum = const Value.absent(),
            Value<int?> segwitType = const Value.absent(),
            Value<String?> path = const Value.absent(),
            Value<int?> keyTypekeyType = const Value.absent(),
            Value<bool?> isXPub = const Value.absent(),
          }) =>
              MonitorTableCompanion.insert(
            id: id,
            chain: chain,
            chainCode: chainCode,
            publickey: publickey,
            batchId: batchId,
            walletId: walletId,
            slip44Id: slip44Id,
            addressNum: addressNum,
            segwitType: segwitType,
            path: path,
            keyTypekeyType: keyTypekeyType,
            isXPub: isXPub,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$MonitorTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $MonitorTableTable,
    MonitorModel,
    $$MonitorTableTableFilterComposer,
    $$MonitorTableTableOrderingComposer,
    $$MonitorTableTableAnnotationComposer,
    $$MonitorTableTableCreateCompanionBuilder,
    $$MonitorTableTableUpdateCompanionBuilder,
    (
      MonitorModel,
      BaseReferences<_$AppDatabase, $MonitorTableTable, MonitorModel>
    ),
    MonitorModel,
    PrefetchHooks Function()>;
typedef $$CoinTableTableCreateCompanionBuilder = CoinTableCompanion Function({
  Value<int> id,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<int?> chainId,
  Value<String?> chainName,
  Value<String?> cnName,
  Value<String?> symbol,
  Value<String?> contract,
  Value<int?> chainDecimal,
  Value<int?> balanceDecimal,
  Value<int?> tokenType,
  Value<String?> price,
  Value<bool?> isSupportToken,
  Value<bool?> isCoinSupported,
  Value<bool?> isToken,
  Value<String?> symbolIcon,
  Value<String?> chainIcon,
  Value<String?> disabledIcon,
  Value<String?> coinFeeTier,
  Value<String?> feeCache,
  Value<String?> gasLimitCache,
  Value<String?> nftGasLimitCache,
  Value<double?> sortedId,
  Value<String?> filGasJsonStr,
  Value<DateTime?> hiddenDateTime,
});
typedef $$CoinTableTableUpdateCompanionBuilder = CoinTableCompanion Function({
  Value<int> id,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<int?> chainId,
  Value<String?> chainName,
  Value<String?> cnName,
  Value<String?> symbol,
  Value<String?> contract,
  Value<int?> chainDecimal,
  Value<int?> balanceDecimal,
  Value<int?> tokenType,
  Value<String?> price,
  Value<bool?> isSupportToken,
  Value<bool?> isCoinSupported,
  Value<bool?> isToken,
  Value<String?> symbolIcon,
  Value<String?> chainIcon,
  Value<String?> disabledIcon,
  Value<String?> coinFeeTier,
  Value<String?> feeCache,
  Value<String?> gasLimitCache,
  Value<String?> nftGasLimitCache,
  Value<double?> sortedId,
  Value<String?> filGasJsonStr,
  Value<DateTime?> hiddenDateTime,
});

class $$CoinTableTableFilterComposer
    extends Composer<_$AppDatabase, $CoinTableTable> {
  $$CoinTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get chainId => $composableBuilder(
      column: $table.chainId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainName => $composableBuilder(
      column: $table.chainName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get cnName => $composableBuilder(
      column: $table.cnName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get symbol => $composableBuilder(
      column: $table.symbol, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get contract => $composableBuilder(
      column: $table.contract, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get chainDecimal => $composableBuilder(
      column: $table.chainDecimal, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get balanceDecimal => $composableBuilder(
      column: $table.balanceDecimal,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get tokenType => $composableBuilder(
      column: $table.tokenType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get price => $composableBuilder(
      column: $table.price, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isSupportToken => $composableBuilder(
      column: $table.isSupportToken,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isCoinSupported => $composableBuilder(
      column: $table.isCoinSupported,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isToken => $composableBuilder(
      column: $table.isToken, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get symbolIcon => $composableBuilder(
      column: $table.symbolIcon, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainIcon => $composableBuilder(
      column: $table.chainIcon, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get disabledIcon => $composableBuilder(
      column: $table.disabledIcon, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get coinFeeTier => $composableBuilder(
      column: $table.coinFeeTier, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get feeCache => $composableBuilder(
      column: $table.feeCache, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get gasLimitCache => $composableBuilder(
      column: $table.gasLimitCache, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftGasLimitCache => $composableBuilder(
      column: $table.nftGasLimitCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get sortedId => $composableBuilder(
      column: $table.sortedId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get filGasJsonStr => $composableBuilder(
      column: $table.filGasJsonStr, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get hiddenDateTime => $composableBuilder(
      column: $table.hiddenDateTime,
      builder: (column) => ColumnFilters(column));
}

class $$CoinTableTableOrderingComposer
    extends Composer<_$AppDatabase, $CoinTableTable> {
  $$CoinTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get chainId => $composableBuilder(
      column: $table.chainId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainName => $composableBuilder(
      column: $table.chainName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get cnName => $composableBuilder(
      column: $table.cnName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get symbol => $composableBuilder(
      column: $table.symbol, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get contract => $composableBuilder(
      column: $table.contract, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get chainDecimal => $composableBuilder(
      column: $table.chainDecimal,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get balanceDecimal => $composableBuilder(
      column: $table.balanceDecimal,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get tokenType => $composableBuilder(
      column: $table.tokenType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get price => $composableBuilder(
      column: $table.price, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isSupportToken => $composableBuilder(
      column: $table.isSupportToken,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isCoinSupported => $composableBuilder(
      column: $table.isCoinSupported,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isToken => $composableBuilder(
      column: $table.isToken, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get symbolIcon => $composableBuilder(
      column: $table.symbolIcon, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainIcon => $composableBuilder(
      column: $table.chainIcon, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get disabledIcon => $composableBuilder(
      column: $table.disabledIcon,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get coinFeeTier => $composableBuilder(
      column: $table.coinFeeTier, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get feeCache => $composableBuilder(
      column: $table.feeCache, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get gasLimitCache => $composableBuilder(
      column: $table.gasLimitCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftGasLimitCache => $composableBuilder(
      column: $table.nftGasLimitCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get sortedId => $composableBuilder(
      column: $table.sortedId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get filGasJsonStr => $composableBuilder(
      column: $table.filGasJsonStr,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get hiddenDateTime => $composableBuilder(
      column: $table.hiddenDateTime,
      builder: (column) => ColumnOrderings(column));
}

class $$CoinTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $CoinTableTable> {
  $$CoinTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get chainCode =>
      $composableBuilder(column: $table.chainCode, builder: (column) => column);

  GeneratedColumn<int> get chainId =>
      $composableBuilder(column: $table.chainId, builder: (column) => column);

  GeneratedColumn<String> get chainName =>
      $composableBuilder(column: $table.chainName, builder: (column) => column);

  GeneratedColumn<String> get cnName =>
      $composableBuilder(column: $table.cnName, builder: (column) => column);

  GeneratedColumn<String> get symbol =>
      $composableBuilder(column: $table.symbol, builder: (column) => column);

  GeneratedColumn<String> get contract =>
      $composableBuilder(column: $table.contract, builder: (column) => column);

  GeneratedColumn<int> get chainDecimal => $composableBuilder(
      column: $table.chainDecimal, builder: (column) => column);

  GeneratedColumn<int> get balanceDecimal => $composableBuilder(
      column: $table.balanceDecimal, builder: (column) => column);

  GeneratedColumn<int> get tokenType =>
      $composableBuilder(column: $table.tokenType, builder: (column) => column);

  GeneratedColumn<String> get price =>
      $composableBuilder(column: $table.price, builder: (column) => column);

  GeneratedColumn<bool> get isSupportToken => $composableBuilder(
      column: $table.isSupportToken, builder: (column) => column);

  GeneratedColumn<bool> get isCoinSupported => $composableBuilder(
      column: $table.isCoinSupported, builder: (column) => column);

  GeneratedColumn<bool> get isToken =>
      $composableBuilder(column: $table.isToken, builder: (column) => column);

  GeneratedColumn<String> get symbolIcon => $composableBuilder(
      column: $table.symbolIcon, builder: (column) => column);

  GeneratedColumn<String> get chainIcon =>
      $composableBuilder(column: $table.chainIcon, builder: (column) => column);

  GeneratedColumn<String> get disabledIcon => $composableBuilder(
      column: $table.disabledIcon, builder: (column) => column);

  GeneratedColumn<String> get coinFeeTier => $composableBuilder(
      column: $table.coinFeeTier, builder: (column) => column);

  GeneratedColumn<String> get feeCache =>
      $composableBuilder(column: $table.feeCache, builder: (column) => column);

  GeneratedColumn<String> get gasLimitCache => $composableBuilder(
      column: $table.gasLimitCache, builder: (column) => column);

  GeneratedColumn<String> get nftGasLimitCache => $composableBuilder(
      column: $table.nftGasLimitCache, builder: (column) => column);

  GeneratedColumn<double> get sortedId =>
      $composableBuilder(column: $table.sortedId, builder: (column) => column);

  GeneratedColumn<String> get filGasJsonStr => $composableBuilder(
      column: $table.filGasJsonStr, builder: (column) => column);

  GeneratedColumn<DateTime> get hiddenDateTime => $composableBuilder(
      column: $table.hiddenDateTime, builder: (column) => column);
}

class $$CoinTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CoinTableTable,
    CoinModel,
    $$CoinTableTableFilterComposer,
    $$CoinTableTableOrderingComposer,
    $$CoinTableTableAnnotationComposer,
    $$CoinTableTableCreateCompanionBuilder,
    $$CoinTableTableUpdateCompanionBuilder,
    (CoinModel, BaseReferences<_$AppDatabase, $CoinTableTable, CoinModel>),
    CoinModel,
    PrefetchHooks Function()> {
  $$CoinTableTableTableManager(_$AppDatabase db, $CoinTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CoinTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CoinTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CoinTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<int?> chainId = const Value.absent(),
            Value<String?> chainName = const Value.absent(),
            Value<String?> cnName = const Value.absent(),
            Value<String?> symbol = const Value.absent(),
            Value<String?> contract = const Value.absent(),
            Value<int?> chainDecimal = const Value.absent(),
            Value<int?> balanceDecimal = const Value.absent(),
            Value<int?> tokenType = const Value.absent(),
            Value<String?> price = const Value.absent(),
            Value<bool?> isSupportToken = const Value.absent(),
            Value<bool?> isCoinSupported = const Value.absent(),
            Value<bool?> isToken = const Value.absent(),
            Value<String?> symbolIcon = const Value.absent(),
            Value<String?> chainIcon = const Value.absent(),
            Value<String?> disabledIcon = const Value.absent(),
            Value<String?> coinFeeTier = const Value.absent(),
            Value<String?> feeCache = const Value.absent(),
            Value<String?> gasLimitCache = const Value.absent(),
            Value<String?> nftGasLimitCache = const Value.absent(),
            Value<double?> sortedId = const Value.absent(),
            Value<String?> filGasJsonStr = const Value.absent(),
            Value<DateTime?> hiddenDateTime = const Value.absent(),
          }) =>
              CoinTableCompanion(
            id: id,
            chain: chain,
            chainCode: chainCode,
            chainId: chainId,
            chainName: chainName,
            cnName: cnName,
            symbol: symbol,
            contract: contract,
            chainDecimal: chainDecimal,
            balanceDecimal: balanceDecimal,
            tokenType: tokenType,
            price: price,
            isSupportToken: isSupportToken,
            isCoinSupported: isCoinSupported,
            isToken: isToken,
            symbolIcon: symbolIcon,
            chainIcon: chainIcon,
            disabledIcon: disabledIcon,
            coinFeeTier: coinFeeTier,
            feeCache: feeCache,
            gasLimitCache: gasLimitCache,
            nftGasLimitCache: nftGasLimitCache,
            sortedId: sortedId,
            filGasJsonStr: filGasJsonStr,
            hiddenDateTime: hiddenDateTime,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<int?> chainId = const Value.absent(),
            Value<String?> chainName = const Value.absent(),
            Value<String?> cnName = const Value.absent(),
            Value<String?> symbol = const Value.absent(),
            Value<String?> contract = const Value.absent(),
            Value<int?> chainDecimal = const Value.absent(),
            Value<int?> balanceDecimal = const Value.absent(),
            Value<int?> tokenType = const Value.absent(),
            Value<String?> price = const Value.absent(),
            Value<bool?> isSupportToken = const Value.absent(),
            Value<bool?> isCoinSupported = const Value.absent(),
            Value<bool?> isToken = const Value.absent(),
            Value<String?> symbolIcon = const Value.absent(),
            Value<String?> chainIcon = const Value.absent(),
            Value<String?> disabledIcon = const Value.absent(),
            Value<String?> coinFeeTier = const Value.absent(),
            Value<String?> feeCache = const Value.absent(),
            Value<String?> gasLimitCache = const Value.absent(),
            Value<String?> nftGasLimitCache = const Value.absent(),
            Value<double?> sortedId = const Value.absent(),
            Value<String?> filGasJsonStr = const Value.absent(),
            Value<DateTime?> hiddenDateTime = const Value.absent(),
          }) =>
              CoinTableCompanion.insert(
            id: id,
            chain: chain,
            chainCode: chainCode,
            chainId: chainId,
            chainName: chainName,
            cnName: cnName,
            symbol: symbol,
            contract: contract,
            chainDecimal: chainDecimal,
            balanceDecimal: balanceDecimal,
            tokenType: tokenType,
            price: price,
            isSupportToken: isSupportToken,
            isCoinSupported: isCoinSupported,
            isToken: isToken,
            symbolIcon: symbolIcon,
            chainIcon: chainIcon,
            disabledIcon: disabledIcon,
            coinFeeTier: coinFeeTier,
            feeCache: feeCache,
            gasLimitCache: gasLimitCache,
            nftGasLimitCache: nftGasLimitCache,
            sortedId: sortedId,
            filGasJsonStr: filGasJsonStr,
            hiddenDateTime: hiddenDateTime,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CoinTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $CoinTableTable,
    CoinModel,
    $$CoinTableTableFilterComposer,
    $$CoinTableTableOrderingComposer,
    $$CoinTableTableAnnotationComposer,
    $$CoinTableTableCreateCompanionBuilder,
    $$CoinTableTableUpdateCompanionBuilder,
    (CoinModel, BaseReferences<_$AppDatabase, $CoinTableTable, CoinModel>),
    CoinModel,
    PrefetchHooks Function()>;
typedef $$WalletTableTableCreateCompanionBuilder = WalletTableCompanion
    Function({
  Value<int> id,
  Value<String?> deviceId,
  Value<String?> deviceType,
  Value<String?> chipVersion,
  Value<String?> appVersion,
  Value<String?> firmwareVersion,
  Value<int?> batchId,
  Value<String?> walletId,
  Value<String?> walletName,
  Value<bool?> checked,
  Value<bool?> isPassphraseWallet,
  Value<String?> bleMacId,
  Value<int?> seedType,
  Value<int?> walletType,
  Value<String?> monitorTime,
  Value<int?> vipLevel,
  Value<String?> chain,
  Value<String?> address,
  Value<String?> addressLabel,
  Value<String?> nftChain,
  Value<String?> nftAddress,
  Value<String?> nftAddressLabel,
  Value<String?> dappChainList,
  Value<String?> totalAssets,
});
typedef $$WalletTableTableUpdateCompanionBuilder = WalletTableCompanion
    Function({
  Value<int> id,
  Value<String?> deviceId,
  Value<String?> deviceType,
  Value<String?> chipVersion,
  Value<String?> appVersion,
  Value<String?> firmwareVersion,
  Value<int?> batchId,
  Value<String?> walletId,
  Value<String?> walletName,
  Value<bool?> checked,
  Value<bool?> isPassphraseWallet,
  Value<String?> bleMacId,
  Value<int?> seedType,
  Value<int?> walletType,
  Value<String?> monitorTime,
  Value<int?> vipLevel,
  Value<String?> chain,
  Value<String?> address,
  Value<String?> addressLabel,
  Value<String?> nftChain,
  Value<String?> nftAddress,
  Value<String?> nftAddressLabel,
  Value<String?> dappChainList,
  Value<String?> totalAssets,
});

class $$WalletTableTableFilterComposer
    extends Composer<_$AppDatabase, $WalletTableTable> {
  $$WalletTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get deviceId => $composableBuilder(
      column: $table.deviceId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get deviceType => $composableBuilder(
      column: $table.deviceType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chipVersion => $composableBuilder(
      column: $table.chipVersion, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get appVersion => $composableBuilder(
      column: $table.appVersion, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get firmwareVersion => $composableBuilder(
      column: $table.firmwareVersion,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get batchId => $composableBuilder(
      column: $table.batchId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletName => $composableBuilder(
      column: $table.walletName, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get checked => $composableBuilder(
      column: $table.checked, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isPassphraseWallet => $composableBuilder(
      column: $table.isPassphraseWallet,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bleMacId => $composableBuilder(
      column: $table.bleMacId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get seedType => $composableBuilder(
      column: $table.seedType, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get walletType => $composableBuilder(
      column: $table.walletType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get monitorTime => $composableBuilder(
      column: $table.monitorTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get vipLevel => $composableBuilder(
      column: $table.vipLevel, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftChain => $composableBuilder(
      column: $table.nftChain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftAddress => $composableBuilder(
      column: $table.nftAddress, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftAddressLabel => $composableBuilder(
      column: $table.nftAddressLabel,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappChainList => $composableBuilder(
      column: $table.dappChainList, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get totalAssets => $composableBuilder(
      column: $table.totalAssets, builder: (column) => ColumnFilters(column));
}

class $$WalletTableTableOrderingComposer
    extends Composer<_$AppDatabase, $WalletTableTable> {
  $$WalletTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get deviceId => $composableBuilder(
      column: $table.deviceId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get deviceType => $composableBuilder(
      column: $table.deviceType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chipVersion => $composableBuilder(
      column: $table.chipVersion, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get appVersion => $composableBuilder(
      column: $table.appVersion, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get firmwareVersion => $composableBuilder(
      column: $table.firmwareVersion,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get batchId => $composableBuilder(
      column: $table.batchId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletName => $composableBuilder(
      column: $table.walletName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get checked => $composableBuilder(
      column: $table.checked, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isPassphraseWallet => $composableBuilder(
      column: $table.isPassphraseWallet,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bleMacId => $composableBuilder(
      column: $table.bleMacId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get seedType => $composableBuilder(
      column: $table.seedType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get walletType => $composableBuilder(
      column: $table.walletType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get monitorTime => $composableBuilder(
      column: $table.monitorTime, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get vipLevel => $composableBuilder(
      column: $table.vipLevel, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftChain => $composableBuilder(
      column: $table.nftChain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftAddress => $composableBuilder(
      column: $table.nftAddress, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftAddressLabel => $composableBuilder(
      column: $table.nftAddressLabel,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappChainList => $composableBuilder(
      column: $table.dappChainList,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get totalAssets => $composableBuilder(
      column: $table.totalAssets, builder: (column) => ColumnOrderings(column));
}

class $$WalletTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $WalletTableTable> {
  $$WalletTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get deviceId =>
      $composableBuilder(column: $table.deviceId, builder: (column) => column);

  GeneratedColumn<String> get deviceType => $composableBuilder(
      column: $table.deviceType, builder: (column) => column);

  GeneratedColumn<String> get chipVersion => $composableBuilder(
      column: $table.chipVersion, builder: (column) => column);

  GeneratedColumn<String> get appVersion => $composableBuilder(
      column: $table.appVersion, builder: (column) => column);

  GeneratedColumn<String> get firmwareVersion => $composableBuilder(
      column: $table.firmwareVersion, builder: (column) => column);

  GeneratedColumn<int> get batchId =>
      $composableBuilder(column: $table.batchId, builder: (column) => column);

  GeneratedColumn<String> get walletId =>
      $composableBuilder(column: $table.walletId, builder: (column) => column);

  GeneratedColumn<String> get walletName => $composableBuilder(
      column: $table.walletName, builder: (column) => column);

  GeneratedColumn<bool> get checked =>
      $composableBuilder(column: $table.checked, builder: (column) => column);

  GeneratedColumn<bool> get isPassphraseWallet => $composableBuilder(
      column: $table.isPassphraseWallet, builder: (column) => column);

  GeneratedColumn<String> get bleMacId =>
      $composableBuilder(column: $table.bleMacId, builder: (column) => column);

  GeneratedColumn<int> get seedType =>
      $composableBuilder(column: $table.seedType, builder: (column) => column);

  GeneratedColumn<int> get walletType => $composableBuilder(
      column: $table.walletType, builder: (column) => column);

  GeneratedColumn<String> get monitorTime => $composableBuilder(
      column: $table.monitorTime, builder: (column) => column);

  GeneratedColumn<int> get vipLevel =>
      $composableBuilder(column: $table.vipLevel, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get address =>
      $composableBuilder(column: $table.address, builder: (column) => column);

  GeneratedColumn<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel, builder: (column) => column);

  GeneratedColumn<String> get nftChain =>
      $composableBuilder(column: $table.nftChain, builder: (column) => column);

  GeneratedColumn<String> get nftAddress => $composableBuilder(
      column: $table.nftAddress, builder: (column) => column);

  GeneratedColumn<String> get nftAddressLabel => $composableBuilder(
      column: $table.nftAddressLabel, builder: (column) => column);

  GeneratedColumn<String> get dappChainList => $composableBuilder(
      column: $table.dappChainList, builder: (column) => column);

  GeneratedColumn<String> get totalAssets => $composableBuilder(
      column: $table.totalAssets, builder: (column) => column);
}

class $$WalletTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $WalletTableTable,
    WalletModel,
    $$WalletTableTableFilterComposer,
    $$WalletTableTableOrderingComposer,
    $$WalletTableTableAnnotationComposer,
    $$WalletTableTableCreateCompanionBuilder,
    $$WalletTableTableUpdateCompanionBuilder,
    (
      WalletModel,
      BaseReferences<_$AppDatabase, $WalletTableTable, WalletModel>
    ),
    WalletModel,
    PrefetchHooks Function()> {
  $$WalletTableTableTableManager(_$AppDatabase db, $WalletTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$WalletTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$WalletTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$WalletTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> deviceId = const Value.absent(),
            Value<String?> deviceType = const Value.absent(),
            Value<String?> chipVersion = const Value.absent(),
            Value<String?> appVersion = const Value.absent(),
            Value<String?> firmwareVersion = const Value.absent(),
            Value<int?> batchId = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> walletName = const Value.absent(),
            Value<bool?> checked = const Value.absent(),
            Value<bool?> isPassphraseWallet = const Value.absent(),
            Value<String?> bleMacId = const Value.absent(),
            Value<int?> seedType = const Value.absent(),
            Value<int?> walletType = const Value.absent(),
            Value<String?> monitorTime = const Value.absent(),
            Value<int?> vipLevel = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> addressLabel = const Value.absent(),
            Value<String?> nftChain = const Value.absent(),
            Value<String?> nftAddress = const Value.absent(),
            Value<String?> nftAddressLabel = const Value.absent(),
            Value<String?> dappChainList = const Value.absent(),
            Value<String?> totalAssets = const Value.absent(),
          }) =>
              WalletTableCompanion(
            id: id,
            deviceId: deviceId,
            deviceType: deviceType,
            chipVersion: chipVersion,
            appVersion: appVersion,
            firmwareVersion: firmwareVersion,
            batchId: batchId,
            walletId: walletId,
            walletName: walletName,
            checked: checked,
            isPassphraseWallet: isPassphraseWallet,
            bleMacId: bleMacId,
            seedType: seedType,
            walletType: walletType,
            monitorTime: monitorTime,
            vipLevel: vipLevel,
            chain: chain,
            address: address,
            addressLabel: addressLabel,
            nftChain: nftChain,
            nftAddress: nftAddress,
            nftAddressLabel: nftAddressLabel,
            dappChainList: dappChainList,
            totalAssets: totalAssets,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> deviceId = const Value.absent(),
            Value<String?> deviceType = const Value.absent(),
            Value<String?> chipVersion = const Value.absent(),
            Value<String?> appVersion = const Value.absent(),
            Value<String?> firmwareVersion = const Value.absent(),
            Value<int?> batchId = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> walletName = const Value.absent(),
            Value<bool?> checked = const Value.absent(),
            Value<bool?> isPassphraseWallet = const Value.absent(),
            Value<String?> bleMacId = const Value.absent(),
            Value<int?> seedType = const Value.absent(),
            Value<int?> walletType = const Value.absent(),
            Value<String?> monitorTime = const Value.absent(),
            Value<int?> vipLevel = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> addressLabel = const Value.absent(),
            Value<String?> nftChain = const Value.absent(),
            Value<String?> nftAddress = const Value.absent(),
            Value<String?> nftAddressLabel = const Value.absent(),
            Value<String?> dappChainList = const Value.absent(),
            Value<String?> totalAssets = const Value.absent(),
          }) =>
              WalletTableCompanion.insert(
            id: id,
            deviceId: deviceId,
            deviceType: deviceType,
            chipVersion: chipVersion,
            appVersion: appVersion,
            firmwareVersion: firmwareVersion,
            batchId: batchId,
            walletId: walletId,
            walletName: walletName,
            checked: checked,
            isPassphraseWallet: isPassphraseWallet,
            bleMacId: bleMacId,
            seedType: seedType,
            walletType: walletType,
            monitorTime: monitorTime,
            vipLevel: vipLevel,
            chain: chain,
            address: address,
            addressLabel: addressLabel,
            nftChain: nftChain,
            nftAddress: nftAddress,
            nftAddressLabel: nftAddressLabel,
            dappChainList: dappChainList,
            totalAssets: totalAssets,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$WalletTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $WalletTableTable,
    WalletModel,
    $$WalletTableTableFilterComposer,
    $$WalletTableTableOrderingComposer,
    $$WalletTableTableAnnotationComposer,
    $$WalletTableTableCreateCompanionBuilder,
    $$WalletTableTableUpdateCompanionBuilder,
    (
      WalletModel,
      BaseReferences<_$AppDatabase, $WalletTableTable, WalletModel>
    ),
    WalletModel,
    PrefetchHooks Function()>;
typedef $$AddressTableTableCreateCompanionBuilder = AddressTableCompanion
    Function({
  Value<int> id,
  Value<String?> walletId,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<String?> address,
  Value<String?> publickey,
  Value<String?> xpubData,
  Value<String?> path,
  Value<String?> balance,
  Value<int?> sortedId,
  Value<int?> addressIndex,
  Value<String?> addressLabel,
  Value<bool?> isSelected,
  Value<bool?> isUploaded,
  Value<int?> slip44Id,
  Value<int?> segwitType,
  Value<int?> walletType,
  Value<String?> nftAmountCache,
  Value<String?> nftAssetsCache,
  Value<String?> nftCollectionCache,
  Value<String?> nftUsdCache,
  Value<String?> totalBalance,
  Value<String?> availableBalance,
  Value<String?> unavailableBalance,
  Value<String?> eosAccountInfoCache,
  Value<String?> tronAccountInfoCache,
  Value<String?> tronResourceCache,
  Value<String?> utxoCache,
  Value<String?> solMinimumRent,
});
typedef $$AddressTableTableUpdateCompanionBuilder = AddressTableCompanion
    Function({
  Value<int> id,
  Value<String?> walletId,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<String?> address,
  Value<String?> publickey,
  Value<String?> xpubData,
  Value<String?> path,
  Value<String?> balance,
  Value<int?> sortedId,
  Value<int?> addressIndex,
  Value<String?> addressLabel,
  Value<bool?> isSelected,
  Value<bool?> isUploaded,
  Value<int?> slip44Id,
  Value<int?> segwitType,
  Value<int?> walletType,
  Value<String?> nftAmountCache,
  Value<String?> nftAssetsCache,
  Value<String?> nftCollectionCache,
  Value<String?> nftUsdCache,
  Value<String?> totalBalance,
  Value<String?> availableBalance,
  Value<String?> unavailableBalance,
  Value<String?> eosAccountInfoCache,
  Value<String?> tronAccountInfoCache,
  Value<String?> tronResourceCache,
  Value<String?> utxoCache,
  Value<String?> solMinimumRent,
});

class $$AddressTableTableFilterComposer
    extends Composer<_$AppDatabase, $AddressTableTable> {
  $$AddressTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get publickey => $composableBuilder(
      column: $table.publickey, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get xpubData => $composableBuilder(
      column: $table.xpubData, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get path => $composableBuilder(
      column: $table.path, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get balance => $composableBuilder(
      column: $table.balance, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get sortedId => $composableBuilder(
      column: $table.sortedId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get addressIndex => $composableBuilder(
      column: $table.addressIndex, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isSelected => $composableBuilder(
      column: $table.isSelected, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isUploaded => $composableBuilder(
      column: $table.isUploaded, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get slip44Id => $composableBuilder(
      column: $table.slip44Id, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get segwitType => $composableBuilder(
      column: $table.segwitType, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get walletType => $composableBuilder(
      column: $table.walletType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftAmountCache => $composableBuilder(
      column: $table.nftAmountCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftAssetsCache => $composableBuilder(
      column: $table.nftAssetsCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftCollectionCache => $composableBuilder(
      column: $table.nftCollectionCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get nftUsdCache => $composableBuilder(
      column: $table.nftUsdCache, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get totalBalance => $composableBuilder(
      column: $table.totalBalance, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get availableBalance => $composableBuilder(
      column: $table.availableBalance,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get unavailableBalance => $composableBuilder(
      column: $table.unavailableBalance,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get eosAccountInfoCache => $composableBuilder(
      column: $table.eosAccountInfoCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get tronAccountInfoCache => $composableBuilder(
      column: $table.tronAccountInfoCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get tronResourceCache => $composableBuilder(
      column: $table.tronResourceCache,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get utxoCache => $composableBuilder(
      column: $table.utxoCache, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get solMinimumRent => $composableBuilder(
      column: $table.solMinimumRent,
      builder: (column) => ColumnFilters(column));
}

class $$AddressTableTableOrderingComposer
    extends Composer<_$AppDatabase, $AddressTableTable> {
  $$AddressTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get publickey => $composableBuilder(
      column: $table.publickey, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get xpubData => $composableBuilder(
      column: $table.xpubData, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get path => $composableBuilder(
      column: $table.path, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get balance => $composableBuilder(
      column: $table.balance, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get sortedId => $composableBuilder(
      column: $table.sortedId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get addressIndex => $composableBuilder(
      column: $table.addressIndex,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isSelected => $composableBuilder(
      column: $table.isSelected, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isUploaded => $composableBuilder(
      column: $table.isUploaded, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get slip44Id => $composableBuilder(
      column: $table.slip44Id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get segwitType => $composableBuilder(
      column: $table.segwitType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get walletType => $composableBuilder(
      column: $table.walletType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftAmountCache => $composableBuilder(
      column: $table.nftAmountCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftAssetsCache => $composableBuilder(
      column: $table.nftAssetsCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftCollectionCache => $composableBuilder(
      column: $table.nftCollectionCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get nftUsdCache => $composableBuilder(
      column: $table.nftUsdCache, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get totalBalance => $composableBuilder(
      column: $table.totalBalance,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get availableBalance => $composableBuilder(
      column: $table.availableBalance,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get unavailableBalance => $composableBuilder(
      column: $table.unavailableBalance,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get eosAccountInfoCache => $composableBuilder(
      column: $table.eosAccountInfoCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get tronAccountInfoCache => $composableBuilder(
      column: $table.tronAccountInfoCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get tronResourceCache => $composableBuilder(
      column: $table.tronResourceCache,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get utxoCache => $composableBuilder(
      column: $table.utxoCache, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get solMinimumRent => $composableBuilder(
      column: $table.solMinimumRent,
      builder: (column) => ColumnOrderings(column));
}

class $$AddressTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $AddressTableTable> {
  $$AddressTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get walletId =>
      $composableBuilder(column: $table.walletId, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get chainCode =>
      $composableBuilder(column: $table.chainCode, builder: (column) => column);

  GeneratedColumn<String> get address =>
      $composableBuilder(column: $table.address, builder: (column) => column);

  GeneratedColumn<String> get publickey =>
      $composableBuilder(column: $table.publickey, builder: (column) => column);

  GeneratedColumn<String> get xpubData =>
      $composableBuilder(column: $table.xpubData, builder: (column) => column);

  GeneratedColumn<String> get path =>
      $composableBuilder(column: $table.path, builder: (column) => column);

  GeneratedColumn<String> get balance =>
      $composableBuilder(column: $table.balance, builder: (column) => column);

  GeneratedColumn<int> get sortedId =>
      $composableBuilder(column: $table.sortedId, builder: (column) => column);

  GeneratedColumn<int> get addressIndex => $composableBuilder(
      column: $table.addressIndex, builder: (column) => column);

  GeneratedColumn<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel, builder: (column) => column);

  GeneratedColumn<bool> get isSelected => $composableBuilder(
      column: $table.isSelected, builder: (column) => column);

  GeneratedColumn<bool> get isUploaded => $composableBuilder(
      column: $table.isUploaded, builder: (column) => column);

  GeneratedColumn<int> get slip44Id =>
      $composableBuilder(column: $table.slip44Id, builder: (column) => column);

  GeneratedColumn<int> get segwitType => $composableBuilder(
      column: $table.segwitType, builder: (column) => column);

  GeneratedColumn<int> get walletType => $composableBuilder(
      column: $table.walletType, builder: (column) => column);

  GeneratedColumn<String> get nftAmountCache => $composableBuilder(
      column: $table.nftAmountCache, builder: (column) => column);

  GeneratedColumn<String> get nftAssetsCache => $composableBuilder(
      column: $table.nftAssetsCache, builder: (column) => column);

  GeneratedColumn<String> get nftCollectionCache => $composableBuilder(
      column: $table.nftCollectionCache, builder: (column) => column);

  GeneratedColumn<String> get nftUsdCache => $composableBuilder(
      column: $table.nftUsdCache, builder: (column) => column);

  GeneratedColumn<String> get totalBalance => $composableBuilder(
      column: $table.totalBalance, builder: (column) => column);

  GeneratedColumn<String> get availableBalance => $composableBuilder(
      column: $table.availableBalance, builder: (column) => column);

  GeneratedColumn<String> get unavailableBalance => $composableBuilder(
      column: $table.unavailableBalance, builder: (column) => column);

  GeneratedColumn<String> get eosAccountInfoCache => $composableBuilder(
      column: $table.eosAccountInfoCache, builder: (column) => column);

  GeneratedColumn<String> get tronAccountInfoCache => $composableBuilder(
      column: $table.tronAccountInfoCache, builder: (column) => column);

  GeneratedColumn<String> get tronResourceCache => $composableBuilder(
      column: $table.tronResourceCache, builder: (column) => column);

  GeneratedColumn<String> get utxoCache =>
      $composableBuilder(column: $table.utxoCache, builder: (column) => column);

  GeneratedColumn<String> get solMinimumRent => $composableBuilder(
      column: $table.solMinimumRent, builder: (column) => column);
}

class $$AddressTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AddressTableTable,
    AddressModel,
    $$AddressTableTableFilterComposer,
    $$AddressTableTableOrderingComposer,
    $$AddressTableTableAnnotationComposer,
    $$AddressTableTableCreateCompanionBuilder,
    $$AddressTableTableUpdateCompanionBuilder,
    (
      AddressModel,
      BaseReferences<_$AppDatabase, $AddressTableTable, AddressModel>
    ),
    AddressModel,
    PrefetchHooks Function()> {
  $$AddressTableTableTableManager(_$AppDatabase db, $AddressTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AddressTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AddressTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AddressTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> publickey = const Value.absent(),
            Value<String?> xpubData = const Value.absent(),
            Value<String?> path = const Value.absent(),
            Value<String?> balance = const Value.absent(),
            Value<int?> sortedId = const Value.absent(),
            Value<int?> addressIndex = const Value.absent(),
            Value<String?> addressLabel = const Value.absent(),
            Value<bool?> isSelected = const Value.absent(),
            Value<bool?> isUploaded = const Value.absent(),
            Value<int?> slip44Id = const Value.absent(),
            Value<int?> segwitType = const Value.absent(),
            Value<int?> walletType = const Value.absent(),
            Value<String?> nftAmountCache = const Value.absent(),
            Value<String?> nftAssetsCache = const Value.absent(),
            Value<String?> nftCollectionCache = const Value.absent(),
            Value<String?> nftUsdCache = const Value.absent(),
            Value<String?> totalBalance = const Value.absent(),
            Value<String?> availableBalance = const Value.absent(),
            Value<String?> unavailableBalance = const Value.absent(),
            Value<String?> eosAccountInfoCache = const Value.absent(),
            Value<String?> tronAccountInfoCache = const Value.absent(),
            Value<String?> tronResourceCache = const Value.absent(),
            Value<String?> utxoCache = const Value.absent(),
            Value<String?> solMinimumRent = const Value.absent(),
          }) =>
              AddressTableCompanion(
            id: id,
            walletId: walletId,
            chain: chain,
            chainCode: chainCode,
            address: address,
            publickey: publickey,
            xpubData: xpubData,
            path: path,
            balance: balance,
            sortedId: sortedId,
            addressIndex: addressIndex,
            addressLabel: addressLabel,
            isSelected: isSelected,
            isUploaded: isUploaded,
            slip44Id: slip44Id,
            segwitType: segwitType,
            walletType: walletType,
            nftAmountCache: nftAmountCache,
            nftAssetsCache: nftAssetsCache,
            nftCollectionCache: nftCollectionCache,
            nftUsdCache: nftUsdCache,
            totalBalance: totalBalance,
            availableBalance: availableBalance,
            unavailableBalance: unavailableBalance,
            eosAccountInfoCache: eosAccountInfoCache,
            tronAccountInfoCache: tronAccountInfoCache,
            tronResourceCache: tronResourceCache,
            utxoCache: utxoCache,
            solMinimumRent: solMinimumRent,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> publickey = const Value.absent(),
            Value<String?> xpubData = const Value.absent(),
            Value<String?> path = const Value.absent(),
            Value<String?> balance = const Value.absent(),
            Value<int?> sortedId = const Value.absent(),
            Value<int?> addressIndex = const Value.absent(),
            Value<String?> addressLabel = const Value.absent(),
            Value<bool?> isSelected = const Value.absent(),
            Value<bool?> isUploaded = const Value.absent(),
            Value<int?> slip44Id = const Value.absent(),
            Value<int?> segwitType = const Value.absent(),
            Value<int?> walletType = const Value.absent(),
            Value<String?> nftAmountCache = const Value.absent(),
            Value<String?> nftAssetsCache = const Value.absent(),
            Value<String?> nftCollectionCache = const Value.absent(),
            Value<String?> nftUsdCache = const Value.absent(),
            Value<String?> totalBalance = const Value.absent(),
            Value<String?> availableBalance = const Value.absent(),
            Value<String?> unavailableBalance = const Value.absent(),
            Value<String?> eosAccountInfoCache = const Value.absent(),
            Value<String?> tronAccountInfoCache = const Value.absent(),
            Value<String?> tronResourceCache = const Value.absent(),
            Value<String?> utxoCache = const Value.absent(),
            Value<String?> solMinimumRent = const Value.absent(),
          }) =>
              AddressTableCompanion.insert(
            id: id,
            walletId: walletId,
            chain: chain,
            chainCode: chainCode,
            address: address,
            publickey: publickey,
            xpubData: xpubData,
            path: path,
            balance: balance,
            sortedId: sortedId,
            addressIndex: addressIndex,
            addressLabel: addressLabel,
            isSelected: isSelected,
            isUploaded: isUploaded,
            slip44Id: slip44Id,
            segwitType: segwitType,
            walletType: walletType,
            nftAmountCache: nftAmountCache,
            nftAssetsCache: nftAssetsCache,
            nftCollectionCache: nftCollectionCache,
            nftUsdCache: nftUsdCache,
            totalBalance: totalBalance,
            availableBalance: availableBalance,
            unavailableBalance: unavailableBalance,
            eosAccountInfoCache: eosAccountInfoCache,
            tronAccountInfoCache: tronAccountInfoCache,
            tronResourceCache: tronResourceCache,
            utxoCache: utxoCache,
            solMinimumRent: solMinimumRent,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AddressTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AddressTableTable,
    AddressModel,
    $$AddressTableTableFilterComposer,
    $$AddressTableTableOrderingComposer,
    $$AddressTableTableAnnotationComposer,
    $$AddressTableTableCreateCompanionBuilder,
    $$AddressTableTableUpdateCompanionBuilder,
    (
      AddressModel,
      BaseReferences<_$AppDatabase, $AddressTableTable, AddressModel>
    ),
    AddressModel,
    PrefetchHooks Function()>;
typedef $$TokenTableTableCreateCompanionBuilder = TokenTableCompanion Function({
  Value<int> id,
  Value<String?> walletId,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<String?> address,
  Value<String?> contract,
  Value<String?> balance,
  Value<String?> addressLabel,
  Value<int?> tokenType,
  Value<String?> derivedAddresses,
});
typedef $$TokenTableTableUpdateCompanionBuilder = TokenTableCompanion Function({
  Value<int> id,
  Value<String?> walletId,
  Value<String?> chain,
  Value<String?> chainCode,
  Value<String?> address,
  Value<String?> contract,
  Value<String?> balance,
  Value<String?> addressLabel,
  Value<int?> tokenType,
  Value<String?> derivedAddresses,
});

class $$TokenTableTableFilterComposer
    extends Composer<_$AppDatabase, $TokenTableTable> {
  $$TokenTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get contract => $composableBuilder(
      column: $table.contract, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get balance => $composableBuilder(
      column: $table.balance, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get tokenType => $composableBuilder(
      column: $table.tokenType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get derivedAddresses => $composableBuilder(
      column: $table.derivedAddresses,
      builder: (column) => ColumnFilters(column));
}

class $$TokenTableTableOrderingComposer
    extends Composer<_$AppDatabase, $TokenTableTable> {
  $$TokenTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get contract => $composableBuilder(
      column: $table.contract, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get balance => $composableBuilder(
      column: $table.balance, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get tokenType => $composableBuilder(
      column: $table.tokenType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get derivedAddresses => $composableBuilder(
      column: $table.derivedAddresses,
      builder: (column) => ColumnOrderings(column));
}

class $$TokenTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $TokenTableTable> {
  $$TokenTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get walletId =>
      $composableBuilder(column: $table.walletId, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get chainCode =>
      $composableBuilder(column: $table.chainCode, builder: (column) => column);

  GeneratedColumn<String> get address =>
      $composableBuilder(column: $table.address, builder: (column) => column);

  GeneratedColumn<String> get contract =>
      $composableBuilder(column: $table.contract, builder: (column) => column);

  GeneratedColumn<String> get balance =>
      $composableBuilder(column: $table.balance, builder: (column) => column);

  GeneratedColumn<String> get addressLabel => $composableBuilder(
      column: $table.addressLabel, builder: (column) => column);

  GeneratedColumn<int> get tokenType =>
      $composableBuilder(column: $table.tokenType, builder: (column) => column);

  GeneratedColumn<String> get derivedAddresses => $composableBuilder(
      column: $table.derivedAddresses, builder: (column) => column);
}

class $$TokenTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TokenTableTable,
    TokenModel,
    $$TokenTableTableFilterComposer,
    $$TokenTableTableOrderingComposer,
    $$TokenTableTableAnnotationComposer,
    $$TokenTableTableCreateCompanionBuilder,
    $$TokenTableTableUpdateCompanionBuilder,
    (TokenModel, BaseReferences<_$AppDatabase, $TokenTableTable, TokenModel>),
    TokenModel,
    PrefetchHooks Function()> {
  $$TokenTableTableTableManager(_$AppDatabase db, $TokenTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$TokenTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$TokenTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$TokenTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> contract = const Value.absent(),
            Value<String?> balance = const Value.absent(),
            Value<String?> addressLabel = const Value.absent(),
            Value<int?> tokenType = const Value.absent(),
            Value<String?> derivedAddresses = const Value.absent(),
          }) =>
              TokenTableCompanion(
            id: id,
            walletId: walletId,
            chain: chain,
            chainCode: chainCode,
            address: address,
            contract: contract,
            balance: balance,
            addressLabel: addressLabel,
            tokenType: tokenType,
            derivedAddresses: derivedAddresses,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> contract = const Value.absent(),
            Value<String?> balance = const Value.absent(),
            Value<String?> addressLabel = const Value.absent(),
            Value<int?> tokenType = const Value.absent(),
            Value<String?> derivedAddresses = const Value.absent(),
          }) =>
              TokenTableCompanion.insert(
            id: id,
            walletId: walletId,
            chain: chain,
            chainCode: chainCode,
            address: address,
            contract: contract,
            balance: balance,
            addressLabel: addressLabel,
            tokenType: tokenType,
            derivedAddresses: derivedAddresses,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TokenTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $TokenTableTable,
    TokenModel,
    $$TokenTableTableFilterComposer,
    $$TokenTableTableOrderingComposer,
    $$TokenTableTableAnnotationComposer,
    $$TokenTableTableCreateCompanionBuilder,
    $$TokenTableTableUpdateCompanionBuilder,
    (TokenModel, BaseReferences<_$AppDatabase, $TokenTableTable, TokenModel>),
    TokenModel,
    PrefetchHooks Function()>;
typedef $$DappTableTableCreateCompanionBuilder = DappTableCompanion Function({
  Value<int> id,
  Value<String?> chain,
  Value<String?> dappId,
  Value<String?> dappName,
  Value<String?> dappLabel,
  Value<String?> dappUrl,
  Value<String?> dappInfo,
  Value<String?> symbol,
  Value<String?> dappLogoUrl,
  Value<String?> btcDappJs,
  Value<String?> netWork,
  Value<String?> slip44,
  Value<int?> dappMode,
  Value<DateTime?> lastTime,
  Value<int?> sortIndex,
  Value<bool?> isApprove,
  Value<bool?> isBan,
});
typedef $$DappTableTableUpdateCompanionBuilder = DappTableCompanion Function({
  Value<int> id,
  Value<String?> chain,
  Value<String?> dappId,
  Value<String?> dappName,
  Value<String?> dappLabel,
  Value<String?> dappUrl,
  Value<String?> dappInfo,
  Value<String?> symbol,
  Value<String?> dappLogoUrl,
  Value<String?> btcDappJs,
  Value<String?> netWork,
  Value<String?> slip44,
  Value<int?> dappMode,
  Value<DateTime?> lastTime,
  Value<int?> sortIndex,
  Value<bool?> isApprove,
  Value<bool?> isBan,
});

class $$DappTableTableFilterComposer
    extends Composer<_$AppDatabase, $DappTableTable> {
  $$DappTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappId => $composableBuilder(
      column: $table.dappId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappName => $composableBuilder(
      column: $table.dappName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappLabel => $composableBuilder(
      column: $table.dappLabel, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappUrl => $composableBuilder(
      column: $table.dappUrl, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappInfo => $composableBuilder(
      column: $table.dappInfo, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get symbol => $composableBuilder(
      column: $table.symbol, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get dappLogoUrl => $composableBuilder(
      column: $table.dappLogoUrl, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get btcDappJs => $composableBuilder(
      column: $table.btcDappJs, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get netWork => $composableBuilder(
      column: $table.netWork, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get slip44 => $composableBuilder(
      column: $table.slip44, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get dappMode => $composableBuilder(
      column: $table.dappMode, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastTime => $composableBuilder(
      column: $table.lastTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get sortIndex => $composableBuilder(
      column: $table.sortIndex, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isApprove => $composableBuilder(
      column: $table.isApprove, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isBan => $composableBuilder(
      column: $table.isBan, builder: (column) => ColumnFilters(column));
}

class $$DappTableTableOrderingComposer
    extends Composer<_$AppDatabase, $DappTableTable> {
  $$DappTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappId => $composableBuilder(
      column: $table.dappId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappName => $composableBuilder(
      column: $table.dappName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappLabel => $composableBuilder(
      column: $table.dappLabel, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappUrl => $composableBuilder(
      column: $table.dappUrl, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappInfo => $composableBuilder(
      column: $table.dappInfo, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get symbol => $composableBuilder(
      column: $table.symbol, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get dappLogoUrl => $composableBuilder(
      column: $table.dappLogoUrl, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get btcDappJs => $composableBuilder(
      column: $table.btcDappJs, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get netWork => $composableBuilder(
      column: $table.netWork, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get slip44 => $composableBuilder(
      column: $table.slip44, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get dappMode => $composableBuilder(
      column: $table.dappMode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastTime => $composableBuilder(
      column: $table.lastTime, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get sortIndex => $composableBuilder(
      column: $table.sortIndex, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isApprove => $composableBuilder(
      column: $table.isApprove, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isBan => $composableBuilder(
      column: $table.isBan, builder: (column) => ColumnOrderings(column));
}

class $$DappTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $DappTableTable> {
  $$DappTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get dappId =>
      $composableBuilder(column: $table.dappId, builder: (column) => column);

  GeneratedColumn<String> get dappName =>
      $composableBuilder(column: $table.dappName, builder: (column) => column);

  GeneratedColumn<String> get dappLabel =>
      $composableBuilder(column: $table.dappLabel, builder: (column) => column);

  GeneratedColumn<String> get dappUrl =>
      $composableBuilder(column: $table.dappUrl, builder: (column) => column);

  GeneratedColumn<String> get dappInfo =>
      $composableBuilder(column: $table.dappInfo, builder: (column) => column);

  GeneratedColumn<String> get symbol =>
      $composableBuilder(column: $table.symbol, builder: (column) => column);

  GeneratedColumn<String> get dappLogoUrl => $composableBuilder(
      column: $table.dappLogoUrl, builder: (column) => column);

  GeneratedColumn<String> get btcDappJs =>
      $composableBuilder(column: $table.btcDappJs, builder: (column) => column);

  GeneratedColumn<String> get netWork =>
      $composableBuilder(column: $table.netWork, builder: (column) => column);

  GeneratedColumn<String> get slip44 =>
      $composableBuilder(column: $table.slip44, builder: (column) => column);

  GeneratedColumn<int> get dappMode =>
      $composableBuilder(column: $table.dappMode, builder: (column) => column);

  GeneratedColumn<DateTime> get lastTime =>
      $composableBuilder(column: $table.lastTime, builder: (column) => column);

  GeneratedColumn<int> get sortIndex =>
      $composableBuilder(column: $table.sortIndex, builder: (column) => column);

  GeneratedColumn<bool> get isApprove =>
      $composableBuilder(column: $table.isApprove, builder: (column) => column);

  GeneratedColumn<bool> get isBan =>
      $composableBuilder(column: $table.isBan, builder: (column) => column);
}

class $$DappTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $DappTableTable,
    DappModel,
    $$DappTableTableFilterComposer,
    $$DappTableTableOrderingComposer,
    $$DappTableTableAnnotationComposer,
    $$DappTableTableCreateCompanionBuilder,
    $$DappTableTableUpdateCompanionBuilder,
    (DappModel, BaseReferences<_$AppDatabase, $DappTableTable, DappModel>),
    DappModel,
    PrefetchHooks Function()> {
  $$DappTableTableTableManager(_$AppDatabase db, $DappTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$DappTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$DappTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$DappTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> dappId = const Value.absent(),
            Value<String?> dappName = const Value.absent(),
            Value<String?> dappLabel = const Value.absent(),
            Value<String?> dappUrl = const Value.absent(),
            Value<String?> dappInfo = const Value.absent(),
            Value<String?> symbol = const Value.absent(),
            Value<String?> dappLogoUrl = const Value.absent(),
            Value<String?> btcDappJs = const Value.absent(),
            Value<String?> netWork = const Value.absent(),
            Value<String?> slip44 = const Value.absent(),
            Value<int?> dappMode = const Value.absent(),
            Value<DateTime?> lastTime = const Value.absent(),
            Value<int?> sortIndex = const Value.absent(),
            Value<bool?> isApprove = const Value.absent(),
            Value<bool?> isBan = const Value.absent(),
          }) =>
              DappTableCompanion(
            id: id,
            chain: chain,
            dappId: dappId,
            dappName: dappName,
            dappLabel: dappLabel,
            dappUrl: dappUrl,
            dappInfo: dappInfo,
            symbol: symbol,
            dappLogoUrl: dappLogoUrl,
            btcDappJs: btcDappJs,
            netWork: netWork,
            slip44: slip44,
            dappMode: dappMode,
            lastTime: lastTime,
            sortIndex: sortIndex,
            isApprove: isApprove,
            isBan: isBan,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> dappId = const Value.absent(),
            Value<String?> dappName = const Value.absent(),
            Value<String?> dappLabel = const Value.absent(),
            Value<String?> dappUrl = const Value.absent(),
            Value<String?> dappInfo = const Value.absent(),
            Value<String?> symbol = const Value.absent(),
            Value<String?> dappLogoUrl = const Value.absent(),
            Value<String?> btcDappJs = const Value.absent(),
            Value<String?> netWork = const Value.absent(),
            Value<String?> slip44 = const Value.absent(),
            Value<int?> dappMode = const Value.absent(),
            Value<DateTime?> lastTime = const Value.absent(),
            Value<int?> sortIndex = const Value.absent(),
            Value<bool?> isApprove = const Value.absent(),
            Value<bool?> isBan = const Value.absent(),
          }) =>
              DappTableCompanion.insert(
            id: id,
            chain: chain,
            dappId: dappId,
            dappName: dappName,
            dappLabel: dappLabel,
            dappUrl: dappUrl,
            dappInfo: dappInfo,
            symbol: symbol,
            dappLogoUrl: dappLogoUrl,
            btcDappJs: btcDappJs,
            netWork: netWork,
            slip44: slip44,
            dappMode: dappMode,
            lastTime: lastTime,
            sortIndex: sortIndex,
            isApprove: isApprove,
            isBan: isBan,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$DappTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $DappTableTable,
    DappModel,
    $$DappTableTableFilterComposer,
    $$DappTableTableOrderingComposer,
    $$DappTableTableAnnotationComposer,
    $$DappTableTableCreateCompanionBuilder,
    $$DappTableTableUpdateCompanionBuilder,
    (DappModel, BaseReferences<_$AppDatabase, $DappTableTable, DappModel>),
    DappModel,
    PrefetchHooks Function()>;
typedef $$TransactionsActivityTableTableCreateCompanionBuilder
    = TransactionsActivityTableCompanion Function({
  Value<int> id,
  Value<String?> txId,
  Value<int?> txTime,
  Value<String?> action,
  Value<String?> type,
  Value<String?> walletId,
  Value<String?> chain,
  Value<String?> contract,
  Value<String?> chainCode,
  Value<bool?> isToken,
  Value<String?> coinName,
  Value<String?> symbol,
  Value<String?> address,
  Value<String?> rawTx,
  Value<int?> blockNumber,
  Value<int?> blockTime,
  Value<String?> amount,
  Value<String?> fee,
  Value<String?> memo,
  Value<String?> fromAddress,
  Value<String?> receiveAddress,
  Value<int?> nonce,
  Value<String?> gasPrice,
  Value<String?> gasLimit,
  Value<String?> gasUsed,
  Value<int?> xrpLedger,
  Value<int?> xrpSequence,
  Value<String?> txSource,
  Value<String?> indata,
  Value<String?> outData,
  Value<String?> txSequence,
  Value<String?> txLedger,
  Value<int?> txStatus,
  Value<int?> dataSize,
  Value<bool?> isPacking,
  Value<String?> extra,
  Value<bool?> valid,
  Value<String?> detailsCache,
});
typedef $$TransactionsActivityTableTableUpdateCompanionBuilder
    = TransactionsActivityTableCompanion Function({
  Value<int> id,
  Value<String?> txId,
  Value<int?> txTime,
  Value<String?> action,
  Value<String?> type,
  Value<String?> walletId,
  Value<String?> chain,
  Value<String?> contract,
  Value<String?> chainCode,
  Value<bool?> isToken,
  Value<String?> coinName,
  Value<String?> symbol,
  Value<String?> address,
  Value<String?> rawTx,
  Value<int?> blockNumber,
  Value<int?> blockTime,
  Value<String?> amount,
  Value<String?> fee,
  Value<String?> memo,
  Value<String?> fromAddress,
  Value<String?> receiveAddress,
  Value<int?> nonce,
  Value<String?> gasPrice,
  Value<String?> gasLimit,
  Value<String?> gasUsed,
  Value<int?> xrpLedger,
  Value<int?> xrpSequence,
  Value<String?> txSource,
  Value<String?> indata,
  Value<String?> outData,
  Value<String?> txSequence,
  Value<String?> txLedger,
  Value<int?> txStatus,
  Value<int?> dataSize,
  Value<bool?> isPacking,
  Value<String?> extra,
  Value<bool?> valid,
  Value<String?> detailsCache,
});

class $$TransactionsActivityTableTableFilterComposer
    extends Composer<_$AppDatabase, $TransactionsActivityTableTable> {
  $$TransactionsActivityTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get txId => $composableBuilder(
      column: $table.txId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get txTime => $composableBuilder(
      column: $table.txTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get action => $composableBuilder(
      column: $table.action, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get contract => $composableBuilder(
      column: $table.contract, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isToken => $composableBuilder(
      column: $table.isToken, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get coinName => $composableBuilder(
      column: $table.coinName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get symbol => $composableBuilder(
      column: $table.symbol, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get rawTx => $composableBuilder(
      column: $table.rawTx, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get blockNumber => $composableBuilder(
      column: $table.blockNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get blockTime => $composableBuilder(
      column: $table.blockTime, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get fee => $composableBuilder(
      column: $table.fee, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get memo => $composableBuilder(
      column: $table.memo, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get fromAddress => $composableBuilder(
      column: $table.fromAddress, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get receiveAddress => $composableBuilder(
      column: $table.receiveAddress,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get nonce => $composableBuilder(
      column: $table.nonce, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get gasPrice => $composableBuilder(
      column: $table.gasPrice, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get gasLimit => $composableBuilder(
      column: $table.gasLimit, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get gasUsed => $composableBuilder(
      column: $table.gasUsed, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get xrpLedger => $composableBuilder(
      column: $table.xrpLedger, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get xrpSequence => $composableBuilder(
      column: $table.xrpSequence, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get txSource => $composableBuilder(
      column: $table.txSource, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get indata => $composableBuilder(
      column: $table.indata, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get outData => $composableBuilder(
      column: $table.outData, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get txSequence => $composableBuilder(
      column: $table.txSequence, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get txLedger => $composableBuilder(
      column: $table.txLedger, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get txStatus => $composableBuilder(
      column: $table.txStatus, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get dataSize => $composableBuilder(
      column: $table.dataSize, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isPacking => $composableBuilder(
      column: $table.isPacking, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get extra => $composableBuilder(
      column: $table.extra, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get valid => $composableBuilder(
      column: $table.valid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get detailsCache => $composableBuilder(
      column: $table.detailsCache, builder: (column) => ColumnFilters(column));
}

class $$TransactionsActivityTableTableOrderingComposer
    extends Composer<_$AppDatabase, $TransactionsActivityTableTable> {
  $$TransactionsActivityTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get txId => $composableBuilder(
      column: $table.txId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get txTime => $composableBuilder(
      column: $table.txTime, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get action => $composableBuilder(
      column: $table.action, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get contract => $composableBuilder(
      column: $table.contract, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chainCode => $composableBuilder(
      column: $table.chainCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isToken => $composableBuilder(
      column: $table.isToken, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get coinName => $composableBuilder(
      column: $table.coinName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get symbol => $composableBuilder(
      column: $table.symbol, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get rawTx => $composableBuilder(
      column: $table.rawTx, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get blockNumber => $composableBuilder(
      column: $table.blockNumber, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get blockTime => $composableBuilder(
      column: $table.blockTime, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get fee => $composableBuilder(
      column: $table.fee, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get memo => $composableBuilder(
      column: $table.memo, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get fromAddress => $composableBuilder(
      column: $table.fromAddress, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get receiveAddress => $composableBuilder(
      column: $table.receiveAddress,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get nonce => $composableBuilder(
      column: $table.nonce, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get gasPrice => $composableBuilder(
      column: $table.gasPrice, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get gasLimit => $composableBuilder(
      column: $table.gasLimit, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get gasUsed => $composableBuilder(
      column: $table.gasUsed, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get xrpLedger => $composableBuilder(
      column: $table.xrpLedger, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get xrpSequence => $composableBuilder(
      column: $table.xrpSequence, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get txSource => $composableBuilder(
      column: $table.txSource, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get indata => $composableBuilder(
      column: $table.indata, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get outData => $composableBuilder(
      column: $table.outData, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get txSequence => $composableBuilder(
      column: $table.txSequence, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get txLedger => $composableBuilder(
      column: $table.txLedger, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get txStatus => $composableBuilder(
      column: $table.txStatus, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get dataSize => $composableBuilder(
      column: $table.dataSize, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isPacking => $composableBuilder(
      column: $table.isPacking, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get extra => $composableBuilder(
      column: $table.extra, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get valid => $composableBuilder(
      column: $table.valid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get detailsCache => $composableBuilder(
      column: $table.detailsCache,
      builder: (column) => ColumnOrderings(column));
}

class $$TransactionsActivityTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $TransactionsActivityTableTable> {
  $$TransactionsActivityTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get txId =>
      $composableBuilder(column: $table.txId, builder: (column) => column);

  GeneratedColumn<int> get txTime =>
      $composableBuilder(column: $table.txTime, builder: (column) => column);

  GeneratedColumn<String> get action =>
      $composableBuilder(column: $table.action, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<String> get walletId =>
      $composableBuilder(column: $table.walletId, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get contract =>
      $composableBuilder(column: $table.contract, builder: (column) => column);

  GeneratedColumn<String> get chainCode =>
      $composableBuilder(column: $table.chainCode, builder: (column) => column);

  GeneratedColumn<bool> get isToken =>
      $composableBuilder(column: $table.isToken, builder: (column) => column);

  GeneratedColumn<String> get coinName =>
      $composableBuilder(column: $table.coinName, builder: (column) => column);

  GeneratedColumn<String> get symbol =>
      $composableBuilder(column: $table.symbol, builder: (column) => column);

  GeneratedColumn<String> get address =>
      $composableBuilder(column: $table.address, builder: (column) => column);

  GeneratedColumn<String> get rawTx =>
      $composableBuilder(column: $table.rawTx, builder: (column) => column);

  GeneratedColumn<int> get blockNumber => $composableBuilder(
      column: $table.blockNumber, builder: (column) => column);

  GeneratedColumn<int> get blockTime =>
      $composableBuilder(column: $table.blockTime, builder: (column) => column);

  GeneratedColumn<String> get amount =>
      $composableBuilder(column: $table.amount, builder: (column) => column);

  GeneratedColumn<String> get fee =>
      $composableBuilder(column: $table.fee, builder: (column) => column);

  GeneratedColumn<String> get memo =>
      $composableBuilder(column: $table.memo, builder: (column) => column);

  GeneratedColumn<String> get fromAddress => $composableBuilder(
      column: $table.fromAddress, builder: (column) => column);

  GeneratedColumn<String> get receiveAddress => $composableBuilder(
      column: $table.receiveAddress, builder: (column) => column);

  GeneratedColumn<int> get nonce =>
      $composableBuilder(column: $table.nonce, builder: (column) => column);

  GeneratedColumn<String> get gasPrice =>
      $composableBuilder(column: $table.gasPrice, builder: (column) => column);

  GeneratedColumn<String> get gasLimit =>
      $composableBuilder(column: $table.gasLimit, builder: (column) => column);

  GeneratedColumn<String> get gasUsed =>
      $composableBuilder(column: $table.gasUsed, builder: (column) => column);

  GeneratedColumn<int> get xrpLedger =>
      $composableBuilder(column: $table.xrpLedger, builder: (column) => column);

  GeneratedColumn<int> get xrpSequence => $composableBuilder(
      column: $table.xrpSequence, builder: (column) => column);

  GeneratedColumn<String> get txSource =>
      $composableBuilder(column: $table.txSource, builder: (column) => column);

  GeneratedColumn<String> get indata =>
      $composableBuilder(column: $table.indata, builder: (column) => column);

  GeneratedColumn<String> get outData =>
      $composableBuilder(column: $table.outData, builder: (column) => column);

  GeneratedColumn<String> get txSequence => $composableBuilder(
      column: $table.txSequence, builder: (column) => column);

  GeneratedColumn<String> get txLedger =>
      $composableBuilder(column: $table.txLedger, builder: (column) => column);

  GeneratedColumn<int> get txStatus =>
      $composableBuilder(column: $table.txStatus, builder: (column) => column);

  GeneratedColumn<int> get dataSize =>
      $composableBuilder(column: $table.dataSize, builder: (column) => column);

  GeneratedColumn<bool> get isPacking =>
      $composableBuilder(column: $table.isPacking, builder: (column) => column);

  GeneratedColumn<String> get extra =>
      $composableBuilder(column: $table.extra, builder: (column) => column);

  GeneratedColumn<bool> get valid =>
      $composableBuilder(column: $table.valid, builder: (column) => column);

  GeneratedColumn<String> get detailsCache => $composableBuilder(
      column: $table.detailsCache, builder: (column) => column);
}

class $$TransactionsActivityTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $TransactionsActivityTableTable,
    TransactionsActivityModel,
    $$TransactionsActivityTableTableFilterComposer,
    $$TransactionsActivityTableTableOrderingComposer,
    $$TransactionsActivityTableTableAnnotationComposer,
    $$TransactionsActivityTableTableCreateCompanionBuilder,
    $$TransactionsActivityTableTableUpdateCompanionBuilder,
    (
      TransactionsActivityModel,
      BaseReferences<_$AppDatabase, $TransactionsActivityTableTable,
          TransactionsActivityModel>
    ),
    TransactionsActivityModel,
    PrefetchHooks Function()> {
  $$TransactionsActivityTableTableTableManager(
      _$AppDatabase db, $TransactionsActivityTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$TransactionsActivityTableTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$TransactionsActivityTableTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$TransactionsActivityTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> txId = const Value.absent(),
            Value<int?> txTime = const Value.absent(),
            Value<String?> action = const Value.absent(),
            Value<String?> type = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> contract = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<bool?> isToken = const Value.absent(),
            Value<String?> coinName = const Value.absent(),
            Value<String?> symbol = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> rawTx = const Value.absent(),
            Value<int?> blockNumber = const Value.absent(),
            Value<int?> blockTime = const Value.absent(),
            Value<String?> amount = const Value.absent(),
            Value<String?> fee = const Value.absent(),
            Value<String?> memo = const Value.absent(),
            Value<String?> fromAddress = const Value.absent(),
            Value<String?> receiveAddress = const Value.absent(),
            Value<int?> nonce = const Value.absent(),
            Value<String?> gasPrice = const Value.absent(),
            Value<String?> gasLimit = const Value.absent(),
            Value<String?> gasUsed = const Value.absent(),
            Value<int?> xrpLedger = const Value.absent(),
            Value<int?> xrpSequence = const Value.absent(),
            Value<String?> txSource = const Value.absent(),
            Value<String?> indata = const Value.absent(),
            Value<String?> outData = const Value.absent(),
            Value<String?> txSequence = const Value.absent(),
            Value<String?> txLedger = const Value.absent(),
            Value<int?> txStatus = const Value.absent(),
            Value<int?> dataSize = const Value.absent(),
            Value<bool?> isPacking = const Value.absent(),
            Value<String?> extra = const Value.absent(),
            Value<bool?> valid = const Value.absent(),
            Value<String?> detailsCache = const Value.absent(),
          }) =>
              TransactionsActivityTableCompanion(
            id: id,
            txId: txId,
            txTime: txTime,
            action: action,
            type: type,
            walletId: walletId,
            chain: chain,
            contract: contract,
            chainCode: chainCode,
            isToken: isToken,
            coinName: coinName,
            symbol: symbol,
            address: address,
            rawTx: rawTx,
            blockNumber: blockNumber,
            blockTime: blockTime,
            amount: amount,
            fee: fee,
            memo: memo,
            fromAddress: fromAddress,
            receiveAddress: receiveAddress,
            nonce: nonce,
            gasPrice: gasPrice,
            gasLimit: gasLimit,
            gasUsed: gasUsed,
            xrpLedger: xrpLedger,
            xrpSequence: xrpSequence,
            txSource: txSource,
            indata: indata,
            outData: outData,
            txSequence: txSequence,
            txLedger: txLedger,
            txStatus: txStatus,
            dataSize: dataSize,
            isPacking: isPacking,
            extra: extra,
            valid: valid,
            detailsCache: detailsCache,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> txId = const Value.absent(),
            Value<int?> txTime = const Value.absent(),
            Value<String?> action = const Value.absent(),
            Value<String?> type = const Value.absent(),
            Value<String?> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> contract = const Value.absent(),
            Value<String?> chainCode = const Value.absent(),
            Value<bool?> isToken = const Value.absent(),
            Value<String?> coinName = const Value.absent(),
            Value<String?> symbol = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<String?> rawTx = const Value.absent(),
            Value<int?> blockNumber = const Value.absent(),
            Value<int?> blockTime = const Value.absent(),
            Value<String?> amount = const Value.absent(),
            Value<String?> fee = const Value.absent(),
            Value<String?> memo = const Value.absent(),
            Value<String?> fromAddress = const Value.absent(),
            Value<String?> receiveAddress = const Value.absent(),
            Value<int?> nonce = const Value.absent(),
            Value<String?> gasPrice = const Value.absent(),
            Value<String?> gasLimit = const Value.absent(),
            Value<String?> gasUsed = const Value.absent(),
            Value<int?> xrpLedger = const Value.absent(),
            Value<int?> xrpSequence = const Value.absent(),
            Value<String?> txSource = const Value.absent(),
            Value<String?> indata = const Value.absent(),
            Value<String?> outData = const Value.absent(),
            Value<String?> txSequence = const Value.absent(),
            Value<String?> txLedger = const Value.absent(),
            Value<int?> txStatus = const Value.absent(),
            Value<int?> dataSize = const Value.absent(),
            Value<bool?> isPacking = const Value.absent(),
            Value<String?> extra = const Value.absent(),
            Value<bool?> valid = const Value.absent(),
            Value<String?> detailsCache = const Value.absent(),
          }) =>
              TransactionsActivityTableCompanion.insert(
            id: id,
            txId: txId,
            txTime: txTime,
            action: action,
            type: type,
            walletId: walletId,
            chain: chain,
            contract: contract,
            chainCode: chainCode,
            isToken: isToken,
            coinName: coinName,
            symbol: symbol,
            address: address,
            rawTx: rawTx,
            blockNumber: blockNumber,
            blockTime: blockTime,
            amount: amount,
            fee: fee,
            memo: memo,
            fromAddress: fromAddress,
            receiveAddress: receiveAddress,
            nonce: nonce,
            gasPrice: gasPrice,
            gasLimit: gasLimit,
            gasUsed: gasUsed,
            xrpLedger: xrpLedger,
            xrpSequence: xrpSequence,
            txSource: txSource,
            indata: indata,
            outData: outData,
            txSequence: txSequence,
            txLedger: txLedger,
            txStatus: txStatus,
            dataSize: dataSize,
            isPacking: isPacking,
            extra: extra,
            valid: valid,
            detailsCache: detailsCache,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$TransactionsActivityTableTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $TransactionsActivityTableTable,
        TransactionsActivityModel,
        $$TransactionsActivityTableTableFilterComposer,
        $$TransactionsActivityTableTableOrderingComposer,
        $$TransactionsActivityTableTableAnnotationComposer,
        $$TransactionsActivityTableTableCreateCompanionBuilder,
        $$TransactionsActivityTableTableUpdateCompanionBuilder,
        (
          TransactionsActivityModel,
          BaseReferences<_$AppDatabase, $TransactionsActivityTableTable,
              TransactionsActivityModel>
        ),
        TransactionsActivityModel,
        PrefetchHooks Function()>;
typedef $$AddressMappingTableTableCreateCompanionBuilder
    = AddressMappingTableCompanion Function({
  Value<int> id,
  required String deviceId,
  required String walletId,
  Value<String?> chain,
  Value<String?> address,
  Value<int?> addressIndex,
});
typedef $$AddressMappingTableTableUpdateCompanionBuilder
    = AddressMappingTableCompanion Function({
  Value<int> id,
  Value<String> deviceId,
  Value<String> walletId,
  Value<String?> chain,
  Value<String?> address,
  Value<int?> addressIndex,
});

class $$AddressMappingTableTableFilterComposer
    extends Composer<_$AppDatabase, $AddressMappingTableTable> {
  $$AddressMappingTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get deviceId => $composableBuilder(
      column: $table.deviceId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get addressIndex => $composableBuilder(
      column: $table.addressIndex, builder: (column) => ColumnFilters(column));
}

class $$AddressMappingTableTableOrderingComposer
    extends Composer<_$AppDatabase, $AddressMappingTableTable> {
  $$AddressMappingTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get deviceId => $composableBuilder(
      column: $table.deviceId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get walletId => $composableBuilder(
      column: $table.walletId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chain => $composableBuilder(
      column: $table.chain, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get address => $composableBuilder(
      column: $table.address, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get addressIndex => $composableBuilder(
      column: $table.addressIndex,
      builder: (column) => ColumnOrderings(column));
}

class $$AddressMappingTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $AddressMappingTableTable> {
  $$AddressMappingTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get deviceId =>
      $composableBuilder(column: $table.deviceId, builder: (column) => column);

  GeneratedColumn<String> get walletId =>
      $composableBuilder(column: $table.walletId, builder: (column) => column);

  GeneratedColumn<String> get chain =>
      $composableBuilder(column: $table.chain, builder: (column) => column);

  GeneratedColumn<String> get address =>
      $composableBuilder(column: $table.address, builder: (column) => column);

  GeneratedColumn<int> get addressIndex => $composableBuilder(
      column: $table.addressIndex, builder: (column) => column);
}

class $$AddressMappingTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AddressMappingTableTable,
    AddressMappingModel,
    $$AddressMappingTableTableFilterComposer,
    $$AddressMappingTableTableOrderingComposer,
    $$AddressMappingTableTableAnnotationComposer,
    $$AddressMappingTableTableCreateCompanionBuilder,
    $$AddressMappingTableTableUpdateCompanionBuilder,
    (
      AddressMappingModel,
      BaseReferences<_$AppDatabase, $AddressMappingTableTable,
          AddressMappingModel>
    ),
    AddressMappingModel,
    PrefetchHooks Function()> {
  $$AddressMappingTableTableTableManager(
      _$AppDatabase db, $AddressMappingTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AddressMappingTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AddressMappingTableTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AddressMappingTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> deviceId = const Value.absent(),
            Value<String> walletId = const Value.absent(),
            Value<String?> chain = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<int?> addressIndex = const Value.absent(),
          }) =>
              AddressMappingTableCompanion(
            id: id,
            deviceId: deviceId,
            walletId: walletId,
            chain: chain,
            address: address,
            addressIndex: addressIndex,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String deviceId,
            required String walletId,
            Value<String?> chain = const Value.absent(),
            Value<String?> address = const Value.absent(),
            Value<int?> addressIndex = const Value.absent(),
          }) =>
              AddressMappingTableCompanion.insert(
            id: id,
            deviceId: deviceId,
            walletId: walletId,
            chain: chain,
            address: address,
            addressIndex: addressIndex,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AddressMappingTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AddressMappingTableTable,
    AddressMappingModel,
    $$AddressMappingTableTableFilterComposer,
    $$AddressMappingTableTableOrderingComposer,
    $$AddressMappingTableTableAnnotationComposer,
    $$AddressMappingTableTableCreateCompanionBuilder,
    $$AddressMappingTableTableUpdateCompanionBuilder,
    (
      AddressMappingModel,
      BaseReferences<_$AppDatabase, $AddressMappingTableTable,
          AddressMappingModel>
    ),
    AddressMappingModel,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$AddressBookTableTableTableManager get addressBookTable =>
      $$AddressBookTableTableTableManager(_db, _db.addressBookTable);
  $$MonitorTableTableTableManager get monitorTable =>
      $$MonitorTableTableTableManager(_db, _db.monitorTable);
  $$CoinTableTableTableManager get coinTable =>
      $$CoinTableTableTableManager(_db, _db.coinTable);
  $$WalletTableTableTableManager get walletTable =>
      $$WalletTableTableTableManager(_db, _db.walletTable);
  $$AddressTableTableTableManager get addressTable =>
      $$AddressTableTableTableManager(_db, _db.addressTable);
  $$TokenTableTableTableManager get tokenTable =>
      $$TokenTableTableTableManager(_db, _db.tokenTable);
  $$DappTableTableTableManager get dappTable =>
      $$DappTableTableTableManager(_db, _db.dappTable);
  $$TransactionsActivityTableTableTableManager get transactionsActivityTable =>
      $$TransactionsActivityTableTableTableManager(
          _db, _db.transactionsActivityTable);
  $$AddressMappingTableTableTableManager get addressMappingTable =>
      $$AddressMappingTableTableTableManager(_db, _db.addressMappingTable);
}
