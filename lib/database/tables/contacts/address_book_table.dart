/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-29 00:06:06
 * @LastEditTime: 2024-08-23 17:11:20
 */
/*
 * @description: 地址簿表
 * @Author: wangdognshenng
 * @Date: 2024-01-29 00:06:06
 * @LastEditTime: 2024-01-29 08:59:08
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('AddressBookModel')
class AddressBookTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // chain字段不为空
  TextColumn get chain => text().withLength(min: 1, max: 50)();

  // address字段不为空
  TextColumn get address => text()();

  // constantName字段可以为空
  TextColumn get constantName => text().withLength(min: 1, max: 100)();

  // 唯一约束，确保 chain 和 address 的组合是唯一的
  @override
  List<String> get customConstraints => ['UNIQUE(chain, address)'];
}
