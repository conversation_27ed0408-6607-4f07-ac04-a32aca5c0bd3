/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-29 00:06:06
 * @LastEditTime: 2024-09-18 17:00:45
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('DappModel')
class DappTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 链
  TextColumn get chain => text().nullable()();

  // dappId
  TextColumn get dappId => text().nullable()();

  // dappName
  TextColumn get dappName => text().nullable()();

  // dappLabel
  TextColumn get dappLabel => text().nullable()();

  // dappUrl
  TextColumn get dappUrl => text().nullable()();

  // dapp 介绍
  TextColumn get dappInfo => text().nullable()();

  // dapp symbol
  TextColumn get symbol => text().nullable()();

  // dapp imageUrl
  TextColumn get dappLogoUrl => text().nullable()();

  // btc Dapp Js
  TextColumn get btcDappJs => text().nullable()();

  // dapp netWork
  TextColumn get netWork => text().nullable()();

  // dapp slip44
  TextColumn get slip44 => text().nullable()();

  // Dapp类型 0普通类型  1搜索类型，2最近浏览，3 收藏
  IntColumn get dappMode => integer().nullable()();

  // 最后一次点击时间
  DateTimeColumn get lastTime => dateTime().nullable()();

  //排序
  IntColumn get sortIndex => integer().nullable()();

  //是否已经授权允许访问地址
  BoolColumn get isApprove => boolean().nullable()();

  // 是否支持被禁
  BoolColumn get isBan => boolean().nullable()();
}
