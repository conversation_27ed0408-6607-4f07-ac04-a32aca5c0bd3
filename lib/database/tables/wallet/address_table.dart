/*
 * @description: 地址表
 * @Author: wangdognshenng
 * @Date: 2024-01-29 00:06:06
 * @LastEditTime: 2024-11-27 14:32:57
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('AddressModel')
class AddressTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 钱包Id
  TextColumn get walletId => text().nullable()();

  // 链
  TextColumn get chain => text().nullable()();

  // 链Id
  TextColumn get chainCode => text().nullable()();

  // 地址
  TextColumn get address => text().nullable()();

  // 地址公钥
  TextColumn get publickey => text().nullable()();

  // 地址扩展公钥
  TextColumn get xpubData => text().nullable()();

  // 当前的扩展公钥路径
  TextColumn get path => text().nullable()();

  // 余额
  TextColumn get balance => text().nullable()();

  // 排序 Id
  IntColumn get sortedId => integer().nullable()();

  //地址编号
  IntColumn get addressIndex => integer().nullable()();

  //地址标签
  TextColumn get addressLabel => text().nullable()();

  //地址是否是默认选中地址
  BoolColumn get isSelected => boolean().nullable()();

  //地址是否已经上传服务器
  BoolColumn get isUploaded => boolean().nullable()();

  //币种slip44 Id
  IntColumn get slip44Id => integer().nullable()();

  //隔离验证格式 0普通 1 P2SH 2 BECH32
  IntColumn get segwitType => integer().nullable()();

  //钱包类型  1  硬件钱包钱包  2 热钱包 3 云钱包
  IntColumn get walletType => integer().nullable()();

  //NFT余额缓存
  TextColumn get nftAmountCache => text().nullable()();

  //NFT资产列表第一页缓存
  TextColumn get nftAssetsCache => text().nullable()();

  //NFT合集列表第一页缓存
  TextColumn get nftCollectionCache => text().nullable()();

  //NFT余额折合美元缓存
  TextColumn get nftUsdCache => text().nullable()();

  /// --- btc 系列 ----
  // totalBalance
  TextColumn get totalBalance => text().nullable()();

  // availableBalance
  TextColumn get availableBalance => text().nullable()();

  // unavailableBalance
  TextColumn get unavailableBalance => text().nullable()();

  //Eos账户详情缓存
  TextColumn get eosAccountInfoCache => text().nullable()();

  //Tron账户详情缓存
  TextColumn get tronAccountInfoCache => text().nullable()();

  //Torn资源详情缓存
  TextColumn get tronResourceCache => text().nullable()();

  //UTXO
  TextColumn get utxoCache => text().nullable()();

  // SOL 支付对方租金
  TextColumn get solMinimumRent => text().nullable()();
}
