/*
 * @description: 钱包表
 * @Author: wangdognshenng
 * @Date: 2024-01-29 00:06:06
 * @LastEditTime: 2024-05-30 10:43:02
 */

import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('WalletModel')
class WalletTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 设备Id
  TextColumn get deviceId => text().nullable()();

  // 设备型号
  TextColumn get deviceType => text().nullable()();

// 芯片版本
  TextColumn get chipVersion => text().nullable()();

// 应用版本
  TextColumn get appVersion => text().nullable()();

  //固件版本
  TextColumn get firmwareVersion => text().nullable()();

  //批次号
  IntColumn get batchId => integer().nullable()();

  // bip44钱包Id
  TextColumn get walletId => text().nullable()();

  //Wallet name
  TextColumn get walletName => text().nullable()();

  //钱包是否选中
  BoolColumn get checked => boolean().nullable()();

  //钱包是否为增强模式
  BoolColumn get isPassphraseWallet => boolean().nullable()();

  //smart 蓝牙mac地址
  TextColumn get bleMacId => text().nullable()();

  //助记词类型  1 英文助记词  2 数字助记词 3 中文助记词
  IntColumn get seedType => integer().nullable()();

  //钱包类型  1  硬件钱包钱包  2 热钱包 3 云钱包
  IntColumn get walletType => integer().nullable()();

  // 绑定时间
  TextColumn get monitorTime => text().nullable()();

  //钱包类型vip类型 0普通，其他代表vip等级
  IntColumn get vipLevel => integer().nullable()();

  // 钱包选中chain
  TextColumn get chain => text().nullable()();

  // 钱包选中 address
  TextColumn get address => text().nullable()();

  // 钱包选中 address 标签
  TextColumn get addressLabel => text().nullable()();

  // nft选中chain
  TextColumn get nftChain => text().nullable()();

  // nft选中address
  TextColumn get nftAddress => text().nullable()();

  // nft选中address label
  TextColumn get nftAddressLabel => text().nullable()();

  // dapp默认支持地址列表
  TextColumn get dappChainList => text().nullable()();

  // 全部网络余额
  TextColumn get totalAssets => text().nullable()();
}
