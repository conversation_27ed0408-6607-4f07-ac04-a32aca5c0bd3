/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-09-09 10:52:47
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('CoinModel')
class CoinTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 币种链
  TextColumn get chain => text().nullable()();

  // 链Id
  TextColumn get chainCode => text().nullable()();

  //Eehter Chain Id
  IntColumn get chainId => integer().nullable()();

  // 币种链全称
  TextColumn get chainName => text().nullable()();

  // 币种链中文Name
  TextColumn get cnName => text().nullable()();

  // 币种币名
  TextColumn get symbol => text().nullable()();

  // 币种合约
  TextColumn get contract => text().nullable()();

  // 精度
  IntColumn get chainDecimal => integer().nullable()();

  // 余额精确多少位精度
  IntColumn get balanceDecimal => integer().nullable()();

  //Token 类型 Erc20,Trx10,Trx20
  IntColumn get tokenType => integer().nullable()();

  // 币种行情最新价格
  TextColumn get price => text().nullable()();

  // 是否支持Token
  BoolColumn get isSupportToken => boolean().nullable()();

  // 此币种是否打开
  BoolColumn get isCoinSupported => boolean().nullable()();

  // 是否是Token而非主链
  BoolColumn get isToken => boolean().nullable()();

  // 币种logo
  TextColumn get symbolIcon => text().nullable()();

  // 币种主链Icon
  TextColumn get chainIcon => text().nullable()();

  // 币种主链置灰Icon
  TextColumn get disabledIcon => text().nullable()();

  // 币种档位缓存
  TextColumn get coinFeeTier => text().nullable()();

  // Fee 缓存
  TextColumn get feeCache => text().nullable()();

  // gasLimit缓存
  TextColumn get gasLimitCache => text().nullable()();

  // nft gasLimit缓存
  TextColumn get nftGasLimitCache => text().nullable()();

  // 排序 Id
  RealColumn get sortedId => real().nullable()();

  // filGasJsonStr
  TextColumn get filGasJsonStr => text().nullable()();

  // 隐藏时间
  DateTimeColumn get hiddenDateTime => dateTime().nullable()();
}
