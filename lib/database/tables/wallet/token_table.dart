/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-11-27 17:47:49
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('TokenModel')
class TokenTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 钱包Id
  TextColumn get walletId => text().nullable()();

  // 链
  TextColumn get chain => text().nullable()();

  // 链Id
  TextColumn get chainCode => text().nullable()();

  // 地址
  TextColumn get address => text().nullable()();

  // 币种合约
  TextColumn get contract => text().nullable()();

  // 余额
  TextColumn get balance => text().nullable().nullable()();

  //地址标签
  TextColumn get addressLabel => text().nullable()();

  //如ERC20 TRC10 TRC20 SPL SPL Token-2022
  IntColumn get tokenType => integer().nullable().nullable()();

  //sol 派生地址
  TextColumn get derivedAddresses => text().nullable()();
}
