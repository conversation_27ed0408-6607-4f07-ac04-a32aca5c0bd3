import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('TransactionsActivityModel')
class TransactionsActivityTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  //交易Hash txId
  TextColumn get txId => text().nullable()();

  // 交易时间 txTime
  IntColumn get txTime => integer().nullable()();

  //交易动作 transfer delegatebw  等等
  TextColumn get action => text().nullable()();

  //交易类型 转入、转出、内部转账
  TextColumn get type => text().nullable()();

  // 钱包Id
  TextColumn get walletId => text().nullable()();

  // 币种链
  TextColumn get chain => text().nullable()();

  //合约地址
  TextColumn get contract => text().nullable()();

  // 链Id
  TextColumn get chainCode => text().nullable()();

  // 是否是Token而非主链
  BoolColumn get isToken => boolean().nullable()();

  //币全称
  TextColumn get coinName => text().nullable()();

  //币单位
  TextColumn get symbol => text().nullable()();

  //钱包当前地址
  TextColumn get address => text().nullable()();

//16进制交易
  TextColumn get rawTx => text().nullable()();

//交易所在区块
  IntColumn get blockNumber => integer().nullable()();

  //区块时间
  IntColumn get blockTime => integer().nullable()();

  //交易数量
  TextColumn get amount => text().nullable()();

  //交易矿工费
  TextColumn get fee => text().nullable()();

  //交易备注
  TextColumn get memo => text().nullable()();

  //发送地址
  TextColumn get fromAddress => text().nullable()();

  //接收地址
  TextColumn get receiveAddress => text().nullable()();

  //nonce
  IntColumn get nonce => integer().nullable()();

  //gasPrice
  TextColumn get gasPrice => text().nullable()();

  //gasLimit
  TextColumn get gasLimit => text().nullable()();

  //gasUsed 实际消耗Gas
  TextColumn get gasUsed => text().nullable()();

  //xrpLedger
  IntColumn get xrpLedger => integer().nullable()();

  //xrpSequence
  IntColumn get xrpSequence => integer().nullable()();

//交易來源 广播出去的，钱包账户查询的
  TextColumn get txSource => text().nullable()();

  //输入列表json格式数据，地址+
  TextColumn get indata => text().nullable()();

  //输出列表json格式数据，地址+金额
  TextColumn get outData => text().nullable()();

  //Sequence
  TextColumn get txSequence => text().nullable()();

  //txLedger
  TextColumn get txLedger => text().nullable()();

//交易状态：0成功 1失败(暂时只用于USDT) ETH 2019年10月16日16:17:16也添加status
  IntColumn get txStatus => integer().nullable()();

// 数据量
  IntColumn get dataSize => integer().nullable()();

  /// 是否是手动添加的缓存 状态为打包中
  BoolColumn get isPacking => boolean().nullable()();

  /// extra
  TextColumn get extra => text().nullable()();

  /// valid
  BoolColumn get valid => boolean().nullable()();

  /// 交易详情Cache
  TextColumn get detailsCache => text().nullable()();
}
