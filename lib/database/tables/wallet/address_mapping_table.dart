/*
 * @description: 地址表
 * @Author: wangdognshenng
 * @Date: 2024-01-29 00:06:06
 * @LastEditTime: 2024-11-13 13:49:28
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('AddressMappingModel')
class AddressMappingTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 设备Id
  TextColumn get deviceId => text()();

  // 钱包Id
  TextColumn get walletId => text()();

  // 链
  TextColumn get chain => text().nullable()();

  // 地址
  TextColumn get address => text().nullable()();

  //地址编号(账户ID)
  IntColumn get addressIndex => integer().nullable()();
}
