/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2024-05-28 13:08:51
 */
import 'package:coinbag/database/tables/auto_primary_key.dart';
import 'package:drift/drift.dart';

@DataClassName('MonitorModel')
class MonitorTable extends Table with AutoIncrementingPrimaryKey {
  @override
  IntColumn get id => integer().autoIncrement()();

  // 链
  TextColumn get chain => text().nullable()();

  // 链Id
  TextColumn get chainCode => text().nullable()();

  // 扩展公钥 (P系列是公钥+链码\S系列 、B系列是完整扩展公钥)
  TextColumn get publickey => text().nullable()();

  //批次号
  IntColumn get batchId => integer().nullable()();

  // 钱包Id
  TextColumn get walletId => text().nullable()();

  //币种slip44 Id
  IntColumn get slip44Id => integer().nullable()();

  //生成地址数量
  IntColumn get addressNum => integer().nullable()();

  //隔离验证格式 0普通 1 P2SH 2 BECH32
  IntColumn get segwitType => integer().nullable()();

  // 当前的扩展公钥路径
  TextColumn get path => text().nullable()();

  //公钥类型 0 单签名公钥 COLDLAR_PUBKEY_TYPE_SINGLE 1 多签名公钥 COLDLAR_PUBKEY_TYPE_MULSIG
  IntColumn get keyTypekeyType => integer().nullable()();

  // 是否是扩展公钥：0 - 公钥，1 - 扩展公钥
  BoolColumn get isXPub => boolean().nullable()();
}
