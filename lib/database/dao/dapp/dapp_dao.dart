/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-30 23:44:14
 */
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/dapp/dapp_table.dart';
import 'package:coinbag/modules/dapp/common/dapp_constant.dart';
import 'package:coinbag/modules/dapp/models/dapp_models.dart';
import 'package:drift/drift.dart';

part 'dapp_dao.g.dart';

@DriftAccessor(tables: [DappTable])
class DappDao extends DatabaseAccessor<AppDatabase> with _$DappDaoMixin {
  final AppDatabase db;

  DappDao(this.db) : super(db);

  //查(非响应式)
  Future<List<DappModel>> getAllData() => select(dappTable).get();

  //查(响应式)
  Stream<List<DappModel>> watchAllData() {
    return (select(dappTable)
          ..orderBy([
            // 按 lastTime 降序排序（如果存在）
            (model) => OrderingTerm(
                expression: model.lastTime, mode: OrderingMode.desc),
          ]))
        .watch();
  }

  //增
  Future insertData(Insertable<DappModel> model) =>
      into(dappTable).insert(model);

  //改无则增
  Future insertOnConflictUpdate(Insertable<DappModel> model) =>
      into(dappTable).insertOnConflictUpdate(model);

  //改
  Future updateData(Insertable<DappModel> model) =>
      update(dappTable).replace(model);

  //删
  Future deleteData(Insertable<DappModel> model) =>
      delete(dappTable).delete(model);

  // 是否授权地址
  Future<bool> isApprove(String? dappId, int dappMode) async {
    final exists = await existsDapp(dappId, dappMode);
    if (!exists) return false;
    final count = await (select(dappTable)
          ..where((m) =>
              m.dappId.equals(dappId!) &
              m.dappMode.equals(dappMode) &
              m.isApprove.equals(true)))
        .get();
    return count.isNotEmpty;
  }

  // 授权地址
  Future<void> approveAddress(String? dappId, int dappMode) async {
    if (dappId != null) {
      final exists = await existsDapp(dappId, dappMode);
      if (exists) {
        // 检查是否存在该条目
        DappTableCompanion model = DappTableCompanion.insert(
          dappId: Value(dappId),
          dappMode: Value(dappMode),
          isApprove: const Value(true),
        );
        // 如果存在，执行更新
        await (update(dappTable)
              ..where((m) =>
                  m.dappId.equals(dappId) &
                  m.dappMode.equals(dappMode))) // 使用两个条件更新
            .write(model);
      }
    }
  }

  // 是否收藏
  Future<bool> isFavorited(String? dappId) async {
    final count = await (select(dappTable)
          ..where((m) =>
              m.dappId.equals(dappId!) &
              m.dappMode.equals(DappMode.favoritesMode)))
        .get();
    return count.isNotEmpty;
  }

  // 删除最近和收藏
  Future<void> deleteDappRecord(String? dappId, int mode) async {
    if (dappId != null) {
      await (delete(dappTable)
            ..where((t) =>
                t.dappId.equals(dappId) & t.dappMode.equals(mode))) // 使用两个条件删除
          .go();
    }
  }

  // 取消收藏
  Future<void> cancelFavorited(String? dappId) async {
    if (dappId != null) {
      await (delete(dappTable)
            ..where((t) =>
                t.dappId.equals(dappId) &
                t.dappMode.equals(DappMode.favoritesMode))) // 使用两个条件删除
          .go();
    }
  }

  // 检查是否存在记录
  Future<bool> existsDapp(String? dappId, int dappMode) async {
    final count = await (select(dappTable)
          ..where(
              (m) => m.dappId.equals(dappId!) & m.dappMode.equals(dappMode)))
        .get();
    return count.isNotEmpty;
  }

  Future<void> insertOrUpdateDapp(DappModels dappModels, int dappMode) async {
    DappTableCompanion model = DappTableCompanion.insert(
      dappId: Value(dappModels.dAppId!.toString()),
      dappName: Value(dappModels.dAppName ?? ""),
      dappUrl: Value(dappModels.dAppUrl ?? ""),
      symbol: Value(dappModels.coinSymbol ?? ""),
      chain: Value(dappModels.chain ?? ""),
      dappLogoUrl: Value(dappModels.dAppLogo ?? ""),
      dappInfo: Value(dappModels.abs ?? ""),
      dappLabel: Value(dappModels.label ?? ""),
      btcDappJs: Value(dappModels.js ?? ""),
      netWork: Value(dappModels.network ?? ""),
      slip44: Value(dappModels.slip44 ?? ""),
      dappMode: Value(dappMode),
      isApprove: const Value(false),
      sortIndex: const Value(0),
      lastTime: Value(DateTime.now()),
      isBan: const Value(false),
    );

    // 检查当前条目数量
    final count = await (select(dappTable)
          ..where((t) => t.dappMode.equals(dappMode)))
        .get()
        .then((rows) => rows.length);

    // 如果超过 300 条，则删除时间最旧的条目
    if (count >= 300) {
      // 获取时间最旧的条目并删除
      final oldestDapp = await (select(dappTable)
            ..where((t) => t.dappMode.equals(dappMode))
            ..orderBy([(t) => OrderingTerm.asc(t.lastTime)])) // 按时间升序排序
          .get()
          .then((rows) => rows.isNotEmpty ? rows.first : null);

      if (oldestDapp != null) {
        await (delete(dappTable)
              ..where((t) =>
                  t.dappId.equals(oldestDapp.dappId!) &
                  t.dappMode.equals(dappMode))) // 使用两个条件删除
            .go();
      }
    }

    // 检查是否存在该条目
    final exists = await existsDapp(model.dappId.value, dappMode);
    if (exists) {
      // 如果存在，执行更新
      await (update(dappTable)
            ..where((m) =>
                m.dappId.equals(model.dappId.value!) &
                m.dappMode.equals(dappMode))) // 使用两个条件更新
          .write(model);
    } else {
      // 如果不存在，执行插入
      await insertData(model);
    }
  }

  Future<List<DappModels>> getAllDappModels() async {
    // 如果有数据，创建主要查询
    final query = select(dappTable)
      ..orderBy([
        // 按 lastTime 降序排序（如果存在）
        (model) =>
            OrderingTerm(expression: model.lastTime, mode: OrderingMode.desc),
      ]);

    // 执行查询
    List<DappModel> dataList = await query.get();

    // 将查询结果转换为 DappModels 列表
    return dataList
        .map<DappModels>((model) => DappModels.fromDB(model))
        .toList();
  }

  // 批量插入缓存Dapp
  Future insertBatch(
    List<DappModels> dappModelsList,
  ) async {
    List<DappTableCompanion> dataList = dappModelsList.map((dappModels) {
      return DappTableCompanion.insert(
        dappId: Value(dappModels.dAppId!.toString()),
        dappName: Value(dappModels.dAppName ?? ""),
        dappUrl: Value(dappModels.dAppUrl),
        symbol: Value(dappModels.coinSymbol),
        chain: Value(dappModels.chain),
        dappLogoUrl: Value(dappModels.dAppLogo),
        dappInfo: Value(dappModels.abs),
        dappLabel: Value(dappModels.label),
        btcDappJs: Value(dappModels.js),
        netWork: Value(dappModels.network),
        slip44: Value(dappModels.slip44),
        dappMode: const Value(0),
        isApprove: const Value(false),
        sortIndex: const Value(0),
        lastTime: Value(DateTime.now()),
        isBan: const Value(false),
      );
    }).toList();

    await transaction(() async {
      for (var item in dataList) {
        // 检查是否存在相同的dappId
        final existing = await (select(dappTable)
              ..where((tbl) => tbl.dappId.equals(item.dappId.value!)))
            .get();

        if (existing.isEmpty) {
          // 如果不存在相同的txId，则插入
          await into(dappTable).insert(item);
        } else {
          // 如果不存在相同的txId，则插入
          if (item.dappId.value != null && item.dappId.value != null) {
            final updatedData = DappTableCompanion(
              id: Value(existing.first.id),
              dappId: Value(item.dappId.value),
              dappName: Value(item.dappName.value),
              dappUrl: Value(item.dappUrl.value),
              symbol: Value(item.symbol.value),
              chain: Value(item.chain.value),
              dappLogoUrl: Value(item.dappLogoUrl.value),
              dappInfo: Value(item.dappInfo.value),
              dappLabel: Value(item.dappLabel.value),
              btcDappJs: Value(item.btcDappJs.value),
              netWork: Value(item.netWork.value),
              slip44: Value(item.slip44.value),
              dappMode: Value(item.dappMode.value),
              isApprove: Value(item.isApprove.value),
              sortIndex: Value(item.sortIndex.value),
              isBan: Value(item.isBan.value),
            );
            await updateData(updatedData);
          }
        }
      }
    });
  }
}
