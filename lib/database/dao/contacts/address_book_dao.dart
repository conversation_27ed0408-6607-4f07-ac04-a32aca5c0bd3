/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-22 13:18:39
 * @LastEditTime: 2024-09-02 14:36:16
 */
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/contacts/address_book_table.dart';
import 'package:drift/drift.dart';

part 'address_book_dao.g.dart';

@DriftAccessor(tables: [AddressBookTable])
class AddressBookDao extends DatabaseAccessor<AppDatabase>
    with _$AddressBookDaoMixin {
  final AppDatabase db;

  AddressBookDao(this.db) : super(db);

  //查(非响应式)
  Future<List<AddressBookModel>> getAllData() => select(addressBookTable).get();

  //查(响应式)
  Stream<List<AddressBookModel>> watchAllData() =>
      select(addressBookTable).watch();

  //增
  Future insertData(Insertable<AddressBookModel> model) =>
      into(addressBookTable).insert(model);

  //改无则增
  Future insertOnConflictUpdate(Insertable<AddressBookModel> model) =>
      into(addressBookTable).insertOnConflictUpdate(model);

  //改
  Future updateData(Insertable<AddressBookModel> model) =>
      update(addressBookTable).replace(model);

  //删
  Future deleteData(Insertable<AddressBookModel> model) =>
      delete(addressBookTable).delete(model);

  // 批量插入
  Future insertBatch(List<AddressTableCompanion> dataList) async {
    return transaction(() async {
      await batch((batch) {
        batch.insertAll(addressBookTable, dataList,
            mode: InsertMode.insertOrReplace);
      });
    });
  }

  // 检查地址是否存在于地址簿中，并返回备注
  Future<String?> checkAddressExistsOrGetName(
      {String? address, String? chain}) async {
    final rows = await (select(addressBookTable)
          ..where((item) =>
              item.address.lower().equals(address!.toLowerCase()) &
              item.chain.lower().equals(chain!.toLowerCase())))
        .get();

    if (rows.isNotEmpty) {
      // 假设 rows[0] 是你想要的地址簿条目，并且有一个备注字段
      return rows[0].constantName; // 返回备注
    }

    return null; // 如果没有找到，返回 null
  }

  // 检查是否存在除当前地址和链
  Future<bool> checkAddressExists({String? address, String? chain}) async {
    return (select(addressBookTable)
          ..where((item) =>
              item.address.lower().equals(address!.toLowerCase()) &
              item.chain.lower().equals(chain!.toLowerCase())))
        .get()
        .then((rows) => rows.isNotEmpty);
  }

  // 检查是否存在除当前地址本以外的具有相同属性
  Future<bool> checkAddressExistsIfUnique(
      {String? address, String? chain, String? constantName}) async {
    return transaction(() async {
      // 查询是否存在具有相同 chain、address 和 constantName 的记录
      var existingAddress = await (select(addressBookTable)
            ..where((item) =>
                item.chain.lower().equals(chain!.toLowerCase()) &
                item.address.lower().equals(address!.toLowerCase()) &
                item.constantName.equals(constantName!)))
          .getSingleOrNull();

      // 如果找到了匹配的记录，返回 true
      return existingAddress != null;
    });
  }

// 更新或插入的方法
  Future<void> upsertAddressBook({
    required String? chain,
    required String? address,
    required String? constantName,
  }) async {
    // 查找是否存在相同的 chain 和 address
    var existingEntry = await (select(addressBookTable)
          ..where((item) =>
              item.chain.lower().equals(chain!.toLowerCase()) &
              item.address.lower().equals(address!.toLowerCase())))
        .getSingleOrNull();

    if (existingEntry != null) {
      // 如果存在，执行更新操作
      await (update(addressBookTable)
            ..where((item) =>
                item.chain.equals(chain!) & item.address.equals(address!)))
          .write(
        AddressBookTableCompanion(
            constantName: Value(constantName!)), // 更新 constantName
      );
    } else {
      // 如果不存在，执行插入操作
      final addressBookModel = AddressBookTableCompanion.insert(
          chain: chain!,
          address: address!,
          constantName: constantName!); // 可以是 nullable

      await into(addressBookTable).insert(addressBookModel);
    }
  }
}
