import 'dart:async';

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/wallet/coin_table.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/market_price_model.dart';
import 'package:coinbag/utils/log_utils.dart' show Log;
import 'package:common_utils/common_utils.dart';
import 'package:drift/drift.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/ethereum/evm/polygon.dart';
import 'package:wallet_core/chain/ethereum/layer/arb.dart';
import 'package:wallet_core/chain/ethereum/layer/base.dart';
import 'package:wallet_core/chain/ethereum/layer/blast.dart';
import 'package:wallet_core/chain/ethereum/layer/linea.dart';
import 'package:wallet_core/chain/ethereum/layer/opt.dart';
import 'package:wallet_core/chain/ethereum/layer/zks.dart';
import 'package:wallet_core/chain/ethereum/testnet/heth.dart';

part 'coin_dao.g.dart';

@DriftAccessor(tables: [CoinTable])
class CoinDao extends DatabaseAccessor<AppDatabase> with _$CoinDaoMixin {
  final AppDatabase db;

  CoinDao(this.db) : super(db);

  //查(非响应式)
  Future<List<CoinModel>> getAllData() => select(coinTable).get();

  //查(响应式)
  Stream<List<CoinModel>> watchAllData() => select(coinTable).watch();

  ///查  所有打开的全部链
  Future<List<CoinModel>> getAllSupportedCoinModel(
      {required String chain}) async {
    if (chain == AllChain.get.chain) {
      return (select(coinTable)
            ..where((coin) => coin.isCoinSupported.equals(true)))
          .get();
    } else {
      return (select(coinTable)..where((c) => c.chain.equals(chain))).get();
    }
  }

  //查 条件chain 和合约地址
  Future<CoinModel?> getCoinModel(
      {required chain, required String contract}) async {
    bool isToken = !ObjectUtil.isEmptyString(contract);

    return isToken
        ? getTokenCoinModel(chain: chain, contract: contract)
        : getMainCoinModel(chain: chain);
  }

  //查 仅获取主链币
  Future<CoinModel?> getMainCoinModel({required chain}) async {
    List<CoinModel> coinModellist = await (select(coinTable)
          ..where(
              (coin) => coin.chain.equals(chain) & coin.isToken.equals(false)))
        .get();

    return coinModellist.isNotEmpty ? coinModellist.first : null;
  }

  //查 获取Token币
  Future<CoinModel?> getTokenCoinModel(
      {required chain, required contract}) async {
    List<CoinModel> coinModellist = await (select(coinTable)
          ..where((coin) =>
              coin.chain.lower().equals(chain.toLowerCase()) &
              coin.isToken.equals(true) &
              coin.contract.lower().equals(contract.toLowerCase())))
        .get();
    if (coinModellist.isNotEmpty) {
      return coinModellist.first;
    }

    return null;
  }

  // 查 获取显示的Token币种列表
  Future<List<CoinModel>?> getCoinSupportedTokenCoinModels(
      {required chain}) async {
    List<CoinModel> coinModellist = await (select(coinTable)
          ..where((coin) =>
              coin.chain.equals(chain) &
              coin.isToken.equals(true) &
              coin.isCoinSupported.equals(true)))
        .get();
    if (coinModellist.isNotEmpty) {
      return coinModellist;
    }

    return null;
  }

  //查 条件symbol 和合约地址 这个不准确，只用来取行情
  Future<List<CoinModel?>> getCoinModelBySymbol(
      {required String symbol}) async {
    List<CoinModel> coinModellist = [];
    if (symbol == "arb_eth") {
      symbol = ArbitrumChain.get.chain;
      coinModellist = await (select(coinTable)
            ..where((coin) => coin.chain.lower().equals(symbol.toLowerCase())))
          .get();
    } else if (symbol == "opt_eth") {
      symbol = OptimismChain.get.chain;
      coinModellist = await (select(coinTable)
            ..where((coin) => coin.chain.lower().equals(symbol.toLowerCase())))
          .get();
    } else {
      coinModellist = await (select(coinTable)
            ..where((coin) => coin.symbol.lower().equals(symbol.toLowerCase())))
          .get();
    }

    return coinModellist;
  }

  //增
  Future insertData(Insertable<CoinModel> model) =>
      into(coinTable).insert(model);

  //改无则增
  Future insertOnConflictUpdate(Insertable<CoinModel> model) =>
      into(coinTable).insertOnConflictUpdate(model);

  //改
  Future updateData(Insertable<CoinModel> model) =>
      update(coinTable).replace(model);

  //删
  Future deleteData(Insertable<CoinModel> model) =>
      delete(coinTable).delete(model);

  // 批量插入
  Future<void> insertBatch(List<CoinTableCompanion> dataList) async {
    return transaction(() async {
      for (var coin in dataList) {
        // 查询现有记录，添加多个条件
        final existingCoin = await (select(coinTable)
              ..where((tbl) =>
                  tbl.chain.equals(coin.chain.value!) &
                  (tbl.isToken.equals(false))))
            .getSingleOrNull();

        if (existingCoin != null) {
          // 更新现有记录，排除 sortedId 列
          await (update(coinTable)
                ..where((tbl) =>
                    tbl.chain.equals(coin.chain.value!) &
                    tbl.isToken.equals(false)))
              .write(CoinTableCompanion(
            // 复制现有记录的所有字段
            // 但不包括 sortedId
            id: Value(existingCoin.id), // 假设 id 是主键
            // 其他字段根据需要更新
            chain: Value(existingCoin.chain),
            isToken: Value(existingCoin.isToken),
            // 复制其他需要更新的字段...
            // 不更新 sortedId
          ));
        } else {
          // 插入新记录
          await into(coinTable).insert(coin);
        }
      }
    });
  }

  // 更新币价
  Future<void> updateCoinPrices(List<MarketPriceModel> markModelList) async {
    for (var marketPrice in markModelList) {
      try {
        if (_hasValidContractAddress(marketPrice)) {
          await _updateTokenPrice(marketPrice);
        } else {
          await _updateChainPrice(marketPrice);
        }
      } catch (e) {
        // 记录错误但继续处理其他价格更新
        Log.e('Error updating price for ${marketPrice.symbol}: $e');
        continue;
      }
    }
  }

  bool _hasValidContractAddress(MarketPriceModel marketPrice) {
    return !Get.isEmptyString(marketPrice.chain) &&
        !Get.isEmptyString(marketPrice.tokenContract);
  }

  Future<void> _updateTokenPrice(MarketPriceModel marketPrice) async {
    CoinModel? coinModel = await getCoinModel(
        chain: marketPrice.chain, contract: marketPrice.tokenContract!);

    if (coinModel != null) {
      await _updatePrice(coinModel.id, marketPrice.currentPrice!);
    }
  }

  Future<void> _updateChainPrice(MarketPriceModel marketPrice) async {
    if (Get.isEmptyString(marketPrice.symbol)) return;

    String symbol = _getChainSymbol(marketPrice.symbol!);
    if (Get.isEmptyString(symbol)) return;

    List<CoinModel?> coinModels = await getCoinModelBySymbol(symbol: symbol);
    await _updateModelsPrices(coinModels, marketPrice.currentPrice!);
  }

  String _getChainSymbol(String markSymbol) {
    final Map<String, String> symbolMap = {
      "matic": PolygonChain.get.symbol,
      "eth_holesky": EthereumHoleskyChain.get.symbol,
      "arb_eth": ArbitrumChain.get.symbol,
      "opt_eth": OptimismChain.get.symbol,
      "base_eth": BaseChain.get.symbol,
      "blast_eth": BlastChain.get.symbol,
      "linea_eth": LineaChain.get.symbol,
      "zks_eth": ZkSyncEraChain.get.symbol,
      "arb": "",
      "blast": "",
    };

    return symbolMap[markSymbol] ?? markSymbol.toLowerCase();
  }

  Future<void> _updateModelsPrices(
      List<CoinModel?> models, String price) async {
    for (var model in models) {
      if (model != null) {
        await _updatePrice(model.id, price);
      }
    }
  }

  Future<void> _updatePrice(int id, String price) async {
    await batch((batch) {
      batch.update(
        coinTable,
        CoinTableCompanion(id: Value(id), price: Value(price)),
        where: (tbl) => tbl.id.equals(id),
      );
    });
  }

  /// 更新币种精度
  Future<void> updateDecimal(
      CoinModel coinModel, int decimal, int balanceDecimal) async {
    if (coinModel.chainDecimal == null) {
      final coin = CoinTableCompanion(
        id: Value(coinModel.id),
        balanceDecimal: Value(balanceDecimal),
        chainDecimal: Value(decimal),
      );
      insertOnConflictUpdate(coin);
    }
  }

  /// 更新Trx币种类型
  Future<void> updateTokenType(CoinModel coinModel, int? tokenType) async {
    final coin = CoinTableCompanion(
      id: Value(coinModel.id),
      tokenType: Value(tokenType),
    );
    insertOnConflictUpdate(coin);
  }

  /// 更新币种图标
  Future<void> updateTokenSymbolUrl(
      CoinModel coinModel, String imageUlr) async {
    String symbol = coinModel.symbol!.toLowerCase();
    if (symbol == 'usdt' ||
        symbol == 'usdc' ||
        symbol == 'ht' ||
        symbol == 'eth') {
      return;
    }

    if (imageUlr.isNotEmpty) {
      final coin = CoinTableCompanion(
        id: Value(coinModel.id),
        symbolIcon: Value(imageUlr),
      );
      insertOnConflictUpdate(coin);
    }
  }

  Future<CoinModel?> _getFeeCoinModel(CoinModel coinModel) async {
    List<CoinModel> list = await (select(coinTable)
          ..where((coin) => coin.id.equals(coinModel.id)))
        .get();
    if (list.isNotEmpty) {
      return list.first;
    }
    return null;
  }

  /// 缓存矿工费档位
  Future<void> updateTier(CoinModel coinModel, String? coinFeeTier) async {
    if (!Get.isEmptyString(coinFeeTier)) {
      final coin = CoinTableCompanion(
        id: Value(coinModel.id),
        coinFeeTier: Value(coinFeeTier),
      );
      insertOnConflictUpdate(coin);
    }
  }

  /// 查询缓存矿工费档位
  Future<String?> getCacheTier(CoinModel coinModel) async {
    CoinModel? result = await _getFeeCoinModel(coinModel);
    return result?.coinFeeTier;
  }

  /// 缓存gaLimit
  Future<void> updateGasLimit(CoinModel coinModel, String? gasLimit) async {
    if (!Get.isEmptyString(gasLimit)) {
      final coin = CoinTableCompanion(
          id: Value(coinModel.id), gasLimitCache: Value(gasLimit));
      insertOnConflictUpdate(coin);
    }
  }

  /// 缓存NNFT gaLimit
  Future<void> updateNftGasLimit(CoinModel coinModel, String? gasLimit) async {
    if (!Get.isEmptyString(gasLimit)) {
      final coin = CoinTableCompanion(
          id: Value(coinModel.id), nftGasLimitCache: Value(gasLimit));
      insertOnConflictUpdate(coin);
    }
  }

  /// 查询缓存gasLimit
  Future<String?> getCacheGasLimit(CoinModel coinModel) async {
    CoinModel? result = await _getFeeCoinModel(coinModel);
    return result?.gasLimitCache;
  }

  /// 查询缓存NFT gasLimit
  Future<String?> getNftCacheGasLimit(CoinModel coinModel) async {
    CoinModel? result = await _getFeeCoinModel(coinModel);
    return result?.nftGasLimitCache;
  }

  /// updateFilGasJsonStr
  Future<void> updateFilGasJsonStr(
      CoinModel coinModel, String? filGasJsonStr) async {
    if (!Get.isEmptyString(filGasJsonStr)) {
      final coin = CoinTableCompanion(
          id: Value(coinModel.id), filGasJsonStr: Value(filGasJsonStr));
      insertOnConflictUpdate(coin);
    }
  }

  /// 查询缓存gasLimit
  Future<String?> getCacheFilGasJsonStr(CoinModel coinModel) async {
    CoinModel? result = await _getFeeCoinModel(coinModel);
    return result?.filGasJsonStr;
  }

  /// updateDateTime
  Future<void> updateCoinStatus(
      {required CoinModel coinModel,
      required bool isCoinSupported,
      DateTime? dataTime}) async {
    final coin = CoinTableCompanion(
        id: Value(coinModel.id),
        isCoinSupported: Value(isCoinSupported),
        hiddenDateTime: Value(dataTime));
    insertOnConflictUpdate(coin);
  }

  /// updateCoinSortedId
  Future<void> updateCoinSortedId(
      {required CoinModel coinModel, required double sortedId}) async {
    final coin = CoinTableCompanion(
      id: Value(coinModel.id),
      sortedId: Value(sortedId),
    );
    insertOnConflictUpdate(coin);
  }

  /// isCoinSupported
  Future<void> updateCoinisSupported({
    required CoinModel coinModel,
    required bool isCoinSupported,
    required int? tokenType,
  }) async {
    final coin = CoinTableCompanion(
      id: Value(coinModel.id),
      isCoinSupported: Value(isCoinSupported),
      symbolIcon: Value(coinModel.symbolIcon),
      tokenType: Value(tokenType),
    );
    insertOnConflictUpdate(coin);
  }

  // 插入Token
  Future<void> intertTokenCoinModel(
      BalanceModel balanceModel, CoinType coinType,
      {bool isCoinSupported = true}) async {
    if (balanceModel.name!.isEmpty || balanceModel.symbol!.isEmpty) {
      return;
    }

    int chainId = coinType.chainId == null ? 0 : coinType.chainId!;
    String chainIcon = "";

    if (!Get.isEmptyString(balanceModel.logoUrl)) {
      chainIcon = balanceModel.logoUrl!;
    }

    if (balanceModel.symbol != null) {
      if (balanceModel.symbol!.toLowerCase() == 'usdt') {
        chainIcon = TokenIconUrl.usdt;
      } else if (balanceModel.symbol!.toLowerCase() == 'usdc') {
        chainIcon = TokenIconUrl.usdc;
      } else if (balanceModel.symbol!.toLowerCase() == 'ht') {
        chainIcon = TokenIconUrl.ht;
      } else if (balanceModel.symbol!.toLowerCase() == 'eth') {
        chainIcon = TokenIconUrl.eth;
      }
    }

    insertOnConflictUpdate(CoinTableCompanion(
        chain: Value(coinType.chain),
        chainCode: Value(coinType.id),
        chainId: Value(chainId),
        chainName: Value(balanceModel.name!),
        cnName: Value(balanceModel.name!),
        symbol: Value(balanceModel.symbol!),
        contract: Value(balanceModel.contract!),
        chainDecimal: Value(balanceModel.decimal!),
        balanceDecimal: Value(coinType.balanceDecimals),
        tokenType: Value(balanceModel.tokenType),
        isSupportToken: const Value(true),
        isCoinSupported: Value(isCoinSupported),
        symbolIcon: Value(chainIcon),
        chainIcon: Value(coinType.chainIcon),
        isToken: const Value(true)));
  }
}
