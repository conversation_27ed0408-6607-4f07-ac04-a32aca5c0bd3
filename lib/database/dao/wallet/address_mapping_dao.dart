/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-11-21 13:06:12
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/wallet/address_mapping_table.dart';
import 'package:coinbag/res/resource.dart';
import 'package:drift/drift.dart';

part 'address_mapping_dao.g.dart';

@DriftAccessor(tables: [AddressMappingTable])
class AddressMappingDao extends DatabaseAccessor<AppDatabase>
    with _$AddressMappingDaoMixin {
  final AppDatabase db;

  AddressMappingDao(this.db) : super(db);

  // 批量插入
  Future insertBatch(List<AddressMappingTableCompanion> dataList) async {
    return await transaction(() async {
      for (var data in dataList) {
        final exists = await (select(addressMappingTable)
              ..where((tbl) =>
                  tbl.deviceId.equals(data.deviceId.value) &
                  tbl.walletId.equals(data.walletId.value) &
                  tbl.chain.equals(data.chain.value!) &
                  tbl.address.equals(data.address.value!)))
            .getSingleOrNull();
        if (exists == null) {
          await into(addressMappingTable).insert(data);
        } else {
          // 更新现有记录
          await (update(addressMappingTable)
                ..where((tbl) =>
                    tbl.deviceId.equals(data.deviceId.value) &
                    tbl.walletId.equals(data.walletId.value) &
                    tbl.chain.equals(data.chain.value!) &
                    tbl.address.equals(data.address.value!) &
                    tbl.addressIndex.equals(data.addressIndex.value!)))
              .write(data);
        }
      }
    });
  }

  //获取该设备下某个链所有地址
  Future<List<AddressMappingModel>> getAddressMappingList(
      String deviceId, String walletId, String chain,
      [String? address]) {
    final query = select(addressMappingTable)
      ..where((tbl) =>
          tbl.deviceId.equals(deviceId) &
          tbl.walletId.equals(walletId) &
          tbl.chain.equals(chain));

    // 仅在 address 不为空时添加额外的条件
    if (!Get.isEmptyString(address)) {
      query.where((tbl) => tbl.address.equals(address!));
    }

    return query.get();
  }

  //根据walletId 和deviceId 删除记录
  Future<void> deleteByWalletIdOrDeviceId(
      {required String walletId, required String deviceId}) async {
    await (delete(addressMappingTable)
          ..where(
              (w) => w.walletId.equals(walletId) & w.deviceId.equals(deviceId)))
        .go();
  }
}
