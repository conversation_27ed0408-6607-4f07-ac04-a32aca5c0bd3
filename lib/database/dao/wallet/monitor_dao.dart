/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-21 16:22:31
 * @LastEditTime: 2024-11-08 13:40:57
 */
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/wallet/monitor_table.dart';
import 'package:drift/drift.dart';

part 'monitor_dao.g.dart';

@DriftAccessor(tables: [MonitorTable])
class MonitorDao extends DatabaseAccessor<AppDatabase> with _$MonitorDaoMixin {
  final AppDatabase db;

  MonitorDao(this.db) : super(db);

  //查(非响应式)
  Future<List<MonitorModel>> getAllData() => select(monitorTable).get();

  //查(响应式)
  Stream<List<MonitorModel>> watchAllData() => select(monitorTable).watch();

  //增
  Future insertData(Insertable<MonitorModel> model) =>
      into(monitorTable).insert(model);

  //改
  Future updateData(Insertable<MonitorModel> model) =>
      into(monitorTable).insertOnConflictUpdate(model);

  //删
  Future deleteData(Insertable<MonitorModel> model) =>
      delete(monitorTable).delete(model);

  //根据walletId删除记录
  Future<void> deleteByWalletId(String walletId) async {
    await (delete(monitorTable)..where((w) => w.walletId.equals(walletId)))
        .go();
  }

  // 批量插入
  Future insertBatch(List<MonitorTableCompanion> dataList) async {
    return await transaction(() async {
      for (var data in dataList) {
        final exists = await (select(monitorTable)
              ..where((tbl) =>
                  tbl.walletId.equals(data.walletId.value!) &
                  tbl.chain.equals(data.chain.value!) &
                  tbl.segwitType.equals(data.segwitType.value!)))
            .getSingleOrNull();
        if (exists == null) {
          await into(monitorTable).insert(data);
        } else {
          // 更新现有记录
          await (update(monitorTable)
                ..where((tbl) =>
                    tbl.walletId.equals(data.walletId.value!) &
                    tbl.chain.equals(data.chain.value!) &
                    tbl.segwitType.equals(data.segwitType.value!)))
              .write(data);
        }
      }
    });
  }

  //查 条件chain 和 waleltId segwitType(btc ltc)
  Future<MonitorModel?> getMonitorModel(
      {required chain, required String walletId, int? segwitType}) async {
    List<MonitorModel> monitorModels = await (select(monitorTable)
          ..where((coin) => segwitType == null
              ? (coin.chain.equals(chain) & coin.walletId.equals(walletId))
              : (coin.chain.equals(chain) &
                  coin.walletId.equals(walletId) &
                  coin.segwitType.equals(segwitType))))
        .get();

    return monitorModels.isNotEmpty ? monitorModels.first : null;
  }

  // 获取公钥
  Future<String?> getPublicKey(String walletId, String chain) async {
    final publicKeys = await (select(monitorTable)
          ..where((w) => w.walletId.equals(walletId) & w.chain.equals(chain)))
        .map((row) => row.publickey)
        .get();

    if (publicKeys.isEmpty) {
      return "";
    }
    if (publicKeys.length == 1) {
      return publicKeys.first;
    } else {
      return "";
    }
  }
}
