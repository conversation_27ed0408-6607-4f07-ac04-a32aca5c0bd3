/*
 * @description: 钱包
 * @Author: wangdog<PERSON>henng
 * @Date: 2024-02-29 10:36:52
 * @LastEditTime: 2024-12-13 15:26:17
 */
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/wallet/wallet_table.dart';
import 'package:drift/drift.dart';

part 'wallet_dao.g.dart';

@DriftAccessor(tables: [WalletTable])
class WalletDao extends DatabaseAccessor<AppDatabase> with _$WalletDaoMixin {
  final AppDatabase db;

  WalletDao(this.db) : super(db);

  //查(非响应式)
  Future<List<WalletModel>> getAllData() => select(walletTable).get();

//查(非响应式) 选中的放第一个
  Future<List<WalletModel>> getSortAllData() async {
    final List<WalletModel> rawData = await getAllData();

    // 查找checked为true的项的索引
    int checkedIndex = rawData.indexWhere((item) => item.checked!);
    // 如果找到了，并且它不在列表的最前面，则将其移动到列表的最前面
    if (checkedIndex != -1 && checkedIndex != 0) {
      WalletModel checkedItem = rawData.removeAt(checkedIndex);
      rawData.insert(0, checkedItem);
    }

    return rawData;
  }

  //查(响应式)
  Stream<List<WalletModel>> watchAllData() => select(walletTable).watch();

  //增
  Future insertData(Insertable<WalletModel> model) =>
      into(walletTable).insert(model);

// 检查是否存在记录
  Future<bool> existsWallet(String? deviceId, String? wlaletId) async {
    final count = await (select(walletTable)
          ..where((m) =>
              m.deviceId.equals(deviceId!) & m.walletId.equals(wlaletId!)))
        .get();
    return count.isNotEmpty;
  }

  // 插入或更新
  Future<void> insertOrUpdateWallet(WalletTableCompanion model) async {
    final exists =
        await existsWallet(model.deviceId.value, model.walletId.value);
    if (exists) {
      // 如果存在，执行更新
      await (update(walletTable)
            ..where((m) =>
                m.deviceId.equals(model.deviceId.value!) &
                m.walletId.equals(model.walletId.value!)))
          .write(model);
    } else {
      // 如果不存在，执行插入
      await insertData(model);
    }
  }

  //改
  Future updateData(Insertable<WalletModel> model) =>
      update(walletTable).replace(model);

  //删
  Future deleteData(Insertable<WalletModel> model) =>
      delete(walletTable).delete(model);

  //根据walletId删除记录
  Future<void> deleteByWalletId(String walletId) async {
    await (delete(walletTable)..where((w) => w.walletId.equals(walletId))).go();
  }

  //根据walletId 和deviceId 删除记录
  Future<void> deleteByWalletIdOrDeviceId(
      String walletId, String deviceId) async {
    await (delete(walletTable)
          ..where(
              (w) => w.walletId.equals(walletId) & w.deviceId.equals(deviceId)))
        .go();
  }

  // 批量插入
  Future insertBatch(List<WalletTableCompanion> dataList) async {
    return transaction(() async {
      await batch((batch) {
        batch.insertAll(walletTable, dataList,
            mode: InsertMode.insertOrReplace);
      });
    });
  }

  // 通过walletId查询第一个钱包
  Future<WalletModel?> findWalletsByWalletId(String walletId) async {
    List<WalletModel> walletList = await (select(walletTable)
          ..where((tbl) => tbl.walletId.equals(walletId)))
        .get();

    try {
      return walletList.firstWhere((wallet) => wallet.walletId == walletId);
    } catch (e) {
      return null;
    }
  }

  // 通过 walletId 查询所有钱包
  Future<List<WalletModel>> findAllWalletsByWalletId(String walletId) async {
    // 执行查询
    final walletList = await (select(walletTable)
          ..where((tbl) => tbl.walletId.equals(walletId)))
        .get();

    // 返回所有匹配的钱包，可能是空列表
    return walletList; // 返回 List<WalletModel>
  }

  // 通过WalletId 和deviceId 查询钱包
  Future<WalletModel?> findWalletsByWalletWalletOrDeviceId(
      String walletId, String deviceId) async {
    List<WalletModel> walletList = await (select(walletTable)
          ..where((tbl) =>
              tbl.walletId.equals(walletId) & tbl.deviceId.equals(deviceId)))
        .get();

    try {
      return walletList.firstWhere((wallet) => wallet.walletId == walletId);
    } catch (e) {
      return null;
    }
  }

  // 选中的钱包(响应式) - 返回第一个选中的钱包
  Stream<WalletModel?> watchCheckedWallet() {
    return (select(walletTable)..where((c) => c.checked.equals(true)))
        .watch()
        .map((wallets) => wallets.isNotEmpty ? wallets.first : null);
  }

  Future<WalletModel?> getCheckedWallets() async {
    List<WalletModel> walletModelList = await (select(walletTable)
          ..where((wallet) => wallet.checked.equals(true)))
        .get();

    return walletModelList.isNotEmpty ? walletModelList.first : null;
  }

  ///选中这个钱包
  Future<void> selectWallet(String walletId, String deviceId) {
    return transaction(() async {
      // 将所有钱包设为未选中
      await (update(walletTable)..where((tbl) => tbl.checked.equals(true)))
          .write(const WalletTableCompanion(checked: Value(false)));
      // 将选定的钱包设为选中
      await (update(walletTable)
            ..where((tbl) =>
                tbl.walletId.equals(walletId) & tbl.deviceId.equals(deviceId)))
          .write(const WalletTableCompanion(checked: Value(true)));
    });
  }

  ///查是否还存在已选中的钱包，没选中的根据绑定时间来排序monitorTime
  Future<void> deleteWalletAndUpdateChecked(
    String walletId,
    String deviceId,
  ) async {
    // 开始一个事务
    return transaction(() async {
      // 删除指定的钱包
      await deleteByWalletIdOrDeviceId(walletId, deviceId);

      // 检查是否还有其他已选中的钱包
      final List<WalletModel> checkedWallets = await (select(walletTable)
            ..where((tbl) => tbl.checked.equals(true)))
          .get();

      // 如果没有已选中的钱包，则选择一个新的钱包并将其设置为选中
      if (checkedWallets.isEmpty) {
        final WalletModel? walletToCheck = await (select(walletTable)
              ..orderBy([(tbl) => OrderingTerm.desc(tbl.monitorTime)])
              ..limit(1))
            .getSingleOrNull();

        // 如果有钱包，将其设置为选中
        if (walletToCheck != null) {
          await update(walletTable)
              .replace(walletToCheck.copyWith(checked: const Value(true)));
        }
      }
    });
  }

  Future<void> updateWalletAddress(
      String walletId, AddressModel addressModel) async {
    await (update(walletTable)..where((tbl) => tbl.walletId.equals(walletId)))
        .write(WalletTableCompanion(
      addressLabel: Value(addressModel.addressLabel),
      address: Value(addressModel.address),
    ));
  }

  Future<void> updateWalletName(String walletId, String walletName) async {
    await (update(walletTable)..where((tbl) => tbl.walletId.equals(walletId)))
        .write(WalletTableCompanion(
      walletName: Value(walletName),
    ));
  }

  Future<void> updateTotalAssets(String walletId, String? totalAssets) async {
    await (update(walletTable)..where((tbl) => tbl.walletId.equals(walletId)))
        .write(WalletTableCompanion(
      totalAssets: Value(totalAssets),
    ));
  }

  Future<void> updateWalletChainOrAddress(
      {required String walletId, String? chain, String? address}) async {
    await (update(walletTable)..where((tbl) => tbl.walletId.equals(walletId)))
        .write(WalletTableCompanion(
      chain: Value(chain),
      address: Value(address),
    ));
  }

  Future<void> updateWalletNFTChainOrAddress({
    required String walletId,
    required String chain,
    required String address,
    required String addressLabel,
  }) async {
    await (update(walletTable)..where((tbl) => tbl.walletId.equals(walletId)))
        .write(WalletTableCompanion(
      nftChain: Value(chain),
      nftAddress: Value(address),
      nftAddressLabel: Value(addressLabel),
    ));
  }
}
