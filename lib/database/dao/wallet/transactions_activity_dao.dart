/*
 * @description: Do not edit
 * @Author: wangdog<PERSON>henng
 * @Date: 2024-02-21 16:22:31
 * @LastEditTime: 2024-12-12 11:24:26
 */
/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-02-21 16:22:31
 * @LastEditTime: 2024-02-21 16:24:11
 */
import 'dart:convert';
import 'dart:math';

import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/wallet/transactions_activity_table.dart';
import 'package:coinbag/modules/wallet/assets/models/token_activity_model.dart';
import 'package:coinbag/modules/wallet/assets/models/token_transaction_model.dart';
import 'package:coinbag/utils/date_helper.dart';
import 'package:drift/drift.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/extension/string_decimal.dart';
import 'package:wallet_core/model/transfer_model.dart';

part 'transactions_activity_dao.g.dart';

@DriftAccessor(tables: [TransactionsActivityTable])
class TransactionsActivityDao extends DatabaseAccessor<AppDatabase>
    with _$TransactionsActivityDaoMixin {
  final AppDatabase db;

  TransactionsActivityDao(this.db) : super(db);

  //查(非响应式)
  Future<List<TransactionsActivityModel>> getAllData() =>
      select(transactionsActivityTable).get();

  //查(响应式)
  Stream<List<TransactionsActivityModel>> watchAllData() =>
      select(transactionsActivityTable).watch();

  //增
  Future insertData(Insertable<TransactionsActivityModel> model) =>
      into(transactionsActivityTable).insert(model);

  //改无则增
  Future insertOnConflictUpdate(Insertable<TransactionsActivityModel> model) =>
      into(transactionsActivityTable).insertOnConflictUpdate(model);

  //改
  Future updateData(Insertable<TransactionsActivityModel> model) =>
      update(transactionsActivityTable).replace(model);

  //删
  Future deleteData(Insertable<TransactionsActivityModel> model) =>
      delete(transactionsActivityTable).delete(model);

  //根据walletId删除记录
  Future<void> deleteByWalletId(String walletId) async {
    await (delete(transactionsActivityTable)
          ..where((w) => w.walletId.equals(walletId)))
        .go();
  }

  // 检查是否存在记录
  Future<bool> existsActivity(
      {required TokenActivityModel activityModel,
      required String address}) async {
    final count = await (select(transactionsActivityTable)
          ..where((m) =>
              m.txId.equals(activityModel.txId!) &
              m.address.equals(address) &
              m.chain.equals(activityModel.coinType!.chain)))
        .get();
    return count.isNotEmpty;
  }

  // 更新交易详情
  Future<void> insertOrUpdateDetails(
      {required TokenActivityModel activityModel,
      required String address,
      required String detailsJson}) async {
    final exists =
        await existsActivity(activityModel: activityModel, address: address);
    if (exists) {
      // 如果存在，执行更新操作
      await (update(transactionsActivityTable)
            ..where((item) =>
                item.txId.equals(activityModel.txId!) &
                item.address.equals(address) &
                item.chain.equals(activityModel.coinType!.chain)))
          .write(
        TransactionsActivityTableCompanion(
            detailsCache: Value(detailsJson)), // 更新 交易详情
      );
    }
  }

  // 交易详情缓存

  Future<TokenTransactionModel?> getTransactionDetailsCache(
      {required TokenActivityModel activityModel,
      required String address}) async {
    try {
      List<TransactionsActivityModel> dataList =
          await (select(transactionsActivityTable)
                ..where((transactionsActivityTable) =>
                    transactionsActivityTable.txId.equals(activityModel.txId!) &
                    transactionsActivityTable.address.equals(address) &
                    transactionsActivityTable.chain
                        .equals(activityModel.coinType!.chain)))
              .get();

      // 检查查询结果是否为空
      if (dataList.isEmpty) {
        return null; // 直接返回 null
      }

      TransactionsActivityModel result = dataList.first;

      if (result.detailsCache == null) {
        return null;
      }

      // 解析 JSON 并返回 TokenTransactionModel
      dynamic json = jsonDecode(result.detailsCache!);
      return TokenTransactionModel.fromJson(json);
    } catch (e) {
      return null; // 或者根据需求选择抛出异常
    }
  }

  // 获取数据库缓存交易记录
  Future<List<TokenActivityModel>> getTokenActivityList({
    required String walletId,
    required CoinModel coinModel,
    required String address,
  }) async {
    // 定义查询的通用部分
    final query = select(transactionsActivityTable)
      ..where((model) {
        if (coinModel.isToken == true) {
          return model.walletId.equals(walletId) &
              model.chain.equals(coinModel.chain ?? "") &
              model.isToken.equals(coinModel.isToken!) &
              model.contract.equals(coinModel.contract ?? "") &
              (model.address.lower().equals(address.toLowerCase()));
        } else {
          return model.walletId.equals(walletId) &
              model.chain.equals(coinModel.chain ?? "") &
              model.isToken.equals(coinModel.isToken!) &
              (model.address.lower().equals(address.toLowerCase()));
        }
      })
      ..orderBy([
        // 按 txTime 降序排序，最新的记录首先出现
        (model) =>
            OrderingTerm(expression: model.txTime, mode: OrderingMode.desc),
      ]);

    // 执行查询
    List<TransactionsActivityModel> dataList = await query.get();

    // 将查询结果转换为 TokenActivityModel 列表
    return dataList
        .map<TokenActivityModel>(
            (model) => TokenActivityModel.fromTransactionsActivityModel(model))
        .toList();
  }

  /// 获取广播缓存打包记录
  Future<List<TokenActivityModel>> getTokenActivityPackingList({
    required String walletId,
    required CoinModel coinModel,
    required String address,
    required String? contract,
  }) async {
    final query = select(transactionsActivityTable)
      ..where((model) {
        if (coinModel.isToken == true) {
          return model.walletId.equals(walletId) &
              model.chain.equals(coinModel.chain ?? "") &
              model.isPacking.equals(true) &
              model.isToken.equals(coinModel.isToken!) &
              model.contract.equals(coinModel.contract ?? "") &
              model.address.lower().equals(address.toLowerCase());
        } else {
          return model.walletId.equals(walletId) &
              model.chain.equals(coinModel.chain ?? "") &
              model.isPacking.equals(true) &
              model.isToken.equals(coinModel.isToken!) &
              model.address.lower().equals(address.toLowerCase());
        }
      });

    List<TransactionsActivityModel> dataList = await query.get();
    return dataList.reversed
        .map<TokenActivityModel>(
            (model) => TokenActivityModel.fromTransactionsActivityModel(model))
        .toList();
  }

  /// 广播交易结束增加一条交易记录
  Future addPacking({
    required TransferModel tsModel,
    required CoinType coinType,
    required String txId,
  }) async {
    String amount =
        tsModel.amount!.mul((pow(10, tsModel.coinDecimal!).toString()));

    TransactionsActivityTableCompanion table =
        TransactionsActivityTableCompanion.insert(
      txId: Value(txId),
      isPacking: const Value(true),
      txTime: const Value(null),
      type: const Value('out'),
      action: const Value('transfer'),
      extra: const Value(null),
      walletId: Value(tsModel.walletId),
      chainCode: Value(coinType.id),
      chain: Value(coinType.chain),
      address: Value(tsModel.fromAddress),
      contract: Value(tsModel.contract),
      symbol: Value(tsModel.coinSymbol),
      isToken: Value(tsModel.isToken),
      amount: Value(amount),
      fromAddress: Value(tsModel.fromAddress),
      receiveAddress: Value(tsModel.toAddress),
      blockNumber: const Value(0),
      blockTime: const Value(null),
    );
    return insertOnConflictUpdate(table);
  }

  // 批量插入缓存交易记录
  Future insertBatch(
      {required List<TokenActivityModel> tokenActivityList,
      required String walletId,
      required String address,
      required CoinModel coinModel}) async {
    /// 防止旧的数据库中有错误交易记录，所以先删除旧记录，后插入
    await deleteTokenActivityList(
        walletId: walletId, address: address, coinModel: coinModel);

    if (tokenActivityList.isEmpty) return;

    List<TransactionsActivityTableCompanion> dataList =
        tokenActivityList.map((tokenActivity) {
      int blockTime = 0;
      if (tokenActivity.blockNumber == null || tokenActivity.blockNumber == 0) {
        blockTime = DateHeleper.getCurrentTimeSecond();
      } else {
        blockTime = tokenActivity.txTime! ~/ 1000;
      }
      return TransactionsActivityTableCompanion.insert(
        txId: Value(tokenActivity.txId),
        txTime: Value(tokenActivity.txTime),
        type: Value(tokenActivity.type),
        action: Value(tokenActivity.action),
        walletId: Value(walletId),
        chainCode: Value(coinModel.chainCode),
        chain: Value(coinModel.chain),
        extra: Value(tokenActivity.extraText),
        valid: Value(tokenActivity.valid),
        address: Value(address),
        contract: Value(coinModel.contract),
        symbol: Value(coinModel.symbol),
        isToken: Value(coinModel.isToken),
        amount: Value(tokenActivity.amount),
        fromAddress: Value(tokenActivity.from),
        receiveAddress: Value(tokenActivity.to),
        blockNumber: Value(tokenActivity.blockNumber ?? 0),
        blockTime: Value(blockTime),
        isPacking: Value(tokenActivity.isPacking),
      );
    }).toList();

    await transaction(() async {
      for (var item in dataList) {
        // 检查是否存在相同的txId 和address
        final existing = await (select(transactionsActivityTable)
              ..where((tbl) =>
                  tbl.txId.equals(item.txId.value!) &
                  tbl.address
                      .lower()
                      .equals(item.address.value!.toLowerCase())))
            .get();

        if (existing.isEmpty) {
          // 如果不存在相同的txId，则插入
          await into(transactionsActivityTable).insert(item);
        } else {
          // 如果不存在相同的txId，则更新
          if (item.txId.value != null && item.txId.value != null) {
            final updatedData = TransactionsActivityTableCompanion(
              id: Value(existing.first.id),
              txId: Value(item.txId.value),
              txTime: Value(item.txTime.value),
              type: Value(item.type.value),
              action: Value(item.action.value),
              walletId: Value(item.walletId.value),
              chainCode: Value(item.chainCode.value),
              chain: Value(item.chain.value),
              extra: Value(item.extra.value),
              valid: Value(item.valid.value),
              address: Value(item.address.value),
              contract: Value(item.contract.value),
              symbol: Value(item.symbol.value),
              isToken: Value(item.isToken.value),
              amount: Value(item.amount.value),
              fromAddress: Value(item.fromAddress.value),
              receiveAddress: Value(item.receiveAddress.value),
              blockNumber: Value(item.blockNumber.value),
              blockTime: Value(item.blockTime.value),
              isPacking: Value(item.isPacking.value),
            );
            await updateData(updatedData);
          }
        }
      }
    });
  }

  Future<void> deleteTokenActivityList({
    required String walletId,
    required CoinModel coinModel,
    required String address,
  }) async {
    final query = delete(transactionsActivityTable)
      ..where((model) {
        final baseCondition = model.walletId.equals(walletId) &
            model.chain.equals(coinModel.chain ?? "") &
            model.isToken.equals(coinModel.isToken!) &
            model.address.lower().equals(address.toLowerCase());

        // Check if isPacking is null or false for deletion
        return coinModel.isToken == true
            ? baseCondition &
                model.contract.equals(coinModel.contract ?? "") &
                (model.isPacking.isNull() | model.isPacking.equals(false))
            : baseCondition &
                (model.isPacking.isNull() | model.isPacking.equals(false));
      });

    await query.go();
  }
}
