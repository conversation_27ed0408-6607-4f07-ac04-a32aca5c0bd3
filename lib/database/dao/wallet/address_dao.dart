import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/service/wallet_database_service.dart';
import 'package:coinbag/database/tables/wallet/address_table.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:drift/drift.dart';
import 'package:wallet_core/chain/coin_type.dart';

part 'address_dao.g.dart';

@DriftAccessor(tables: [AddressTable])
class AddressDao extends DatabaseAccessor<AppDatabase> with _$AddressDaoMixin {
  final AppDatabase db;

  AddressDao(this.db) : super(db);

  //查
  Future<List<AddressModel>> getAllData() => select(addressTable).get();

  //查(响应式)
  Stream<List<AddressModel>> watchAllData() => select(addressTable).watch();

  //walletId查
  Future<List<AddressModel>> findAddressesByWalletId(String walletId) {
    return (select(addressTable)
          ..where((address) => address.walletId.equals(walletId)))
        .get();
  }

  /// Eos AddressModel
  Future<AddressModel?> getEosAddressModel({
    required String walletId,
    required String deviceId,
    required String chain,
    required String eosPublickey,
  }) async {
    List<AddressModel> addressModelList = await getAddressModelList(
      deviceId: deviceId,
      walletId: walletId,
      chain: chain,
    );
    addressModelList =
        addressModelList.where((e) => e.publickey == eosPublickey).toList();
    return addressModelList.isNotEmpty ? addressModelList.first : null;
  }

  //增
  Future insertData(Insertable<AddressModel> model) =>
      into(addressTable).insert(model);

  //改无则增
  Future insertOnConflictUpdate(Insertable<AddressModel> model) =>
      into(addressTable).insertOnConflictUpdate(model);

  //改
  Future updateData(Insertable<AddressModel> model) =>
      update(addressTable).replace(model);

  //删
  Future deleteData(Insertable<AddressModel> model) =>
      delete(addressTable).delete(model);

  //根据walletId删除记录
  Future<void> deleteByWalletId(String walletId) async {
    await (delete(addressTable)..where((w) => w.walletId.equals(walletId)))
        .go();
  }

  // 返回所有已存在地址对应的 walletId
  Future<List<String?>> getAllExistsAddressWalletIds(
      List<AddressTableCompanion> addressCompanions) async {
    if (addressCompanions.isEmpty) {
      return []; // 返回空列表而不是 null
    }

    // 构建查询条件
    var customExpression = addressCompanions.map((companion) {
      return addressTable.address
              .lower()
              .equals(companion.address.value!.toLowerCase()) &
          addressTable.chain
              .lower()
              .equals(companion.chain.value!.toLowerCase());
    }).reduce((currentExpression, nextExpression) =>
        currentExpression | nextExpression);

    // 执行查询
    var query = select(addressTable)..where((_) => customExpression);

    // 获取所有结果
    var result = await query.get();

    // 返回所有 walletId，确保不返回 null
    // 如果没有结果，将返回空列表
    return result.map((row) => row.walletId).toList();
  }

  // 批量插入
  Future insertBatch(List<AddressTableCompanion> dataList) async {
    return await transaction(() async {
      for (var data in dataList) {
        final exists = await (select(addressTable)
              ..where((tbl) =>
                  tbl.walletId.equals(data.walletId.value!) &
                  tbl.chain.equals(data.chain.value!) &
                  tbl.address.equals(data.address.value!)))
            .getSingleOrNull();
        if (exists == null) {
          await into(addressTable).insert(data);
        } else {
          // 更新现有记录
          await (update(addressTable)
                ..where((tbl) =>
                    tbl.walletId.equals(data.walletId.value!) &
                    tbl.chain.equals(data.chain.value!) &
                    tbl.address.equals(data.address.value!)))
              .write(data);
        }
      }
    });
  }

  // 批量插入如果存在就不插入
  Future<void> insertBatchIfNotExists(
    List<AddressTableCompanion> dataList,
  ) async {
    await transaction(() async {
      for (var data in dataList) {
        final exists = await (select(addressTable)
              ..where((tbl) =>
                  tbl.walletId.equals(data.walletId.value!) &
                  tbl.chain.equals(data.chain.value!) &
                  tbl.address.equals(data.address.value!)))
            .getSingleOrNull();
        if (exists == null) {
          await into(addressTable).insert(data);
        }
      }
    });
  }

  // 获取addressModel
  Future<AddressModel?> getAddressModel({
    required String walletId,
    required String deviceId,
    required String chain,
    required String address,
  }) async {
    List<AddressModel> addressModelList = await getAddressModelList(
      deviceId: deviceId,
      walletId: walletId,
      chain: chain,
      address: address,
    );
    addressModelList = _unique(addressModelList);
    return addressModelList.isNotEmpty ? addressModelList.first : null;
  }

  // 获取硬件钱包设备下某个链的所有地址，传Address表示获取当前地址的AddressModel
  Future<List<AddressModel>> getAddressModelList(
      {required String deviceId,
      required String walletId,
      required String chain,
      String? address}) async {
    // 获取硬件钱包地址映射
    List<String> addresses = await WalletDatabaseService().getWalletAddressList(
      deviceId: deviceId,
      walletId: walletId,
      chain: chain,
      address: address,
    );

    // 查询 AddressModel
    // 查询匹配的地址
    final addressModelList = await (select(addressTable)
          ..where((tbl) =>
              tbl.walletId.equals(walletId) &
              tbl.chain.equals(chain) &
              tbl.address.isIn(addresses))
          ..orderBy([(t) => OrderingTerm.asc(t.addressIndex)]))
        .get();

    return _unique(addressModelList);
  }

  /// 地址去重
  List<AddressModel> _unique(List<AddressModel> addressModelList) {
    // 使用一个 Set 来追踪已存在的唯一键
    final Set<String> uniqueKeys = {};
    final List<AddressModel> uniqueAddresses = [];

    for (final model in addressModelList) {
      // 创建一个唯一键，基于 chain 和 address
      final String key = '${model.chain}_${model.address}';

      // 如果这个键还不存在，则添加到唯一地址列表
      if (!uniqueKeys.contains(key)) {
        uniqueKeys.add(key);
        uniqueAddresses.add(model);
      }
    }
    return uniqueAddresses;
  }

  /// 返回第一个地址
  Future<AddressModel?> getDefaultAddressModel({
    required String walletId,
    required String deviceId,
    required String chain,
  }) async {
    List<AddressModel> addressModelList = await getAddressModelList(
      deviceId: deviceId,
      walletId: walletId,
      chain: chain,
    );
    return addressModelList.isNotEmpty ? addressModelList.first : null;
  }

  /// 首页更新币价
  Future<void> updateBalances(List<AddressModel> addressList,
      BalanceModel balanceModel, CoinType coinType,
      {bool hasUnconfirmed = false}) async {
    // 转换 balanceModel 中的地址为小写
    final lowerCaseBalanceAddress = balanceModel.address?.toLowerCase();

    // 如果 balanceModel 的地址为空或列表为空，直接返回
    if (lowerCaseBalanceAddress == null || addressList.isEmpty) {
      return;
    }

    // 在列表中查找匹配的 AddressModel

    AddressModel? matchingAddressModel;
    try {
      matchingAddressModel = addressList.firstWhere((address) =>
          address.address!.toLowerCase() == lowerCaseBalanceAddress);
    } on StateError {
      matchingAddressModel = null; // 如果没有找到，捕获异常并设置 coin 为 null
    }
    if (matchingAddressModel == null) {
      return;
    }

    // 计算余额
    String? balance = DecimalUtils.getBalance(
      rawBalance: balanceModel.balance,
      decimal: balanceModel.decimal!,
      balanceDecimal: coinType.balanceDecimals,
    );

    // 构建更新的数据对象，可能是一个伴随类或直接是实体类
    final updatedAddress = AddressTableCompanion(
      id: Value(matchingAddressModel.id),
      balance: Value(balance),
    );

    await updateData(updatedAddress);
  }

//币种详情更新币价
  Future<void> updateAddressBalances(
      AddressModel addressModel, String balance) async {
    if (!FileUtils.isNum(balance)) {
      return;
    }
    final updatedAddress = AddressTableCompanion(
      id: Value(addressModel.id),
      balance: Value(balance),
    );

    await updateData(updatedAddress);
  }

  /// 更新utxo balance
  Future<void> updateUtxoBalance({
    required AddressModel addressModel,
    required String? totalBalance,
    required String? availableBalance,
    required String? unavailableBalance,
  }) async {
    final model = AddressTableCompanion(
      id: Value(addressModel.id),
      totalBalance: Value(totalBalance),
      availableBalance: Value(availableBalance),
      unavailableBalance: Value(unavailableBalance),
    );
    await updateData(model);
  }

  /// 更新地址备注
  Future<void> updateAddressLabel(
      AddressModel addressModel, String addressLabel) async {
    final updatedAddress = AddressTableCompanion(
      id: Value(addressModel.id),
      addressLabel: Value(addressLabel),
    );

    await updateData(updatedAddress);
  }

  /// 更新 sortedId
  Future<void> updateAddressSortedId(
      AddressModel addressModel, int? sortedId) async {
    final updatedAddress = AddressTableCompanion(
      id: Value(addressModel.id),
      sortedId: Value(sortedId),
    );

    await updateData(updatedAddress);
  }

  /// 缓存EOS Tron账户详情
  Future<void> updateEOSAccountInfo(AddressModel addressModel, String? balance,
      String? eosAccountInfo) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      balance: Value(balance!),
      eosAccountInfoCache: Value(eosAccountInfo),
    );

    await insertOnConflictUpdate(data);
  }

  /// 缓存Torn账户信息
  Future<void> updateTronAccountInfo(
      AddressModel addressModel, String? accountInfo) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      tronAccountInfoCache: Value(accountInfo),
    );

    await insertOnConflictUpdate(data);
  }

  /// 缓存Torn资源详情
  Future<void> updateTronResource(
      AddressModel addressModel, String? json) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      tronResourceCache: Value(json),
    );

    await insertOnConflictUpdate(data);
  }

  Future<void> updateUtxos(
    AddressModel addressModel,
    String? utxosValue,
  ) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      utxoCache: Value(utxosValue),
    );

    await insertOnConflictUpdate(data);
  }

  Future<String?> getutxoCache({
    required String walletId,
    required String deviceId,
    required String chain,
    required String address,
  }) async {
    AddressModel? addressModel = await getAddressModel(
      walletId: walletId,
      deviceId: deviceId,
      chain: chain,
      address: address,
    );
    return addressModel!.utxoCache ?? "";
  }

  Future<String?> getNftAssetsCache({
    required String walletId,
    required String deviceId,
    required String chain,
    required String address,
  }) async {
    AddressModel? addressModel = await getAddressModel(
      walletId: walletId,
      deviceId: walletId,
      chain: chain,
      address: address,
    );
    return addressModel!.nftAssetsCache;
  }

  Future<void> updateNftAssets(
    AddressModel addressModel,
    String? assetsJson,
  ) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      nftAssetsCache: Value(assetsJson),
    );

    await insertOnConflictUpdate(data);
  }

  Future<void> updateNftCollection(
    AddressModel addressModel,
    String? collectionJson,
  ) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      nftCollectionCache: Value(collectionJson),
    );

    await insertOnConflictUpdate(data);
  }

  /// 缓存sol 支付对方租金
  Future<void> updateSolMinimumRent(
    AddressModel addressModel,
    String? minimumRent,
  ) async {
    final data = AddressTableCompanion(
      id: Value(addressModel.id),
      solMinimumRent: Value(minimumRent),
    );

    await insertOnConflictUpdate(data);
  }
}
