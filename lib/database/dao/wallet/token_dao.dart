/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-12-11 17:57:53
 */
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/tables/wallet/token_table.dart';
import 'package:coinbag/modules/wallet/home/<USER>/balance/balance_model.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:common_utils/common_utils.dart';
import 'package:drift/drift.dart';
import 'package:wallet_core/chain/coin_type.dart';
import 'package:wallet_core/chain/ethereum/eth.dart';

part 'token_dao.g.dart';

@DriftAccessor(tables: [TokenTable])
class TokenDao extends DatabaseAccessor<AppDatabase> with _$TokenDaoMixin {
  final AppDatabase db;

  TokenDao(this.db) : super(db);

  //查(非响应式)
  Future<List<TokenModel>> getAllData() => select(tokenTable).get();

  //查(响应式)
  Stream<List<TokenModel>> watchAllData() => select(tokenTable).watch();

  //增
  Future insertData(Insertable<TokenModel> model) =>
      into(tokenTable).insert(model);

  //改无则增
  Future insertOnConflictUpdate(Insertable<TokenModel> model) =>
      into(tokenTable).insertOnConflictUpdate(model);

  //改
  Future updateData(Insertable<TokenModel> model) =>
      update(tokenTable).replace(model);

  //删
  Future deleteData(Insertable<TokenModel> model) =>
      delete(tokenTable).delete(model);

  //根据walletId删除记录
  Future<void> deleteByWalletId(String walletId) async {
    await (delete(tokenTable)..where((w) => w.walletId.equals(walletId))).go();
  }

  // 批量插入
  Future insertBatch(List<TokenTableCompanion> dataList) async {
    return transaction(() async {
      await batch((batch) {
        batch.insertAll(tokenTable, dataList, mode: InsertMode.insertOrReplace);
      });
    });
  }

  /// 地址去重
  List<TokenModel> _unique(List<TokenModel> tokenModelList) {
    // 使用一个 Set 来追踪已存在的唯一键
    final Set<String> uniqueKeys = {};
    final List<TokenModel> uniqueAddresses = [];

    for (final model in tokenModelList) {
      // 创建一个唯一键，基于 chain 和 address
      final String key = '${model.chain}_${model.address}_${model.contract}';

      // 如果这个键还不存在，则添加到唯一地址列表
      if (!uniqueKeys.contains(key)) {
        uniqueKeys.add(key);
        uniqueAddresses.add(model);
      }
    }
    return uniqueAddresses;
  }

  // 获取Token 列表 - token管理使用
  Future<List<TokenModel>> getTokenListByContract({
    required String walletId,
    required String chain,
    required String contract,
  }) async {
    List<TokenModel> tokenList = await (select(tokenTable)
          ..where((token) =>
              token.walletId.equals(walletId) &
              token.chain.lower().equals(chain.toLowerCase()) &
              token.contract.lower().equals(contract.toLowerCase())))
        .get();
    return _unique(tokenList);
  }

  // 获取Token
  Future<TokenModel?> getTokenModel({
    required String walletId,
    required String address,
    required String contract,
    required String chain,
  }) async {
    List<TokenModel> tokenList = await (select(tokenTable)
          ..where((token) =>
              token.walletId.equals(walletId) &
              token.chain.lower().equals(chain.toLowerCase()) &
              token.address.lower().equals(address.toLowerCase()) &
              token.contract.lower().equals(contract.toLowerCase())))
        .get();

    tokenList = _unique(tokenList);

    if (tokenList.isNotEmpty) {
      return tokenList.first;
    }

    return null;
  }

  // 插入Token
  Future<void> intertToken(
    BalanceModel balanceModel,
    CoinType coinType,
    String walletId,
    String addressLabel,
  ) async {
    // 计算余额
    final balance = DecimalUtils.getBalance(
      rawBalance: balanceModel.balance,
      decimal: balanceModel.decimal!,
      balanceDecimal: coinType.balanceDecimals,
    );

    String address = balanceModel.address!;

    if (coinType.isEthereumSeries && !ObjectUtil.isEmptyString(address)) {
      address = EthereumChain.get.toEIP55(address);
    }

    insertOnConflictUpdate(TokenTableCompanion(
        walletId: Value(walletId),
        chain: Value(coinType.chain),
        chainCode: Value(coinType.id),
        address: Value(address),
        contract: Value(balanceModel.contract!),
        balance: Value(balance),
        addressLabel: Value(addressLabel),
        tokenType: Value(balanceModel.tokenType),
        derivedAddresses: Value(balanceModel.derivedAddresses)));
  }

//首页更新币价
  Future<void> updateBalances(TokenModel tokenModel, BalanceModel balanceModel,
      CoinType coinType) async {
    // 转换 balanceModel 中的地址为小写
    final lowerCaseBalanceAddress = balanceModel.address?.toLowerCase();

    // 如果 balanceModel 的地址为空或列表为空，直接返回
    if (lowerCaseBalanceAddress == null) {
      return;
    }

    // 计算余额
    final balance = DecimalUtils.getBalance(
      rawBalance: balanceModel.balance,
      decimal: balanceModel.decimal!,
      balanceDecimal: coinType.balanceDecimals,
    );

    // 构建更新的数据对象，可能是一个伴随类或直接是实体类
    final updatedAddress = TokenTableCompanion(
      id: Value(tokenModel.id),
      balance: Value(balance),
    );

    await updateData(updatedAddress);
  }

  /// 币种详情更新币价
  Future<void> updateAddressBalances(
      TokenModel tokenModel, String balance) async {
    if (!FileUtils.isNum(balance)) {
      return;
    }
    final updatedAddress = TokenTableCompanion(
      id: Value(tokenModel.id),
      balance: Value(balance),
    );

    await updateData(updatedAddress);
  }

  /// derivedAddresses
  Future<void> updateDerivedAddresses(
      TokenModel tokenModel, String derivedAddresses) async {
    final token = TokenTableCompanion(
      id: Value(tokenModel.id),
      derivedAddresses: Value(derivedAddresses),
    );

    await updateData(token);
  }

  /// 更新tokenType
  Future<void> updateTokenType(TokenModel tokenModel, int? tokenType) async {
    final token = TokenTableCompanion(
      id: Value(tokenModel.id),
      tokenType: Value(tokenType),
    );

    await updateData(token);
  }
}
