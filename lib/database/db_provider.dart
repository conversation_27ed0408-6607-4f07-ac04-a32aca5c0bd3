/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-28 22:34:53
 * @LastEditTime: 2024-11-27 17:46:02
 */

import 'dart:io';

import 'package:coinbag/database/dao/contacts/address_book_dao.dart';
import 'package:coinbag/database/dao/dapp/dapp_dao.dart';
import 'package:coinbag/database/dao/wallet/address_dao.dart';
import 'package:coinbag/database/dao/wallet/address_mapping_dao.dart';
import 'package:coinbag/database/dao/wallet/coin_dao.dart';
import 'package:coinbag/database/dao/wallet/monitor_dao.dart';
import 'package:coinbag/database/dao/wallet/token_dao.dart';
import 'package:coinbag/database/dao/wallet/transactions_activity_dao.dart';
import 'package:coinbag/database/dao/wallet/wallet_dao.dart';
import 'package:coinbag/database/tables/contacts/address_book_table.dart';
import 'package:coinbag/database/tables/dapp/dapp_table.dart';
import 'package:coinbag/database/tables/wallet/address_mapping_table.dart';
import 'package:coinbag/database/tables/wallet/address_table.dart';
import 'package:coinbag/database/tables/wallet/coin_table.dart';
import 'package:coinbag/database/tables/wallet/monitor_table.dart';
import 'package:coinbag/database/tables/wallet/token_table.dart';
import 'package:coinbag/database/tables/wallet/transactions_activity_table.dart';
import 'package:coinbag/database/tables/wallet/wallet_table.dart';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart' as pp;

part 'db_provider.g.dart';

@DriftDatabase(tables: [
  AddressBookTable,
  MonitorTable,
  CoinTable,
  WalletTable,
  AddressTable,
  TokenTable,
  DappTable,
  TransactionsActivityTable,
  AddressMappingTable,
], daos: [
  AddressBookDao,
  MonitorDao,
  CoinDao,
  WalletDao,
  AddressDao,
  TokenDao,
  DappDao,
  TransactionsActivityDao,
  AddressMappingDao,
], include: {
  './drift/sql.drift'
})
class AppDatabase extends _$AppDatabase {
  // 私有构造函数
  AppDatabase._internal(super.e);

  // 私有静态变量
  static AppDatabase? _instance;

  // 公共的工厂构造函数
  factory AppDatabase(QueryExecutor e) {
    return _instance ??= AppDatabase._internal(e);
  }

  @override
  int get schemaVersion => 7;

  @override
  MigrationStrategy get migration => MigrationStrategy(
          onUpgrade: (Migrator m, int oldVersion, int newVersion) async {
        if (oldVersion < 2) {
          await addColumnsVersion1(m);
        }
        if (oldVersion < 3) {
          await addColumnsVersion2(m);
        }
        if (oldVersion < 4) {
          await addColumnsVersion3(m);
        }
        // 在版本 5 时创建新表
        if (oldVersion < 5) {
          await m.createTable(addressMappingTable);
          await syncAddressMappingTable(m);
        }
        if (oldVersion < 6) {
          await addColumnsVersion4(m);
        }
        if (oldVersion < 7) {
          await addColumnsVersion5(m);
        }
      });

  Future<void> addColumnsVersion1(Migrator m) async {
    final columnsToAdd = [
      addressTable.eosAccountInfoCache,
      addressTable.tronAccountInfoCache,
      addressTable.tronResourceCache,
      addressTable.utxoCache,
    ];
    for (var column in columnsToAdd) {
      await m.addColumn(addressTable, column);
    }
  }

  Future<void> addColumnsVersion2(Migrator m) async {
    final columnsToAdd = [
      dappTable.dappId,
      dappTable.dappName,
      dappTable.dappLabel,
      dappTable.dappUrl,
      dappTable.dappInfo,
      dappTable.symbol,
      dappTable.dappLogoUrl,
      dappTable.btcDappJs,
      dappTable.netWork,
      dappTable.slip44,
      dappTable.dappMode,
      dappTable.lastTime,
      dappTable.sortIndex,
      dappTable.isApprove,
      dappTable.isBan,
    ];
    for (var column in columnsToAdd) {
      await m.addColumn(dappTable, column);
    }
  }

  Future<void> addColumnsVersion3(Migrator m) async {
    final columnsToAdd = [
      transactionsActivityTable.detailsCache,
    ];
    for (var column in columnsToAdd) {
      await m.addColumn(transactionsActivityTable, column);
    }
  }

  Future<void> syncAddressMappingTable(Migrator m) async {
    // 1. 从 wallettable 中获取 deviceId 和 walletId
    final wallets = await select(walletTable).get();

    if (wallets.isEmpty) {
      return;
    }

    // 2. 遍历所有钱包，查找对应的地址并同步到 addressMappingTable
    for (final wallet in wallets) {
      String? walletId = wallet.walletId;
      String? deviceId = wallet.deviceId;
      if (walletId == null || deviceId == null) {
        continue;
      }

      // 3. 根据 walletId 查找 addressTable 中的所有地址
      final addressModelList = await (select(addressTable)
            ..where((t) => t.walletId.equals(walletId))
            ..orderBy([(t) => OrderingTerm.asc(t.addressIndex)]))
          .get();

      // 使用一个 Set 来追踪已存在的唯一键
      final Set<String> uniqueKeys = {};
      final List<AddressModel> uniqueAddresses = [];

      for (final model in addressModelList) {
        // 创建一个唯一键，基于 chain 和 address
        final String key = '${model.chain}_${model.address}';

        // 如果这个键还不存在，则添加到唯一地址列表
        if (!uniqueKeys.contains(key)) {
          uniqueKeys.add(key);
          uniqueAddresses.add(model);
        }
      }

      // 4. 将找到的地址同步到 addressMappingTable
      for (final address in uniqueAddresses) {
        final newAddressMapping = AddressMappingTableCompanion(
          deviceId: Value(deviceId), // 从 wallet 获取 deviceId
          walletId: Value(walletId), // 当前的 walletId
          chain: Value(address.chain), // 从 address 获取链
          address: Value(address.address), // 从 address 获取地址
          addressIndex: Value(address.addressIndex), // 从 address 获取地址编号
        );

        await into(addressMappingTable).insert(newAddressMapping);
      }
    }
  }

  Future<void> addColumnsVersion4(Migrator m) async {
    await m.addColumn(addressTable, addressTable.solMinimumRent);
  }

  Future<void> addColumnsVersion5(Migrator m) async {
    await m.addColumn(tokenTable, tokenTable.derivedAddresses);
  }
}

LazyDatabase openConnection() {
  // 我们创建一个LazyDatabase，它在需要时才会打开数据库。
  return LazyDatabase(() async {
    // 获取应用的文档目录，并使用它来构建数据库文件的路径。
    final dbFolder = await pp.getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'coin_bag.sqlite'));
    return NativeDatabase(file);
  });
}
