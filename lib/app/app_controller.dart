/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-26 09:13:26
 * @LastEditTime: 2024-12-04 14:33:59
 */

import 'dart:convert';

import 'package:audioplayers/audioplayers.dart';
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/service/wallet_database_service.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/apiService/config_service.dart';
import 'package:coinbag/http/response/base_response.dart';
import 'package:coinbag/modules/locale/locale_controller.dart';
import 'package:coinbag/modules/locale/models/language_model.dart';
import 'package:coinbag/modules/profile/qr/qr_controller.dart';
import 'package:coinbag/modules/profile/qr/qr_model.dart';
import 'package:coinbag/modules/profile/upgrade/model/upgrade_model/upgrade_model.dart';
import 'package:coinbag/modules/wallet/connect/models/address/address_info.dart';
import 'package:coinbag/modules/wallet/connect/models/address/wallet_upload_model.dart';
import 'package:coinbag/modules/wallet/connect/models/monitorinfo_model.dart';
import 'package:coinbag/modules/wallet/home/<USER>/usd_rate_model.dart';
import 'package:coinbag/res/strings.dart';
import 'package:coinbag/route/routes.dart';
import 'package:coinbag/utils/aes_helper.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/deveice_utils.dart';
import 'package:coinbag/utils/file_utils.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/utils/package_info_manager.dart';
import 'package:drift/drift.dart' as drift;
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_app_update/flutter_app_update.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:get/get.dart';
import 'package:local_auth_platform_interface/types/biometric_type.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:wallet_core/chain/all/all_chain.dart';
import 'package:wallet_core/chain/chain.dart';
import 'package:wallet_core/encrypt/key/encrypt_key.dart';
import 'package:wallet_core/wallet/cold/ultra_plus_wallet.dart';
import 'package:wallet_core/wallet/wallet.dart';

class AppController extends BaseController<ApiService> {
  bool isPlayingSound = false; //是否在播放

  late AudioPlayer _audioPlayer;

  @override
  void onInit() {
    super.onInit();
    _audioPlayer = AudioPlayer();
    // 确保 api 字段已初始化，防止 LateInitializationError
    try {
      api = Get.find<ApiService>();
    } catch (e) {
      Log.e(
          'AppController.onInit: ApiService not found, please ensure it is injected.');
    }
  }

  AudioPlayer get player => _audioPlayer;

  @override
  void loadData() {}

  /// 语言
  static Rx<LanguageModel> languageModel =
      LocaleController.defaultLanguageModel().obs;

  /// 二维码容量
  static Rx<QRModel> qrModel = QRController.qrModel.obs;

  /// 货币单位
  static Rx<Currency> currency = CurrencyController.getCurrency.obs;

  /// 刷新首页资产
  static Rx<bool> refreshHomeAssets = false.obs;

  /// 是否连接钱包或者断开连接
  static Rx<bool> isConnencWallet = false.obs;

  /// 刷新首页资产是否展示Loading
  static Rx<bool> homeShowLoading = false.obs;

  /// 刷新首页资产是否置顶
  static Rx<bool> homeScrollToTop = false.obs;

  /// 刷新交易记录
  static Rx<bool> refreshTxActivity = false.obs;

  /// 刷新NFT
  static Rx<bool> isRefreshNFT = false.obs;

  /// 刷新NFT wallet
  static Rx<bool> isRefreshNFTWallet = false.obs;

  /// dapp取消交易
  static Rx<bool> cancelTx = false.obs;

  /// 生物识别是否可用
  static bool isBiometric = true;

  /// 升级后更新 UpgradDialog Button
  static Rx<bool> isAppUpgrading = false.obs;

  /// 来自dapp的签名消息
  static Rx<bool> isDappSignMessageFlag = false.obs;

  /// 汇率
  static Rx<UsdRateModel?> usdRateModel = usdRate.obs;

  /// 当前生物识别类型
  static BiometricType biometricType = BiometricType.fingerprint;

  /// NFC是否可用
  static NFCAvailability nfcAvailability = NFCAvailability.available;

  ///升级model
  static Rx<UpgradeModel?> upgradeModel = Rx<UpgradeModel?>(null);

  ///VIP引导Key
  final GlobalKey guildeOne = GlobalKey();
  final GlobalKey guildeTwo = GlobalKey();

  /// 刷新首页
  static void refreshHome(
      {bool isLoading = false,
      bool isScrollToTop = false,
      bool isConnectWallet = false}) {
    homeShowLoading.value = isLoading;
    homeScrollToTop.value = isScrollToTop;
    refreshHomeAssets.value = true;
    isConnencWallet.value = isConnectWallet;
  }

  /// dapp取消交易
  static void cancelTxAction() {
    cancelTx.value = true;
  }

  /// 刷新交易记录
  static void refreshActicvity() {
    refreshTxActivity.value = true;
  }

  static void refreshNft() {
    isRefreshNFT.value = !isRefreshNFT.value;
  }

  static void refreshNftWallet() {
    isRefreshNFTWallet.value = !isRefreshNFTWallet.value;
  }

  /// 初始化BaseUrl
  void initCoinfigApi() {
    httpRequest<dynamic>(
        showToast: false,
        Get.find<ConfigService>().getConfigBaseUrl(), (value) {
      if (value != null) {
        String baseUrl = value.domain!.api;
        String blockChainUrl = value.domain!.blockchain;
        StorageManager.saveValue(key: GetKey.apiBaseUrl, value: baseUrl);
        StorageManager.saveValue(
            key: GetKey.blockBaseUrl, value: blockChainUrl);
      }
    });
  }

  /// 初始化默认支持的币种到数据库，只首次启动会执行
  Future<void> initCoinBase() async {
    List<CoinType> allChains = List<CoinType>.from(CoinBase.allChains);
    allChains.removeWhere((chain) => chain is AllChain);
    List<CoinTableCompanion> coinModelList = allChains
        .map((coinType) => CoinTableCompanion(
              chain: drift.Value(coinType.chain),
              chainId: drift.Value(coinType.chainId ?? 0),
              chainCode: drift.Value(coinType.id),
              chainName: drift.Value(coinType.chainName),
              symbol: drift.Value(coinType.symbol),
              cnName: drift.Value(coinType.cnName),
              isSupportToken: drift.Value(coinType.isSupportToken),
              isCoinSupported: const drift.Value(true),
              isToken: const drift.Value(false),
              symbolIcon: drift.Value(coinType.symbolIcon),
              chainIcon: drift.Value(coinType.chainIcon),
              disabledIcon: drift.Value(coinType.disabledIcon),
              chainDecimal: drift.Value(coinType.decimals),
              balanceDecimal: drift.Value(coinType.balanceDecimals),
              sortedId: drift.Value(coinType.sortIndex.toDouble()),
            ))
        .toList();

    await Get.find<AppDatabase>().coinDao.insertBatch(coinModelList);
  }

  Future<void> upload(
    WalletTableCompanion walletModel,
    List<MonitorinfoModel> monitorList,
    List<AddressTableCompanion> addresList,
  ) async {
    await Future.wait([
      uploadMonitorMsg(monitorList),
      uploadWalletAddress(addresList, walletModel),
    ]);
  }

  Future<void> uploadMonitorMsg(List<MonitorinfoModel> monitorList) async {
    String content = jsonEncode(monitorList.map((e) => e.toJson()).toList());
    Log.r("uploadMonitorMsg=$content");
    content =
        AesHelpler.encryptPKCS7(content, mainKey: EncryptKey.keyUploadUserData);

    httpRequest<BaseResponse<dynamic>>(
        handleError: true,
        showToast: false,
        api.uploadMonitorMessage(content), (value) {
      if (value.code != APIConstant.responseCode) {
        StorageManager.saveValue(
            key: StorageKey.monitorinfoModel, value: content);
      }
    }, error: (e) {
      StorageManager.saveValue(
          key: StorageKey.monitorinfoModel, value: content);
    });
  }

  Future<void> uploadWalletAddress(
    List<AddressTableCompanion> addresList,
    WalletTableCompanion walletModel,
  ) async {
    if (addresList.isEmpty) {
      return;
    }

    WalletUploadModel walletUploadModel = WalletUploadModel();
    walletUploadModel.walletId = walletModel.walletId.value; // 账户ID
    walletUploadModel.batchCode = walletModel.batchId.value.toString();
    walletUploadModel.deviceCode = walletModel.deviceId.value; // 设备ID
    walletUploadModel.walletAccount = walletModel.walletName.value; // 账户名
    walletUploadModel.time = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    walletUploadModel.phoneId = await DeviceUtils.getDeviceInfo();
    walletUploadModel.platform = GetPlatform.isAndroid ? 0 : 1;
    walletUploadModel.uid = 0;
    walletUploadModel.walletAccount == walletModel.deviceType.value;
    walletUploadModel.uuid = await DeviceUtils.getDeviceId();
    walletUploadModel.isRoot = 0;
    walletUploadModel.hotWalletType = 0;
    walletUploadModel.seedType = 0;
    walletUploadModel.vipLevel = walletModel.vipLevel.value;
    List<AddressInfo> addressInfoList = [];
    for (int i = 0; i < addresList.length; i++) {
      AddressTableCompanion addressModel = addresList[i];
      AddressInfo model = AddressInfo(); // Dart 对象实例化
      model.coinAddress = addressModel.address.value;
      model.slip44 = '${addressModel.slip44Id.value}';
      model.coinCode = addressModel.chainCode.value;
      model.remark = addressModel.addressLabel.value;
      addressInfoList.add(model);
    }
    walletUploadModel.addressInfo = addressInfoList;
    String content = jsonEncode(walletUploadModel.toJson());
    Log.r("uploadWalletAddress=$content");
    content =
        AesHelpler.encryptPKCS7(content, mainKey: EncryptKey.keyUploadUserData);
    httpRequest<BaseResponse<dynamic>>(
        handleError: true,
        showToast: false,
        api.uploadWalletAddress(content), (value) {
      if (value.code != APIConstant.responseCode) {
        StorageManager.saveValue(key: StorageKey.walletAddress, value: content);
      }
    }, error: (e) {
      StorageManager.saveValue(key: StorageKey.walletAddress, value: content);
    });
  }

  ///用户未上报监控信息，在上报一次
  Future<void> uploadUserData() async {
    final monitorInfo =
        StorageManager.getValue(key: StorageKey.monitorinfoModel);
    final walletAddress =
        StorageManager.getValue(key: StorageKey.walletAddress);

    if (!Get.isEmptyString(monitorInfo)) {
      httpRequest<BaseResponse<dynamic>>(
          showToast: false, api.uploadMonitorMessage(monitorInfo), (value) {
        if (value.code == APIConstant.responseCode) {
          StorageManager.remove(key: StorageKey.monitorinfoModel);
        }
      });
    }

    if (!Get.isEmptyString(walletAddress)) {
      httpRequest<BaseResponse<dynamic>>(
          showToast: false, api.uploadWalletAddress(walletAddress), (value) {
        if (value.code == APIConstant.responseCode) {
          StorageManager.remove(key: StorageKey.walletAddress);
        }
      });
    }
  }

  ///主页检查更新
  Future checkUpgrade({bool isShowUpgradeDialog = true}) async {
    httpRequest<BaseResponse<dynamic>>(
        showToast: true,
        Get.find<ApiService>().checkUpgrade(
            PackageInfoManager().version,
            GetPlatform.isAndroid ? "Android" : "IOS",
            AppConfig.instance.getFlavor), (value) {
      if (value.data != null) {
        UpgradeModel model =
            UpgradeModel.fromJson(Map<String, dynamic>.from(value.data));
        upgradeModel.value = model;
        if (upgradeModel.value != null &&
            upgradeModel.value!.hasNewVersion! &&
            isShowUpgradeDialog) {
          Get.showUpgradeDialog();
        }
      }
    });
  }

  ///关于页面检查版本
  void checkVersion() {
    if (upgradeModel.value == null) {
      checkUpgrade(isShowUpgradeDialog: false);
      return;
    }
    if (hasNewVersion()) {
      Get.showUpgradeDialog();
    } else {
      Get.showAlertDialog(
          title: ID.stringNotices.tr, content: ID.stringHaveLatestVersion.tr);
    }
  }

  ///是否有新版本
  bool hasNewVersion() {
    return upgradeModel.value != null && upgradeModel.value!.hasNewVersion!;
  }

  void upgrade(UpgradeModel? upgradeModel) {
    if (upgradeModel == null) return; // Ensure upgradeModel is not null

    bool isMandatoryUpdate =
        upgradeModel.isMandatoryUpdate ?? false; // Default to false if null
    bool isAndroid = GetPlatform.isAndroid;
    bool isOfficialFlavor = AppConfig.instance.getFlavor == Flavor.official;
    bool isGooglePlayFlavor = AppConfig.instance.getFlavor == Flavor.googlePlay;

    if (isAndroid && isOfficialFlavor) {
      // If not a mandatory update, dismiss dialog
      if (!isMandatoryUpdate) {
        Get.dismissDialog();
      }

      AppController.isAppUpgrading.value = true;
      Get.showToast(ID.stringBackgroundUpgrade.tr);
      appUpdate(upgradeModel);
    } else {
      if (!isMandatoryUpdate) {
        Get.dismissDialog();
      } else {
        AppController.isAppUpgrading.value = true;
      }
      if (isAndroid && isGooglePlayFlavor) {
        FileUtils.launchAppStore(upgradeModel.downloadLink!);
      } else {
        appUpdate(upgradeModel);
      }
    }
  }

  void appUpdate(UpgradeModel? upgradeModel) {
    UpdateModel model = UpdateModel(
      upgradeModel!.downloadLink!,
      FileUtils.getUrlfileName(upgradeModel.downloadLink!),
      "ic_launcher",
      upgradeModel.downloadLink!,
    );
    AzhonAppUpdate.update(model);
  }

  bool isVip(WalletModel? walletModel) {
    return walletModel != null &&
        walletModel.vipLevel != null &&
        walletModel.vipLevel! > 0;
  }

  Future<void> play(String url) async {
    if (!isPlayingSound) {
      isPlayingSound = true;
      await player.play(AssetSource(url));
      isPlayingSound = false;
    }
  }

  void stop() {
    if (isPlayingSound) {
      _audioPlayer.stop();
    }
  }

  static UsdRateModel? get usdRate {
    return StorageManager.getObject(
        key: StorageKey.rateModel, fromJson: UsdRateModel.fromJson);
  }

  Future<void> unBindWallet(
      {required String walletId, required String deviceId}) async {
    refreshHomeAssets.value = true;
    isConnencWallet.value = true;

    await WalletDatabaseService()
        .unBindWallet(walletId: walletId, deviceId: deviceId);
  }

  /// 设置屏幕亮度 默认为 0.5
  static Future<void> setBrightness({
    double brightness = CommonConstant.brightness,
  }) async {
    try {
      double current = await ScreenBrightness.instance.application;
      if (current < brightness) {
        await ScreenBrightness.instance
            .setApplicationScreenBrightness(brightness);
      }
    } catch (_) {}
  }

  /// 重置屏幕亮度
  static Future<void> resetBrightness() async {
    try {
      if (Get.currentRoute == AppRoutes.walletQRCodePage) {
        await setBrightness();
      } else {
        await ScreenBrightness.instance.resetApplicationScreenBrightness();
      }
    } catch (_) {}
  }

  /// 启动VIP引导
  void showVIPCase(Wallet wallet, WalletModel? walletModel) {
    // 检查钱包类型和是否已展示VIP引导
    if (!isVip(walletModel) ||
        wallet is! UltraPlusWallet ||
        AppConfig.instance.isTestMode ||
        Get.isShowVIPGuide()) {
      return;
    }

    // 防止重复展示对话框
    if (Get.isDialogOpen == true) {
      return;
    }

    // 延迟启动引导
    Future.delayed(const Duration(milliseconds: 1000), () {
      StorageManager.saveValue(key: StorageKey.isShowVIPGuide, value: true);
      startShowCase([Get.appController.guildeOne]);
    });
  }

  /// 启动引导具体方法
  void startShowCase(List<GlobalKey> widgetIds) {
    ShowCaseWidget.of(Get.context!).startShowCase(widgetIds);
  }

  ///加载测试数据
  static Future<String> loadTestWalletData() async {
    try {
      String testData = await rootBundle.loadString('assets/test/ultra.json');
      return testData;
    } catch (e) {
      return '';
    }
  }

  @override
  void onHidden() {
    stop();
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }
}
