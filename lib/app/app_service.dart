/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-12-03 15:53:17
 */

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/http/apiService/api_service.dart';
import 'package:coinbag/http/apiService/block_chain_service.dart';
import 'package:coinbag/http/apiService/config_service.dart';
import 'package:coinbag/modules/profile/auth/auth_manager.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_controller.dart';
import 'package:coinbag/modules/wallet/connect/connect_wallet_controller.dart';
import 'package:coinbag/utils/package_info_manager.dart';
import 'package:coinbag/utils/permissions_service.dart';
import 'package:coinbag/widgets/dialog/dialog_loading.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_update/flutter_app_update.dart';
import 'package:flutter_app_update/result_model.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:wallet_core/wallet_core.dart';

//SDK初始化服务
//可以使用初始化注入对象Get.lazyPut， 其作用是实现懒加载，即在真正需要使用对象的时候才去创建它，
// 而不是在应用程序启动的时候就创建。这可以有效地减少应用程序的启动时间，提高性能
class AppService extends GetxService {
  Future<AppService> init() async {
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    LogUtil.init(isDebug: kDebugMode, tag: 'Tag:', maxLen: 1000);
    //关闭软件盘
    SystemChannels.textInput.invokeMethod('TextInput.hide');
    Get.lazyPut<AppDatabase>(
      () => AppDatabase(openConnection()),
      fenix: true,
    );

    await Future.wait([
      PackageInfoManager().init(),
      GetStorage.init(),
      //JPushManager.instance.setup(),
    ]);

    await dotenv.load(fileName: "assets/env/.env", mergeWith: {
      'TEST_VAR': '5',
    });

    Get.lazyPut(() => ConfigService(), fenix: true);
    Get.lazyPut(() => ApiService(), fenix: true);
    Get.lazyPut(() => AppController(), fenix: true);
    Get.lazyPut(() => ConnectWalletController(), fenix: true);
    Get.lazyPut(() => BlockChainService(), fenix: true);
    Get.lazyPut(() => PermissionsService(), fenix: true);
    Get.lazyPut(() => WalletCore(), fenix: true);
    Get.lazyPut(() => ChainManagerController(), fenix: true);

    // await LocaleController.updateLanguage();
    AppController.isBiometric = await AuthManager.checkBiometrics();
    AppController.biometricType = await AuthManager.biometricType();

    LoadingDialog().init();
    AzhonAppUpdate.listener((ResultModel model) {});
    return this;
  }
}
