import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/file_utils.dart';

enum AppConfigType {
  dev,
  testnet,
  testnetDev,
  product,
  googleplay,
  testFlight,
  test
}

class AppConfig {
  static final AppConfig instance = AppConfig._internal();

  ///构造函数私有化，防止被误创建
  AppConfig._internal();

  ///工厂构造函数
  factory AppConfig() {
    return instance;
  }

  static AppConfigType? _appConfigType;

  static set appConfigType(AppConfigType value) {
    if (null != _appConfigType) {
      throw Exception(
          "AppConfig has initialized, this field not allow assignment repeat again.");
    }

    _appConfigType = value;
  }

  /// 配置不同环境baseUrl
  void setApiConfig() {
    API.configBaseUrl = AppConfig.instance.apiConfigBaseUrl;
    API.baseUrl = AppConfig.instance.apiBaseUrl;
    BlockChainAPI.baseUrl = AppConfig.instance.blockChainBaseUrl;
  }

  ///是否打开有响应时才返回请求日志和响应日志，返回全部可复制的Json，请求日志Json格式化输出，响应日志不Json格式化
  bool get enableResponseJsonLog {
    return true;
  }

  ///是否打开请求日志，只返回响应数据全部信息，可复制的Json
  bool get enableRequestJsonLog {
    return false;
  }

  ///是否打开请求日志和响应日志Log，全部Json格式化输出, 格式规范，但无法复制json
  bool get enableRequestLog {
    return false;
  }

  ///是否上报Bugly错误日志
  bool get enableBuglyHttpLog {
    return true;
  }

  ///是否打开Gex相关生命周期 Log
  bool get enableGetXLog {
    return false;
  }

  ///是否打开Dapp
  bool get enableDapp {
    return true;
  }

  ///是否打开测试网络
  bool get enableTestNetwork {
    return isDev || isTestNetWork || isTestNetWorkDev;
  }

  ///是否测试环境
  bool get isDev {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.dev;
  }

  ///是否打开测试网络
  bool get isTestNetWork {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.testnet ||
        _appConfigType == AppConfigType.dev;
  }

  ///用于调试，手动广播
  bool get isTestNetWorkDev {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.testnetDev;
  }

  ///是否生产环境
  bool get isProduct {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.product;
  }

  ///是否IosTestFlight环境
  bool get isIosTestFlight {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.testFlight;
  }

  ///是否GooglePlay
  bool get isGoogleplay {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.googleplay;
  }

  ///是否自动化测试模式
  bool get isTestMode {
    assert(null != _appConfigType);
    return _appConfigType == AppConfigType.test;
  }

  ///获取不同环境
  String get getFlavor {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.testFlight:
        return Flavor.testFlight;
      case AppConfigType.testnet:
      case AppConfigType.testnetDev:
        return GetPlatform.isAndroid ? Flavor.official : Flavor.testFlight;
      case AppConfigType.googleplay:
        return Flavor.googlePlay;
      default:
        return GetPlatform.isAndroid ? Flavor.official : Flavor.appStore;
    }
  }

  ///获取不同环境版本后缀
  String get getFlavorName {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.testFlight:
        return "(TF)";
      case AppConfigType.testnet:
        return GetPlatform.isAndroid ? "(TestNet)" : "(TF_TestNet)";
      case AppConfigType.testnetDev:
        return GetPlatform.isAndroid ? "(TestNetDev)" : "(TF_TestNetDev)";
      case AppConfigType.dev:
        return GetPlatform.isAndroid ? "(Dev)" : "(TF_Dev)";
      case AppConfigType.test:
        return "(TestMode)";
      case AppConfigType.googleplay:
        return "(GooglePlay)";
      default:
        return "";
    }
  }

  String get appName {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.dev:
        return _AppFlavorDevConfig.appName;
      case AppConfigType.testnet:
      case AppConfigType.testnetDev:
      case AppConfigType.product:
      case AppConfigType.googleplay:
      case AppConfigType.testFlight:
      case AppConfigType.test:
        return _AppFlavorProductConfig.appName;
      default:
        return _AppFlavorProductConfig.appName;
    }
  }

  String get apiConfigBaseUrl {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.dev:
        return _AppFlavorDevConfig.apiConfigUrl;
      case AppConfigType.testnet:
      case AppConfigType.testnetDev:
      case AppConfigType.product:
      case AppConfigType.googleplay:
      case AppConfigType.testFlight:
      case AppConfigType.test:
        return _AppFlavorProductConfig.apiConfigUrl;
      default:
        return _AppFlavorProductConfig.apiConfigUrl;
    }
  }

  String get apiBaseUrl {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.dev:
        return StorageManager.getValue(key: GetKey.apiBaseUrl) ??
            _AppFlavorDevConfig.apiBaseUrl;
      case AppConfigType.testnet:
      case AppConfigType.testnetDev:
      case AppConfigType.product:
      case AppConfigType.googleplay:
      case AppConfigType.testFlight:
      case AppConfigType.test:
        final baseUrl = StorageManager.getValue(key: GetKey.apiBaseUrl) ??
            _AppFlavorProductConfig.apiBaseUrl;
        return baseUrl;
      default:
        return _AppFlavorProductConfig.apiBaseUrl;
    }
  }

  String get blockChainBaseUrl {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.dev:
        return StorageManager.getValue(key: GetKey.blockBaseUrl) ??
            _AppFlavorDevConfig.blockChainBaseUrl;
      case AppConfigType.testnet:
      case AppConfigType.testnetDev:
      case AppConfigType.product:
      case AppConfigType.googleplay:
      case AppConfigType.testFlight:
      case AppConfigType.test:
        final blockBaseUrl =
            StorageManager.getValue(key: GetKey.blockBaseUrl) ??
                _AppFlavorProductConfig.blockChainBaseUrl;
        return blockBaseUrl;
      default:
        return _AppFlavorProductConfig.blockChainBaseUrl;
    }
  }

  bool get httpProxy {
    assert(null != _appConfigType);
    switch (_appConfigType) {
      case AppConfigType.dev:
        return _AppFlavorDevConfig.httpProxy;
      case AppConfigType.testnet:
      case AppConfigType.testnetDev:
      case AppConfigType.product:
      case AppConfigType.googleplay:
      case AppConfigType.testFlight:
      case AppConfigType.test:
        return _AppFlavorProductConfig.httpProxy;
      default:
        return _AppFlavorProductConfig.httpProxy;
    }
  }
}

class _AppFlavorDevConfig {
  static const String appName = "Wallet 测试环境";

  static String apiConfigUrl = FileUtils.deCodeBase64(API.devApiConfigUrl);

  static const String apiBaseUrl = API.devBaseUrl;

  static const String blockChainBaseUrl = BlockChainAPI.devBaseUrl;

  static const bool httpProxy = true;
}

class _AppFlavorProductConfig {
  static const String appName = "Wallet";

  static const String apiConfigUrl = API.productApiConfigUrl;

  static const String apiBaseUrl = API.productBaseUrl;

  static const String blockChainBaseUrl = BlockChainAPI.productBaseUrl;

  static const bool httpProxy = false;
}
