abstract class AppRoutes {
  ///---------------------------old app-------------------------------
  static const oldMainPage = "/old_main_page"; //main
  static const homePage = "/home_page"; //home
  static const settingPage = '/my_page'; // my
  static const oldReceivePage = "/old_receive_page"; // 接收
  static const facePage = '/face_page'; // face
  static const oldSendPage = "/oldsend_page"; // 发送
  static const oldWalletManage = "/oldWalletManage";

  ///---------------------------Main-------------------------------
  static const mainPage = "/main_page"; //主页Main
  static const walletPage = "/wallet_page"; //首页wallet
  static const nftPage = "/nft_page"; //首页NFT
  static const dappPage = "/dapp_page"; //首页Dapp
  static const profilePage = "/profile_page"; //首页个人中心
  static const guidePage = '/guide_page'; //引导页面
  static const splashPage = '/splash_page'; //启动页面
  static const verifySnCodePage = '/verify_sn_code'; //防伪验证
  static const verifySuccessfulPage = '/verifySnCodeSuccessful'; //验证成功

  ///----------------------------wallet------------------------------
  static const sendPage = "/send_page"; // 发送
  static const receivePage = "/receive_page"; // 接收
  static const broadcastPage = "/broadcast_page"; // 广播
  static const tokenAssetsPage = "/token_assets_page"; // 币种详情
  static const tokenAssetsAllPage = "/token_assets_all_page"; // 币种详情转账列表
  static const tokenAssetsSendPage = "/token_assets_send_page"; // 币种详情转账列表
  static const manageWalletPage = "/manage_wallet_page"; //钱包管理
  static const connectWalletPage = "/connect_wallet_page"; //钱包连接方式
  static const hardWareDetailPage = "/hardware_detail_page"; //硬件钱包详情
  static const tokenManagerPage = "/token_manager_page"; //Token管理
  static const tokenSearchPage = '/token_search_page'; //搜索Token
  static const addTokenPage = '/token_add_page'; //提交Token
  static const chainManager = '/chain_manager_page'; //链管理
  static const walletManager = '/wallet_manager_page'; //钱包管理
  static const connectSuccessfullyPage = '/connectSuccessfullyPage'; //连接钱包成功
  static const walletDetailsPage = '/WalletDetailsPage'; //钱包资料
  static const feePage = '/fee_page'; //矿工费
  static const verifyDevice = '/verify_device_page'; // 防伪验证
  static const tokenTransactionDetailsPage =
      '/token_TransactionDetails_page'; // 交易详情
  static const touchBindPage = '/touch_bind_page'; // Touch bind
  static const touchNewWalletPage =
      '/touch_new_wallet_page'; // Touch New Wallet
  static const touchWalletNamePage =
      '/touch_wallet_name_page'; // Touch Wallet name
  static const touchWalletPasswordPage =
      '/touch_password_page'; // Touch Wallet password
  static const touchMnemonicsPage =
      '/touch_mnemonics_page'; // Touch backup_mnemonics
  static const touchMnemonicsVerifyPage =
      '/touch_mnemonics_verify_page'; // Touch touch_mnemonics_verify_page
  static const touchReadCardPage =
      '/touch_read_card_page'; // Touch touch_read_card_page
  static const touchImportWalletPage =
      '/touch_import_wallet_page'; // Touch touchImportWalletPage
  static const touchChangePasswordPage =
      '/touch_change_password_page'; // Touch touchChangePasswordPage
  static const touchSignPage = '/touch_sign_page'; // Touch转账签名
  static const touchStatusPage = '/touch_status_page';
  static const messageSignPage = '/message_sign_page'; // 消息签名
  static const eosExportAccountPage = '/eos_export_account_page'; // EOS导出账户
  static const eosAccountDetailPage = '/eos_account_detail_page'; // EOS账户详情
  static const tronResourceManagerPage =
      '/tron_resource_manager_page'; // tron资源管理
  /// tron代理资源
  static const tronDelegatePage = '/tron_delegate_page';

  /// 资源回收
  static const tronReclaimManagerPage = '/tron_reclaim_manager_page';
  static const tronReclaimPage = '/tron_reclaim_page';

  /// 质押
  static const stakeManagerPage = '/stake_manager_page';
  static const stakePage = '/stake_page';

  /// 解锁
  static const unstakePage = '/unstake_page';

  /// EOS 资源管理
  static const eosResourcePage = '/eos_resource_page';

  /// 同步Token
  static const tokenSyncPage = '/token_sync_page';

  /// 同步余额P1P2P3
  static const syncBalance = '/syncBalance';

  ///----------------------------二维码页面------------------------------
  static const walletQRCodePage = '/WalletQRCodePage'; //二维码页面，签名消息,绑定钱包

  ///----------------------------扫码------------------------------
  static const scanPage = "/scan_page"; // 扫码
  static const scanResultPage = "/scan_reselt_page"; // 扫码错误结果页面

  ///----------------------------Dapp------------------------------
  static const webPage = "/web_page"; //App内置浏览器
  static const dappSearchPage = '/dapp_search_page'; //dapp搜索
  static const dappManagerPage = '/dapp_manager_page'; //dapp管理

  ///----------------------------个人中心------------------------------
  static const loginPage = "/login_page"; //登录
  static const updatepasswordPage = "/update_password"; //修改密码
  static const customerServicePage = "/customer_service_page"; //联系客服
  static const addressBookPage = "/addressbook"; //地址簿
  static const bestFeePage = "/bestFeePage"; //当前最佳矿工费
  static const makeQrPage = "/make_qr_page"; //生成二维码
  static const timeSyncPage = "/timeSyncPage"; //同步时间
  static const accountPage = "/account_page"; //个人中心账户信息
  static const setNicknamePage = "/set_nickname_page"; //账号信息 昵称
  static const helpPage = "/help_page"; //帮助中心
  static const aboutPage = "/about_page"; //关于
  static const registerPage = "/register_page"; //关于
  static const password = "/password_page"; //设置密码
  static const forget = "/forget_page"; //忘记密码
  static const bindAccount = "/bind_account_page"; //绑定邮箱或手机
  static const addContact = "/add_Contact"; //添加联系人
  static const notificationPage = "/notification_page"; //通知中心
  static const safeSetting = "/safe_setting_page"; //安全设置
  static const walletManagerPage = '/wallet_manager'; // wallet_manager
  static const importWalletPage = '/import_wallet_page'; // 导入钱包
  static const preferencesPage = '/preferences_page'; // 偏好设置
  static const walletToolPage = '/wallet_tool_page'; // 钱包工具
  static const createQRPage = '/create_qr_page'; // 生成二维码
  static const feedbookPage = '/feedbook_page'; // 意见反馈
  static const networkPage = '/network_page'; // 网络诊断
  static const networkResultPage = '/network_result_page'; // 网络诊断结果
  static const securitySettingPage = '/security_setting_page'; // 安全验证
  static const securityPasswordPage = '/security_password_page';
  static const securityPage = '/security_page';

  ///----------------------------NFT------------------------------
  /// assets 详情
  static const assetsDetailPage = '/nft_assets_detail_page';

  /// 交易记录
  static const nftRecordPage = '/nft_record_page';

  /// 交易详情
  static const nftActivityDetailPage = '/nft_activity_detail_page';
}
