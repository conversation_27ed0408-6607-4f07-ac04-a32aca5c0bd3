import 'package:coinbag/main/main_controller.dart';
import 'package:coinbag/main/main_page.dart';
import 'package:coinbag/modules/dapp/dapp_controller.dart';
import 'package:coinbag/modules/dapp/dapp_page.dart';
import 'package:coinbag/modules/dapp/manager/dapp_manager_controller.dart';
import 'package:coinbag/modules/dapp/manager/dapp_manager_page.dart';
import 'package:coinbag/modules/dapp/search/dapp_search_controller.dart';
import 'package:coinbag/modules/dapp/search/dapp_search_page.dart';
import 'package:coinbag/modules/nft/controllers/nft_assets_detail_controller.dart';
import 'package:coinbag/modules/nft/controllers/nft_record_controller.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/modules/nft/nft_page.dart';
import 'package:coinbag/modules/nft/pages/nft_activity_detail_page.dart';
import 'package:coinbag/modules/nft/pages/nft_assets_detail_page.dart';
import 'package:coinbag/modules/nft/pages/nft_record_page.dart';
import 'package:coinbag/modules/profile/contacts/address_book_controller.dart';
import 'package:coinbag/modules/profile/contacts/address_book_page.dart';
import 'package:coinbag/modules/profile/contacts/controllers/add_contact_controller.dart';
import 'package:coinbag/modules/profile/contacts/page/add_contact_page.dart';
import 'package:coinbag/modules/profile/controller/best_fee_controller.dart';
import 'package:coinbag/modules/profile/controller/feedbook_controller.dart';
import 'package:coinbag/modules/profile/controller/network_controller.dart';
import 'package:coinbag/modules/profile/controller/network_result_controller.dart';
import 'package:coinbag/modules/profile/controller/preferences_controller.dart';
import 'package:coinbag/modules/profile/controller/time_sync_controller.dart';
import 'package:coinbag/modules/profile/pages/about_page.dart';
import 'package:coinbag/modules/profile/pages/best_fee_page.dart';
import 'package:coinbag/modules/profile/pages/create_qr_page.dart';
import 'package:coinbag/modules/profile/pages/customer_service_page.dart';
import 'package:coinbag/modules/profile/pages/feedbook_page.dart';
import 'package:coinbag/modules/profile/pages/network_page.dart';
import 'package:coinbag/modules/profile/pages/network_result_page.dart';
import 'package:coinbag/modules/profile/pages/preferences_page.dart';
import 'package:coinbag/modules/profile/pages/time_sync_page.dart';
import 'package:coinbag/modules/profile/pages/wallet_tool_page.dart';
import 'package:coinbag/modules/profile/security/controller/Security_password_controller.dart';
import 'package:coinbag/modules/profile/security/controller/security_controller.dart';
import 'package:coinbag/modules/profile/security/controller/security_setting_controller.dart';
import 'package:coinbag/modules/profile/security/pages/security_page.dart';
import 'package:coinbag/modules/profile/security/pages/security_password_page.dart';
import 'package:coinbag/modules/profile/security/pages/security_setting_page.dart';
import 'package:coinbag/modules/scan/pages/scan_result_page.dart';
import 'package:coinbag/modules/scan/scan_controller.dart';
import 'package:coinbag/modules/scan/scan_page.dart';
import 'package:coinbag/modules/splash/page/guide_page.dart';
import 'package:coinbag/modules/wallet/assets/controllers/eos_export_account_controller.dart';
import 'package:coinbag/modules/wallet/assets/details/token_transaction_details_controller.dart';
import 'package:coinbag/modules/wallet/assets/details/token_transaction_details_page.dart';
import 'package:coinbag/modules/wallet/assets/pages/eos_account_detail_page.dart';
import 'package:coinbag/modules/wallet/assets/pages/eos_export_account_page.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/controllers/eos_resource_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/eos/pages/eos_resource_page.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_delegate_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_reclaim_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_reclaim_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/controllers/tron_resource_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/pages/tron_delegate_page.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/pages/tron_reclaim_manager_page.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/pages/tron_reclaim_page.dart';
import 'package:coinbag/modules/wallet/assets/resource/trx/pages/tron_resource_manager_page.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/stake_manager_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/controllers/unstake_controller.dart';
import 'package:coinbag/modules/wallet/assets/stake/pages/stake_manager_page.dart';
import 'package:coinbag/modules/wallet/assets/stake/pages/stake_page.dart';
import 'package:coinbag/modules/wallet/assets/stake/pages/unstake_page.dart';
import 'package:coinbag/modules/wallet/assets/token_assets_controller.dart';
import 'package:coinbag/modules/wallet/assets/token_assets_page.dart';
import 'package:coinbag/modules/wallet/broadcast/broadcast_controller.dart';
import 'package:coinbag/modules/wallet/broadcast/broadcast_page.dart';
import 'package:coinbag/modules/wallet/chains/chain_manager_page.dart';
import 'package:coinbag/modules/wallet/chains/controllers/message_sign_controller.dart';
import 'package:coinbag/modules/wallet/chains/pages/message_sign_page.dart';
import 'package:coinbag/modules/wallet/connect/connect_wallet_page.dart';
import 'package:coinbag/modules/wallet/connect/controllers/sync_balance_controller.dart';
import 'package:coinbag/modules/wallet/connect/pages/connect_success_page.dart';
import 'package:coinbag/modules/wallet/connect/pages/sync_balance_page.dart';
import 'package:coinbag/modules/wallet/manager/controller/wallet_details_controller.dart';
import 'package:coinbag/modules/wallet/manager/pages/wallet_details_page.dart';
import 'package:coinbag/modules/wallet/manager/wallet_manager_controller.dart';
import 'package:coinbag/modules/wallet/manager/wallet_manager_page.dart';
import 'package:coinbag/modules/wallet/qrcode/wallet_qr_code_controller.dart';
import 'package:coinbag/modules/wallet/qrcode/wallet_qr_code_page.dart';
import 'package:coinbag/modules/wallet/receive/receive_cotroller.dart';
import 'package:coinbag/modules/wallet/receive/receive_page.dart';
import 'package:coinbag/modules/wallet/send/controllers/fee_controller.dart';
import 'package:coinbag/modules/wallet/send/pages/fee_page.dart';
import 'package:coinbag/modules/wallet/send/send_controller.dart';
import 'package:coinbag/modules/wallet/send/send_page.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_add_controller.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_search_controller.dart';
import 'package:coinbag/modules/wallet/tokens/controllers/token_sync_controller.dart';
import 'package:coinbag/modules/wallet/tokens/pages/token_add_page.dart';
import 'package:coinbag/modules/wallet/tokens/pages/token_search_page.dart';
import 'package:coinbag/modules/wallet/tokens/pages/token_sync_page.dart';
import 'package:coinbag/modules/wallet/tokens/token_manager_controlle.dart';
import 'package:coinbag/modules/wallet/tokens/token_manager_page.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_bind_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_change_password_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_import_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_mnemonics_verify_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_sign_controller.dart';
import 'package:coinbag/modules/wallet/touch/controller/touch_status_controller.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_bind_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_change_password_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_import_wallet_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_mnemonics_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_mnemonics_verify_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_new_wallet_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_password_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_sign_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_status_page.dart';
import 'package:coinbag/modules/wallet/touch/pages/touch_wallet_name_page.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_controller.dart';
import 'package:coinbag/modules/wallet/touch/touch_read_card_page.dart';
import 'package:coinbag/modules/wallet/verify/verify_device_controller.dart';
import 'package:coinbag/modules/wallet/verify/verify_device_page.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/modules/wallet/wallet_page.dart';
import 'package:coinbag/modules/web/web_controller.dart';
import 'package:coinbag/modules/web/web_page.dart';
import 'package:coinbag/route/routes.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';

final routerPages = <GetPage>[
  ///---------------------------- main------------------------------

  ///主入口
  GetPage(
      name: AppRoutes.mainPage,
      page: () => const MainPage(),
      transition: Transition.noTransition,
      binding: MainBinding()),

  ///首页钱包
  GetPage(
    name: AppRoutes.walletPage,
    transition: Transition.noTransition,
    page: () => const WalletPage(),
    binding: WalletBinding(),
  ),

  //首页NFT
  GetPage(
    name: AppRoutes.nftPage,
    page: () => const NftPage(),
    binding: NftBinding(),
  ),

  ///首页Dapp
  GetPage(
    name: AppRoutes.dappPage,
    page: () => const DappPage(),
    binding: DappBinding(),
  ),

  ///首页个人中心
  GetPage(
    name: AppRoutes.profilePage,
    page: () => const DappPage(),
    binding: DappBinding(),
  ),

  ///引导
  GetPage(
    name: AppRoutes.guidePage,
    page: () => const GuidePage(),
    transition: Transition.noTransition,
  ),

  ///----------------------------wallet------------------------------

  //钱包连接
  GetPage(
    name: AppRoutes.connectWalletPage,
    page: () => const ConnectWalletPage(),
  ),

  //防伪验证
  GetPage(
    name: AppRoutes.verifyDevice,
    page: () => const VerifyDevicePage(),
    binding: VerifyDeviceBinding(),
  ),

  // 发送
  GetPage(
      name: AppRoutes.sendPage,
      page: () => const SendPage(),
      binding: SendBinding()),

  // 广播
  GetPage(
      name: AppRoutes.broadcastPage,
      page: () => const BroadcastPage(),
      popGesture: false,
      binding: BroadcastBinding()),

  // 接收
  GetPage(
      name: AppRoutes.receivePage,
      page: () => const ReceivePage(),
      binding: ReceiveBinding()),

  // 币种详情
  GetPage(
    name: AppRoutes.tokenAssetsPage,
    page: () => const TokenAssetsPage(),
    binding: TokenAssetsBinding(),
  ),

  // 交易详情
  GetPage(
    name: AppRoutes.tokenTransactionDetailsPage,
    page: () => const TokenTransactionDetailsPage(),
    binding: TokenTransactionDetailsBinding(),
  ),

  // 链管理
  GetPage(
    name: AppRoutes.chainManager,
    page: () => const ChainManagerPage(),
  ),

  // 钱包管理
  GetPage(
    name: AppRoutes.walletManager,
    page: () => const WalletManagerPage(),
    binding: WalletManagerBinding(),
    transition: Transition.leftToRight,
  ),

  // 钱包连接成功或失败
  GetPage(
    name: AppRoutes.connectSuccessfullyPage,
    page: () => const ConnectSuccessfullyPage(),
    transition: Transition.fade,
  ),

  // 钱包资料
  GetPage(
      name: AppRoutes.walletDetailsPage,
      page: () => const WalletDetailsPage(),
      binding: WalletDetailsBinding()),

  // 矿工费
  GetPage(
    name: AppRoutes.feePage,
    page: () => const FeePage(),
    binding: FeeBinding(),
  ),

  // 签名消息
  GetPage(
    name: AppRoutes.walletQRCodePage,
    page: () => WalletQRCodePage(),
    binding: WalletQRCodeBinding(),
  ),

  // Token管理
  GetPage(
    name: AppRoutes.tokenManagerPage,
    page: () => const TokenManagerPage(),
    binding: TokenManagerBinding(),
  ),

  // Token搜索
  GetPage(
    name: AppRoutes.tokenSearchPage,
    page: () => const TokenSearchPage(),
    binding: TokenSearchBinding(),
  ),

  // 提交Token
  GetPage(
    name: AppRoutes.addTokenPage,
    page: () => const TokenAddPage(),
    binding: TokenAddBinding(),
  ),

  //  Touch bind
  GetPage(
    name: AppRoutes.touchBindPage,
    page: () => const TouchBindPage(),
    binding: TouchBindBinding(),
  ),

  //  Touch New Wallet
  GetPage(
    name: AppRoutes.touchNewWalletPage,
    page: () => const TouchNewWalletPage(),
  ),

  // Touch Wallet Name
  GetPage(
    name: AppRoutes.touchWalletNamePage,
    page: () => const TouchWalletNamePage(),
  ),

  GetPage(
    name: AppRoutes.touchWalletPasswordPage,
    page: () => const TouchWalletPasswordPage(),
  ),

  GetPage(
    name: AppRoutes.touchMnemonicsPage,
    page: () => const TouchMnemonicsPage(),
    binding: TouchBackupBinding(),
  ),

  GetPage(
    name: AppRoutes.touchMnemonicsVerifyPage,
    page: () => const TouchMnemonicsVerifyPage(),
    binding: TouchMnemonicsVerifyBinding(),
  ),

  GetPage(
    name: AppRoutes.touchReadCardPage,
    page: () => const TouchReadCardPage(),
    binding: TouchReadCardBinding(),
  ),

  GetPage(
    name: AppRoutes.touchImportWalletPage,
    page: () => const TouchImportWalletPage(),
    binding: TouchImportBinding(),
  ),

  GetPage(
    name: AppRoutes.touchChangePasswordPage,
    page: () => const TouchChangePasswordPage(),
    binding: TouchChangePasswordBinding(),
  ),

  GetPage(
    name: AppRoutes.touchSignPage,
    page: () => const TouchSignPage(),
    binding: TouchSignBinding(),
  ),

  GetPage(
    name: AppRoutes.touchStatusPage,
    page: () => const TouchStatusPage(),
    binding: TouchStatusBinding(),
  ),

  GetPage(
    name: AppRoutes.messageSignPage,
    page: () => const MessageSignPage(),
    binding: MessageSignBinding(),
  ),

  GetPage(
    name: AppRoutes.eosExportAccountPage,
    page: () => const EosExportAccountPage(),
    binding: EOSExportAccountBinding(),
  ),

  GetPage(
    name: AppRoutes.eosAccountDetailPage,
    page: () => const EOSAccountDetailPage(),
  ),

  GetPage(
    name: AppRoutes.tronResourceManagerPage,
    page: () => const TronResourceManagerPage(),
    binding: TronResourceManagerBinding(),
  ),

  GetPage(
    name: AppRoutes.tronDelegatePage,
    page: () => const TronDelegatePage(),
    binding: TronDelegateBinding(),
  ),

  GetPage(
    name: AppRoutes.tronReclaimManagerPage,
    page: () => const TronReclaimManagerPage(),
    binding: TronReclaimManagerBinding(),
  ),

  GetPage(
    name: AppRoutes.tronReclaimPage,
    page: () => const TronReclaimPage(),
    binding: TronReclaimBinding(),
  ),

  GetPage(
    name: AppRoutes.stakeManagerPage,
    page: () => const StakeManagerPage(),
    binding: StakeManagerBinding(),
  ),

  GetPage(
    name: AppRoutes.stakePage,
    page: () => const StakePage(),
    binding: StakeBinding(),
  ),

  GetPage(
    name: AppRoutes.unstakePage,
    page: () => const UnstakePage(),
    binding: UnstakeBinding(),
  ),

  GetPage(
    name: AppRoutes.eosResourcePage,
    page: () => const EOSResourcePage(),
    binding: EosResourceBindings(),
  ),

  GetPage(
    name: AppRoutes.tokenSyncPage,
    page: () => const TokenSyncPage(),
    binding: TokenSyncBinding(),
  ),

  GetPage(
    name: AppRoutes.syncBalance,
    page: () => const SyncBalancePage(),
    binding: BalanceBinding(),
  ),

  ///----------------------------扫码------------------------------
  // 扫码
  GetPage(
    name: AppRoutes.scanPage,
    page: () => const ScanPage(),
    binding: ScanBinding(),
    transition: Transition.fadeIn,
  ),

  // 扫码结果页面
  GetPage(
    name: AppRoutes.scanResultPage,
    page: () => const ScanResultPage(),
  ),

  ///----------------------------Dapp------------------------------

  ///web
  GetPage(
      name: AppRoutes.webPage,
      page: () => WebViewPage(),
      binding: WebBinding()),

  GetPage(
      name: AppRoutes.dappSearchPage,
      page: () => const DappSearchPage(),
      binding: DappSearchBinding()),

  GetPage(
      name: AppRoutes.dappManagerPage,
      page: () => const DappManagerPage(),
      binding: DappManagerBinding()),

  ///----------------------------个人中心------------------------------

  //联系客服
  GetPage(
      name: AppRoutes.customerServicePage,
      page: () => const CustomerServicePage()),

  //地址簿
  GetPage(
      name: AppRoutes.addressBookPage,
      page: () => const AddressBookPage(),
      binding: AddressBookBinding()),

  //同步时间
  GetPage(
    name: AppRoutes.timeSyncPage,
    page: () => const TimeSyncPage(),
    binding: TimeSyncBinding(),
  ),

  //钱包工具
  GetPage(
    name: AppRoutes.walletToolPage,
    page: () => const WalletToolPage(),
  ),

  //矿工费
  GetPage(
      name: AppRoutes.bestFeePage,
      page: () => const BsetFeePage(),
      binding: BestFeeBinding()),

  //生成二维码
  GetPage(
    name: AppRoutes.createQRPage,
    page: () => const CreateQRPage(),
  ),

  //添加联系人
  GetPage(
      name: AppRoutes.addContact,
      page: () => const AddContactPage(),
      binding: AddContactBinding()),

  //偏好设置
  GetPage(
      name: AppRoutes.preferencesPage,
      page: () => const PreferencesPage(),
      binding: PreferencesBinding()),

  //关于
  GetPage(
    name: AppRoutes.aboutPage,
    page: () => const AboutPage(),
  ),

  //关于
  GetPage(
    name: AppRoutes.feedbookPage,
    page: () => const FeedbookPage(),
    binding: FeedbookBinding(),
  ),

  // 网络诊断
  GetPage(
    name: AppRoutes.networkPage,
    page: () => const NetworkDiagnosticsPage(),
    binding: NetworkDiagnosticsBinding(),
  ),

  // 网络诊断结果
  GetPage(
    name: AppRoutes.networkResultPage,
    page: () => const NetworkDiagnosticsResultPage(),
    binding: NetworkDiagnosticsResultBinding(),
  ),

  GetPage(
    name: AppRoutes.securitySettingPage,
    page: () => const SecuritySettingPage(),
    binding: SecuritySettingBinding(),
  ),

  GetPage(
    name: AppRoutes.securityPasswordPage,
    transition: Transition.fadeIn,
    page: () => const SecurityPasswordPage(),
    binding: SecurityPasswordBinding(),
  ),

  GetPage(
    name: AppRoutes.securityPage,
    page: () => const SecurityPage(),
    transition: Transition.fadeIn,
    binding: SecurityBinding(),
  ),

  ///----------------------------NFT------------------------------
  /// assets详情
  GetPage(
    name: AppRoutes.assetsDetailPage,
    page: () => const NftAssetsDetailPage(),
    binding: NftAssetsDetailBinding(),
  ),

  /// 交易记录
  GetPage(
    name: AppRoutes.nftRecordPage,
    page: () => const NftRecordPage(),
    binding: NftRecordBinding(),
  ),

  /// 交易详情
  GetPage(
    name: AppRoutes.nftActivityDetailPage,
    page: () => const NftActivityDetailPage(),
  ),
];
