/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-12 14:44:30
 */
import 'dart:async';

import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/app/app_service.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/modules/locale/locale_controller.dart';
import 'package:coinbag/modules/splash/splash_screen_controller.dart';
import 'package:coinbag/modules/splash/splash_screen_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/route/pages.dart';
import 'package:coinbag/widgets/pull_refresh/refresh_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:showcaseview/showcaseview.dart';

void main() async {
  ///Bugly
  FlutterBugly.postCatchedException(() async {
    WidgetsFlutterBinding.ensureInitialized();
    await GetStorage.init();
    AppConfig.appConfigType = AppConfigType.product; //默认生产环境
    AppConfig.instance.setApiConfig();
    await startApp();
  });
}

Future<void> startApp() async {
  await Get.putAsync<AppService>(() async => await AppService().init());
  runApp(const MyApp());
  // 初始化 Bugly
  FlutterBugly.init(
    androidAppId: BuglayKey.androidAppId,
    iOSAppId: BuglayKey.iOSAppId,
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      enableLog: AppConfig.instance.enableGetXLog && kDebugMode,
      debugShowCheckedModeBanner: AppConfig.instance.isDev,
      getPages: routerPages,
      defaultTransition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
      title: 'Coinbag',
      builder: (context, child) {
        return MediaQuery(
            data: MediaQuery.of(context).copyWith(
                textScaler: const TextScaler.linear(1.0)), // 禁止跟随系统字体大小
            child: ShowCaseWidget(
              builder: (context) => OKToast(
                  dismissOtherOnShow: true,
                  child: EasyLoading.init()(
                      context,
                      RefreshConfig(
                        child: ScreenUtilInit(
                          fontSizeResolver: (fontSize, instance) =>
                              FontSizeResolvers.radius(fontSize, instance),
                          designSize: const Size(375, 812),
                          builder: (_, c) {
                            return child!;
                          },
                        ),
                      ))),
            ));
      },
      darkTheme: darkTheme,
      theme: lightTheme,
      themeMode: ThemeMode.light,
      translations: TranslationsMessage(),
      locale: LocaleController.locale(),
      fallbackLocale: const Locale('en', 'US'),
      localizationsDelegates: const [
        RefreshLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocaleController.supportedLocales,
      initialBinding: SplashScreenBinding(),
      home: const SplashScreenPage(),
    );
  }
}
