import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/dapp/dapp_controller.dart';
import 'package:coinbag/modules/nft/nft_controller.dart';
import 'package:coinbag/modules/profile/profile_controller.dart';
import 'package:coinbag/modules/wallet/wallet_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/keyboard_util.dart';
import 'package:flutter/material.dart';
import 'package:wallet_core/wallet/wallet.dart';

class MainController extends BaseController {
  DateTime? lastPopTime;
  final RxInt curPage = 0.obs;
  final RxInt oldPage = 0.obs;
  final PageController pageController = PageController(initialPage: 0);
  var isSupportNft = false.obs;

  @override
  void onInit() async {
    super.onInit();
    _initializeWallet();
    _handleFirstLaunch();
    _initializeCoinBase();
    Get.appController.initCoinfigApi();
  }

  void _handleFirstLaunch() {
    if (Get.isFirstLaunch()) {
      StorageManager.saveValue(key: StorageKey.fristLaunch, value: false);
    }
  }

  void _initializeCoinBase() {
    if (Get.isInitCoinBase()) {
      StorageManager.saveValue(key: StorageKey.initCoinBaseV4, value: false);
      Get.appController.initCoinBase();
    }
  }

  void _initializeWallet() async {
    WalletModel? walletModel = await Get.database.walletDao.getCheckedWallets();
    if (walletModel != null) {
      Wallet wallet = Wallet.getWalletByBatch(walletModel.batchId!);
      updataItem(wallet);
    }
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void loadData() {
    Future.delayed(const Duration(milliseconds: 1500), () {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.appController.checkUpgrade();
        Get.appController.uploadUserData();
      });
    });
  }

  int getProfileIndex() {
    return isSupportNft.value ? 3 : 2;
  }

  int getDappnIndex() {
    return isSupportNft.value ? 2 : 1;
  }

  void navigateToPage(BuildContext context,
      {int index = 0, bool isVipGuilde = false}) {
    if (isVipGuilde) {
      index = getProfileIndex();
    }
    // 隐藏键盘
    KeyboardUtils.hideKeyboard(context);
    // 跳转到指定页面
    pageController.jumpToPage(index);
    // 点击当前tab时触发
    onTabNavigateToPage(curPage.value, index);
    // 更新当前页索引
    curPage.value = index;

    if (isVipGuilde) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.appController.startShowCase([Get.appController.guildeTwo]);
      });
    }
  }

  void updataItem(Wallet wallet) {
    if (wallet.isSupportNft == isSupportNft.value) {
      return;
    }

    isSupportNft.value = GetPlatform.isAndroid ? wallet.isSupportNft : false;
    if (isSupportNft.value == true && !Get.isRegistered<NftController>()) {
      Get.lazyPut(() => NftController());
    } else {
      AppController.refreshNftWallet();
    }
  }

  void onTabNavigateToPage(int oldIndex, int newIndex) {
    // 如果当前页面为0，且点击的是当前页面，则触发滚动到顶部
    if (oldIndex == 0 && newIndex == 0) {
      if (Get.isRegistered<WalletController>()) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.find<WalletController>().scrollToTop();
        });
      }
    } else if (oldIndex == getDappnIndex() && newIndex == getDappnIndex()) {
      if (Get.isRegistered<DappController>()) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.find<DappController>().scrollToTop();
        });
      }
    }
  }
}

class MainBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => MainController());
    Get.lazyPut(() => WalletController());
    if (GetPlatform.isAndroid) {
      Get.lazyPut(() => NftController());
    }
    Get.lazyPut(() => DappController());
    Get.lazyPut(() => ProfileController());
  }
}
