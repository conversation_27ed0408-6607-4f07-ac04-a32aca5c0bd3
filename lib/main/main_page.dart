/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2025-03-14 10:41:09
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/base/state/base_stateful_widget.dart';
import 'package:coinbag/base/state/keep_alive_wrapper.dart';
import 'package:coinbag/main/main_controller.dart';
import 'package:coinbag/modules/dapp/dapp_page.dart';
import 'package:coinbag/modules/nft/nft_page.dart';
import 'package:coinbag/modules/profile/profile_page.dart';
import 'package:coinbag/modules/wallet/wallet_page.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:showcaseview/showcaseview.dart';

class MainPage extends BaseStatefulWidget<MainController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: Obx(() => PopScope(
              canPop: false,
              onPopInvokedWithResult: (didPop, result) async {
                if (result != null) {
                  return; // 如果 result 不为空，直接返回
                }
                if (didPop) {
                  return;
                }
                if (controller.lastPopTime == null ||
                    DateTime.now().difference(controller.lastPopTime!) >
                        const Duration(seconds: 2)) {
                  // 存储当前按下back键的时间
                  controller.lastPopTime = DateTime.now();
                  // toast
                  Get.showToast(ID.exitApplication.tr);
                } else {
                  controller.lastPopTime = DateTime.now();
                  // 退出app
                  await SystemNavigator.pop();
                }
              },
              child: Scaffold(
                body: KeepAliveWrapper(
                  child: PageView(
                      controller: controller.pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        const WalletPage(),
                        if (controller.isSupportNft.value &&
                            GetPlatform.isAndroid)
                          const NftPage(),
                        const DappPage(),
                        const ProfilePage(),
                      ]),
                ),
                bottomNavigationBar: Theme(
                  data: ThemeData(
                    splashColor: Colors.transparent,
                  ),
                  child: BottomNavigationBar(
                    backgroundColor: Get.theme.bgColor,
                    type: BottomNavigationBarType.fixed,
                    currentIndex: controller.curPage.value,
                    items: [
                      BottomNavigationBarItem(
                          icon: ImageWidget(
                            assetUrl: 'tab_wallet_normal',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                          activeIcon: ImageWidget(
                            assetUrl: 'tab_wallet',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                          label: ID.stringTabWallet.tr),
                      if (controller.isSupportNft.value &&
                          GetPlatform.isAndroid)
                        BottomNavigationBarItem(
                            icon: ImageWidget(
                              assetUrl: 'tab_nft_normal',
                              width: Get.setImageSize(28),
                              height: Get.setImageSize(28),
                            ),
                            activeIcon: ImageWidget(
                              assetUrl: 'tab_nft',
                              width: Get.setImageSize(28),
                              height: Get.setImageSize(28),
                            ),
                            label: 'NFT'),
                      BottomNavigationBarItem(
                          icon: ImageWidget(
                            assetUrl: 'tab_discover_normal',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                          activeIcon: ImageWidget(
                            assetUrl: 'tab_discover',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                          label: ID.stringTabDiscover.tr),
                      BottomNavigationBarItem(
                          icon: Showcase(
                              targetPadding:
                                  EdgeInsets.all(Get.setPaddingSize(10)),
                              key: Get.appController.guildeOne,
                              tooltipPosition: TooltipPosition.top,
                              title: ID.stringVIPTip1.tr,
                              titlePadding: EdgeInsets.only(
                                  bottom: Get.setPaddingSize(10)),
                              titleTextStyle: TextStyle(
                                  color: Get.theme.bgColor,
                                  fontWeight: FontWeightX.semibold,
                                  fontSize: Get.setFontSize(16)),
                              description: ID.stringVIPTip2.tr,
                              tooltipPadding:
                                  EdgeInsets.all(Get.setPaddingSize(14)),
                              descTextStyle: TextStyle(
                                  color: Get.theme.bgColor,
                                  fontWeight: FontWeightX.medium,
                                  fontSize: Get.setFontSize(14)),
                              tooltipBackgroundColor: Get.theme.colorFF6A16,
                              textColor: Get.theme.bgColor,
                              targetShapeBorder: const CircleBorder(),
                              disposeOnTap: true,
                              onTargetClick: () => controller
                                  .navigateToPage(context, isVipGuilde: true),
                              onToolTipClick: () => controller
                                  .navigateToPage(context, isVipGuilde: true),
                              onBarrierClick: () => controller
                                  .navigateToPage(context, isVipGuilde: true),
                              child: ImageWidget(
                                assetUrl: 'tab_profile_normal',
                                width: Get.setImageSize(28),
                                height: Get.setImageSize(28),
                              )),
                          activeIcon: ImageWidget(
                            assetUrl: 'tab_profile',
                            width: Get.setImageSize(28),
                            height: Get.setImageSize(28),
                          ),
                          label: ID.stringTabProfile.tr),
                    ],
                    selectedItemColor: Get.theme.textPrimary, // 设置选中项的颜色
                    unselectedItemColor: Get.theme.textTertiary, // 设置未选中项的颜色
                    selectedLabelStyle: TextStyle(
                        fontWeight: FontWeightX.medium,
                        fontSize: Get.setFontSize(11)),
                    unselectedLabelStyle: TextStyle(
                        fontWeight: FontWeightX.medium,
                        fontSize: Get.setFontSize(11)),
                    onTap: (int index) =>
                        controller.navigateToPage(context, index: index),
                  ),
                ),
              ),
            )));
  }
}
