/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-11-28 09:03:04
 */
import 'dart:math';

import 'package:coinbag/app/app_controller.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/currency_controller.dart';
import 'package:coinbag/utils/decimal_utils.dart';

class BalanceManager {
  /// 根据余额和价格计算 法定余额
  static String calculateFiatValue(String? balance, CoinModel? coinModel) {
    if (coinModel == null ||
        Get.isEmptyString(balance) ||
        balance == CommonConstant.emptyAsstes ||
        Get.isEmptyString(coinModel.price)) {
      return CommonConstant.emptyAsstes;
    }

    String value = CurrencyController.convertCurrency(
        usdRateModel: AppController.usdRateModel.value,
        unitPrice: coinModel.price,
        balance: balance);
    value = CurrencyController.formatMoney(value,
        withThousandmat: true, hasUnit: true);
    return value;
  }

  /// 币种余额
  static String? getBalance(String? balance, CoinModel? coinModel) {
    try {
      if (coinModel == null ||
          Get.isEmptyString(balance) ||
          coinModel.chainDecimal == null ||
          balance == CommonConstant.emptyAsstes) {
        return CommonConstant.emptyAsstes;
      }

      String value = DecimalUtils.divide(
          balance!, pow(10, coinModel.chainDecimal!).toString(),
          scale: coinModel.balanceDecimal!);

      return DecimalUtils.stripTrailingZeros(value);
    } catch (e) {}
    return '';
  }
}
