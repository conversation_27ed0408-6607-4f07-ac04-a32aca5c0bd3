import 'package:coinbag/utils/decimal_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';

class PackageInfoManager {
  static final PackageInfoManager _instance = PackageInfoManager._internal();

  String appName = '';
  String packageName = '';
  String version = '';
  String buildNumber = '';

  factory PackageInfoManager() {
    return _instance;
  }

  PackageInfoManager._internal();

  Future<void> init() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    appName = packageInfo.appName;
    packageName = packageInfo.packageName;
    version = packageInfo.version;
    buildNumber = packageInfo.buildNumber;
  }

  static int parseVersion(String version) {
    var sp = version.split('.');
    var num = "";
    for (var item in sp) {
      num = num + item.padLeft(2, '0');
    }
    return DecimalUtils.toIntSafe(num);
  }
}
