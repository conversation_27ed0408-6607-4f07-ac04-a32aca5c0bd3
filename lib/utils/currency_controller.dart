/*
 * @description: 金额法币汇率工具类
 * @Author: wangdog<PERSON>henng
 * @Date: 2024-01-10 14:47:46
 * @LastEditTime: 2024-11-20 09:37:16
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/database/storage/storage_provider.dart';
import 'package:coinbag/modules/wallet/home/<USER>/usd_rate_model.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/utils/decimal_utils.dart';
import 'package:common_utils/common_utils.dart';
import 'package:decimal/decimal.dart';
import 'package:intl/intl.dart';

enum Currency {
  cny,
  usd,
}

class CurrencyController {
  static Currency get getCurrency {
    int? result = StorageManager.getValue(key: CommonConstant.currency);
    return Currency.values[result ?? 1];
  }

  static void updateCurrency(Currency currency) {
    StorageManager.saveValue(
        key: CommonConstant.currency, value: currency.index);
  }

  // @description : 获取当前计价币种汇率
  static String getRateByCurrency(UsdRateModel? usdRateModel) {
    Currency currency = CurrencyController.getCurrency;
    switch (currency) {
      case Currency.usd:
        return "1";
      case Currency.cny:
        return usdRateModel!.cny!;
    }
  }

  // @description : 获取当前计价单位
  static String getCurrencyUnit() {
    Currency currency = getCurrency;
    switch (currency) {
      case Currency.cny:
        return MoneyUtil.YUAN;
      default:
        Currency.usd;
        return MoneyUtil.DOLLAR;
    }
  }

  static String getCurrencyUnitByCurrency(Currency currency) {
    switch (currency) {
      case Currency.cny:
        return MoneyUtil.YUAN;
      default:
        Currency.usd;
        return MoneyUtil.DOLLAR;
    }
  }

  // @description : 金额格式化，
  static String formatMoney(String? value,
      {bool appointment = false, //是否加约等
      bool isZeroAssets = true, //是否金额为0时显示0.00
      bool withThousandmat = false, //是否千分位
      bool legal = false, //是否保留两位
      bool hasUnit = false} //是否后缀美元或人民币符号

      ) {
    StringBuffer buffer = StringBuffer();
    buffer.write((appointment ? "≈ " : ""));
    buffer.write(hasUnit ? "${getCurrencyUnit()} " : "");
    buffer.write(withThousandmat
        ? withThousandSeparator(value, isZeroAssets: isZeroAssets, legal: legal)
        : value);
    return buffer.toString();
  }

  // @description : 金额千分位，
  static String withThousandSeparator(String? amount,
      {bool isZeroAssets = false, bool legal = false}) {
    String fromStr = legal ? "#,##0.##" : "#,##0.########";
    final format = NumberFormat(fromStr, 'en_US');
    try {
      String value = DecimalUtils.stripTrailingZeros(amount!);
      if (DecimalUtils.isZeros(amount) && isZeroAssets) {
        return CommonConstant.zeroAsstes;
      }

      double amountValue = Decimal.parse(value).toDouble();

      return format.format(amountValue);
    } catch (e) {
      return amount!;
    }
  }

  // @description : 金额数量转换为美元或人民币汇率的值
  static String convertCurrency(
      {UsdRateModel? usdRateModel,
      String? balance,
      String? unitPrice,
      bool formZero = false,
      int scale = 2}) {
    if (Get.isEmptyString(balance) ||
        Get.isEmptyString(unitPrice) ||
        usdRateModel == null) {
      return '';
    }
    String? unitRate = getRateByCurrency(usdRateModel);
    String? rate = DecimalUtils.multiplyOnly(unitPrice!, unitRate, scale: 30);
    String? value = DecimalUtils.multiplyOnly(balance!, rate, scale: scale);
    if ((formZero && DecimalUtils.isZeros(value)) || Get.isEmptyString(value)) {
      return CommonConstant.zeroAsstes;
    }
    return DecimalUtils.stripTrailingZeros(value);
  }
}
