/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-10-24 10:13:15
 */
import 'package:big_decimal/big_decimal.dart';
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:common_utils/common_utils.dart';
import 'package:decimal/decimal.dart';

/// @description :防止科学计数，精度计算
class DecimalUtils {
  ///加  且去除尾数无效0
  static String add(String value1, String value2, {int scale = 0}) {
    if (!GetUtils.isNum(value1) || !GetUtils.isNum(value2)) {
      return '';
    }
    String value =
        NumUtil.addDecStr(value1, value2).toStringAsFixed(scale).toString();
    return stripTrailingZeros(value);
  }

  ///减
  static String subtract(String value1, String value2, {int scale = 0}) {
    if (!GetUtils.isNum(value1) || !GetUtils.isNum(value2)) {
      return '';
    }
    String value = NumUtil.subtractDecStr(value1, value2)
        .toStringAsFixed(scale)
        .toString();

    return stripTrailingZeros(value);
  }

  ///乘
  static String multiplyOnly(String value1, String value2, {int scale = 0}) {
    if (!GetUtils.isNum(value1) || !GetUtils.isNum(value2)) {
      return '';
    }
    String value = NumUtil.multiplyDecStr(value1, value2)
        .toStringAsFixed(scale)
        .toString();

    return value;
  }

  ///乘 scale四舍五入保留位数
  static String multiply(String value1, String value2, {int scale = 0}) {
    if (!GetUtils.isNum(value1) || !GetUtils.isNum(value2)) {
      return '';
    }
    String value = NumUtil.multiplyDecStr(value1, value2)
        .toStringAsFixed(scale)
        .toString();

    return stripTrailingZeros(value);
  }

  ///除且去除尾数无效0 scale四舍五入保留位数
  static String divide(String value1, String value2,
      {RoundingMode roundingMode = RoundingMode.DOWN, int scale = 0}) {
    if (!GetUtils.isNum(value1) || !GetUtils.isNum(value2)) {
      return '';
    }
    return stripTrailingZeros(BigDecimal.parse(value1)
        .divide(BigDecimal.parse(value2),
            roundingMode: roundingMode, scale: scale)
        .toPlainString());
  }

  /// @description :防止科学计数去除尾部无效0
  /// @description : Prevents scientific notation and removes trailing zeros
  /// @param value : The numeric string to process
  /// @param scale : The maximum number of decimal places to keep (0 means no limit)
  static String stripTrailingZeros(String value, {int scale = 0}) {
    if (value.isEmpty || value == CommonConstant.zeroAsstes) return value;
    try {
      final bd = BigDecimal.parse(value);
      String str = bd.toPlainString();
      // 去除小数点后多余的0，如果全是0则去除小数点
      if (str.contains('.')) {
        str = str.replaceFirst(RegExp(r'\.?0*$'), ''); // 处理 40.00 → 40
        str = str.replaceFirst(
            RegExp(r'(\.\d*[1-9])0+$'), r'$1'); // 处理 0.00300 → 0.003
      }
      // 如果结果是0，统一返回'0'
      if (BigDecimal.parse(str).compareTo(BigDecimal.zero) == 0) {
        return '0';
      }
      return str;
    } catch (_) {
      return value;
    }
  }

  /// @description :判断是0
  static bool isZeros(String value) {
    if (value.isEmpty || value == CommonConstant.zeroAsstes || !value.isNum) {
      return true;
    }
    return NumUtil.isZero(Decimal.parse(value).toDouble());
  }

  // @description : 通过精度计算真实余额 ,decimal链精度，币种保留小数位balanceDecimal
  static String? getBalance(
      {required String? rawBalance,
      required int decimal,
      required int balanceDecimal}) {
    if (Get.isEmptyString(rawBalance)) {
      return rawBalance;
    }

    if (!rawBalance!.isNum) return rawBalance;

    String realBalance = divide(
        rawBalance, BigInt.from(10).pow(decimal).toString(),
        scale: balanceDecimal);
    return stripTrailingZeros(realBalance, scale: balanceDecimal);
  }

  /// @description :compare value1> value2 true
  static bool compare(String value1, String value2) {
    if (value1.isEmpty ||
        value1 == CommonConstant.zeroAsstes ||
        !value1.isNum ||
        value2.isEmpty ||
        value2 == CommonConstant.zeroAsstes ||
        !value2.isNum) {
      return false;
    }
    return NumUtil.greaterThanDecStr(value1, value2);
  }

  /// 安全将字符串（含小数、非法字符串）转为 int，非法时返回默认值
  static int toIntSafe(String? value, {int defaultValue = 0}) {
    if (value == null || value.isEmpty) return defaultValue;
    final doubleVal = double.tryParse(value);
    if (doubleVal == null) return defaultValue;
    return doubleVal.toInt();
  }
}
