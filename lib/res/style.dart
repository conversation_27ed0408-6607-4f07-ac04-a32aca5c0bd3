/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-19 16:57:44
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';

///---------------------------字体样式-------------------------------

TextStyle get styleNarmal => TextStyle(
    color: Get.theme.textPrimary,
    fontSize: Get.setFontSize(14),
    fontWeight: FontWeightX.regular,
    fontFamily: Get.setFontFamily());

/// 黑色16号中粗
TextStyle get stylePrimary_16_m => TextStyle(
    color: Get.theme.textPrimary,
    fontSize: Get.setFontSize(16),
    fontWeight: FontWeightX.medium,
    fontFamily: Get.setFontFamily());

/// 黑色14号中粗
TextStyle get stylePrimary_14_m => TextStyle(
    color: Get.theme.textPrimary,
    fontSize: Get.setFontSize(14),
    fontWeight: FontWeightX.medium,
    fontFamily: Get.setFontFamily());

/// Text二级颜色16号
TextStyle get styleSecond_16 => TextStyle(
    color: Get.theme.textSecondary,
    fontSize: Get.setFontSize(16),
    fontWeight: FontWeightX.regular,
    fontFamily: Get.setFontFamily());

/// Text二级颜色14号
TextStyle get styleSecond_14 => TextStyle(
    color: Get.theme.textSecondary,
    fontSize: Get.setFontSize(14),
    fontWeight: FontWeightX.regular,
    fontFamily: Get.setFontFamily());

/// 导航 appbar title
TextStyle get styleAppbarTitle => TextStyle(
    color: Get.theme.textPrimary,
    fontSize: Get.setFontSize(16),
    fontWeight: FontWeightX.medium,
    fontFamily: Get.setFontFamily());

/// dapp item
TextStyle style_Dapp_Item = TextStyle(
    color: Get.theme.primary,
    overflow: TextOverflow.ellipsis,
    fontSize: Get.setFontSize(16),
    fontWeight: FontWeightX.medium);

/// 钱包首页币种 和余额 样式
TextStyle styleHomeSymbol = TextStyle(
    color: Get.theme.textPrimary,
    fontSize: Get.setFontSize(16),
    fontWeight: FontWeightX.semibold,
    fontFamily: Get.setNumberFontFamily());

/// 钱包首页汇率样式
TextStyle styleHomeRateSymbol = TextStyle(
    color: Get.theme.textSecondary,
    fontSize: Get.setFontSize(12),
    fontWeight: FontWeightX.regular,
    fontFamily: Get.setNumberFontFamily());

///---------------------------Button样式-------------------------------
