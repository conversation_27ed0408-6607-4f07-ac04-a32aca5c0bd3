/*
 * @author: wds
 * @description: 应用文本样式定义
 * @LastEditTime: 2024-09-19 16:57:44
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';

/// 基础文本样式
class AppTextStyles {
  // 私有构造函数，防止实例化
  AppTextStyles._();

  /// 基础样式
  static TextStyle get _baseStyle => TextStyle(
        color: Get.theme.textPrimary,
        fontFamily: Get.setFontFamily(),
        fontSize: Get.setFontSize(14),
        fontWeight: FontWeightX.regular,
      );

  /// 基础数字样式
  static TextStyle get _baseNumberStyle => _baseStyle.copyWith(
        fontFamily: Get.setNumberFontFamily(),
      );

  /// 常规样式 - 14号
  static TextStyle get normal => _baseStyle;

  /// 主要文本 - 16号中等粗细
  static TextStyle get primaryMedium16 => _baseStyle.copyWith(
        fontSize: Get.setFontSize(16),
        fontWeight: FontWeightX.medium,
      );

  /// 主要文本 - 14号中等粗细
  static TextStyle get primaryMedium14 => _baseStyle.copyWith(
        fontWeight: FontWeightX.medium,
      );

  /// 次要文本 - 16号
  static TextStyle get secondary16 => _baseStyle.copyWith(
        color: Get.theme.textSecondary,
        fontSize: Get.setFontSize(16),
      );

  /// 次要文本 - 14号
  static TextStyle get secondary14 => _baseStyle.copyWith(
        color: Get.theme.textSecondary,
      );

  /// 导航栏标题样式
  static TextStyle get appBarTitle => primaryMedium16;

  /// DApp 项目样式
  static TextStyle get dappItem => _baseStyle.copyWith(
        color: Get.theme.primary,
        fontSize: Get.setFontSize(16),
        fontWeight: FontWeightX.medium,
        overflow: TextOverflow.ellipsis,
      );

  /// 钱包首页币种和余额样式
  static TextStyle get homeSymbol => _baseNumberStyle.copyWith(
        fontSize: Get.setFontSize(16),
        fontWeight: FontWeightX.semibold,
      );

  /// 钱包首页汇率样式
  static TextStyle get homeRate => _baseNumberStyle.copyWith(
        color: Get.theme.textSecondary,
        fontSize: Get.setFontSize(12),
      );
}

///---------------------------Button样式-------------------------------
