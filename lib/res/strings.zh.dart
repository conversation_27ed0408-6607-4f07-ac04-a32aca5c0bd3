import 'strings.dart';

// zh
const Map<String, String> localizedValueZH = {
  ID.appName: 'Coinbag',
  ID.settingLanguageDefault: '跟随系统',
  ID.stringConfirm: '确认',
  ID.stringCancel: '取消',
  ID.stringDiscover: '发现',
  ID.stringAvatar: '头像',
  ID.stringUid: 'stringNfcInputOnePsw',
  ID.stringPhone: '电话',
  ID.stringEmail: '邮箱',
  ID.stringLoginpassword: '登录密码',
  ID.stringAccount: '账号',
  ID.stringNickname: '昵称',
  ID.stringHelp: '帮助中心',
  ID.stringcommonproblem: '常见问题',
  ID.profileLogin: '登录账号',
  ID.profileTool: '钱包工具',
  ID.profileHelp: '帮助中心',
  ID.profileAddressBook: '地址簿',
  ID.profileMall: '概览',
  ID.profileLanguage: '多语言',
  ID.profileQR: '二维码容量',
  ID.profileAbout: '关于',
  ID.profileCurrency: '货币单位',
  ID.qrStandard: '标准',
  ID.qrSmall: '较小',
  ID.qrLarge: '较大',
  ID.qrSuperLarge: '超大',
  ID.qrBest: '推荐',
  ID.clientNetError: '网络异常，请检网络连接状态',
  ID.timeOutError: '请求超时!请稍后再试',
  ID.serverNetError: '服务器连接出现问题\n请稍后重试或联系客服处理',
  ID.netConnectError: '网络未连接，请检查后重试',
  ID.cancelConnectError: '请求取消',
  ID.qrTip: '二维码容量越小扫码识别越快，但会导致二维码的页数增多',
  ID.emptyData: '暂无数据',
  ID.dataError: '加载失败',
  ID.reload: '重新加载',
  ID.exitApplication: '再按一次退出应用',
  ID.releaseText: '释放立即刷新',
  ID.refreshingText: '正在刷新...',
  ID.completeText: '刷新完成',
  ID.idleText: '下拉可以刷新',
  ID.loadingText: '正在加载中...',
  ID.pullUpToLoad: '上拉加载更多',
  ID.canLoadingText: '松手加载更多',
  ID.noDataText: '没有更多数据了',
  ID.aboutKS: '关于',
  ID.userAgreement: '用户协议',
  ID.wallet: '钱包',
  ID.send: '发送',
  ID.receive: '接收',
  ID.scan: '扫一扫',
  ID.activity: '交易记录',
  ID.currencySearch: '币种搜索',
  ID.allWallet: '所有钱包',
  ID.totalAsset: '资产总值',
  ID.loginString: '登录',
  ID.loginPhone: '手机号',
  ID.loginPhoneHintText: '输入手机号',
  ID.loginEmail: '邮箱',
  ID.loginEmailHintText: '输入邮箱',
  ID.loginPassword: '密码',
  ID.loginPasswordHintText: '输入密码',
  ID.loginForgetPassword: '忘记密码？',
  ID.loginRegisterNew: '注册新账号',
  ID.loginOkPhone: '请输入正确的手机号',
  ID.loginOkEmail: '请输入正确的邮箱',
  ID.loginPswTip: '请设置6-20个数字和字母组成的密码',
  ID.loginSuccess: '登录成功',
  ID.emptyWallethint: '保护资产安全，收发快捷方便',
  ID.stringGuideTitle: '不止超越 更重安全',
  ID.stringGuideSubTitle: '冷热分离 私钥永不触网',
  ID.stringBindNow: '现在连接',
  ID.stringBindBuyU1: '选购 U1',
  ID.stringGuideimmediatelyExperience: '立即体验',
  ID.stringAllWallet: '所有钱包',
  ID.stringDefaultWallet: '使用中',
  ID.stringBalance: '余额',
  ID.stringRegister: '注册',
  ID.stringCode: '验证码',
  ID.stringInputCode: '输入验证码',
  ID.stringGetCode: '获取验证码',
  ID.stringInvitationCode: '邀请码（选填）',
  ID.stringInputInvitationCode: '输入邀请码',
  ID.stringCollection: '收款',
  ID.stringReceiveAddress: '收款地址',
  ID.stringCopyAddress: '复制地址',
  ID.stringSaveImage: '保存图片',
  ID.stringAgreement1: '我已阅读并同意',
  ID.stringAgreement2: '《用户协议》',
  ID.stringHasAccount: '已有账号，',
  ID.stringGotoLogin: '立即登录',
  ID.stringCopySuccess: '复制成功',
  ID.stringAll: '全部',
  ID.stringTransferSend: '转帐',
  ID.stringSendAddress: '发送地址',
  ID.stringReciveAddress: '接收地址',
  ID.stringComplte: '完成',
  ID.stringTxFail: '已失败',
  ID.stringReadAgreement: '请阅读并同意《用户服务协议》',
  ID.stringOKCode: '请输入正确的验证码',
  ID.stringPswTip1: '6-20个字符',
  ID.stringPswTip2: '密码不可以为空',
  ID.stringPswTip3: '必须同时包含字母及数字，不可以包含特殊字符',
  ID.stringPswTip4: '与首次输入密码相同',
  ID.stringPswTip5: '确认密码不可以为空',
  ID.stringConfirmPswHit: '再次输入密码',
  ID.stringSetPassword: '设置密码',
  ID.stringRegisterSuccess: '注册成功',
  ID.forgetTitle: '忘记密码',
  ID.resetPasswordSuccess: '重置成功',
  ID.sendAddress: '发送地址',
  ID.sendCoin: '发送币种',
  ID.receiveAddress: '接收地址',
  ID.sendNumber: '数量',
  ID.sendAvailable: '可用',
  ID.sendButton: '转账',
  ID.sendMaxText: '最大',
  ID.stringChange: '修改',
  ID.stringChangePassword: '修改密码',
  ID.oldPassword: '旧密码',
  ID.oldHintPassword: '输入旧密码',
  ID.newPassword: '新密码',
  ID.newHintPassword: '输入新密码',
  ID.okNewPassword: '确认新密码',
  ID.okNewHintPassword: '再次输入新密码',
  ID.passwordChangeSuccess: '修改成功',
  ID.inputOkToAddress: '请输入正确的接收地址',
  ID.balanceNot: '余额不足',
  ID.broadcastSending: '发送中，请稍等…',
  ID.broadcastSuccess: '交易广播成功',
  ID.broadcastError: '交易广播失败',
  ID.broadcastReset: '重试',
  ID.stringBack: '返回',
  ID.stringSendOneself: '转给自己',
  ID.stringAddress: '地址',
  ID.storageAccessNotEnabled: '未开启存储权限',
  ID.cameraAccessNotEnabled: '相机权限未开启',
  ID.contactPerson: '联系人',
  ID.insertaddress: '输入地址',
  ID.addressTag: '联系人名称',
  ID.addAddress: '添加联系人',
  ID.usernameEditor: '修改昵称',
  ID.accountback: '退出账号',
  ID.bindEmail: '绑定邮箱',
  ID.stringonlineservice: '联系客服',
  ID.stringuserfeedback: '反馈意见',
  ID.stringhelpqq: '官方QQ群',
  ID.stringhelpwechat: '官方客服微信',
  ID.stringhelptel: '客服电话',
  ID.feedbackname: '您的称呼',
  ID.feedbackcontext: '您要反馈的内容,不超过300字',
  ID.feedbackmode: '您的联系方式(QQ、微信号、电话号码等)',
  ID.submitfeedback: '提交反馈',
  ID.feenotegasprice: '当前最佳矿工费',
  ID.makeqr: '生成二维码',
  ID.textcontent: '输入文本内容',
  ID.contentText: '内容',
  ID.timesync: '同步时间',
  ID.afterRefresh: '@seconds 秒后刷新',
  ID.bindHardwareWallet: '连接硬件钱包',
  ID.verifysn: '防伪验证',
  ID.p4verifysucess: '验证成功',
  ID.p4verifyfail: '验证失败',
  ID.p4verifyRemind2: '设备信息验证通过，请使用设备扫码，以进行签名。',
  ID.p4verifyRemind: '扫码验证签名',
  ID.verifysnSucceedHint: '此钱包设备为官方出售的正品，感谢您的支持。',
  ID.verifySnTime: '首次验证时间',
  ID.errwalletFake: '此钱包设备非官方正品,请您谨慎使用。',
  ID.stringNotices: '提示：',
  ID.textTimesyncInfo:
      'Ultra  \n1.打开硬件钱包，进入首页。\n2.点击\\"扫一扫\\"按钮，扫描以上二维码。\n\n Pro 3+\n1.打开硬件钱包，进入首页。\n2.点击\\"扫一扫\\"按钮，扫描以上二维码。\n\n Pro 3\n1.打开硬件钱包，进入首页。\n2.点击\\"菜单\\" ->\\"设置\\"->\\"时间\\"。\n3.点击"扫码同步"按钮，扫描以上二维码。。',
  ID.stringOfficialEmail: '官方邮箱',
  ID.stringTextDial: '拨打',
  ID.textFeedbackDialogMessage: '提交成功，谢谢您的反馈！',
  ID.textRemind: '请完善信息',
  ID.textUnbounded: '未绑定',
  ID.textBindPhone: '绑定手机',
  ID.texSubmit: '提交',
  ID.textbindSuccess: '绑定成功',
  ID.manageCoin: '币种管理',
  ID.supportToken: '已添加币种',
  ID.nosupportToken: '待添加币种',
  ID.tokenSearch: '代币简称、名称、合约地址',
  ID.notifiAllRead: '全部已读',
  ID.notifiTxTitle: '转账通知',
  ID.notifiSysTitle: '系统通知',
  ID.textChooseChain: '请选择主链',
  ID.textCorrectAddress: '请输入正确的地址',
  ID.textChain: '主链',
  ID.submitToken: '提交Token',
  ID.tokenSymbol: '币种名称',
  ID.tokenContracts: '合约地址',
  ID.enterContractsInfo: '输入合约地址',
  ID.addContractRemind1:
      '1.您只需要向我们提供token的合约地址，审核通过后即可正常使用，可联系客服获知审核进度；\n2.如果需要显示您的token logo，请将以下指定信息发送至********************，或者联系客服。 \n',
  ID.stringConfirmPsw: '确认密码',
  ID.addContractRemind2:
      '① 合约地址； \n② logo.png (500像素x500像素)； \n③ 公司、个人或官网等相关信息； \n④ 其他情况说明。 \n',
  ID.importTokenSymbol: '输入币种名称',
  ID.addressBookEditContacts: '编辑联系人',
  ID.addressBookDelContacts: '删除联系人',
  ID.addressBookDelContactsContent: '确认删除@contacts联系人？',
  ID.addressBookDelContactsConfirm: '确认删除',
  ID.addressAlreadyExists: '该地址已存在!',
  ID.unsearchToken: '未找到指定的Token?',
  ID.tokenLabel: '类型',
  ID.authFingerprintTip: '请按压屏内指纹感应区验证指纹',
  ID.authContnetTextFace: '如需使用面容ID解锁，请到系统设置开启权限',
  ID.authContnetTextFingerprint: '如需使用指纹解锁，请到系统设置开启权限',
  ID.authContnetTextSaveFingerprint: '您还没有录入指纹，请在设置界面录入指纹',
  ID.gotoSettingTitle: '去设置',
  ID.authFaceUnlock: '面容解锁',
  ID.authFingerprintUnlock: '指纹解锁',
  ID.authFingerprintTap: '按压指纹识别器',
  ID.authFaceTitle: '面容ID',
  ID.authFingerprintTitle: '指纹验证',
  ID.stringTips: '提示',
  ID.nfcOpen: '已开启',
  ID.nfcClose: '已关闭',
  ID.addHardwareTitle: '添加硬件钱包',
  ID.bindWalletTitle: '连接钱包',
  ID.scanBindTitle: '扫码连接',
  ID.pageCount: '暂缺第@page页',
  ID.noinstallQQ: '未安装QQ或安装的版本不支持',
  ID.noinstallWechat: '未安装微信',
  ID.scanResult: '扫描结果',
  ID.copy: '复制',
  ID.zxingError: '二维码扫描错误，请重新扫描',
  ID.pleaseScanPage: '请扫第@scanPage页',
  ID.scanNotMatchWallet: '扫码信息与当前选择钱包不符',
  ID.scanError: '扫码二维码错误，请重试',
  ID.bindSucessInfo: '您已成功连接硬件钱包@wallet，快去使用吧',
  ID.bindSucess: '绑定成功!',
  ID.openWallet: '打开钱包',
  ID.chainManager: '管理',
  ID.chainSegWitP2sh: '兼容',
  ID.chainSegWit: '隔离',
  ID.chainLegacy: '普通',
  ID.chainSegWitP2shItem: '隔离见证(兼容)',
  ID.chainLegacyItem: '普通地址',
  ID.chainSegWitItem: '隔离见证(原生)',
  ID.chainSelectWallet: '选择钱包',
  ID.chainWalletManager: '钱包管理',
  ID.chainAddAddress: '添加地址',
  ID.chainAddressDetail: '地址详情',
  ID.chainEditRemark: '编辑备注名',
  ID.chainAddressDetailTip1: '仅支持接收',
  ID.chainAddressDetailTip2: '网络资产',
  ID.chainAddType: '类型',
  ID.chainAddRemark: '备注名',
  ID.chainComplete: '完成',
  ID.chainAllChains: '全部网络',
  ID.chainTotalAssets: '资产总值',
  ID.allWallets: '所有钱包',
  ID.walletInfo: '钱包资料',
  ID.walletName: '钱包名称',
  ID.bindingTime: '绑定时间',
  ID.deviceModel: '设备型号',
  ID.deviceId: '设备ID',
  ID.seVersion: '安全芯片版本',
  ID.walletAppVersion: '应用版本',
  ID.unbind: '断开连接',
  ID.using: '使用中',
  ID.bindFailed: '连接失败!',
  ID.rebind: '再次连接',
  ID.accountDelete: '注销账号',
  ID.accountDeleteTip: '是否注销当前账号？',
  ID.safeSetting: '安全设置',
  ID.deleteError: '注销失败',
  ID.deleteSuccess: '注销成功',
  ID.loading: '加载中...',
  ID.binding: '正在连接...',
  ID.sameMnemonicWallet: '检测到已导入相同钱包，是否覆盖？',
  ID.sureDeleteWallet: '确定要断开连接吗',
  ID.textAddressCount: '(@indexAdd个地址)',
  ID.textAccountCount: '(@indexAcc个账户)',
  ID.baseSettings: '偏好设置',
  ID.myTitle: '我的',
  ID.feeSettingTitle: '矿工费设置',
  ID.feeTitle: '矿工费',
  ID.gearSelect: '档位选择',
  ID.customizeTitle: '自定义',
  ID.fastTitle: '快速',
  ID.normalTitle: '标准',
  ID.slowTitle: '缓慢',
  ID.estimateTxTime: '预计交易时间 @minute',
  ID.minerFeeTitle: '矿工费率',
  ID.minuteTitle: '分钟',
  ID.hourTitle: '小时',
  ID.secondTitle: '秒',
  ID.p4seedRemind:
      '助记词增强模式下，私钥由助记词和密码共同生成，密码不可更改。需要同时拥有助记词和密码才能恢复钱包，两者缺一不可。\n\n 普通模式下，私钥仅由助记词生成，密码可更改。只要拥有助记词就可以恢复钱包。\n\n请务必保管好助记词并牢记密码。',
  ID.p4seedIspharase: '助记词增强模式',
  ID.p4seedIspharaseYes: '是',
  ID.p4seedIspharaseNo: '否',
  ID.stringBind: '绑定',
  ID.textPro1Binding:
      '1.打开硬件钱包，选定某币种并打开“接收”页面；\n\n 2.进入“我的地址”列表，选中需要绑定的地址；\n\n 3.跳转到“余额”页面，点击“更新余额”，显示二维码；',
  ID.scanConnect: '扫码连接',
  ID.customFeeConfirm: '矿工费设置未确认，确认要离开此页？',
  ID.customFeeSuccess: '自定义矿工费已生效',
  ID.feeSlowTips: '矿工费率过低，将会影响交易确认时间',
  ID.feeFastTips: '矿工费率过高，将会造成矿工费浪费',
  ID.feeOkTips: '请输入有效的矿工费率',
  ID.inputToAddressTip: '请输入收款地址',
  ID.inputOkToAddressTip: '请输入正确的地址',
  ID.inputAmountTip: '请输入支付金额',
  ID.amounNumberError: '请输入正确的金额',
  ID.transferToSelfTip: '无法给自己转账',
  ID.insufficientBalanceFee: '余额不足以抵扣矿工费',
  ID.insufficientBalance: '余额不足',
  ID.insufficientMainFee: '矿工费不足',
  ID.coinTransferTitle: '@symbol 转账',
  ID.coinTsTypeTitle: '交易类型',
  ID.qrTitleTransfer: '签名交易',
  ID.qrAutoPage: '自动翻页',
  ID.qrManualPage: '手动翻页',
  ID.scanQrSendTitle: '扫码发送',
  ID.qrSettingQrsizeTitle1: '使用硬件钱包扫码\n识别困难？',
  ID.qrSettingQrsizeTitle2: '设置二维码容量 >',
  ID.sendAmountSmall: '转账金额太小，不能转账',
  ID.completionProgress: '当前进度',
  ID.ethGasPriceSlowTip: '您设置的最高基本费用低于当前网络基本费用@fee，交易成功需等待较长时间',
  ID.ethGasPriceLargeTip: 'Gas price偏高，可能浪费矿工费',
  ID.ethGasLimitTip: 'Gas上限必须大于@limit小于15.00M',
  ID.filGasEmtyTips: '请输入有效的Gas',
  ID.filGasSlowTips: 'Gas过低可能导致Out of Gas交易失败',
  ID.ethGasPriceMinTip: 'Gas价格不得低于@gas',
  ID.remarkMemo: '备注(Memo)',
  ID.remark: '备注',
  ID.remarkNoText: '暂无备注',
  ID.remarkHitText: '选填',
  ID.remarkTag: '备注(Tag)',
  ID.xrpRemarkTips: 'XRP主链规定，地址余额中只有大于@value XRP的部分才可进行转账。',
  ID.xrpTsTips: '余额不足！XRP主链规定，地址余额中只有大于@value XRP的部分才可进行转账。',
  ID.xrpNoActivityTips: '账户未激活，请往该地址一次性转入不低于@valueXRP以激活账户',
  ID.maxFeeValidTip: '请输入有效的 maxFee',
  ID.maxFeeNoLessMaxPriorityFeeTip: 'maxFee 不能小于 maxPriorityFee',
  ID.maxPriorityFeeTip:
      '设置的 maxPriorityFee @inputMax 可能无法发挥全部效用，maxPriorityFee @rawMax 、currentBaseFee @baseFee 之和大于设置的 maxFee @maxFee，调高 maxFee 以发挥 maxPriorityFee 全部效用',
  ID.maxPriorityFeeHeightTip: 'maxPriorityFee 偏高，可能浪费矿工费',
  ID.maxFeeHeightTip:
      '设置的 maxFee 偏大，超过预估 @rawFee Gwei 的 4 倍以上，只提高 maxFee 可能无法让交易更快被打包',
  ID.tronActivatedTip: '该地址未激活，将消耗 1 TRX 激活该地址',
  ID.tronNeedSource: '交易所需资源',
  ID.valueBandwidth: '@bandwidth 带宽',
  ID.valueEnergy: '@energy 能量',
  ID.activedAccount: '激活账户',
  ID.deductionValueBandwidth: '抵扣 @bandwidth 带宽',
  ID.deductionValueEnergy: '抵扣 @energy 能量',
  ID.tronPopTip: '账户可用 TRX 不足以抵扣矿工费，交易可能失败',
  ID.tronRemarkTip: '添加转账备注，将额外消耗1TRX的手续费，不超过200个字符',
  ID.tronResourceDetail:
      '交易所需资源 = 带宽消耗 + 能量消耗\n\n带宽消耗:\n接收账户交易只能扣除免费带宽或质押带宽，若二者都不满足该笔交易消耗，则需要全部用 TRX 支付，无法部分扣除\n\n能量消耗:\n交易支持扣除全部能量后，剩余部分由 TRX 支付\n\n交易所需资源按用户支付全部资源进行估算，实际消耗资源以链上数据为准',
  ID.tronFeeDetail:
      'TRON网络费用：\n\n用户在TRON网络上的费用包括因消耗带宽或能量不足而需要抵扣的TRX费用，以及特定交易手续费（例如，激活账户需支付1 TRX）。\n\n所有费用均由链自行收取，实际消耗的数量以链上数据为准。',
  ID.tronRiskTip: '继续交易会消耗账户剩余 TRX/资源，且存在交易失败的风险，请确认是否继续',
  ID.basicEnergy: '正常消耗',
  ID.extraEnergy: '额外消耗',
  ID.tronLearnMore: '交易调用热门合约会额外消耗能量，了解详情',
  ID.fromAccount: '发送账户',
  ID.toAccount: '接收账户',
  ID.addressLabelEmpty: '请输入备注名',
  ID.feeRang: '预估范围',
  ID.bindWallet: '绑定钱包',
  ID.scanQrBind: '扫码绑定',
  ID.verifySuccessTitle: '防伪验证成功！',
  ID.verifyFailTitle: '防伪验证失败！',
  ID.verifySuccessSubTitle: '此钱包设备为官方出售的正品，感谢您的支持',
  ID.verifyFailSubTitle: '此钱包设备非官方出售的正品，请您谨慎使用',
  ID.goBackHome: '返回首页',
  ID.stringResource: '资源',
  ID.stringDelegateResource: '代理资源',
  ID.stringVote: '节点投票',
  ID.stringStake2: '质押资产2.0',
  ID.stringUnstake2: '解锁资产2.0',
  ID.stringStake: '质押资产',
  ID.stringUnstake: '解锁资产',
  ID.stringReclaim: '回收资源',
  ID.stringReclaimAddress: '回收地址',
  ID.stringConfirming: '确认中',
  ID.stringPacking: '打包中',
  ID.stringSendFail: '已失败',
  ID.stringTxTime: '交易时间',
  ID.stringTxId: '交易ID',
  ID.stringTxBlock: '区块',
  ID.stringTxSource: '消耗资源',
  ID.stringTxEnergy: '能量',
  ID.stringTxBandwidth: '带宽',
  ID.stringTxWithdraw: '提取 TRX',
  ID.stringBurnedEnergy: '燃烧抵扣能量',
  ID.stringBurnedBandwidth: '燃烧抵扣带宽',
  ID.stringBlockchain: '在区块链浏览器中查看',
  ID.stringSearchEmpty: '未找到指定的 Token？',
  ID.stringSubmitToken: '提交 Token',
  ID.stringInputOkContract: '请输入正确的合约地址',
  ID.stringAddTokenSuccess: '提交成功，请等待审核!',
  ID.stringBuyWallet: '购买硬件钱包',
  ID.stringGuideTitle1: '多链资产钱包',
  ID.stringGuideSubTitle1: '专为硬件钱包定制，在保证资产安全的前提下，提供更方便友好的Web3交互体验。',
  ID.stringGuideTitle2: '欢迎使用Coinbag钱包',
  ID.stringGuideSubTitle2: '即刻享受更好的资产转账、余额概览、质押收益等功能，请绑定硬件钱包后使用。',
  ID.stringEmptyTitle: '连接钱包，管理资产',
  ID.stringEmptyTitle2: '多链支持，全面覆盖',
  ID.stringEmptySubTitle2: '管理全球优质币种，支持主流NFT',
  ID.stringAllNetTitle: '查看全部网络',
  ID.stringWeb3Title: 'Web3新体验',
  ID.stringWeb3SubTitle: '专为硬件钱包定制',
  ID.stringWeb3Title1: '轻松上手',
  ID.stringWeb3SubTitle1: '更方便友好的Web3交互体验',
  ID.stringWeb3Title2: '安全透明',
  ID.stringWeb3SubTitle2: '三层防护，离线储存 ，永不触网，内置安全芯片守护大额资产安全',
  ID.stringWeb3Title3: '多链聚合',
  ID.stringWeb3SubTitle3: '一个钱包完成区块链储存和管理',
  ID.stringSupportChains: '现在已支持的@count个网络',
  ID.stringCreateNewWallet: '创建新钱包',
  ID.stringScanQrBind: '二维码连接',
  ID.stringScanQrBindDes: '扫一扫，连接硬件钱包，轻松管理数字资产',
  ID.stringNFCBind: 'NFC连接',
  ID.stringNFCBindDes: '一触即连Touch卡，极速连接硬件钱包，安全高效。',
  ID.stringTouchPsw: '请输入密码',
  ID.stringTouchPhone: '将卡片靠近手机',
  ID.stringTouchNONfc: '当前设备不支持NFC功能',
  ID.stringTouchReadError: '读取错误，请重试',
  ID.stringTouchReading: '读取中，请勿移走卡片',
  ID.stringTouchNfcSuccess: '操作成功',
  ID.stringNfcPswMax: '密码错误次数已达上限，钱包已重置',
  ID.stringNfcInputOnePsw: '还可尝试 1 次密码，再次错误后将重置钱包',
  ID.stringNfcPswErrorNum: '密码不正确，再错误 @count 次后将重置钱包',
  ID.stringNfcPswError: '密码不正确，请重试',
  ID.stringNfcScanError: '扫描失败，请重试',
  ID.stringCardHaveWallet: '此卡片已存在钱包',
  ID.stringCardWalletError: '钱包不符',
  ID.stringCardNumberError: '卡号错误',
  ID.stringNfcUnknownError: '未知错误',
  ID.stringTouchNoOfficial: '此卡片非Coinbag官方出品',
  ID.stringTouchSpotError: '无法识别，请读取正确的卡片',
  ID.stringToucReadCard: '读取卡片',
  ID.stringTouchReadTips: '读取卡片需要一定时间，请保持卡片与手机接触。',
  ID.stringNewWallet: '新钱包',
  ID.stringCreateTitle: '创建',
  ID.stringCreateDesTitle: '未备份助记词，请选择“创建钱包”',
  ID.stringImportTitle: '导入',
  ID.stringImportDesTitle: '已备份助记词，请选择“导入钱包”',
  ID.stringCreateWalletTitle: '创建钱包',
  ID.stringImportWalletTitle: '导入钱包',
  ID.stringAgreementTip: '请阅读并同意《用户协议》',
  ID.stringTouchNumber: '请使用尾号为@number的卡片',
  ID.stringCreating: '正在创建...',
  ID.stringImporting: '正在导入...',
  ID.stringCreateSuccess: '创建成功',
  ID.stringImportSuccess: '导入成功',
  ID.stringInputMnemonic: '请输入正确的助记词',
  ID.stringVerifyFail: '验证失败',
  ID.stringImportMnemonic: '导入助记词',
  ID.stringBackupMnemonic: '备份助记词',
  ID.stringBackupMnemonicDes: '助记词可用于恢复钱包，掌控钱包资产，请务必按顺序抄写到纸上并妥善保管！',
  ID.stringVerifyMnemonic: '验证助记词',
  ID.stringOrderInputMnemonic: '按顺序输入助记词',
  ID.stringTouchPasswordTip: '6-32个字符，可包含a-z，A-Z和0-9。密码遗失不可找回，请牢记',
  ID.stringTouchInputPsw: '输入密码',
  ID.stringTouchInputPswTwo: '请确认密码',
  ID.stringTouchImInputCorrectPsw: '请输入正确的密码',
  ID.stringTouchTowPswFail: '两次密码不一致',
  ID.stringTouchSetWalletName: '设置钱包名称',
  ID.stringTouchInputWalletNameHint: '输入钱包名称',
  ID.stringTouchWalletNameToast: '请输入钱包名称',
  ID.stringTouchNextTitle: '下一步',
  ID.stringTouchSafeTitle: '安全提示',
  ID.stringTouchPopTip1: '为保护助记词安全，请确保周围没有他人及监控设备',
  ID.stringTouchPopTip2:
      '重要提醒：\n·请勿泄密\n·切勿在任何网页中输入\n·切勿通过网络工具传输\n·切勿使用手机拍照\n·不要存储电子版\n·请勿泄密',
  ID.stringTouchPopOkTitle: '我知道了',
  ID.stringTouchEnglishTitle: '英文',
  ID.stringTouchChineseTitle: '中文',
  ID.stringTouchNumberTitle: '数字',
  ID.stringWalletIdTitle: '钱包ID',
  ID.stringResetCardTitle: '重置卡片',
  ID.stringChangePswTitle: '修改密码',
  ID.stringChangeWalletNameTitle: '修改钱包名称',
  ID.stringCardEmpty: '此卡片为空卡',
  ID.stringBackupComplete: '备份完成',
  ID.stringVerifybackupTitle: '验证备份',
  ID.stringWalletNameNoEmpty: '钱包名称不能为空',
  ID.stringWalletNameMax12: '最多12个字符',
  ID.stringSetWalletName: '设置钱包名称',
  ID.stringNewNameSameOldName: '新名称不能与原名称相同',
  ID.stringNameChangeSuccess: '修改成功',
  ID.stringInputOldPswTip: '请输入旧密码',
  ID.stringInputNewPswTip: '请输入新密码',
  ID.stringInputNewPswTwoTip: '请确认新密码',
  ID.stringTouchVerifyPswTitle: '验证密码',
  ID.stringOldNewSameTip: '新密码不能与旧密码相同',
  ID.stringTouchResetSuccess: '重置成功',
  ID.stringTouchSignErrorTip: '签名数据错误',
  ID.stringTronRawDataHexError: 'Tron交易验签失败',
  ID.stringEmptyWalletTitle: 'Web3入口，一个就够',
  ID.stringEmptyWalletSubTitle: '钱包 · 交易 · NFT · DeFI · DApp',
  ID.stringTabWallet: '钱包',
  ID.stringTabDiscover: '发现',
  ID.stringTabProfile: '我',
  ID.stringBackupTip: '请及时备份助记词，以避免手机、钱包设备丢失或损坏造成资产损失。',
  ID.stringBackupButtonTitle: '立即备份',
  ID.stringJumpTitle: '跳过',
  ID.stringNfcReading: '已准备好扫描',
  ID.stringSignLoading: '签名中，请等待',
  ID.stringBingdingTitle: '立即绑定',
  ID.stringCreateDesString: '您已成功创建钱包，快去使用吧！',
  ID.stringImportDesString: '您已成功导入钱包，快去使用吧！',
  ID.stringCreateLoading: '创建中，请稍等',
  ID.stringImportLoading: '导入中，请稍等',
  ID.stringNfcCloseAndroidTip: 'NFC已关闭，请在“设置”中打开。',
  ID.stringNfcCloseIOSTip: 'NFC已关闭，请在“设置 -> 通用”中打开。',
  ID.stringTxError01: '请更新余额后重新发送',
  ID.stringTxError02: '交易已广播，请勿重复广播',
  ID.stringTxError03: '支付金额过低，请调整后重新发送！',
  ID.stringTxError04: '请填写备注后重新发送',
  ID.stringTxError05: '交易已失效，请重新发送',
  ID.stringTxError06: '发送失败，请检查资源是否充足',
  ID.stringTxError07: '交易未成熟，请达到成熟期后发送<',
  ID.stringTxError08: 'UTXO数量过大，请调整金额查询发送',
  ID.stringTxError09: '转账金额超出地址限额。',
  ID.stringTxError10: '请更新余额后重新发送',
  ID.stringTxError11: '矿工费太高，请调整矿工费重新发送！',
  ID.stringTxError12: '支付金额过低，请调整后重新发送！',
  ID.stringTxError13: '发送失败，交易包含未成熟的币，挖矿奖励的币必须等成熟后才能发送，成熟期为100个确认',
  ID.stringTxError14: '矿工费过低，请调整矿工费重新发送。',
  ID.stringTxError15: '发送失败，请检查系统时间是否正确。',
  ID.stringTxError16: '发送失败，请确认金额是否正确。',
  ID.stringTxError17: '发送失败，瑞波币官方限制：账户余额不能低于20 XRP！',
  ID.stringTxError18: '该账户尚未激活！',
  ID.stringTxError19: '矿工费过低，请重新设置。',
  ID.stringTxError20: '请填写备注后重新发送',
  ID.stringTxError21: '该笔交易GAS过低，可能交易失败，若长时间未确认请调整GAS重新发送。',
  ID.stringTxError22: '请等待上一笔交易完成，或提高10%以上的矿工费再进行转账',
  ID.stringTxError23: 'GasLimit过低，请调整后重新发送',
  ID.stringSetting: '设置',
  ID.stringP2SHDes: '兼容 \"Legacy\" 和 \"Segwit\" 地址类型,交易费中等,地址以\"3\"开头',
  ID.stringP2TRDes: 'Taproot:更高的隐私性和效率,以\"bc1p\"开头',
  ID.stringP2WPKHDes: '当前主流的地址类型,交易费较低,地址以\"bc1\"开头',
  ID.stringP2PKHDes: '最初的比特币地址格式,交易费较高,地址以\" 1\"开头',
  ID.stringSignMessage: '签名消息',
  ID.stringMessgaeSign: '消息签名',
  ID.stringSignMethodTitle: '签名方法',
  ID.stringMessageContentTitle: '消息内容',
  ID.stringSignTextHint: '请输入或粘贴需要签名的文本信息',
  ID.stringSignTextTip: '请输入消息内容',
  ID.stringUnconfirmedUtxo: '包含未确认的UTXO资产',
  ID.stringAddressPublicTitle: '地址公钥',
  ID.stringUnavailableBalance: '不可用余额',
  ID.stringTotalBalance: '总资产:',
  ID.stringNotSupportedTaproot: '当前钱包不支持Taproot地址',
  ID.stringDownloadFailed: '下载失败',
  ID.stringUpgradeNow: '立即更新',
  ID.stringTalkLater: '稍后再说',
  ID.stringBackButton: '返回按钮',
  ID.stringNewVersion: '发现新版本',
  ID.stringCoinbagISavailable: 'Coinbag有新版本了！',
  ID.stringCheckForUpdates: '检查更新',
  ID.stringHaveLatestVersion: '已经是最新版本了',
  ID.stringP2SHDLTCes: '当前主流的地址类型,地址以\"M\"开头',
  ID.stringP2WPKHLTCDes: '隔离见证（SegWit）技术的地址格式，提供了更高的效率和更低的交易费用,地址以\"ltc1\"开头',
  ID.stringP2PKHLTCDes: '最初的莱特币地址格式,地址以\"L\"开头',
  ID.stringBroadcast: '广播',
  ID.stringBroadcastTitle: '点击下面按钮广播',
  ID.stringNodeTimeOut: '节点请求超时，请稍后重试',
  ID.stringNotAddedToAddressBook: '未添加到地址簿,',
  ID.stringAddNow: '去添加',
  ID.stringAddressInTheAddressBook: '地址簿中的地址',
  ID.stringPaste: '粘贴',
  ID.stringSupportsReceivingNetwork: '仅支持接收@receiveInfo网络资产',
  ID.stringEosEmptyTitle: '无账户名',
  ID.stringEosStake: '抵押资源',
  ID.stringEosEmptyDes: '由于EOS生态原因，暂不支持注册。',
  ID.stringEosBuyRam: '购买内存',
  ID.stringEosSellRam: '卖出内存',
  ID.stringEosRefoud: '赎回资源',
  ID.stringEosNewAccount: '注册账户',
  ID.stringEosBidname: '竞拍账户',
  ID.stringEosReceiveTheRefunded: '赎回领取',
  ID.stringExportAccountTitle: '导出账户',
  ID.stringEosPublicKeyTitle: 'EOS 公钥',
  ID.stringEosAccountTitle: 'EOS 账户',
  ID.stringScanCompleteTitle: '扫码完成',
  ID.stringNORemarkTitle: '没有标签',
  ID.stringCheckDetailTitle: '查看详情',
  ID.stringShowQRTitle: '显示二维码',
  ID.stringCopyAccmountTitle: '复制账户',
  ID.stringEditRemarkTitle: '编辑标签',
  ID.stringEthNotSupportedTips: '以太坊已升级到 EIP155，旧版硬件钱包不再支持。请及时更换为新钱包以确保资产安全。',
  ID.stringNotSupportedChain: '当前硬件钱包已不再支持这条链',
  ID.stringZecNotSupportedTips: '旧版硬件钱包不再支持ZEC链，请及时更换为新钱包以确保资产安全。',
  ID.stringCPULease: 'CPU租赁',
  ID.stringEosTxInvalid: '交易已失效，请重新发送',
  ID.stringInsufficientResources: '发送失败，请检查资源是否充足',
  ID.stringEosAccountDetialTitle: 'EOS账户详情',
  ID.stringCreateDateTitle: '创建时间：@date',
  ID.stringAccountRemarkTitle: '账户标签：@tag',
  ID.stringThresholdTitle: '阈值：@value',
  ID.stringWeightTitle: '权重：@value',
  ID.stringVIPbenefits: 'VIP权益',
  ID.stringVIPTip1: '查看权益引导',
  ID.stringVIPTip2: '欢迎尊贵的VIP用户,您的尊享权益支持在\"我的\"页面查看,请跟随指引操作',
  ID.stringVIPTip3: '在"我的"页面点击"查看VIP权益"即可',
  ID.stringNotSupportEIP1559: '当前钱包不支持EIP1559交易',
  ID.stringResourceManager: '资源管理',
  ID.stringNetDetail: '带宽详情',
  ID.stringNetManager: '带宽管理',
  ID.stringStakeGet: '质押获得',
  ID.stringOtherDelegateOwner: '他人代理给自己',
  ID.stringDelegateToOther: '代理给他人',
  ID.stringFreeGet: '免费赠送',
  ID.stringDelegated: '已代理',
  ID.stringCanDelegate: '可代理',
  ID.stringDelegate: '代理',
  ID.stringReclaimTitle: '回收',
  ID.stringEnergy: '能量',
  ID.stringBandwidth: '带宽',
  ID.stringGetBandwidth: '获得带宽',
  ID.stringEnergyDetail: '能量详情',
  ID.stringEnergyManager: '能量管理',
  ID.stringGetEnergy: '获得能量',
  ID.stringTronNoSupportedTip: '受Tron网络升级Stake 2.0影响，当前钱包不支持此功能',
  ID.stringResourceToAddress: '资源接收地址',
  ID.stringResourceToAddressTip: '请输入资源接收地址',
  ID.stringDelegateAmount: '代理数量',
  ID.stringDelegateAmountTip: '请输入代理数量',
  ID.stringCanDelegateEnergy: '可代理：@value 能量',
  ID.stringCanDelegateBandwidth: '可代理：@value 带宽',
  ID.stringUseStakeTrx: '* 预计占用您质押的 @value TRX',
  ID.stringDelegateTip: '• 代理后 TRX 仍在质押账户里，仅将资源使用权代理给他人',
  ID.stringOk: '确定',
  ID.stringUseStakeAmount: '占用质押数量',
  ID.stringOtherGetResource: '* 预计对方获得 @value @resource',
  ID.stringResourceMaxTip: '超过当前最大使用数量',
  ID.stringInputHintTrx: '请输入TRX数量',
  ID.stringResourceOwnAddressTips: '资源接收地址不能为自己地址',
  ID.stringDelegateUseTrx: '可占用：@value TRX',
  ID.share: '分享',
  ID.penInBrowser: '浏览器打开',
  ID.copyLink: '复制链接',
  ID.favorites: '收藏',
  ID.switchWallet: '切换钱包',
  ID.successfullyAddedToFavorites: '收藏成功',
  ID.unfavorited: '已取消收藏',
  ID.recent: '最近',
  ID.viewAll: '查看全部',
  ID.recentlyUsed: '最近使用',
  ID.refresh: '刷新',
  ID.messages: '消息',
  ID.paymentDetails: '交易详情',
  ID.stringTronErrorMessage1: '未到解冻时间，请解冻后操作',
  ID.stringTronErrorMessage2: '请勿转到自己的地址',
  ID.stringTronErrorMessage3: '无可解冻资产',
  ID.stringDelegatePopAmount: '占用质押数量',
  ID.stringDeleagteEnergy: '代理能量',
  ID.stringDelegateBandwidth: '代理带宽',
  ID.stringResouecePopTips:
      '矿工费：包含燃烧抵扣资源矿工费和特定交易矿工费\n\n矿工费按用户支付全部资源进行估算，实际支付数量以链上数据为准',
  ID.stringSearchDapp: '搜索Dapp或输入网址',
  ID.stringSearchResult: '搜索结果',
  ID.stringSearchHistory: '搜索历史',
  ID.stringUnableToFind: '搜索不到该应用',
  ID.recommend: '推荐',
  ID.stringLockingResource: '锁定中资源',
  ID.stringCanReclaimTitle: '可回收',
  ID.stringDateTitle: '时间',
  ID.stringReclaimAmount1: '回收数量',
  ID.stringReclaimAmount2: '解除占用 TRX 数量',
  ID.stringCanReclaimValue1: '可回收：@value @resource',
  ID.stringCanReclaimValue2: '可解除：@value TRX',
  ID.stringInputReclaimHint1: '请输入回收数量',
  ID.stringInputReclaimHint2: '请输入解除占用TRX数量',
  ID.stringInputReclaimTip1: '* 预计解除占用您质押的 @value TRX',
  ID.stringInputReclaimTip2: '* 预计从对方地址回收 @value @resource',
  ID.stringReclaimEnergy: '回收能量',
  ID.stringReclaimBandwidth: '回收带宽',
  ID.stringResourcePopTip1: '注：硬件钱包回收显示为解锁',
  ID.stringResourcePopTip2: '注：硬件钱包代理显示为质押',
  ID.stringStakeTitle: '质押',
  ID.stringTotalAmount: '总资产',
  ID.stringTotalStakeAmount: '总质押',
  ID.stringUnlocking: '解锁中',
  ID.stringCanExtract: '可提取',
  ID.stringUnderstandingStaking: '了解质押2.0',
  ID.stringStakeBandwidthTips: '• 带宽需要质押TRX获得',
  ID.stringStakeEnergyTips: '• 能量需要质押TRX获得',
  ID.stringUnstakeTitle: '解锁',
  ID.stringFavorited: '已收藏',
  ID.stringEdit: '编辑',
  ID.stringWithraw: '提取',
  ID.stringGetAmount: '获得数量',
  ID.stringStakeGetResource: '可获取：@value @resource',
  ID.stringStakeHint1: '输入获取资源数量',
  ID.stringStakeHint2: '输入质押数量',
  ID.stringStakeAmount: '质押数量',
  ID.stringStakeTip1: '* 预计需质押 @trx TRX，同时获得 @vote 投票权',
  ID.stringStakeTip2: '* 预计获得 @value @resource，同时获得 @vote 投票权',
  ID.stringStakeTip3: '• 仅支持给自己质押，质押获得的资源可随时代理给他人',
  ID.stringStakeTip4: '• 解锁后需等待14天时间方可提取',
  ID.stringNoSearchResultsFound: '未找到搜索内容',
  ID.stringTrxCanUse: '可用：@value TRX',
  ID.stringStakeEnergy: '质押能量',
  ID.stringStakeBandwidth: '质押带宽',
  ID.stringUnstake1: '解锁资产1.0',
  ID.stringCanUnstakeTitle: '可解锁：@value TRX',
  ID.stringUnstakeTip: '• 解除TRX需要14天的等待期，解除质押的资产在14天后可取出',
  ID.stringClearAll: '清除全部',
  ID.stringUnstakeTrx2: '解锁 TRX 2.0',
  ID.stringUnstakeHint: '输入解锁数量',
  ID.stringUnstakeEnergy: '解锁能量',
  ID.stringUnstakeBandwidth: '解锁带宽',
  ID.stringUnstakeEnergy2: '解锁能量2.0',
  ID.stringUnstakeBandwidth2: '解锁带宽2.0',
  ID.stringDayToWithdraw: '@day 天后可提取',
  ID.stringUnlockingTrx: '解锁中 TRX',
  ID.stringWithdrawTrx: '提取 TRX',
  ID.stringTronNoSupportedStake2: '受Tron网络升级Stake 2.0影响，当前硬件钱包不支持质押资产2.0',
  ID.stringTronNoSupportedUnstake2: '受Tron网络升级Stake 2.0影响，当前硬件钱包不支持解锁资产2.0',
  ID.stringStake1DetialTitle: '质押详情',
  ID.stringStakeToOwnTitle: '为自己质押',
  ID.stringStakeToOtherTitle: '为他人质押',
  ID.stingCannotAccessThisWebsite: '无法访问此网站',
  ID.stingCannotAccessThisWebsiteVPN: '访问网站出错，请尝试重新加载或使用VPN',
  ID.stringRedirecting: '正在跳转三方网站',
  ID.stringPledgedTitle: '已抵押',
  ID.stringRedemptionTitle: '赎回中',
  ID.stringEosResourceTip1: '• 内存资源需要使用EOS购买',
  ID.stringEosResourceTip2: '• 带宽资源需要抵押EOS获取',
  ID.stringAvailableBalance: '可用余额',
  ID.stringOnlySupportsTaproot: '该Dapp仅支持Taproot或隔离见证原生地址格式,请在管理里添加地址',
  ID.stringRequestAccess: '申请访问你的钱包地址，确认将钱包地址公开给此网站吗？',
  ID.stringDeny: '拒绝',
  ID.stringRequestAuthorization: '申请授权',
  ID.stringCurrentNetworkNotSupported: '暂不支持当前网络',
  ID.stringContractCall: '合约调用',
  ID.stringAssetSecurityWarning1: '为保障资产安全，授权前请先确认了解相关风险和后果',
  ID.stringAssetSecurityWarning2: '我已了解授权的常用诈骗手段',
  ID.stringAssetSecurityWarning3: '以场外交易为由，要求扫码验资并引导授权。',
  ID.stringAssetSecurityWarning4: '以空投假币为由，要求兑换并引导授权。',
  ID.stringAssetSecurityWarning5: '以存币生息为由，要求兑换并引导授权',
  ID.stringAssetSecurityWarning6: '进入不明Dapp网址,并被诱导授权',
  ID.stringColdWalletAuthorizationWarning:
      '我已了解：一旦我的冷钱包地址授权给其他人，对方无需经过我的同意，即可转走我的钱包中已授权地址上的资产。',
  ID.stringWarning: '警告',
  ID.stringAuthorizeSmartContract: '授权智能合约',
  ID.stringAuthorizedAddress: '授权地址',
  ID.stringSignTransaction: '签名交易',
  ID.stringTransactionType: '交易类型',
  ID.stringData: '数据',
  ID.stringNftAmount: 'NFT数量',
  ID.stringNotCurrentlySupporte: '暂不支持此链',
  ID.stringHoldValue: '我持有的 @value',
  ID.stringValuation: '估值',
  ID.stringNftOverview: '概览',
  ID.stringNftActivity: '交易动态',
  ID.stringContractAddress: '合约地址',
  ID.stringTokenStandards: '代币标准',
  ID.stringNftNetwork: '网络',
  ID.stringNftTransfer: '转移',
  ID.stringNftFrom: '来自 @value',
  ID.stringNftTo: '去向 @value',
  ID.stringNftSale: '成交',
  ID.stringNftMint: '铸造',
  ID.stringNftBurn: '销毁',
  ID.stringNftSaleBuy: '购买',
  ID.stringNftSaleSell: '出售',
  ID.stringNftReceiveAddress: '接收地址 @value',
  ID.stringNftSendAddress: '发送地址 @value',
  ID.stringNftContractAddress: '合约地址 @value',
  ID.stringQrMaxNumberTip: '二维码页面过多，当前钱包暂不支持',
  ID.stringNftSuccess: '成功',
  ID.stringTargetAddress: '目标地址',
  ID.stringSendNft: '发送NFT',
  ID.stringNftSend: 'NFT 发送',
  ID.stringNftReceive: 'NFT 接收',
  ID.stringQRScanError: '二维码信息错误，请重新扫描',
  ID.stringBindAddressNow: '立即绑定',
  ID.stringNotBoundYet: '暂未绑定',
  ID.stringBindAddressInfo: '绑定主链才能添加地址',
  ID.stringTokenAddTitle: '添加 ERC20',
  ID.stringTokenAddTip1: '使用硬件钱包扫码以添加Token',
  ID.stringTokenPro1Tip1: 'Pro 1添加步骤:',
  ID.stringTokenPro1Tip2:
      '1.打开硬件钱包，进入“所有资产”页面；\n2.点击右上角图标，然后点击“添加币种”；\n3.点击“添加新资产”，扫描以上二维码。',
  ID.stringTokenPro2Tip1: 'Pro 2 / Pro 2+添加步骤:',
  ID.stringTokenPro2Tip2:
      '1.打开硬件钱包，进入“我的资产”页面;\n2.点击右上角图标，进入“币种管理”页面;\n3.点击右上角加号图标，进入“添加TOKEN”页面;\n4.点击“扫码添加TOKEN”按钮，扫描以上二维码。',
  ID.stringTokenPro3Tip1: 'Pro 3添加步骤:',
  ID.stringTokenPro3Tip2:
      '1.打开硬件钱包，进入“我的钱包”页面;\n2.点击右上角加号图标，进入“币种管理”页面;\n3.点击右上角加号图标，进入“添加TOKEN”页面;\n4.点击“扫码添加Token”按钮，扫描以上二维码。',
  ID.stringSyncAmount: '同步余额',
  ID.stringSyncAmountValue: '@value 同步余额',
  ID.stringCurrentSyncAmount: '本次同步数额',
  ID.stringSyncAmountTip1: '请用硬件钱包扫码',
  ID.stringSyncAmountTip2:
      '每次发送交易前请在冷钱包设置合适的矿工费率，当前最佳 Gas Price 为 @value  Gwei。',
  ID.stringSyncAmountTip3: '每次发送交易前请在冷钱包设置合适的矿工费率，当前最佳矿工费率为 @value BTC/KB 。',
  ID.stringP1info: '1.打开硬件钱包，选定某币种并打开“接收”页面；”\n2.跳转到“余额”页面，点击“更新余额”，显示二维码；',
  ID.stringP2info:
      '1.打开硬件钱包\n2.点击“功能”->“导出地址”\n3.选中需要绑定的地址，点击“导出”按钮，显示二维码\n4.点击下方“扫码绑定”按钮，扫描冷端二维码',
  ID.stringP2Plusinfo:
      '1.打开硬件钱包\n2.点击“功能”->“监控账户”\n3.选中需要绑定的币种，点击“导出”按钮，显示二维码\n4.点击下方“扫码绑定”按钮，扫描冷端二维码',
  ID.stringP3info:
      '1.打开硬件钱包；\n2.打开系统菜单;然后选择“监控账户”\n3.选中需要监控的币种,点击“传至APP”按钮,可使用二维码\n4.点击下方“扫码绑定”按钮,根据提示进行绑定。',
  ID.stringNotBoundInfo: '该硬件钱包未绑定，请先绑定',
  ID.stirngSyncBroadInfo: '同步广播结果',
  ID.stringConfirmTransaction: '确认交易',
  ID.stringNoSupportChain: '由于生态原因，不再支持此链',
  ID.stringNoMonitorAddress: '未查询到地址，请先监控该地址',
  ID.stringSwitchAddress: '切换地址格式',
  ID.stringNotBoundInfoAddres: '该硬件钱包的地址未绑定，请先绑定',
  ID.stirngSelectHardwareWallet: '选择硬件钱包',
  ID.stirngSwitchHardwareWallet: '当前选中硬件钱包和扫码中的硬件钱包不符，请切换',
  ID.stirngBroadcastNoData: '广播数据缺失，请重试',
  ID.stringNoSupportedFeature: '生态原因，不再支持此功能',
  ID.stringNetworkDiagnostics: '网络诊断',
  ID.stringNetworkStatus: '请检查你的网络设置',
  ID.stringXrpNotSupportedTips: '旧版硬件钱包不再支持XRP，请及时升级新钱包以确保资产安全，有问题联系客服。',
  ID.stringSynAddressTitle: '同步地址',
  ID.stringSynAddressTip:
      '若需要添加新地址需在硬件钱包中操作，之后同步地址到热端，操作步骤如下：\n1.打开硬件钱包，选中Solana链，进入收款二维码页面；\n2.点击右上角图标，进入地址管理页面；\n3.选中需同步的地址，进入同步地址页面；\n4.点击下方同步地址按钮，扫描硬件钱包二维码。',
  ID.stringSynChainError: '主链不匹配',
  ID.stringSynAddressError: '同步地址错误',
  ID.stringBuildError: '构建交易失败，请重新构建',
  ID.stringInputSolTip1: '请输入有效的 Compute Unit Limit',
  ID.stringInputSolTip2: '请输入有效的 Compute Unit Price',
  ID.stringInputSolTip3: '请发送至少 @value 以支付租金',
  ID.stringSolanaTxError1: '交易已过期或Blockhash错误',
  ID.stringBnbRecieveTip: '官方已不支持充币',
  ID.stringAddressSuccess: '添加成功',
  ID.stringSynSuccess: '同步成功',
  ID.stringBackgroundUpgrade: '已开始后台升级，请耐心等待！',
  ID.stringBackgroundUpgradeIng: '升级中...',
  ID.stringContactCustomerServiceToAdd: '联系客服添加',
  ID.stringContinueTx: '仍旧执行',
  ID.stringXrpAddressError: '查询XRP接收地址开户信息失败',
  ID.stringXripFeeError: '请输入有效的矿工费',
  ID.stringSolPriceMinTip: 'Compute Unit Price 低于 @value 可能导致交易失败',
  ID.stringBabylonError: 'Dapp 项目存在风险，暂不支持交易',
  ID.stringWebUserName: '用户名',
  ID.stringWebPassword: '密码',
  ID.stringWebLogin: '登录',
  ID.stringTouchChineseTip: '中文输入法无安全键盘，输入助记词可能存在安全风险，慎用中文助记词',
  ID.stringBestFeeTitle: '请输入转账数额，以便计算预估矿工费',
  ID.stringMinerFee: '预估矿工费',
  ID.stringBestFee: '最佳矿工费',
  ID.stringThemeMode: '主题模式',
  ID.stringLightMode: '日间模式',
  ID.stringDarkMode: '夜间模式',
  ID.stringThemeModeText: '我们将根据您设备的系统设置调整应用的主题模式',
  ID.stringSecurityVerification: '安全验证',
  ID.stringOpenSecurityTitle: '启动安全验证',
  ID.stringVerMethodTitle: '验证方式',
  ID.stringVerPasswordTitle: '密码',
  ID.stringBiometricsTitle: '生物识别',
  ID.stringVerChangePswTitle: '更改密码',
  ID.stringUnlockAppTitle: '解锁应用',
  ID.stringUnlockAppSub1Title: '需要密码才能解锁应用',
  ID.stringUnlockAppSub2Title: '需要生物识别才能解锁应用',
  ID.stringAuthTsTitle: '授权交易',
  ID.stringAuthTsSub2Title: '使用生物识别或密码授权交易或添加账户',
  ID.stringCreatePasswordTitle: '创建密码',
  ID.stringPasswordNotEqualTip: '两次密码不一致，请重新输入',
  ID.stringOpenBiometricsTip: '是否启用生物识别，以便解锁应用或进行安全验证？',
  ID.stringInputPassword: '输入密码',
  ID.stringForgetPassword: '忘记密码？',
  ID.stringPasswordErrorNumber: '密码错误,还可以尝试@value次',
  ID.stringPasswordError: '密码错误',
  ID.stringIsOpenSecurity:
      '为了提升安全性，我们新增了 PIN 码功能，配合指纹或人脸识别。完成设置后，您将能够更便捷地解锁应用和授权交易。\n请注意，如果您选择不设置 PIN 码，指纹或人脸识别验证功能将默认关闭',
  ID.stringPasswordErrorMax: '密码错误次数已达上限,请@value后重试',
  ID.stringPaswordSecond: '@value 秒',
  ID.stringPaswordMinute: '@value 分',
  ID.stringPaswordHour: '@value 小时',
  ID.stringSoloTokenOpenTip: 'SOL余额不足以支付租金',
  ID.stringAppLockInfo: '通过生物识别或密码来解锁应用，授权交易及添加地址时验证。',
  ID.stringChangeSuccess: '更改成功',
  ID.strinTalkAboutItlater: '以后再说',
  ID.stringCosmosTip1: 'Gas 价格需不低于 @value',
  ID.stringCosmosTip2: 'Gas 数量需大于 0',
  ID.stringCreatePassCode: '创建一个6位数密码',
  ID.stringCreateNewPasscode: '创建一个新的6位数密码',
  ID.stringResetPasscode: '重置密码',
  ID.stringAddToken: '添加币种',
  ID.stringBabyTsTip:
      '重要提示：提币到交易所（如币安）时，请务必填写 MEMO 标签！未填写或填写错误的 MEMO 将导致资产丢失，且无法找回！',
  ID.stringSelectChain: '选择网络',
  ID.stringNoMorePrompts: '以后不再提示',
  ID.stringTronNetworkFees: 'TRON网络费用',
  ID.stringContractInteraction: '合约交互',
  ID.stringNotFullySupportETH:
      '当前硬件钱包版本过旧，对ETH EIP155未能完全兼容，可能导致一定机率广播失败，建议更换新设备以确保正常使用。',
  ID.strinInputMax:
      '检测到你此次交易的体积较大，可能导致签名失败。为确保转账顺利完成，建议将此次转账金额拆分为两笔或多笔进行操作。例如，如计划转账100个，可以先转账50个，再转剩余的50个，以降低签名失败的风险。',
  ID.stringCanceTx: '取消交易',
  ID.stringContinueTx2: '继续交易',
};
