import 'strings.dart';

// en
const Map<String, String> localizedValueEN = {
  ID.appName: 'Coinbag',
  ID.settingLanguageDefault: 'Follow System',
  ID.stringConfirm: 'Confirm',
  ID.stringCancel: 'Cancel',
  ID.stringDiscover: 'Discover',
  ID.stringAvatar: 'Avatar',
  ID.stringUid: 'UID',
  ID.stringPhone: 'Phone',
  ID.stringEmail: 'Email',
  ID.stringLoginpassword: 'Login Password',
  ID.stringAccount: 'Account',
  ID.stringNickname: 'Nickname',
  ID.stringHelp: 'Help Center',
  ID.stringcommonproblem: 'Frequently Asked Questions (FAQs)',
  ID.profileLogin: 'Login Account',
  ID.profileTool: 'Wallet Tools',
  ID.profileHelp: 'Help Center',
  ID.profileAddressBook: 'Address Book',
  ID.profileMall: 'Overview',
  ID.profileLanguage: 'Multilingual',
  ID.profileQR: 'QR Code Capacity',
  ID.profileAbout: 'About',
  ID.profileCurrency: 'Currency Unit',
  ID.qrStandard: 'Standard',
  ID.qrSmall: 'Smaller',
  ID.qrLarge: 'Larger',
  ID.qrSuperLarge: 'Extra Large',
  ID.qrBest: 'Recommended',
  ID.clientNetError: 'Network error, please check your network connection',
  ID.timeOutError: 'Request timed out! Please try again later',
  ID.serverNetError:
      'There is a problem with the server connection\nPlease try again later or contact customer support',
  ID.netConnectError: 'Network not connected, please check and try again',
  ID.cancelConnectError: 'Request canceled',
  ID.qrTip:
      'The smaller the QR code capacity, the faster the scan recognition, but it will increase the number of pages in the QR code',
  ID.emptyData: 'No data available',
  ID.dataError: 'Loading failed',
  ID.reload: 'Reload',
  ID.exitApplication: 'Press again to exit the app',
  ID.releaseText: 'Refresh immediately',
  ID.refreshingText: 'Refreshing...',
  ID.completeText: 'Refresh complete',
  ID.idleText: 'Pull down to refresh',
  ID.loadingText: 'Loading...',
  ID.pullUpToLoad: 'Pull up to load more',
  ID.canLoadingText: 'Release to load more',
  ID.noDataText: 'No more data',
  ID.aboutKS: 'About',
  ID.userAgreement: 'User Agreement',
  ID.wallet: 'Wallet',
  ID.send: 'Send',
  ID.receive: 'Receive',
  ID.scan: 'Scan',
  ID.activity: 'Activity',
  ID.currencySearch: 'Currency Search',
  ID.allWallet: 'All Wallets',
  ID.totalAsset: 'Total Assets',
  ID.loginString: 'Login',
  ID.loginPhone: 'Phone Number',
  ID.loginPhoneHintText: 'Enter Phone Number',
  ID.loginEmail: 'Email',
  ID.loginEmailHintText: 'Enter Email',
  ID.loginPassword: 'Password',
  ID.loginPasswordHintText: 'Enter Password',
  ID.loginForgetPassword: 'Forgot Password?',
  ID.loginRegisterNew: 'Register New Account',
  ID.loginOkPhone: 'Please enter a valid phone number',
  ID.loginOkEmail: 'Please enter a valid email',
  ID.loginPswTip:
      'Please set a password with 6-20 characters consisting of numbers and letters',
  ID.loginSuccess: 'Login Successful',
  ID.emptyWallethint:
      'Protect asset security, fast and convenient transactions',
  ID.stringGuideTitle: 'More than just surpassing, more emphasis on safety',
  ID.stringGuideSubTitle:
      'Cold and hot separation, private keys never touch the network',
  ID.stringBindNow: 'Connect Now',
  ID.stringBindBuyU1: 'Choose U1',
  ID.stringGuideimmediatelyExperience: 'Experience Now',
  ID.stringAllWallet: 'All Wallets',
  ID.stringDefaultWallet: 'In Use',
  ID.stringBalance: 'Balance',
  ID.stringRegister: 'Register',
  ID.stringCode: 'Verification Code',
  ID.stringInputCode: 'Enter Verification Code',
  ID.stringGetCode: 'Get Verification Code',
  ID.stringInvitationCode: 'Invitation Code (optional)',
  ID.stringInputInvitationCode: 'Enter Invitation Code',
  ID.stringCollection: 'Receive',
  ID.stringReceiveAddress: 'Receiving Address',
  ID.stringCopyAddress: 'Copy Address',
  ID.stringSaveImage: 'Save Image',
  ID.stringAgreement1: 'I have read and agree to the',
  ID.stringAgreement2: 'User Agreement',
  ID.stringHasAccount: 'Already have an account,',
  ID.stringGotoLogin: 'Log In Now',
  ID.stringCopySuccess: 'Copy Successful',
  ID.stringAll: 'All',
  ID.stringTransferSend: 'Transfer',
  ID.stringSendAddress: 'Send Address',
  ID.stringReciveAddress: 'Receive Address',
  ID.stringComplte: 'Complete',
  ID.stringTxFail: 'Failed',
  ID.stringReadAgreement: 'Please read and agree to the User Service Agreement',
  ID.stringOKCode: 'Please enter the correct verification code',
  ID.stringPswTip1: '6-20 characters',
  ID.stringPswTip2: 'Password cannot be empty',
  ID.stringPswTip3:
      'Must include both letters and numbers, and cannot contain special characters',
  ID.stringPswTip4: 'Same as the first password entry',
  ID.stringPswTip5: 'Confirm password cannot be empty',
  ID.stringConfirmPswHit: 'Re-enter Password',
  ID.stringSetPassword: 'Set Password',
  ID.stringRegisterSuccess: 'Registration Successful',
  ID.forgetTitle: 'Forgot Password',
  ID.resetPasswordSuccess: 'Reset Successful',
  ID.sendAddress: 'Send Address',
  ID.sendCoin: 'Sending Currency',
  ID.receiveAddress: 'Receive Address',
  ID.sendNumber: 'Quantity',
  ID.sendAvailable: 'Available',
  ID.sendButton: 'Transfer',
  ID.sendMaxText: 'Maximum',
  ID.stringChange: 'Modify',
  ID.stringChangePassword: 'Change Password',
  ID.oldPassword: 'Old Password',
  ID.oldHintPassword: 'Enter Old Password',
  ID.newPassword: 'New Password',
  ID.newHintPassword: 'Enter New Password',
  ID.okNewPassword: 'Confirm New Password',
  ID.okNewHintPassword: 'Re-enter New Password',
  ID.passwordChangeSuccess: 'Change Successful',
  ID.inputOkToAddress: 'Please enter the correct receive address',
  ID.balanceNot: 'Insufficient Balance',
  ID.broadcastSending: 'Sending, please wait…',
  ID.broadcastSuccess: 'Send Successful',
  ID.broadcastError: 'Send Failed',
  ID.broadcastReset: 'Retry',
  ID.stringBack: 'Return',
  ID.stringSendOneself: 'Transfer to Yourself',
  ID.stringAddress: 'Address',
  ID.storageAccessNotEnabled: 'Storage Permission Not Granted',
  ID.cameraAccessNotEnabled: 'Camera Permission Not Granted',
  ID.contactPerson: 'Contacts',
  ID.insertaddress: 'Enter Address',
  ID.addressTag: 'Contact Name',
  ID.addAddress: 'Add Contacts',
  ID.usernameEditor: 'Change Nickname',
  ID.accountback: 'Log Out',
  ID.bindEmail: 'Bind Email',
  ID.stringonlineservice: 'Contact Customer Service',
  ID.stringuserfeedback: 'Feedback',
  ID.stringhelpqq: 'Official QQ Group',
  ID.stringhelpwechat: 'Official Customer Service WeChat',
  ID.stringhelptel: 'Customer Service Phone Number',
  ID.feedbackname: 'Your Name',
  ID.feedbackcontext: 'Feedback Content (up to 300 characters)',
  ID.feedbackmode:
      'Your Contact Information (QQ, WeChat ID, Phone Number, etc.)',
  ID.submitfeedback: 'Submit Feedback',
  ID.feenotegasprice: 'Current Best Miner Fee',
  ID.makeqr: 'Generate QR Code',
  ID.textcontent: 'Enter Text Content',
  ID.contentText: 'Content',
  ID.timesync: 'Sync Time',
  ID.afterRefresh: 'Refresh in @seconds seconds',
  ID.bindHardwareWallet: 'Connect Hardware Wallet',
  ID.verifysn: 'Anti-counterfeit Verification',
  ID.p4verifysucess: 'Verification Successful',
  ID.p4verifyfail: 'Verification Failed',
  ID.p4verifyRemind2:
      'Device information verification passed, please use the device to scan for signing.',
  ID.p4verifyRemind: 'Scan to Verify Signature',
  ID.verifysnSucceedHint:
      'This wallet device is an official genuine product, thank you for your support.',
  ID.verifySnTime: 'First Verification Time',
  ID.errwalletFake:
      'This wallet device is not an official genuine product, please use it with caution.',
  ID.stringNotices: 'Notice:',
  ID.textTimesyncInfo:
      'Ultra 1. Open the hardware wallet, go to the home page. 2. Click "Scan" button, scan the QR code above. Pro 3+ 1. Open the hardware wallet, go to the home page. 2. Click "Scan" button, scan the QR code above. Pro 3 1. Open the hardware wallet, go to the home page. 2. Click "Menu" -> "Settings" -> "Time". 3. Click "Scan to Sync" button, scan the QR code above.',
  ID.stringOfficialEmail: 'Official Email',
  ID.stringTextDial: 'Dial',
  ID.textFeedbackDialogMessage:
      'Submission successful, thank you for your feedback!',
  ID.textRemind: 'Please complete the information',
  ID.textUnbounded: 'Not Bound',
  ID.textBindPhone: 'Bind Phone',
  ID.texSubmit: 'Submit',
  ID.textbindSuccess: 'Binding Successful',
  ID.manageCoin: 'Currency Management',
  ID.supportToken: 'Added Currencies',
  ID.nosupportToken: 'Currencies to Add',
  ID.tokenSearch: 'Token Abbreviation, Name, Contract Address',
  ID.notifiAllRead: 'All Read',
  ID.notifiTxTitle: 'Transfer Notification',
  ID.notifiSysTitle: 'System Notification',
  ID.textChooseChain: 'Please Select Main Chain',
  ID.textCorrectAddress: 'Please Enter the Correct Address',
  ID.textChain: 'Main Chain',
  ID.submitToken: 'Submit Token',
  ID.tokenSymbol: 'Symbol',
  ID.tokenContracts: 'ID/Contract Address',
  ID.enterContractsInfo: 'Enter Contract Address',
  ID.addContractRemind1:
      '1. You only need to provide the token\'s contract address, and it will be usable after the review is approved. 2. If you need to display your token logo, please send the following specified <NAME_EMAIL>.',
  ID.stringConfirmPsw: 'Confirm Password',
  ID.addContractRemind2:
      '① Contract Address; ② logo.png (500 pixels x 500 pixels); ③ Company, individual, or official website related information; ④ Other situation description.',
  ID.importTokenSymbol: 'Enter Token symbol',
  ID.addressBookEditContacts: 'Edit contacts',
  ID.addressBookDelContacts: 'Delete contacts',
  ID.addressBookDelContactsContent: 'Confirm deletion of @contacts contacts?',
  ID.addressBookDelContactsConfirm: 'Confirm deletion',
  ID.addressAlreadyExists: 'This address already exists!',
  ID.unsearchToken: 'Specified Token not found?',
  ID.tokenLabel: 'Type',
  ID.authFingerprintTip:
      'Please press the on-screen fingerprint sensor to verify your fingerprint',
  ID.authContnetTextFace:
      'To use Face ID unlock, please enable the permission in system settings',
  ID.authContnetTextFingerprint:
      'To use fingerprint unlock, please enable the permission in system settings',
  ID.authContnetTextSaveFingerprint:
      'You haven\'t registered a fingerprint yet. Please register your fingerprint in the settings',
  ID.gotoSettingTitle: 'Go to settings',
  ID.authFaceUnlock: 'Face unlock',
  ID.authFingerprintUnlock: 'Fingerprint unlock',
  ID.authFingerprintTap: 'Press the fingerprint sensor',
  ID.authFaceTitle: 'Face ID',
  ID.authFingerprintTitle: 'Fingerprint verification',
  ID.stringTips: 'Tips',
  ID.nfcOpen: 'Opened',
  ID.nfcClose: 'Closed',
  ID.addHardwareTitle: 'Add hardware wallet',
  ID.bindWalletTitle: 'Connect wallet',
  ID.scanBindTitle: 'Connect via QR code',
  ID.pageCount: '@page page temporarily missing',
  ID.noinstallQQ:
      'QQ is not installed or the installed version is not supported',
  ID.noinstallWechat: 'WeChat is not installed',
  ID.scanResult: 'Scan Result',
  ID.copy: 'Copy',
  ID.zxingError: 'QR code scanning error, please scan again',
  ID.pleaseScanPage: 'Please scan @scanPage page',
  ID.scanNotMatchWallet:
      'The scan information does not match the currently selected wallet',
  ID.scanError: 'QR code scanning error, please try again',
  ID.bindSucessInfo:
      'You have successfully connected the hardware wallet @wallet, go ahead and use it',
  ID.bindSucess: 'Binding successful!',
  ID.openWallet: 'Open wallet',
  ID.chainManager: 'Manage',
  ID.chainSegWitP2sh: 'Nested',
  ID.chainSegWit: 'SegWit',
  ID.chainLegacy: 'Legacy',
  ID.chainSegWitP2shItem: 'SegWit(p2sh)',
  ID.chainLegacyItem: 'Legacy',
  ID.chainSegWitItem: 'SegWit',
  ID.chainSelectWallet: 'Select wallet',
  ID.chainWalletManager: 'Wallet management',
  ID.chainAddAddress: 'Add address',
  ID.chainAddressDetail: 'Address details',
  ID.chainEditRemark: 'Edit memo name',
  ID.chainAddressDetailTip1: 'Only support receiving',
  ID.chainAddressDetailTip2: 'Network assets',
  ID.chainAddType: 'Type',
  ID.chainAddRemark: 'Memo name',
  ID.chainComplete: 'Complete',
  ID.chainAllChains: 'All Chains',
  ID.chainTotalAssets: 'Total Asset Value',
  ID.allWallets: 'All wallets',
  ID.walletInfo: 'Wallet information',
  ID.walletName: 'Wallet name',
  ID.bindingTime: 'Binding Time',
  ID.deviceModel: 'Device Model',
  ID.deviceId: 'Device ID',
  ID.seVersion: 'Security Chip Version',
  ID.walletAppVersion: 'Application Version',
  ID.unbind: 'Disconnect',
  ID.using: 'In use',
  ID.bindFailed: 'Connection failed!',
  ID.rebind: 'Reconnect',
  ID.accountDelete: 'Log out account',
  ID.accountDeleteTip: 'Do you want to log out of the current account?',
  ID.safeSetting: 'Security Settings',
  ID.deleteError: 'Logout failed',
  ID.deleteSuccess: 'Logout successful',
  ID.loading: 'Loading...',
  ID.binding: 'Connecting...',
  ID.sameMnemonicWallet:
      'Detected the import of the same wallet. Do you want to overwrite?',
  ID.sureDeleteWallet: 'Are you sure you want to disconnect?',
  ID.textAddressCount: '(@indexAdd addresses)',
  ID.textAccountCount: '(@indexAcc accounts)',
  ID.baseSettings: 'Preferences',
  ID.myTitle: 'Profile',
  ID.feeSettingTitle: 'Miner fee settings',
  ID.feeTitle: 'Miner fee',
  ID.gearSelect: 'Tier selection',
  ID.customizeTitle: 'Customize',
  ID.fastTitle: 'Fast',
  ID.normalTitle: 'Standard',
  ID.slowTitle: 'Slow',
  ID.estimateTxTime: 'Estimated transaction time @minute',
  ID.minerFeeTitle: 'Miner fee rate',
  ID.minuteTitle: 'Minutes',
  ID.hourTitle: 'Hours',
  ID.secondTitle: 'Seconds',
  ID.p4seedRemind:
      'In mnemonic enhanced mode, the private key is generated by both the mnemonic and the password, and the password cannot be changed. You need both the mnemonic and the password to restore the wallet; one without the other is insufficient.\n\n In regular mode, the private key is generated only by the mnemonic, and the password can be changed. You can restore the wallet as long as you have the mnemonic.\n\n Be sure to keep the mnemonic safe and remember the password.',
  ID.p4seedIspharase: 'Mnemonic enhanced mode',
  ID.p4seedIspharaseYes: 'Yes',
  ID.p4seedIspharaseNo: 'No',
  ID.stringBind: 'Bind',
  ID.textPro1Binding:
      '1. Open the hardware wallet, select a currency, and go to the "Receive" page.\n\n 2. Go to the "My Addresses" list and select the address you want to bind.\n\n 3. Go to the "Balance" page, click "Update Balance" to display the QR code.',
  ID.scanConnect: 'Scan to connect',
  ID.customFeeConfirm:
      'Miner fee settings not confirmed. Are you sure you want to leave this page?',
  ID.customFeeSuccess: 'Custom miner fee has been applied',
  ID.feeSlowTips:
      'The miner fee rate is too low and may affect transaction confirmation time',
  ID.feeFastTips:
      'The miner fee rate is too high and may result in wasted miner fees',
  ID.feeOkTips: 'Please enter a valid miner fee rate',
  ID.inputToAddressTip: 'Please enter the recipient address',
  ID.inputOkToAddressTip: 'Please enter the correct address',
  ID.inputAmountTip: 'Please enter the payment amount',
  ID.amounNumberError: 'Please enter the correct amount',
  ID.transferToSelfTip: 'Cannot transfer to yourself',
  ID.insufficientBalanceFee: 'Insufficient balance to cover miner fees',
  ID.insufficientBalance: 'Insufficient balance',
  ID.insufficientMainFee: 'Insufficient miner fees',
  ID.coinTransferTitle: '@symbol Transfer',
  ID.coinTsTypeTitle: 'Transaction type',
  ID.qrTitleTransfer: 'Sign transaction',
  ID.qrAutoPage: 'Auto page flip',
  ID.qrManualPage: 'Manual page flip',
  ID.scanQrSendTitle: 'Scan to send',
  ID.qrSettingQrsizeTitle1:
      'Use hardware wallet to scan QR code\nDifficulty recognizing?',
  ID.qrSettingQrsizeTitle2: 'Set QR code capacity >',
  ID.sendAmountSmall: 'Transfer amount is too small to proceed',
  ID.completionProgress: 'Current progress',
  ID.ethGasPriceSlowTip:
      'The maximum base fee you set is lower than the current network base fee @fee, and the transaction will take longer to be successful',
  ID.ethGasPriceLargeTip: 'Gas price is too high and may waste miner fees',
  ID.ethGasLimitTip:
      'Gas limit must be greater than @limit and less than 15.00M',
  ID.filGasEmtyTips: 'Please enter a valid Gas',
  ID.filGasSlowTips: 'Gas too low may result in Out of Gas transaction failure',
  ID.ethGasPriceMinTip: 'Gas price must not be lower than @gas',
  ID.remarkMemo: 'Memo',
  ID.remark: 'Remark',
  ID.remarkNoText: 'No remarks available',
  ID.remarkHitText: 'Optional',
  ID.remarkTag: 'Tag',
  ID.xrpRemarkTips:
      'According to XRP main chain regulations, only the portion of the address balance greater than @value XRP can be transferred.',
  ID.xrpTsTips:
      'Insufficient balance! According to XRP main chain regulations, only the portion of the address balance greater than @value XRP can be transferred.',
  ID.xrpNoActivityTips:
      'Account not activated. Please transfer at least @value XRP to this address to activate the account',
  ID.maxFeeValidTip: 'Please enter a valid maxFee',
  ID.maxFeeNoLessMaxPriorityFeeTip: 'maxFee cannot be less than maxPriorityFee',
  ID.maxPriorityFeeTip:
      'The set maxPriorityFee @inputMax may not be fully effective. Since maxPriorityFee @rawMax plus currentBaseFee @baseFee is greater than the set maxFee @maxFee, increase maxFee to fully utilize maxPriorityFee.',
  ID.maxPriorityFeeHeightTip:
      'The maxPriorityFee is too high and may result in wasted miner fees',
  ID.maxFeeHeightTip:
      'The set maxFee is too large, exceeding more than 4 times the estimated @rawFee Gwei. Simply increasing maxFee may not make the transaction faster to be packaged',
  ID.tronActivatedTip:
      'The address is not activated; it will consume 1 TRX to activate the address',
  ID.tronNeedSource: 'Resources required for the transaction',
  ID.valueBandwidth: '@bandwidth bandwidth',
  ID.valueEnergy: '@energy energy',
  ID.activedAccount: 'Activate account',
  ID.deductionValueBandwidth: 'Deduct @bandwidth bandwidth',
  ID.deductionValueEnergy: 'Deduct @energy energy',
  ID.tronPopTip:
      'The available TRX in the account is insufficient to cover the miner fee, and the transaction may fail',
  ID.tronRemarkTip:
      'Adding a transfer note will incur an additional 1 TRX fee, up to 200 characters',
  ID.tronResourceDetail:
      'Resources required for the transaction = bandwidth consumption + energy consumption\n\nBandwidth consumption:\nTransactions to the receiving account can only deduct free bandwidth or staked bandwidth. If neither satisfies the transaction consumption, TRX must be paid in full, and partial deduction is not possible\n\nEnergy consumption:\nAfter all energy is deducted, the remaining part is paid with TRX\n\nResources required for the transaction are estimated based on the total resources paid by the user, and actual resource consumption is based on on-chain data',
  ID.tronFeeDetail:
      'TRON Network Fees:\n\nUsers incur fees on the TRON network, which include TRX fees deducted due to insufficient bandwidth or energy consumption, as well as specific transaction fees (for example, activating an account requires 1 TRX). \n\nAll fees are collected by the chain itself, and the actual consumed amounts are based on on-chain data.',
  ID.tronRiskTip:
      'Continuing with the transaction will consume the remaining TRX/resources in the account and there is a risk of transaction failure. Please confirm whether to continue',
  ID.basicEnergy: 'Normal consumption',
  ID.extraEnergy: 'Additional consumption',
  ID.tronLearnMore:
      'Transactions calling popular contracts will consume additional energy. Learn more',
  ID.fromAccount: 'Sending account',
  ID.toAccount: 'Receiving account',
  ID.addressLabelEmpty: 'Please enter the remark name',
  ID.feeRang: 'Estimated range',
  ID.bindWallet: 'Bind wallet',
  ID.scanQrBind: 'Scan QR code to bind',
  ID.verifySuccessTitle: 'Anti-counterfeiting verification successful!',
  ID.verifyFailTitle: 'Anti-counterfeiting verification failed!',
  ID.verifySuccessSubTitle:
      'This wallet device is an official genuine product, thank you for your support',
  ID.verifyFailSubTitle:
      'This wallet device is not an official genuine product, please use it with caution',
  ID.goBackHome: 'Return to homepage',
  ID.stringResource: 'Resources',
  ID.stringDelegateResource: 'Proxy resources',
  ID.stringVote: 'Node voting',
  ID.stringStake2: 'Staked assets 2.0',
  ID.stringUnstake2: 'Unlocked assets 2.0',
  ID.stringStake: 'Staked assets',
  ID.stringUnstake: 'Unlocked assets',
  ID.stringReclaim: 'Recycle resources',
  ID.stringReclaimAddress: 'Recycle address',
  ID.stringConfirming: 'Confirming',
  ID.stringPacking: 'Packaging',
  ID.stringSendFail: 'Failed',
  ID.stringTxTime: 'Transaction time',
  ID.stringTxId: 'Transaction ID',
  ID.stringTxBlock: 'Block',
  ID.stringTxSource: 'Consume resources',
  ID.stringTxEnergy: 'Energy',
  ID.stringTxBandwidth: 'Bandwidth',
  ID.stringTxWithdraw: 'Withdraw TRX',
  ID.stringBurnedEnergy: 'Burn to deduct energy',
  ID.stringBurnedBandwidth: 'Burn to deduct bandwidth',
  ID.stringBlockchain: 'View in blockchain explorer',
  ID.stringSearchEmpty: 'Token not found?',
  ID.stringSubmitToken: 'Submit Token',
  ID.stringInputOkContract: 'Please enter the correct contract address',
  ID.stringAddTokenSuccess: 'Submission successful, please wait for review!',
  ID.stringBuyWallet: 'Purchase hardware wallet',
  ID.stringGuideTitle1: 'Multi-chain asset wallet',
  ID.stringGuideSubTitle1:
      'Custom-designed for hardware wallets, providing a more convenient and user-friendly Web3 interaction experience while ensuring asset security.',
  ID.stringGuideTitle2: 'Welcome to Coinbag Wallet',
  ID.stringGuideSubTitle2:
      'Enjoy better asset transfers, balance overview, staking rewards, and more. Please bind your hardware wallet to use these features.',
  ID.stringEmptyTitle: 'Connect wallet, manage assets',
  ID.stringEmptyTitle2: 'Multi-chain support, comprehensive coverage',
  ID.stringEmptySubTitle2: 'Manage top global coins, support major NFTs',
  ID.stringAllNetTitle: 'View all networks',
  ID.stringWeb3Title: 'New Web3 experience',
  ID.stringWeb3SubTitle: 'Custom-designed for hardware wallets',
  ID.stringWeb3Title1: 'Easy to get started',
  ID.stringWeb3SubTitle1:
      'More convenient and user-friendly Web3 interaction experience',
  ID.stringWeb3Title2: 'Secure and transparent',
  ID.stringWeb3SubTitle2:
      'Three layers of protection, offline storage, never online, with built-in security chip to safeguard large asset security',
  ID.stringWeb3Title3: 'Multi-chain aggregation',
  ID.stringWeb3SubTitle3:
      'Complete blockchain storage and management with one wallet',
  ID.stringSupportChains: 'Now supporting @count networks',
  ID.stringCreateNewWallet: 'Create new wallet',
  ID.stringScanQrBind: 'QR code connection',
  ID.stringScanQrBindDes:
      'Scan to connect hardware wallet and easily manage digital assets',
  ID.stringNFCBind: 'NFC connection',
  ID.stringNFCBindDes:
      'Touch card for instant connection, rapid connection to hardware wallet, secure and efficient.',
  ID.stringTouchPsw: 'Please enter password',
  ID.stringTouchPhone: 'Hold the card near the phone',
  ID.stringTouchNONfc: 'Current device does not support NFC functionality',
  ID.stringTouchReadError: 'Reading error, please try again',
  ID.stringTouchReading: 'Reading in progress, do not remove the card',
  ID.stringTouchNfcSuccess: 'Operation successful',
  ID.stringNfcPswMax:
      'Password error attempts have reached the limit, the wallet has been reset',
  ID.stringNfcInputOnePsw:
      'You can try the password one more time. After another error, the wallet will be reset',
  ID.stringNfcPswErrorNum:
      'Incorrect password. The wallet will be reset after another @count errors',
  ID.stringNfcPswError: 'Incorrect password, please try again',
  ID.stringNfcScanError: 'Scan failed, please retry',
  ID.stringCardHaveWallet: 'This card already exists in the wallet',
  ID.stringCardWalletError: 'Wallet mismatch',
  ID.stringCardNumberError: 'Card number error',
  ID.stringNfcUnknownError: 'Unknown error',
  ID.stringTouchNoOfficial: 'This card is not an official Coinbag product',
  ID.stringTouchSpotError: 'Cannot recognize, please read the correct card',
  ID.stringToucReadCard: 'Reading card',
  ID.stringTouchReadTips:
      'Reading the card takes some time, please keep the card in contact with the phone.',
  ID.stringNewWallet: 'New wallet',
  ID.stringCreateTitle: 'Create',
  ID.stringCreateDesTitle:
      'Mnemonic not backed up, please choose "Create Wallet"',
  ID.stringImportTitle: 'Import',
  ID.stringImportDesTitle: 'Mnemonic backed up, please choose "Import Wallet"',
  ID.stringCreateWalletTitle: 'Create Wallet',
  ID.stringImportWalletTitle: 'Import Wallet',
  ID.stringAgreementTip: 'Please read and agree to the "User Agreement"',
  ID.stringTouchNumber: 'Please use the card with the last four digits @number',
  ID.stringCreating: 'Creating...',
  ID.stringImporting: 'Importing...',
  ID.stringCreateSuccess: 'Creation successful',
  ID.stringImportSuccess: 'Import successful',
  ID.stringInputMnemonic: 'Please enter the correct mnemonic',
  ID.stringVerifyFail: 'Verification failed',
  ID.stringImportMnemonic: 'Import mnemonic',
  ID.stringBackupMnemonic: 'Backup mnemonic',
  ID.stringBackupMnemonicDes:
      'Mnemonics can be used to restore the wallet and control wallet assets. Please be sure to write them down in order on paper and keep them safe!',
  ID.stringVerifyMnemonic: 'Verify mnemonic',
  ID.stringOrderInputMnemonic: 'Enter mnemonic in order',
  ID.stringTouchPasswordTip:
      '6-32 characters, can include a-z, A-Z, and 0-9. Passwords cannot be recovered if lost, please remember',
  ID.stringTouchInputPsw: 'Enter password',
  ID.stringTouchInputPswTwo: 'Confirm password',
  ID.stringTouchImInputCorrectPsw: 'Please enter the correct password',
  ID.stringTouchTowPswFail: 'Passwords do not match',
  ID.stringTouchSetWalletName: 'Set wallet name',
  ID.stringTouchInputWalletNameHint: 'Enter wallet name',
  ID.stringTouchWalletNameToast: 'Please enter wallet name',
  ID.stringTouchNextTitle: 'Next',
  ID.stringTouchSafeTitle: 'Security Notice',
  ID.stringTouchPopTip1:
      'To protect the security of your mnemonic phrase, please ensure that no one and no monitoring devices are around.',
  ID.stringTouchPopTip2:
      'Important Reminder:\n·Do not disclose\n·Do not enter on any webpage\n·Do not transmit via network tools\n·Do not use phone to take photos\n·Do not store in electronic form\n·Do not disclose',
  ID.stringTouchPopOkTitle: 'I got it.',
  ID.stringTouchEnglishTitle: 'English',
  ID.stringTouchChineseTitle: 'Chinese',
  ID.stringTouchNumberTitle: 'Numbers',
  ID.stringWalletIdTitle: 'Wallet ID',
  ID.stringResetCardTitle: 'Reset Card',
  ID.stringChangePswTitle: 'Change Password',
  ID.stringChangeWalletNameTitle: 'Change Wallet Name',
  ID.stringCardEmpty: 'This card is empty',
  ID.stringBackupComplete: 'Backup Complete',
  ID.stringVerifybackupTitle: 'Verify Backup',
  ID.stringWalletNameNoEmpty: 'Wallet name cannot be empty',
  ID.stringWalletNameMax12: 'Up to 12 characters',
  ID.stringSetWalletName: 'Set Wallet Name',
  ID.stringNewNameSameOldName: 'New name cannot be the same as the old name',
  ID.stringNameChangeSuccess: 'Change Successful',
  ID.stringInputOldPswTip: 'Please enter the old password',
  ID.stringInputNewPswTip: 'Please enter the new password',
  ID.stringInputNewPswTwoTip: 'Please confirm the new password',
  ID.stringTouchVerifyPswTitle: 'Verify Password',
  ID.stringOldNewSameTip: 'New password cannot be the same as the old password',
  ID.stringTouchResetSuccess: 'Reset Successful',
  ID.stringTouchSignErrorTip: 'Signature Data Error',
  ID.stringTronRawDataHexError: 'Tron Transaction Verification Failed',
  ID.stringEmptyWalletTitle: 'One Web3 Entry is Enough',
  ID.stringEmptyWalletSubTitle: 'Wallet · Transactions · NFT · DeFi · DApp',
  ID.stringTabWallet: 'Wallet',
  ID.stringTabDiscover: 'Discover',
  ID.stringTabProfile: 'Profile',
  ID.stringBackupTip:
      'Please back up your mnemonic phrase promptly to avoid asset loss due to phone or wallet device loss or damage.',
  ID.stringBackupButtonTitle: 'Back Up Now',
  ID.stringJumpTitle: 'Skip',
  ID.stringNfcReading: 'Ready to Scan',
  ID.stringSignLoading: 'Signing, please wait',
  ID.stringBingdingTitle: 'Bind Now',
  ID.stringCreateDesString:
      'You have successfully created a wallet, go ahead and use it!',
  ID.stringImportDesString:
      'You have successfully imported a wallet, go ahead and use it!',
  ID.stringCreateLoading: 'Creating, please wait',
  ID.stringImportLoading: 'Importing, please wait',
  ID.stringNfcCloseAndroidTip: 'NFC is off, please turn it on in "Settings."',
  ID.stringNfcCloseIOSTip:
      'NFC is off, please turn it on in "Settings -> General."',
  ID.stringTxError01: 'Please update balance and resend',
  ID.stringTxError02:
      'Transaction has been broadcasted, do not broadcast again',
  ID.stringTxError03: 'Payment amount is too low, please adjust and resend!',
  ID.stringTxError04: 'Please fill in the remark and resend',
  ID.stringTxError05: 'Transaction has expired, please resend',
  ID.stringTxError06: 'Send failed, please check if resources are sufficient',
  ID.stringTxError07:
      'Transaction is not matured yet. Please wait until it matures before sending.',
  ID.stringTxError08: 'UTXO count is too high. Adjust the amount and resend.',
  ID.stringTxError09: 'The transfer amount exceeds the address limit.',
  ID.stringTxError10: 'Please update the balance and resend.',
  ID.stringTxError11: 'Transaction fee is too high. Adjust the fee and resend!',
  ID.stringTxError12: 'Payment amount is too low. Adjust and resend!',
  ID.stringTxError13:
      'Send failed. The transaction contains immature coins. Mining reward coins must mature before they can be sent. The maturity period is 100 confirmations.',
  ID.stringTxError14: 'Transaction fee is too low. Adjust the fee and resend.',
  ID.stringTxError15:
      'Send failed. Please check if the system time is correct.',
  ID.stringTxError16: 'Send failed. Please confirm if the amount is correct.',
  ID.stringTxError17:
      'Send failed. Ripple official restriction: Account balance cannot be less than 20 XRP!',
  ID.stringTxError18: 'This account is not yet activated!',
  ID.stringTxError19: 'Transaction fee is too low. Please reset.',
  ID.stringTxError20: 'Please fill in the memo and resend.',
  ID.stringTxError21:
      'The GAS for this transaction is too low, which may cause the transaction to fail. If it remains unconfirmed for a long time, please adjust the GAS and resend.',
  ID.stringTxError22:
      'Please wait for the previous transaction to complete or increase the transaction fee by more than 10% before proceeding with the transfer.',
  ID.stringTxError23: 'GasLimit is too low. Please adjust and resend.',
  ID.stringSetting: 'Settings',
  ID.stringP2SHDes:
      'Compatible with "Legacy" and "Segwit" address types, medium transaction fee, address starts with "3".',
  ID.stringP2TRDes:
      'Taproot: Higher privacy and efficiency, starts with "bc1p".',
  ID.stringP2WPKHDes:
      'Current mainstream address type, lower transaction fee, address starts with "bc1".',
  ID.stringP2PKHDes:
      'Original Bitcoin address format, higher transaction fee, address starts with "1".',
  ID.stringSignMessage: 'Message Signature',
  ID.stringMessgaeSign: 'Sign Message',
  ID.stringSignMethodTitle: 'Signature Method',
  ID.stringMessageContentTitle: 'Message Content',
  ID.stringSignTextHint:
      'Please enter or paste the text information that needs to be signed.',
  ID.stringSignTextTip: 'Please enter the message content.',
  ID.stringUnconfirmedUtxo: 'Contains unconfirmed UTXO assets.',
  ID.stringAddressPublicTitle: 'Address Public Key',
  ID.stringUnavailableBalance: 'Unavailable Balance',
  ID.stringTotalBalance: 'Total Assets:',
  ID.stringNotSupportedTaproot:
      'The current wallet does not support Taproot addresses.',
  ID.stringDownloadFailed: 'Download failed',
  ID.stringUpgradeNow: 'Upgrade Now',
  ID.stringTalkLater: 'Talk later',
  ID.stringBackButton: 'Back Button',
  ID.stringNewVersion: 'A new version is available!',
  ID.stringCoinbagISavailable: 'A new version of Coinbag is available',
  ID.stringCheckForUpdates: 'Check for updates',
  ID.stringHaveLatestVersion: 'You already have the latest version.',
  ID.stringP2SHDLTCes:
      'The current mainstream address type, the address starts with "M"',
  ID.stringP2WPKHLTCDes:
      'The address format of Segregated Witness (SegWit) technology provides higher efficiency and lower transaction fees, the address starts with "ltc1"',
  ID.stringP2PKHLTCDes:
      'The original Litecoin address format, the address starts with "L"',
  ID.stringBroadcast: 'Broadcast',
  ID.stringBroadcastTitle: 'Click the button below to broadcast.',
  ID.stringNodeTimeOut: 'Node request timed out, please try again later',
  ID.stringNotAddedToAddressBook: 'Not added to address book,',
  ID.stringAddNow: 'add now',
  ID.stringAddressInTheAddressBook: 'Address in the address book',
  ID.stringPaste: 'Paste',
  ID.stringSupportsReceivingNetwork:
      'Only supports receiving @receiveInfonetwork asset',
  ID.stringEosEmptyTitle: 'No account name',
  ID.stringEosStake: 'Stake',
  ID.stringEosEmptyDes:
      'Due to EOS ecosystem reasons, registration is not currently supported.',
  ID.stringEosBuyRam: 'Buy RAM',
  ID.stringEosSellRam: 'Sell RAM',
  ID.stringEosRefoud: 'Refund',
  ID.stringEosNewAccount: 'New account',
  ID.stringEosBidname: 'Bidname',
  ID.stringEosReceiveTheRefunded: 'Receive the refunded',
  ID.stringExportAccountTitle: 'Export account',
  ID.stringEosPublicKeyTitle: 'EOS public key',
  ID.stringEosAccountTitle: 'EOS account',
  ID.stringScanCompleteTitle: 'Scan code completed',
  ID.stringNORemarkTitle: 'No tags',
  ID.stringCheckDetailTitle: 'View details',
  ID.stringShowQRTitle: 'Show QR code',
  ID.stringCopyAccmountTitle: 'Copy account',
  ID.stringEditRemarkTitle: 'Edit tags',
  ID.stringEthNotSupportedTips:
      'Ethereum has upgraded to EIP155, and old hardware wallets are no longer supported. Please replace them with new wallets to ensure the safety of your assets.',
  ID.stringNotSupportedChain:
      'The current hardware wallet is no longer supported for this chain.',
  ID.stringZecNotSupportedTips:
      'The old hardware wallet no longer supports the ZEC chain. Please replace it with a new wallet in time to ensure the safety of your assets.',
  ID.stringCPULease: 'CPU Lease',
  ID.stringEosTxInvalid: 'The transaction has expired, please resend',
  ID.stringInsufficientResources:
      'The sending failed, please check whether the resources are sufficient',
  ID.stringEosAccountDetialTitle: 'EOS account details',
  ID.stringCreateDateTitle: 'Creation time: @date',
  ID.stringAccountRemarkTitle: 'Account tag: @tag',
  ID.stringThresholdTitle: 'Threshold: @value',
  ID.stringWeightTitle: 'Weight: @value',
  ID.stringVIPbenefits: 'VIP Benefits',
  ID.stringVIPTip1: 'View Benefits Guide',
  ID.stringVIPTip2:
      'Welcome, esteemed VIP users! Your exclusive benefits can be viewed on the "My" page. Please follow the instructions to proceed.',
  ID.stringVIPTip3: 'Simply click "View VIP Benefits" on the "My" page.',
  ID.stringNotSupportEIP1559:
      'The current wallet does not support EIP1559 transactions.',
  ID.stringResourceManager: 'Resource management',
  ID.stringNetDetail: 'Bandwidth details',
  ID.stringNetManager: 'Bandwidth management',
  ID.stringStakeGet: 'Pledge to obtain',
  ID.stringOtherDelegateOwner: 'Others delegate to you',
  ID.stringDelegateToOther: 'Delegate to others',
  ID.stringFreeGet: 'Free gift',
  ID.stringDelegated: 'Already delegated',
  ID.stringCanDelegate: 'Can delegate',
  ID.stringDelegate: 'Delegate',
  ID.stringReclaimTitle: 'Reclaim',
  ID.stringEnergy: 'Energy',
  ID.stringBandwidth: 'Bandwidth',
  ID.stringGetBandwidth: 'Get bandwidth',
  ID.stringEnergyDetail: 'Energy details',
  ID.stringEnergyManager: 'Energy management',
  ID.stringGetEnergy: 'Get energy',
  ID.stringTronNoSupportedTip:
      'Affected by the Tron network upgrade Stake 2.0, the current wallet does not support this function',
  ID.stringResourceToAddress: 'Resource receiving address',
  ID.stringResourceToAddressTip: 'Please enter the resource receiving address',
  ID.stringDelegateAmount: 'Amount of proxies',
  ID.stringDelegateAmountTip: 'Please enter the number of proxies',
  ID.stringCanDelegateEnergy: 'Can be proxied: @value energy',
  ID.stringCanDelegateBandwidth: 'Can be proxied: @value bandwidth',
  ID.stringUseStakeTrx: '* Estimated to occupy your staked @value TRX',
  ID.stringDelegateTip:
      '• After proxying, TRX is still in the staked account, and only the right to use resources is delegated to others',
  ID.stringOk: 'Ok',
  ID.stringUseStakeAmount: 'Occupied pledge amount',
  ID.stringOtherGetResource:
      '* Expected other party to receive @value @resource',
  ID.stringResourceMaxTip: 'Exceeds the current maximum usage amount',
  ID.stringInputHintTrx: 'Please enter the TRX amount',
  ID.stringResourceOwnAddressTips:
      'The resource receiving address cannot be your own address',
  ID.stringDelegateUseTrx: 'Available: @value TRX',
  ID.share: 'Share',
  ID.penInBrowser: 'Open in browser',
  ID.copyLink: 'Copy link',
  ID.favorites: 'Favorites',
  ID.switchWallet: 'Switch wallet',
  ID.successfullyAddedToFavorites: 'Successfully added to favorites',
  ID.unfavorited: 'Unfavorited',
  ID.recent: 'Recent',
  ID.viewAll: 'View all',
  ID.recentlyUsed: 'Recently used',
  ID.refresh: 'Refresh',
  ID.messages: 'Messages',
  ID.paymentDetails: 'Payment details',
  ID.stringTronErrorMessage1:
      'The unfreezing time has not yet arrived, please operate after unfreezing',
  ID.stringTronErrorMessage2: 'Please do not transfer to your own address',
  ID.stringTronErrorMessage3: 'No unfreezing assets',
  ID.stringDelegatePopAmount: 'Occupied pledge amount',
  ID.stringDeleagteEnergy: 'Delegate energy',
  ID.stringDelegateBandwidth: 'Delegate bandwidth',
  ID.stringResouecePopTips:
      'Miner fee: includes the mining fee for burning deducted resources and the mining fee for specific transactions\n\nThe mining fee is estimated based on the total resources paid by the user, and the actual payment amount is subject to the on-chain data',
  ID.stringSearchDapp: 'Search Dapp or enter URL',
  ID.stringSearchResult: 'Search results',
  ID.stringSearchHistory: 'Search history',
  ID.stringUnableToFind: 'Unable to find the application',
  ID.recommend: 'Recommendations',
  ID.stringLockingResource: 'Locked Resources',
  ID.stringCanReclaimTitle: 'Recyclable',
  ID.stringDateTitle: 'Time',
  ID.stringReclaimAmount1: 'Recycling Quantity',
  ID.stringReclaimAmount2: 'Unstaking TRX Quantity',
  ID.stringCanReclaimValue1: 'Recyclable: @value @resource',
  ID.stringCanReclaimValue2: 'Can be unstaked: @value TRX',
  ID.stringInputReclaimHint1: 'Please enter the recycling quantity',
  ID.stringInputReclaimHint2: 'Please enter the TRX unstaking quantity',
  ID.stringInputReclaimTip1:
      '* Estimated TRX unstaked from your staking: @value',
  ID.stringInputReclaimTip2:
      '* @value @resource will be reclaimed from the delegate',
  ID.stringReclaimEnergy: 'Recycling Energy',
  ID.stringReclaimBandwidth: 'Recycling Bandwidth',
  ID.stringResourcePopTip1:
      'Note: Hardware wallet recovery is displayed as unlocking',
  ID.stringResourcePopTip2:
      'Note: Hardware wallet proxy is displayed as staking',
  ID.stringStakeTitle: 'Staking',
  ID.stringTotalAmount: 'Total assets',
  ID.stringTotalStakeAmount: 'Total stake',
  ID.stringUnlocking: 'Unlocking',
  ID.stringCanExtract: 'Withdrawable',
  ID.stringUnderstandingStaking: 'Learn about Staking 2.0',
  ID.stringStakeBandwidthTips: '• Bandwidth needs to be staked with TRX',
  ID.stringStakeEnergyTips: '• Energy needs to be staked with TRX',
  ID.stringUnstakeTitle: 'Unlocked',
  ID.stringFavorited: 'Favorited',
  ID.stringEdit: 'Edit',
  ID.stringWithraw: 'Withdraw',
  ID.stringGetAmount: 'Received amount',
  ID.stringStakeGetResource: 'Available: @value @resource',
  ID.stringStakeHint1: 'Enter the amount of resources to be received',
  ID.stringStakeHint2: 'Enter the amount to be pledged',
  ID.stringStakeAmount: 'Amount to be pledged',
  ID.stringStakeTip1:
      '* Expected to pledge @trx TRX and get @vote voting rights',
  ID.stringStakeTip2:
      '* Expected to get @value @resource and get @vote voting rights',
  ID.stringStakeTip3:
      '• Only supports staking for yourself, and the resources obtained by staking can be delegated to others at any time',
  ID.stringStakeTip4:
      '• After unlocking, you need to wait 14 days before you can withdraw',
  ID.stringNoSearchResultsFound: 'No search results found',
  ID.stringTrxCanUse: 'Available: @value TRX',
  ID.stringStakeEnergy: 'Staking energy',
  ID.stringStakeBandwidth: 'Staking bandwidth',
  ID.stringUnstake1: 'Unlock Assets 1.0',
  ID.stringCanUnstakeTitle: 'Unlockable: @value TRX',
  ID.stringUnstakeTip:
      '• It takes 14 days to unlock TRX, and the unlocked assets can be withdrawn after 14 days',
  ID.stringClearAll: 'Clear All',
  ID.stringUnstakeTrx2: 'Unlock TRX 2.0',
  ID.stringUnstakeHint: 'Enter unlock quantity',
  ID.stringUnstakeEnergy: 'Unlock energy',
  ID.stringUnstakeBandwidth: 'Unlock bandwidth',
  ID.stringUnstakeEnergy2: 'Unlock energy 2.0',
  ID.stringUnstakeBandwidth2: 'Unlock bandwidth 2.0',
  ID.stringDayToWithdraw: '@day days later, you can withdraw',
  ID.stringUnlockingTrx: 'Unlocking TRX',
  ID.stringWithdrawTrx: 'Withdraw TRX',
  ID.stringTronNoSupportedStake2:
      'Affected by the Tron network upgrade Stake 2.0, the current hardware wallet does not support staking assets 2.0',
  ID.stringTronNoSupportedUnstake2:
      'Affected by the Tron network upgrade Stake 2.0, the current hardware wallet does not support unlocking assets 2.0',
  ID.stringStake1DetialTitle: 'Stake details',
  ID.stringStakeToOwnTitle: 'Stake for yourself',
  ID.stringStakeToOtherTitle: 'Stake for others',
  ID.stingCannotAccessThisWebsite: 'Cannot access this website',
  ID.stingCannotAccessThisWebsiteVPN:
      'There was an error accessing the website, please try reloading or using a VPN',
  ID.stringRedirecting: 'Redirecting to a third-party website',
  ID.stringPledgedTitle: 'Pledged',
  ID.stringRedemptionTitle: 'Redemption',
  ID.stringEosResourceTip1: '• Memory resources need to be purchased with EOS',
  ID.stringEosResourceTip2: '• Bandwidth resources need to be pledged with EOS',
  ID.stringAvailableBalance: 'Available balance',
  ID.stringOnlySupportsTaproot:
      'This Dapp only supports Taproot or Segregated Witness native address formats. Please add an address in the management section.',
  ID.stringRequestAccess:
      'Request access to your wallet address. Do you confirm to publicly disclose your wallet address to this website?',
  ID.stringDeny: 'Deny',
  ID.stringRequestAuthorization: 'Request Authorization',
  ID.stringCurrentNetworkNotSupported: 'Current network not supported',
  ID.stringContractCall: 'Contract Call',
  ID.stringAssetSecurityWarning1:
      'To ensure asset security, please confirm that you understand the relevant risks and consequences before authorizing.',
  ID.stringAssetSecurityWarning2:
      'I understand the common scams related to authorization:',
  ID.stringAssetSecurityWarning3:
      'Requesting QR code verification under the pretext of OTC trading and guiding authorization.',
  ID.stringAssetSecurityWarning4:
      'Asking for a token exchange under the pretext of airdropping fake coins and guiding authorization.',
  ID.stringAssetSecurityWarning5:
      'Requesting an exchange under the pretext of earning interest on deposits and guiding authorization.',
  ID.stringAssetSecurityWarning6:
      'Entering unknown Dapp URLs and being induced to authorize.',
  ID.stringColdWalletAuthorizationWarning:
      'I understand that once my cold wallet address is authorized to others, the other party can transfer assets from the authorized address without my consent.',
  ID.stringWarning: 'Warning',
  ID.stringAuthorizeSmartContract: 'Authorize Smart Contract',
  ID.stringAuthorizedAddress: 'Authorized Address',
  ID.stringSignTransaction: 'Sign Transaction',
  ID.stringTransactionType: 'Transaction Type',
  ID.stringData: 'Data',
  ID.stringNftAmount: 'NFT quantity',
  ID.stringNotCurrentlySupporte: 'This link is not currently supported.',
  ID.stringHoldValue: 'I hold @value',
  ID.stringValuation: 'Valuation',
  ID.stringNftOverview: 'Overview',
  ID.stringNftActivity: 'Activity',
  ID.stringContractAddress: 'Contract Address',
  ID.stringTokenStandards: 'Token Standard',
  ID.stringNftNetwork: 'Chain',
  ID.stringNftTransfer: 'Transfer',
  ID.stringNftFrom: 'from @value',
  ID.stringNftTo: 'to @value',
  ID.stringNftSale: 'Sale',
  ID.stringNftMint: 'Mint',
  ID.stringNftBurn: 'Burn',
  ID.stringNftSaleBuy: 'Buy',
  ID.stringNftSaleSell: 'Sell',
  ID.stringNftReceiveAddress: 'From @value',
  ID.stringNftSendAddress: 'To @value',
  ID.stringNftContractAddress: 'Contract @value',
  ID.stringQrMaxNumberTip:
      'There are too many QR code pages, and the current wallet does not support it.',
  ID.stringNftSuccess: 'Success',
  ID.stringTargetAddress: 'Target',
  ID.stringSendNft: 'Send NFT',
  ID.stringNftSend: 'NFT Send',
  ID.stringNftReceive: 'NFT Receive',
  ID.stringQRScanError: 'Data error, please rescan',
  ID.stringBindAddressNow: 'Bind Now',
  ID.stringNotBoundYet: 'Not Bound Yet',
  ID.stringBindAddressInfo: 'You must bind the main chain to add an address',
  ID.stringTokenAddTitle: 'Add ERC20',
  ID.stringTokenAddTip1: 'Use your hardware to scan to add token',
  ID.stringTokenPro1Tip1: 'Pro 1:',
  ID.stringTokenPro1Tip2:
      '1. Open your hardware wallet, go to \"All Assets\" page;\n2. Click the icon in the upper right corner, then click \"Add Coins\";\n3. Click \"Add new asset\" button to scan the QR code above.;',
  ID.stringTokenPro2Tip1: 'Pro 2 / Pro 2+:',
  ID.stringTokenPro2Tip2:
      '1. Open your hardware wallet, go to \"My Assets\" page;\n2. Click the icon in the upper right corner to enter the \"Coin Management\" page;\n3. Click the plus icon in the upper right corner to enter the \"Add Token\" page;\n4. Click \"SCAN TO ADD TOKEN\" button to scan the QR code above.',
  ID.stringTokenPro3Tip1: 'Pro 3:',
  ID.stringTokenPro3Tip2:
      '1. Open your hardware wallet, go to \"My Wallet\" page;\n2. Click the plus icon in the upper right corner to enter the \"Coin Management\" page;\n3. Click the plus icon in the upper right corner to enter the \"Add Token\" page;\n4. Click \"SCAN TO ADD TOKEN\" button to scan the QR code above.',
  ID.stringSyncAmount: 'Sync balance',
  ID.stringSyncAmountValue: '@value Sync balance',
  ID.stringCurrentSyncAmount: 'Amount of this sync',
  ID.stringSyncAmountTip1: 'Please scan the code with a hardware wallet',
  ID.stringSyncAmountTip2:
      'Please set a suitable miner fee rate in the cold wallet before sending each transaction. The current best Gas Price is @value Gwei.',
  ID.stringSyncAmountTip3:
      'Please set a suitable miner fee rate in the cold wallet before sending each transaction. The current best miner fee rate is @value BTC/KB.',
  ID.stringP1info:
      '1.Open the hardware wallet, select the cryptocurrency, and go to the "Receive" page;\n 2. Navigate to the "Balance" page and click "Update Balance" to display the QR code;',
  ID.stringP2info:
      '1. Open the hardware wallet;\n2. Click “Function” -> “Export Address”;\n3. Select the address to bind, click the “Export” button to display the QR code;\n4. Click the “Scan to Bind” button below to scan the cold-end QR code.',
  ID.stringP2Plusinfo:
      '1. Open the hardware wallet.\n 2. Click “Functions” -> “Monitor Account.”\n 3. Select the cryptocurrency to bind, click the “Export” button to display the QR code.\n 4. Click the “Scan to Bind” button below and scan the cold-end QR code.',
  ID.stringP3info:
      '1. Open the hardware wallet;\n2. Open the system menu; then select "Monitor Account";\n3. Select the cryptocurrency to monitor, click the "Send to APP" button, and use the QR code;\n4. Click the "Scan Code to Bind" button below and follow the prompts to bind.',
  ID.stringNotBoundInfo:
      'This hardware wallet is not bound. Please bind it first.',
  ID.stirngSyncBroadInfo: 'Sync and broadcast the results',
  ID.stringConfirmTransaction: 'Confirm Transaction',
  ID.stringNoSupportChain:
      'Due to ecological reasons, this chain is no longer supported',
  ID.stringNoMonitorAddress:
      'No address found. Please monitor the address first.',
  ID.stringSwitchAddress: 'Switch address format',
  ID.stringNotBoundInfoAddres:
      'The address of the hardware wallet is not bound. Please bind it first.',
  ID.stirngSelectHardwareWallet: 'Select Hardware Wallet',
  ID.stirngSwitchHardwareWallet:
      'The currently selected hardware wallet does not match the hardware wallet scanned. Please switch.',
  ID.stirngBroadcastNoData: 'Broadcast data is missing. Please try again.',
  ID.stringNoSupportedFeature:
      'Due to ecological reasons, this feature is no longer supported.',
  ID.stringNetworkDiagnostics: 'Network Diagnostics',
  ID.stringNetworkStatus: 'Please check your network settings',
  ID.stringXrpNotSupportedTips:
      'Old hardware wallets no longer support XRP. Please upgrade to a new wallet in a timely manner to ensure your asset security. If you have any questions, please contact customer service.',
  ID.stringSynAddressTitle: 'Syn Address',
  ID.stringSynAddressTip:
      'If you need to add a new address, you need to operate in the hardware wallet, and then synchronize the address to the hot end. The steps are as follows:\n1. Open the hardware wallet, select the Solana chain, and enter the payment QR code page;\n2. Click the icon in the upper right corner to enter the address management page;\n3. Select the address to be synchronized and enter the synchronization address page;\n4. Click the synchronize address button below to scan the hardware wallet QR code.',
  ID.stringSynChainError: 'Main chain does not match',
  ID.stringSynAddressError: 'Synchronization address error',
  ID.stringBuildError: 'Failed to build transaction, please rebuild',
  ID.stringInputSolTip1: 'Please enter a valid Compute Unit Limit',
  ID.stringInputSolTip2: 'Please enter a valid Compute Unit Price',
  ID.stringInputSolTip3: 'Please send at least @value to pay the rent',
  ID.stringSolanaTxError1:
      'The transaction has expired or the Blockhash is incorrect.',
  ID.stringBnbRecieveTip: 'Officially no longer supports currency deposit',
  ID.stringAddressSuccess: 'Added successfully',
  ID.stringSynSuccess: 'Synchronized successfully',
  ID.stringBackgroundUpgrade:
      'The background upgrade has started. Please be patient!',
  ID.stringBackgroundUpgradeIng: 'Upgrading...',
  ID.stringContactCustomerServiceToAdd: 'Contact support',
  ID.stringContinueTx: 'Continue',
  ID.stringXrpAddressError: 'Wrong address format',
  ID.stringXripFeeError: 'Please enter a valid mining fee',
  ID.stringSolPriceMinTip:
      'Compute Unit Price lower than @value may cause transaction failure',
  ID.stringBabylonError:
      'Dapp project has risks and does not currently support transactions',
  ID.stringWebUserName: 'Username',
  ID.stringWebPassword: 'Password',
  ID.stringWebLogin: 'Login',
  ID.stringTouchChineseTip:
      'The Chinese input method does not have a secure keyboard. There may be security risks when entering the mnemonic phrase. Use Chinese mnemonic phrases with caution.',
  ID.stringBestFeeTitle:
      'Please enter the transfer amount so that the estimated miner fee can be calculated.',
  ID.stringMinerFee: 'Estimated Miner Fee',
  ID.stringBestFee: 'Optimal Miner Fee',
  ID.stringThemeMode: 'Theme Mode',
  ID.stringLightMode: 'Light Mode',
  ID.stringDarkMode: 'Dark Mode',
  ID.stringThemeModeText:
      'We will adjust the app\'s theme mode based on your device\'s system settings.',
  ID.stringSecurityVerification: 'Security Verification',
  ID.stringOpenSecurityTitle: 'Start security verification',
  ID.stringVerMethodTitle: 'Verification method',
  ID.stringVerPasswordTitle: 'Password',
  ID.stringBiometricsTitle: 'Biometrics',
  ID.stringVerChangePswTitle: 'Change password',
  ID.stringUnlockAppTitle: 'Unlock app',
  ID.stringUnlockAppSub1Title: 'Require password to unlock app',
  ID.stringUnlockAppSub2Title: 'Require biometrics to unlock app',
  ID.stringAuthTsTitle: 'Authorize transaction',
  ID.stringAuthTsSub2Title:
      'Authorize transactions or add accounts using biometric recognition or password',
  ID.stringCreatePasswordTitle: 'Create password',
  ID.stringPasswordNotEqualTip:
      'The two passwords do not match, please re-enter',
  ID.stringOpenBiometricsTip:
      'Would you like to enable biometric authentication for unlocking the app or security verification?',
  ID.stringInputPassword: 'Enter password',
  ID.stringForgetPassword: 'Forgot your password?',
  ID.stringPasswordErrorNumber: 'Wrong password, you can try @value more times',
  ID.stringPasswordError: 'Wrong password',
  ID.stringIsOpenSecurity:
      'To enhance security, we have introduced a new PIN code feature, along with support for fingerprint or facial recognition. Once the setup is complete, you will be able to unlock the app and authorize transactions more. \nPlease note that if you choose not to set a PIN code, the fingerprint or facial recognition verification feature will be disabled by default.',
  ID.stringPasswordErrorMax:
      'The number of incorrect password attempts has reached the upper limit. Please try again after @value',
  ID.stringPaswordSecond: '@value seconds',
  ID.stringPaswordMinute: '@value minutes',
  ID.stringPaswordHour: '@value hours',
  ID.stringSoloTokenOpenTip: 'Insufficient SOL balance to pay rent',
  ID.stringAppLockInfo:
      'Unlock the app using biometric recognition or password, and verify when authorizing transactions and adding addresses',
  ID.stringChangeSuccess: 'Change Success',
  ID.strinTalkAboutItlater: 'We \'ll talk about it later',
  ID.stringCosmosTip1: 'Gas price must not be lower than @value',
  ID.stringCosmosTip2: 'Gas quantity must be greater than 0',
  ID.stringCreatePassCode: 'Create a 6-digit passcode',
  ID.stringCreateNewPasscode: 'Create a new 6-digit passcode',
  ID.stringResetPasscode: 'Reset Password',
  ID.stringAddToken: 'Add Token',
  ID.stringBabyTsTip:
      'Important Notice: When withdrawing to an exchange (e.g., Binance), you MUST fill in the MEMO tag! Missing or incorrect MEMO will result in permanent loss of your assets!',
  ID.stringSelectChain: 'Select Chain',
  ID.stringNoMorePrompts: 'No more prompts in the future',
  ID.stringTronNetworkFees: 'TRON Network Fees',
  ID.stringContractInteraction: 'Contract Interaction',
  ID.stringNotFullySupportETH:
      'Your current hardware wallet version is outdated and may not fully support ETH EIP155, which could lead to transaction failures. We recommend replacing it with a new device to ensure normal operation.',
  ID.strinInputMax:
      'It is detected that the size of your transaction is large, which may cause signature failure. To ensure the smooth completion of the transfer, it is recommended to split the transfer amount into two or more transactions. For example, if you plan to transfer 100, you can transfer 50 first and then transfer the remaining 50 to reduce the risk of signature failure.',
  ID.stringCanceTx: 'Cancel transaction',
  ID.stringContinueTx2: 'Continue transaction',
};
