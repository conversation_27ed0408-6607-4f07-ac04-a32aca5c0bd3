import 'strings.dart';

// ja_jp
const Map<String, String> localizedValueJAJP = {
  ID.appName: 'Coinbag',
  ID.settingLanguageDefault: 'フォローシステム',
  ID.stringConfirm: '確認',
  ID.stringCancel: 'キャンセル',
  ID.stringDiscover: '発見',
  ID.stringAvatar: 'アバター',
  ID.stringUid: 'UID',
  ID.stringPhone: '電話',
  ID.stringEmail: 'メール',
  ID.stringLoginpassword: 'ログインパスワード',
  ID.stringAccount: 'アカウント',
  ID.stringNickname: 'ニックネーム',
  ID.stringHelp: 'ヘルプセンター',
  ID.stringcommonproblem: 'よくある質問',
  ID.profileLogin: 'ログインアカウント',
  ID.profileTool: 'ウォレットツール',
  ID.profileHelp: 'ヘルプセンター',
  ID.profileAddressBook: 'アドレス帳',
  ID.profileMall: '概要',
  ID.profileLanguage: '多言語',
  ID.profileQR: 'QRコード容量',
  ID.profileAbout: 'このアプリについて',
  ID.profileCurrency: '通貨単位',
  ID.qrStandard: '標準',
  ID.qrSmall: '小さい',
  ID.qrLarge: '大きい',
  ID.qrSuperLarge: '超大',
  ID.qrBest: 'おすすめ',
  ID.clientNetError: 'ネットワーク異常、ネットワーク接続状態を確認してください',
  ID.timeOutError: 'リクエストタイムアウト！後でもう一度お試しください',
  ID.serverNetError: 'サーバー接続に問題が発生しました\n後でもう一度お試しいただくか、カスタマーサポートにお問い合わせください',
  ID.netConnectError: 'ネットワークが接続されていません。確認後、再試行してください',
  ID.cancelConnectError: 'リクエストがキャンセルされました',
  ID.qrTip: 'QRコードの容量が小さいほど、スキャンの認識が速くなりますが、QRコードのページ数が増加します',
  ID.emptyData: 'データがありません',
  ID.dataError: '読み込みに失敗しました',
  ID.reload: '再読み込み',
  ID.exitApplication: 'もう一度押すとアプリが終了します',
  ID.releaseText: '即座にリフレッシュ',
  ID.refreshingText: 'リフレッシュ中...',
  ID.completeText: 'リフレッシュ完了',
  ID.idleText: '下に引っ張るとリフレッシュできます',
  ID.loadingText: '読み込み中...',
  ID.pullUpToLoad: '上に引っ張るともっと読み込みます',
  ID.canLoadingText: '手を放すともっと読み込みます',
  ID.noDataText: 'これ以上のデータはありません',
  ID.aboutKS: 'このアプリについて',
  ID.userAgreement: 'ユーザー契約',
  ID.wallet: 'ウォレット',
  ID.send: '送信',
  ID.receive: '受信',
  ID.scan: 'スキャン',
  ID.activity: '取引履歴',
  ID.currencySearch: '通貨検索',
  ID.allWallet: 'すべてのウォレット',
  ID.totalAsset: '総資産',
  ID.loginString: 'ログイン',
  ID.loginPhone: '携帯番号',
  ID.loginPhoneHintText: '携帯番号を入力してください',
  ID.loginEmail: 'メール',
  ID.loginEmailHintText: 'メールを入力してください',
  ID.loginPassword: 'パスワード',
  ID.loginPasswordHintText: 'パスワードを入力してください',
  ID.loginForgetPassword: 'パスワードを忘れた？',
  ID.loginRegisterNew: '新しいアカウントを登録',
  ID.loginOkPhone: '正しい携帯番号を入力してください',
  ID.loginOkEmail: '正しいメールアドレスを入力してください',
  ID.loginPswTip: '6〜20文字の数字とアルファベットを含むパスワードを設定してください',
  ID.loginSuccess: 'ログイン成功',
  ID.emptyWallethint: '資産の安全を保護し、送受信が迅速で便利',
  ID.stringGuideTitle: '超越するだけでなく、より重要なのは安全',
  ID.stringGuideSubTitle: 'コールドとホットの分離、プライベートキーはネットワークに触れない',
  ID.stringBindNow: '今すぐ接続',
  ID.stringBindBuyU1: 'U1を選択',
  ID.stringGuideimmediatelyExperience: '今すぐ体験',
  ID.stringAllWallet: 'すべてのウォレット',
  ID.stringDefaultWallet: '使用中',
  ID.stringBalance: '残高',
  ID.stringRegister: '登録',
  ID.stringCode: '認証コード',
  ID.stringInputCode: '認証コードを入力してください',
  ID.stringGetCode: '認証コードを取得',
  ID.stringInvitationCode: '招待コード（任意）',
  ID.stringInputInvitationCode: '招待コードを入力してください',
  ID.stringCollection: '受け取り',
  ID.stringReceiveAddress: '受取先住所',
  ID.stringCopyAddress: 'アドレスをコピー',
  ID.stringSaveImage: '画像を保存',
  ID.stringAgreement1: '私は読んで同意します',
  ID.stringAgreement2: 'ユーザー契約',
  ID.stringHasAccount: 'すでにアカウントをお持ちの方、',
  ID.stringGotoLogin: '今すぐログイン',
  ID.stringCopySuccess: 'コピー成功',
  ID.stringAll: '全て',
  ID.stringTransferSend: '転送',
  ID.stringSendAddress: '送信先住所',
  ID.stringReciveAddress: '受信先住所',
  ID.stringComplte: '完了',
  ID.stringTxFail: '失敗しました',
  ID.stringReadAgreement: 'ユーザーサービス契約を読み、同意してください',
  ID.stringOKCode: '正しい認証コードを入力してください',
  ID.stringPswTip1: '6〜20文字',
  ID.stringPswTip2: 'パスワードを空にすることはできません',
  ID.stringPswTip3: '英字と数字を含む必要があり、特殊文字を含めることはできません',
  ID.stringPswTip4: '初回のパスワード入力と同じ',
  ID.stringPswTip5: '確認パスワードを空にすることはできません',
  ID.stringConfirmPswHit: 'パスワードを再入力してください',
  ID.stringSetPassword: 'パスワードを設定',
  ID.stringRegisterSuccess: '登録成功',
  ID.forgetTitle: 'パスワードを忘れた',
  ID.resetPasswordSuccess: 'リセット成功',
  ID.sendAddress: '送信先住所',
  ID.sendCoin: '送信通貨',
  ID.receiveAddress: '受信先住所',
  ID.sendNumber: '数量',
  ID.sendAvailable: '利用可能',
  ID.sendButton: '転送',
  ID.sendMaxText: '最大',
  ID.stringChange: '修正',
  ID.stringChangePassword: 'パスワード変更',
  ID.oldPassword: '現在のパスワード',
  ID.oldHintPassword: '現在のパスワードを入力してください',
  ID.newPassword: '新しいパスワード',
  ID.newHintPassword: '新しいパスワードを入力してください',
  ID.okNewPassword: '新しいパスワードを確認',
  ID.okNewHintPassword: '新しいパスワードを再入力してください',
  ID.passwordChangeSuccess: '変更成功',
  ID.inputOkToAddress: '正しい受信先住所を入力してください',
  ID.balanceNot: '残高不足',
  ID.broadcastSending: '送信中、少々お待ちください…',
  ID.broadcastSuccess: '送信成功',
  ID.broadcastError: '送信失敗',
  ID.broadcastReset: '再試行',
  ID.stringBack: '戻る',
  ID.stringSendOneself: '自分に転送',
  ID.stringAddress: 'アドレス',
  ID.storageAccessNotEnabled: 'ストレージ権限が未設定',
  ID.cameraAccessNotEnabled: 'カメラ権限が未設定',
  ID.contactPerson: '連絡先',
  ID.insertaddress: 'アドレスを入力してください',
  ID.addressTag: '連絡先名',
  ID.addAddress: '連絡先を追加',
  ID.usernameEditor: 'ニックネームを変更',
  ID.accountback: 'アカウントからログアウト',
  ID.bindEmail: 'メールをバインド',
  ID.stringonlineservice: 'カスタマーサービスに連絡',
  ID.stringuserfeedback: 'フィードバック',
  ID.stringhelpqq: '公式QQグループ',
  ID.stringhelpwechat: '公式カスタマーサービスWeChat',
  ID.stringhelptel: 'カスタマーサービス電話番号',
  ID.feedbackname: 'お名前',
  ID.feedbackcontext: 'フィードバック内容（300文字以内）',
  ID.feedbackmode: 'ご連絡先（QQ、WeChat ID、電話番号など）',
  ID.submitfeedback: 'フィードバックを送信',
  ID.feenotegasprice: '現在の最良マイナー手数料',
  ID.makeqr: 'QRコードを生成',
  ID.textcontent: 'テキスト内容を入力',
  ID.contentText: '内容',
  ID.timesync: '同期時間',
  ID.afterRefresh: '@秒後に更新',
  ID.bindHardwareWallet: 'ハードウェアウォレットに接続',
  ID.verifysn: '偽造防止認証',
  ID.p4verifysucess: '認証成功',
  ID.p4verifyfail: '認証失敗',
  ID.p4verifyRemind2: 'デバイス情報の認証が成功しました。署名のためにデバイスでスキャンしてください。',
  ID.p4verifyRemind: 'スキャンして署名を認証',
  ID.verifysnSucceedHint: 'このウォレットデバイスは公式に販売された正規品です。ご支援ありがとうございます。',
  ID.verifySnTime: '初回認証時間',
  ID.errwalletFake: 'このウォレットデバイスは公式の正規品ではありません。ご注意ください。',
  ID.stringNotices: 'ご案内:',
  ID.textTimesyncInfo:
      'Ultra 1. ハードウェアウォレットを開き、ホームページにアクセスします。 2. 「スキャン」ボタンをクリックし、上のQRコードをスキャンします。 Pro 3+ 1. ハードウェアウォレットを開き、ホームページにアクセスします。 2. 「スキャン」ボタンをクリックし、上のQRコードをスキャンします。 Pro 3 1. ハードウェアウォレットを開き、ホームページにアクセスします。 2. 「メニュー」 -> 「設定」 -> 「時間」をクリックします。 3. 「スキャンして同期」ボタンをクリックし、上のQRコードをスキャンします。',
  ID.stringOfficialEmail: '公式メールアドレス',
  ID.stringTextDial: '電話をかける',
  ID.textFeedbackDialogMessage: '提出が成功しました。ご意見ありがとうございます！',
  ID.textRemind: '情報を完備してください',
  ID.textUnbounded: '未設定',
  ID.textBindPhone: '携帯電話を設定する',
  ID.texSubmit: '提出',
  ID.textbindSuccess: '設定成功',
  ID.manageCoin: '通貨管理',
  ID.supportToken: '追加済みの通貨',
  ID.nosupportToken: '追加予定の通貨',
  ID.tokenSearch: 'トークンの略称、名称、契約アドレス',
  ID.notifiAllRead: '全て既読',
  ID.notifiTxTitle: '転送通知',
  ID.notifiSysTitle: 'システム通知',
  ID.textChooseChain: 'メインチェーンを選択してください',
  ID.textCorrectAddress: '正しいアドレスを入力してください',
  ID.textChain: 'メインチェーン',
  ID.submitToken: 'トークンを提出する',
  ID.tokenSymbol: 'シンボル',
  ID.tokenContracts: 'ID/契約アドレス',
  ID.enterContractsInfo: '契約アドレスを入力してください',
  ID.addContractRemind1:
      '1. トークンの契約アドレスを提供するだけで、審査が通過すれば正常に使用できます。 2. トークンのロゴを表示する必要がある場合は、指定された情報を********************まで送信してください。',
  ID.stringConfirmPsw: '確認パスワード',
  ID.addContractRemind2:
      '① 契約アドレス； ② logo.png (500ピクセルx500ピクセル)； ③ 会社、個人または公式ウェブサイトなどの関連情報； ④ その他の状況説明。',
  ID.importTokenSymbol: 'トークンシンボルを入力',
  ID.addressBookEditContacts: '連絡先を編集',
  ID.addressBookDelContacts: '連絡先を削除',
  ID.addressBookDelContactsContent: '@contactsの連絡先を削除してもよろしいですか？',
  ID.addressBookDelContactsConfirm: '削除を確認',
  ID.addressAlreadyExists: 'このアドレスは既に存在します！',
  ID.unsearchToken: '指定されたトークンが見つかりませんでしたか？',
  ID.tokenLabel: 'タイプ',
  ID.authFingerprintTip: '画面内の指紋センサーを押して指紋を認証してください',
  ID.authContnetTextFace: '顔認証でロック解除を行うには、システム設定で権限を有効にしてください',
  ID.authContnetTextFingerprint: '指紋認証でロック解除を行うには、システム設定で権限を有効にしてください',
  ID.authContnetTextSaveFingerprint: '指紋が登録されていません。設定画面で指紋を登録してください',
  ID.gotoSettingTitle: '設定へ',
  ID.authFaceUnlock: '顔認証ロック解除',
  ID.authFingerprintUnlock: '指紋認証ロック解除',
  ID.authFingerprintTap: '指紋センサーを押してください',
  ID.authFaceTitle: '顔認証ID',
  ID.authFingerprintTitle: '指紋認証',
  ID.stringTips: 'ヒント',
  ID.nfcOpen: 'オーペンしました',
  ID.nfcClose: 'クロースしました',
  ID.addHardwareTitle: 'ハードウェアウォレットを追加',
  ID.bindWalletTitle: 'ウォレットを接続',
  ID.scanBindTitle: 'QRコードで接続',
  ID.pageCount: '@pageページが一時的に欠けています',
  ID.noinstallQQ: 'QQがインストールされていないか、インストールされたバージョンが対応していません',
  ID.noinstallWechat: 'WeChatがインストールされていません',
  ID.scanResult: 'スキャン結果',
  ID.copy: 'コピー',
  ID.zxingError: 'QRコードのスキャンエラー、再スキャンしてください',
  ID.pleaseScanPage: '@scanPageページをスキャンしてください',
  ID.scanNotMatchWallet: 'スキャン情報が現在選択されているウォレットと一致しません',
  ID.scanError: 'QRコードのスキャンエラー、再試行してください',
  ID.bindSucessInfo: 'ハードウェアウォレット@walletに正常に接続されました、さっそく使用しましょう',
  ID.bindSucess: 'バインド成功！',
  ID.openWallet: 'ウォレットを開く',
  ID.chainManager: '管理',
  ID.chainSegWitP2sh: '互換性',
  ID.chainSegWit: '隔離',
  ID.chainLegacy: '標準',
  ID.chainSegWitP2shItem: '隔離証明（互換性あり）',
  ID.chainLegacyItem: '標準アドレス',
  ID.chainSegWitItem: '隔離証明（ネイティブ）',
  ID.chainSelectWallet: 'ウォレットを選択',
  ID.chainWalletManager: 'ウォレット管理',
  ID.chainAddAddress: 'アドレスを追加',
  ID.chainAddressDetail: 'アドレス詳細',
  ID.chainEditRemark: 'メモ名を編集',
  ID.chainAddressDetailTip1: '受信のみサポート',
  ID.chainAddressDetailTip2: 'ネットワーク資産',
  ID.chainAddType: 'タイプ',
  ID.chainAddRemark: 'メモ名',
  ID.chainComplete: '完了',
  ID.chainAllChains: 'すべてのネットワーク',
  ID.chainTotalAssets: '資産総額',
  ID.allWallets: 'すべてのウォレット',
  ID.walletInfo: 'ウォレット情報',
  ID.walletName: 'ウォレット名',
  ID.bindingTime: 'バインド時間',
  ID.deviceModel: 'デバイスモデル',
  ID.deviceId: 'デバイスID',
  ID.seVersion: 'セキュリティチップバージョン',
  ID.walletAppVersion: 'アプリバージョン',
  ID.unbind: '接続解除',
  ID.using: '使用中',
  ID.bindFailed: '接続に失敗しました！',
  ID.rebind: '再接続',
  ID.accountDelete: 'アカウントをログアウト',
  ID.accountDeleteTip: '現在のアカウントをログアウトしますか？',
  ID.safeSetting: 'セキュリティ設定',
  ID.deleteError: 'ログアウトに失敗しました',
  ID.deleteSuccess: 'ログアウトに成功しました',
  ID.loading: '読み込み中...',
  ID.binding: '接続中...',
  ID.sameMnemonicWallet: '同じニーモニックフレーズのウォレットがインポートされていることを検出しました。上書きしますか？',
  ID.sureDeleteWallet: '接続を解除してもよろしいですか？',
  ID.textAddressCount: '(@indexAdd個のアドレス)',
  ID.textAccountCount: '(@indexAcc個のアカウント)',
  ID.baseSettings: '設定',
  ID.myTitle: 'マイページ',
  ID.feeSettingTitle: 'マイニング手数料設定',
  ID.feeTitle: 'マイニング手数料',
  ID.gearSelect: 'レベル選択',
  ID.customizeTitle: 'カスタム',
  ID.fastTitle: '高速',
  ID.normalTitle: '標準',
  ID.slowTitle: '低速',
  ID.estimateTxTime: '予想取引時間 @minute',
  ID.minerFeeTitle: 'マイニング手数料率',
  ID.minuteTitle: '分',
  ID.hourTitle: '時間',
  ID.secondTitle: '秒',
  ID.p4seedRemind:
      'ニーモニック強化モードでは、秘密鍵はニーモニックとパスワードの両方で生成され、パスワードは変更できません。ウォレットを復元するには、ニーモニックとパスワードの両方が必要で、どちらか一方が欠けると復元できません。\n\n 通常モードでは、秘密鍵はニーモニックのみで生成され、パスワードは変更可能です。ニーモニックさえあればウォレットを復元できます。\n\n ニーモニックを必ず安全に保管し、パスワードを忘れないようにしてください。',
  ID.p4seedIspharase: 'ニーモニック強化モード',
  ID.p4seedIspharaseYes: 'はい',
  ID.p4seedIspharaseNo: 'いいえ',
  ID.stringBind: 'バインド',
  ID.textPro1Binding:
      '1. ハードウェアウォレットを開き、対象の通貨を選んで「受信」ページを開きます。\n\n 2. 「マイアドレス」リストに入り、バインドするアドレスを選択します。\n\n 3. 「残高」ページに移動し、「残高を更新」をクリックしてQRコードを表示します。',
  ID.scanConnect: 'QRコードで接続',
  ID.customFeeConfirm: 'マイニング手数料の設定が確認されていません。このページを離れますか？',
  ID.customFeeSuccess: 'カスタムマイニング手数料が有効になりました',
  ID.feeSlowTips: 'マイニング手数料率が低すぎると、取引確認時間に影響を与える可能性があります',
  ID.feeFastTips: 'マイニング手数料率が高すぎると、マイニング手数料の無駄が生じる可能性があります',
  ID.feeOkTips: '有効なマイニング手数料率を入力してください',
  ID.inputToAddressTip: '受取アドレスを入力してください',
  ID.inputOkToAddressTip: '正しいアドレスを入力してください',
  ID.inputAmountTip: '支払い金額を入力してください',
  ID.amounNumberError: '正しい金額を入力してください',
  ID.transferToSelfTip: '自分自身に送金することはできません',
  ID.insufficientBalanceFee: '残高がマイニング手数料をカバーするのに不足しています',
  ID.insufficientBalance: '残高不足',
  ID.insufficientMainFee: 'マイニング手数料不足',
  ID.coinTransferTitle: '@symbol 送金',
  ID.coinTsTypeTitle: '取引タイプ',
  ID.qrTitleTransfer: '取引に署名',
  ID.qrAutoPage: '自動ページ送り',
  ID.qrManualPage: '手動ページ送り',
  ID.scanQrSendTitle: 'QRコードで送信',
  ID.qrSettingQrsizeTitle1: 'ハードウェアウォレットでQRコードをスキャン\n認識が難しいですか？',
  ID.qrSettingQrsizeTitle2: 'QRコードの容量を設定 >',
  ID.sendAmountSmall: '送金金額が小さすぎるため、送金できません',
  ID.completionProgress: '現在の進行状況',
  ID.ethGasPriceSlowTip:
      '設定した最高基本手数料が現在のネットワーク基本手数料@feeより低いため、取引が成功するまでに長時間かかる可能性があります',
  ID.ethGasPriceLargeTip: 'Gas priceが高すぎると、マイニング手数料が無駄になる可能性があります',
  ID.ethGasLimitTip: 'Gas上限は@limitより大きく、15.00Mより小さい必要があります',
  ID.filGasEmtyTips: '有効なGasを入力してください',
  ID.filGasSlowTips: 'Gasが低すぎると、Out of Gasで取引が失敗する可能性があります',
  ID.ethGasPriceMinTip: 'Gas価格は@gasより低くしてはいけません',
  ID.remarkMemo: 'メモ（Memo）',
  ID.remark: '備考',
  ID.remarkNoText: '現在メモはありません',
  ID.remarkHitText: '任意',
  ID.remarkTag: 'タグ（Tag）',
  ID.xrpRemarkTips: 'XRP主チェーンの規定により、アドレスの残高が@value XRPを超える部分のみが送金可能です。',
  ID.xrpTsTips: '残高不足！XRP主チェーンの規定により、アドレスの残高が@value XRPを超える部分のみが送金可能です。',
  ID.xrpNoActivityTips:
      'アカウントがアクティブ化されていません。アカウントをアクティブにするには、最低@value XRPを一度にこのアドレスに送金してください',
  ID.maxFeeValidTip: '有効なmaxFeeを入力してください',
  ID.maxFeeNoLessMaxPriorityFeeTip: 'maxFeeはmaxPriorityFeeより小さくてはいけません',
  ID.maxPriorityFeeTip:
      '設定されたmaxPriorityFee @inputMaxは、maxPriorityFee @rawMaxとcurrentBaseFee @baseFeeの合計が設定されたmaxFee @maxFeeより大きいため、maxFeeを引き上げてmaxPriorityFeeの効果を最大限に引き出してください',
  ID.maxPriorityFeeHeightTip: 'maxPriorityFeeが高すぎると、マイニング手数料が無駄になる可能性があります',
  ID.maxFeeHeightTip:
      '設定されたmaxFeeが大きすぎると、予想@rawFee Gweiの4倍以上になっており、maxFeeを上げるだけでは取引がより速くパッケージ化されない可能性があります',
  ID.tronActivatedTip: 'このアドレスはアクティブ化されておらず、1 TRXを消費してアクティブ化する必要があります',
  ID.tronNeedSource: '取引に必要なリソース',
  ID.valueBandwidth: '@bandwidth 帯域幅',
  ID.valueEnergy: '@energy エネルギー',
  ID.activedAccount: 'アカウントをアクティブ化',
  ID.deductionValueBandwidth: '@bandwidth 帯域幅を支払う',
  ID.deductionValueEnergy: '@energy エネルギーを支払う',
  ID.tronPopTip: 'アカウントの利用可能なTRXがマイニング手数料をカバーするのに不足しており、取引が失敗する可能性があります',
  ID.tronRemarkTip: '送金メモを追加すると、1 TRXの追加手数料がかかります。文字数は200文字以内です',
  ID.tronResourceDetail:
      '取引に必要なリソース = 帯域幅消費 + エネルギー消費\n\n帯域幅消費:\n受信アカウントの取引は、無料帯域幅またはステーキング帯域幅でのみ差し引かれます。どちらも取引消費を満たさない場合は、すべてTRXで支払う必要があります\n\nエネルギー消費:\n取引は、すべてのエネルギーが差し引かれた後、残りはTRXで支払われます\n\n取引に必要なリソースはユーザーが支払ったすべてのリソースに基づいて推定され、実際のリソース消費はチェーン上のデータを基準とします',
  ID.tronFeeDetail:
      'TRONネットワーク料金：\n\nユーザーはTRONネットワーク上で、帯域幅やエネルギー不足によるTRX料金の控除、および特定の取引手数料（例えば、アカウントの有効化には1 TRXが必要）を含む料金が発生します。\n\nすべての料金はチェーン自体によって徴収され、実際の消費量はオンチェーンデータに基づきます。',
  ID.tronRiskTip:
      '取引を続行すると、アカウントの残りのTRX/リソースが消費され、取引が失敗するリスクがあります。続行するかどうかを確認してください',
  ID.basicEnergy: '通常の消費',
  ID.extraEnergy: '追加の消費',
  ID.tronLearnMore: '人気のある契約を呼び出す取引は、追加でエネルギーを消費します。詳細を確認してください',
  ID.fromAccount: '送信アカウント',
  ID.toAccount: '受信アカウント',
  ID.addressLabelEmpty: 'メモ名を入力してください',
  ID.feeRang: '推定範囲',
  ID.bindWallet: 'ウォレットをバインド',
  ID.scanQrBind: 'QRコードでバインド',
  ID.verifySuccessTitle: '防偽検証成功！',
  ID.verifyFailTitle: '防偽検証失敗！',
  ID.verifySuccessSubTitle: 'このウォレットデバイスは公式販売品であり、ご支援ありがとうございます',
  ID.verifyFailSubTitle: 'このウォレットデバイスは公式販売品ではありません。慎重に使用してください',
  ID.goBackHome: 'ホームに戻る',
  ID.stringResource: 'リソース',
  ID.stringDelegateResource: '代理リソース',
  ID.stringVote: 'ノード投票',
  ID.stringStake2: 'ステーキング資産2.0',
  ID.stringUnstake2: 'アンロック資産2.0',
  ID.stringStake: 'ステーキング資産',
  ID.stringUnstake: 'アンロック資産',
  ID.stringReclaim: 'リソースの回収',
  ID.stringReclaimAddress: '回収アドレス',
  ID.stringConfirming: '確認中',
  ID.stringPacking: 'パッケージング中',
  ID.stringSendFail: '失敗しました',
  ID.stringTxTime: '取引時間',
  ID.stringTxId: '取引ID',
  ID.stringTxBlock: 'ブロック',
  ID.stringTxSource: '消費リソース',
  ID.stringTxEnergy: 'エネルギー',
  ID.stringTxBandwidth: '帯域幅',
  ID.stringTxWithdraw: 'TRXを引き出す',
  ID.stringBurnedEnergy: 'エネルギーを燃焼して控除',
  ID.stringBurnedBandwidth: '帯域幅を燃焼して控除',
  ID.stringBlockchain: 'ブロックチェーンブラウザで表示',
  ID.stringSearchEmpty: '指定されたTokenが見つかりません',
  ID.stringSubmitToken: 'Tokenを提出する',
  ID.stringInputOkContract: '正しい契約アドレスを入力してください',
  ID.stringAddTokenSuccess: '提出が成功しました。審査をお待ちください！',
  ID.stringBuyWallet: 'ハードウェアウォレットを購入',
  ID.stringGuideTitle1: 'マルチチェーン資産ウォレット',
  ID.stringGuideSubTitle1:
      'ハードウェアウォレット専用に設計され、資産の安全を確保しつつ、より便利でユーザーフレンドリーなWeb3インタラクション体験を提供します。',
  ID.stringGuideTitle2: 'Coinbagウォレットへようこそ',
  ID.stringGuideSubTitle2:
      'より良い資産転送、残高の概覧、ステーキングリターンなどの機能を今すぐ体験してください。ハードウェアウォレットを接続して使用してください。',
  ID.stringEmptyTitle: 'ウォレットに接続して資産を管理',
  ID.stringEmptyTitle2: 'マルチチェーンサポート、全面カバー',
  ID.stringEmptySubTitle2: '世界中の優れたコインを管理し、主要なNFTをサポート',
  ID.stringAllNetTitle: '全ネットワークを表示',
  ID.stringWeb3Title: 'Web3の新しい体験',
  ID.stringWeb3SubTitle: 'ハードウェアウォレット専用に設計',
  ID.stringWeb3Title1: '簡単に始める',
  ID.stringWeb3SubTitle1: 'より便利でユーザーフレンドリーなWeb3インタラクション体験',
  ID.stringWeb3Title2: '安全で透明',
  ID.stringWeb3SubTitle2: '三層保護、オフライン保存、ネットに触れることなく、大額の資産の安全を守る内蔵セキュリティチップ',
  ID.stringWeb3Title3: 'マルチチェーン統合',
  ID.stringWeb3SubTitle3: '一つのウォレットでブロックチェーンの保存と管理を完了',
  ID.stringSupportChains: '現在サポートされている @count 個のネットワーク',
  ID.stringCreateNewWallet: '新しいウォレットを作成',
  ID.stringScanQrBind: 'QRコードで接続',
  ID.stringScanQrBindDes: 'スキャンしてハードウェアウォレットを接続し、デジタル資産を簡単に管理',
  ID.stringNFCBind: 'NFC接続',
  ID.stringNFCBindDes: 'タッチカードで即座に接続、ハードウェアウォレットと迅速に接続、安全かつ効率的。',
  ID.stringTouchPsw: 'パスワードを入力してください',
  ID.stringTouchPhone: 'カードをスマートフォンに近づけてください',
  ID.stringTouchNONfc: '現在のデバイスはNFC機能をサポートしていません',
  ID.stringTouchReadError: '読み取りエラー、再試行してください',
  ID.stringTouchReading: '読み取り中、カードを移動しないでください',
  ID.stringTouchNfcSuccess: '操作成功',
  ID.stringNfcPswMax: 'パスワードのエラー回数が上限に達しました。ウォレットがリセットされました',
  ID.stringNfcInputOnePsw: 'パスワードをもう 1 回試すことができます。再度エラーが発生するとウォレットがリセットされます',
  ID.stringNfcPswErrorNum: 'パスワードが正しくありません。さらに @count 回エラーが発生するとウォレットがリセットされます',
  ID.stringNfcPswError: 'パスワードが正しくありません。再試行してください',
  ID.stringNfcScanError: 'スキャンに失敗しました。再試行してください',
  ID.stringCardHaveWallet: 'このカードは既にウォレットに存在します',
  ID.stringCardWalletError: 'ウォレットが一致しません',
  ID.stringCardNumberError: 'カード番号が間違っています',
  ID.stringNfcUnknownError: '不明なエラー',
  ID.stringTouchNoOfficial: 'このカードはCoinbagの公式製品ではありません',
  ID.stringTouchSpotError: '認識できません。正しいカードを読み取ってください',
  ID.stringToucReadCard: 'カードを読み取る',
  ID.stringTouchReadTips: 'カードの読み取りには時間がかかる場合があります。カードとスマートフォンを接触させたままにしてください。',
  ID.stringNewWallet: '新しいウォレット',
  ID.stringCreateTitle: '作成',
  ID.stringCreateDesTitle: 'バックアップされた助記詞がありません。ウォレットを「作成」してください',
  ID.stringImportTitle: 'インポート',
  ID.stringImportDesTitle: '助記詞がバックアップされています。「インポートウォレット」を選択してください',
  ID.stringCreateWalletTitle: 'ウォレットを作成',
  ID.stringImportWalletTitle: 'ウォレットをインポート',
  ID.stringAgreementTip: '「ユーザー契約」をお読みの上、同意してください',
  ID.stringTouchNumber: '尾番号が @number のカードを使用してください',
  ID.stringCreating: '作成中...',
  ID.stringImporting: 'インポート中...',
  ID.stringCreateSuccess: '作成成功',
  ID.stringImportSuccess: 'インポート成功',
  ID.stringInputMnemonic: '正しい助記詞を入力してください',
  ID.stringVerifyFail: '検証に失敗しました',
  ID.stringImportMnemonic: '助記詞をインポート',
  ID.stringBackupMnemonic: '助記詞をバックアップ',
  ID.stringBackupMnemonicDes:
      '助記詞はウォレットの復元に使用できます。ウォレットの資産を管理するために、必ず順番に書き写し、適切に保管してください！',
  ID.stringVerifyMnemonic: '助記詞を検証',
  ID.stringOrderInputMnemonic: '助記詞を順番に入力してください',
  ID.stringTouchPasswordTip:
      '6-32文字で、a-z、A-Z、0-9を含むことができます。パスワードを紛失すると復元できませんので、必ず記憶してください',
  ID.stringTouchInputPsw: 'パスワードを入力してください',
  ID.stringTouchInputPswTwo: 'パスワードを確認してください',
  ID.stringTouchImInputCorrectPsw: '正しいパスワードを入力してください',
  ID.stringTouchTowPswFail: 'パスワードが一致しません',
  ID.stringTouchSetWalletName: 'ウォレット名を設定',
  ID.stringTouchInputWalletNameHint: 'ウォレット名を入力',
  ID.stringTouchWalletNameToast: 'ウォレット名を入力してください',
  ID.stringTouchNextTitle: '次へ',
  ID.stringTouchSafeTitle: 'セキュリティ通知',
  ID.stringTouchPopTip1: 'ニーモニックフレーズの安全を守るため、周囲に他の人や監視装置がないことを確認してください。',
  ID.stringTouchPopTip2:
      '重要な注意事項: \n·漏洩しないでください\n·ウェブページに入力しないでください\n·ネットワークツールを通じて送信しないでください\n·スマートフォンで撮影しないでください\n·電子版で保存しないでください\n·漏洩しないでください',
  ID.stringTouchPopOkTitle: '了解しました',
  ID.stringTouchEnglishTitle: '英語',
  ID.stringTouchChineseTitle: '中国語',
  ID.stringTouchNumberTitle: '数字',
  ID.stringWalletIdTitle: 'ウォレットID',
  ID.stringResetCardTitle: 'カードのリセット',
  ID.stringChangePswTitle: 'パスワードの変更',
  ID.stringChangeWalletNameTitle: 'ウォレット名の変更',
  ID.stringCardEmpty: 'このカードは空のカードです',
  ID.stringBackupComplete: 'バックアップ完了',
  ID.stringVerifybackupTitle: 'バックアップの確認',
  ID.stringWalletNameNoEmpty: 'ウォレット名は空であってはいけません',
  ID.stringWalletNameMax12: '最大12文字',
  ID.stringSetWalletName: 'ウォレット名の設定',
  ID.stringNewNameSameOldName: '新しい名前は以前の名前と同じにできません',
  ID.stringNameChangeSuccess: '変更成功',
  ID.stringInputOldPswTip: '古いパスワードを入力してください',
  ID.stringInputNewPswTip: '新しいパスワードを入力してください',
  ID.stringInputNewPswTwoTip: '新しいパスワードを確認してください',
  ID.stringTouchVerifyPswTitle: 'パスワードの確認',
  ID.stringOldNewSameTip: '新しいパスワードは古いパスワードと同じにできません',
  ID.stringTouchResetSuccess: 'リセット成功',
  ID.stringTouchSignErrorTip: '署名データエラー',
  ID.stringTronRawDataHexError: 'Tron取引の検証に失敗しました',
  ID.stringEmptyWalletTitle: 'Web3の入り口は一つで十分です',
  ID.stringEmptyWalletSubTitle: 'ウォレット · 取引 · NFT · DeFi · DApp',
  ID.stringTabWallet: 'ウォレット',
  ID.stringTabDiscover: '発見',
  ID.stringTabProfile: '私',
  ID.stringBackupTip:
      'スマートフォンやウォレットデバイスの紛失や損傷による資産の損失を避けるために、ニーモニックフレーズを速やかにバックアップしてください。',
  ID.stringBackupButtonTitle: '今すぐバックアップ',
  ID.stringJumpTitle: 'スキップ',
  ID.stringNfcReading: 'スキャンの準備ができました',
  ID.stringSignLoading: '署名中、しばらくお待ちください',
  ID.stringBingdingTitle: '今すぐバインド',
  ID.stringCreateDesString: 'ウォレットの作成が成功しました。すぐに使用してください！',
  ID.stringImportDesString: 'ウォレットのインポートが成功しました。すぐに使用してください！',
  ID.stringCreateLoading: '作成中、しばらくお待ちください',
  ID.stringImportLoading: 'インポート中、しばらくお待ちください',
  ID.stringNfcCloseAndroidTip: 'NFCがオフです。設定でオンにしてください。',
  ID.stringNfcCloseIOSTip: 'NFCがオフです。設定 -> 一般でオンにしてください。',
  ID.stringTxError01: '残高を更新して再送信してください',
  ID.stringTxError02: '取引がブロードキャストされました。再度ブロードキャストしないでください',
  ID.stringTxError03: '支払い金額が低すぎます。調整して再送信してください！',
  ID.stringTxError04: '備考を記入して再送信してください',
  ID.stringTxError05: '取引が失効しました。再送信してください',
  ID.stringTxError06: '送信失敗。リソースが十分か確認してください',
  ID.stringTxError07: '取引が未成熟です。成熟期に達してから送信してください。',
  ID.stringTxError08: 'UTXOの数が多すぎます。金額を調整して再送信してください。',
  ID.stringTxError09: '送金額がアドレスの制限を超えています。',
  ID.stringTxError10: '残高を更新してから再送信してください。',
  ID.stringTxError11: '手数料が高すぎます。手数料を調整して再送信してください！',
  ID.stringTxError12: '支払金額が低すぎます。調整してから再送信してください！',
  ID.stringTxError13:
      '送信失敗、取引には未成熟のコインが含まれています。マイニング報酬のコインは成熟するまで送信できません。成熟期間は100確認です。',
  ID.stringTxError14: '手数料が低すぎます。手数料を調整して再送信してください。',
  ID.stringTxError15: '送信失敗、システム時間が正しいか確認してください。',
  ID.stringTxError16: '送信失敗、金額が正しいか確認してください。',
  ID.stringTxError17: '送信失敗、リップルの公式制限：アカウント残高は20 XRPを下回ることはできません！',
  ID.stringTxError18: 'このアカウントはまだアクティベートされていません！',
  ID.stringTxError19: '手数料が低すぎます。再設定してください。',
  ID.stringTxError20: 'メモを入力してから再送信してください。',
  ID.stringTxError21:
      'この取引のGASが低すぎると取引が失敗する可能性があります。長時間確認されない場合はGASを調整して再送信してください。',
  ID.stringTxError22: '前の取引が完了するまで待つか、手数料を10%以上上げてから送金してください。',
  ID.stringTxError23: 'GasLimitが低すぎます。調整してから再送信してください。',
  ID.stringSetting: '設定',
  ID.stringP2SHDes:
      '"Legacy" と "Segwit" アドレスタイプに対応、取引手数料は中程度、アドレスは "3" で始まります。',
  ID.stringP2TRDes: 'Taproot: より高いプライバシーと効率性、"bc1p" で始まります。',
  ID.stringP2WPKHDes: '現在の主流アドレスタイプ、取引手数料は低め、アドレスは "bc1" で始まります。',
  ID.stringP2PKHDes: '最初のビットコインアドレス形式、取引手数料は高め、アドレスは "1" で始まります。',
  ID.stringSignMessage: 'メッセージ署名',
  ID.stringMessgaeSign: 'メッセージの署名',
  ID.stringSignMethodTitle: '署名方法',
  ID.stringMessageContentTitle: 'メッセージ内容',
  ID.stringSignTextHint: '署名が必要なテキスト情報を入力または貼り付けてください。',
  ID.stringSignTextTip: 'メッセージ内容を入力してください。',
  ID.stringUnconfirmedUtxo: '未確認のUTXO資産を含んでいます。',
  ID.stringAddressPublicTitle: 'アドレスの公開鍵',
  ID.stringUnavailableBalance: '使用できない残高',
  ID.stringTotalBalance: '総資産：',
  ID.stringNotSupportedTaproot: '現在のウォレットはTaprootアドレスをサポートしていません。',
  ID.stringDownloadFailed: 'ダウンロードに失敗しました',
  ID.stringUpgradeNow: '今すぐアップグレード',
  ID.stringTalkLater: '後で話しましょう',
  ID.stringBackButton: '戻るボタン (もどるボタン)',
  ID.stringNewVersion: '新しいバージョンがあります！',
  ID.stringCoinbagISavailable: 'Coinbagの新しいバージョンがあります！',
  ID.stringCheckForUpdates: 'アップデートを確認',
  ID.stringHaveLatestVersion: 'すでに最新バージョンです。',
  ID.stringP2SHDLTCes: '現在主流のアドレスタイプ。アドレスは「M」で始まります',
  ID.stringP2WPKHLTCDes:
      'Segregated Witness (SegWit) テクノロジーのアドレス形式により、効率が向上し、トランザクション手数料が低くなります。アドレスは「ltc1」で始まります。',
  ID.stringP2PKHLTCDes: 'オリジナルのライトコインアドレス形式、アドレスは「L」で始まります',
  ID.stringBroadcast: '放送',
  ID.stringBroadcastTitle: '下のボタンをクリックして放送します',
  ID.stringNodeTimeOut: 'ノード要求がタイムアウトしました。後でもう一度試してください。',
  ID.stringNotAddedToAddressBook: 'アドレス帳に追加されていません、',
  ID.stringAddNow: '追加してください',
  ID.stringAddressInTheAddressBook: 'アドレス帳の中のアドレス',
  ID.stringPaste: '貼り付け',
  ID.stringSupportsReceivingNetwork: '@receiveInfoネットワーク資産の受信のみをサポートしています',
  ID.stringEosEmptyTitle: 'アカウント名がありません',
  ID.stringEosStake: 'リソースを委任する',
  ID.stringEosEmptyDes: 'EOS の環境上の理由により、登録は当面サポートされません。',
  ID.stringEosBuyRam: 'RAMを購入する',
  ID.stringEosSellRam: 'RAMを売却する',
  ID.stringEosRefoud: 'リソースを回収する',
  ID.stringEosNewAccount: 'アカウントを登録する',
  ID.stringEosBidname: 'オークションアカウント',
  ID.stringEosReceiveTheRefunded: '引き換え請求',
  ID.stringExportAccountTitle: 'エクスポートアカウント',
  ID.stringEosPublicKeyTitle: 'EOS公開鍵',
  ID.stringEosAccountTitle: 'EOSアカウント',
  ID.stringScanCompleteTitle: 'コードのスキャンが完了しました',
  ID.stringNORemarkTitle: 'ラベルなし',
  ID.stringCheckDetailTitle: '詳細を確認する',
  ID.stringShowQRTitle: 'QRコードを表示',
  ID.stringCopyAccmountTitle: 'アカウントをコピーする',
  ID.stringEditRemarkTitle: 'タグを編集する',
  ID.stringEthNotSupportedTips:
      'イーサリアムはEIP155にアップグレードされ、古いハードウェアウォレットはサポートされなくなりました。資産の安全を確保するために、新しいウォレットに交換してください。',
  ID.stringNotSupportedChain: '現在のハードウェアウォレットは、このチェーンをサポートしていません。',
  ID.stringZecNotSupportedTips:
      '古いハードウェア ウォレットは ZEC チェーンをサポートしなくなりました。資産の安全を確保するために、時間内に新しいウォレットに変更してください。',
  ID.stringCPULease: 'CPUリース',
  ID.stringEosTxInvalid: 'トランザクションの有効期限が切れました。再送信してください',
  ID.stringInsufficientResources: '送信に失敗しました。リソースが十分であるかどうかを確認してください',
  ID.stringEosAccountDetialTitle: 'EOSアカウントの詳細',
  ID.stringCreateDateTitle: '作成時刻: @date',
  ID.stringAccountRemarkTitle: 'アカウントタグ：@tag',
  ID.stringThresholdTitle: 'しきい値: @value',
  ID.stringWeightTitle: '重み: @value',
  ID.stringVIPbenefits: 'VIP特典',
  ID.stringVIPTip1: '特典ガイドを見る',
  ID.stringVIPTip2: '尊敬なるVIPユーザー様、あなたの特典は「マイページ」でご確認いただけます。ガイドに従って操作してください。',
  ID.stringVIPTip3: '「マイページ」で「VIP特典を見る」をクリックするだけです。',
  ID.stringNotSupportEIP1559: '現在のウォレットはEIP1559取引をサポートしていません。',
  ID.stringResourceManager: 'リソース管理',
  ID.stringNetDetail: '帯域幅の詳細',
  ID.stringNetManager: '帯域幅管理',
  ID.stringStakeGet: '誓約により取得',
  ID.stringOtherDelegateOwner: '他人は自分のために行動する',
  ID.stringDelegateToOther: '他人のために行動する',
  ID.stringFreeGet: '無料ギフト',
  ID.stringDelegated: 'すでに代表されている',
  ID.stringCanDelegate: 'エージェント可能',
  ID.stringDelegate: '演技',
  ID.stringReclaimTitle: 'リサイクル',
  ID.stringEnergy: 'エネルギー',
  ID.stringBandwidth: '帯域幅',
  ID.stringGetBandwidth: 'ゲイン帯域幅',
  ID.stringEnergyDetail: 'エネルギーの詳細',
  ID.stringEnergyManager: 'エネルギー管理',
  ID.stringGetEnergy: 'エネルギーを得る',
  ID.stringTronNoSupportedTip:
      'Tron ネットワークのアップグレード Stake 2.0 の影響により、現在のウォレットはこの機能をサポートしていません',
  ID.stringResourceToAddress: 'リソース受信アドレス',
  ID.stringResourceToAddressTip: 'リソース受信アドレスを入力してください',
  ID.stringDelegateAmount: 'エージェントの数',
  ID.stringDelegateAmountTip: 'エージェントの数を入力してください',
  ID.stringCanDelegateEnergy: 'エージェント可能: @value エネルギー',
  ID.stringCanDelegateBandwidth: 'プロキシ可能: @value 帯域幅',
  ID.stringUseStakeTrx: '* ステークされた @value TRX を占有することが予想されます',
  ID.stringDelegateTip: '• 委任後も、TRX はまだ質権アカウント内にあり、リソース使用権のみが他の人に委任されます。',
  ID.stringOk: 'もちろん',
  ID.stringUseStakeAmount: '占有誓約数量',
  ID.stringOtherGetResource: '* 相手が @value @resource を取得することを期待します',
  ID.stringResourceMaxTip: '現在の最大使用量を超えています',
  ID.stringInputHintTrx: 'TRX数量を入力してください',
  ID.stringResourceOwnAddressTips: 'リソース受信アドレスを自分のアドレスにすることはできません',
  ID.stringDelegateUseTrx: '利用可能: @value TRX',
  ID.share: 'シェア',
  ID.penInBrowser: 'ブラウザを開く',
  ID.copyLink: 'リンクをコピー',
  ID.favorites: 'お気に入り',
  ID.switchWallet: 'ウォレットを切り替える',
  ID.successfullyAddedToFavorites: 'お気に入りに追加しました',
  ID.unfavorited: 'お気に入りを解除しました',
  ID.recent: '最近',
  ID.viewAll: 'すべて表示',
  ID.recentlyUsed: '最近使用',
  ID.refresh: '更新',
  ID.messages: 'メッセージ',
  ID.paymentDetails: '支払いの詳細',
  ID.stringTronErrorMessage1: 'まだ解凍時期ではありませんので、解凍してから操作してください。',
  ID.stringTronErrorMessage2: '自分のアドレスには転送しないでください',
  ID.stringTronErrorMessage3: '凍結を解除する資産はありません',
  ID.stringDelegatePopAmount: '占有誓約数量',
  ID.stringDeleagteEnergy: '代理店のエネルギー',
  ID.stringDelegateBandwidth: 'プロキシ帯域幅',
  ID.stringResouecePopTips:
      'マイニング料金: 差し引かれたリソースを燃やすためのマイニング料金と、特定のトランザクションのマイニング料金が含まれます\n\nマイニング料金は、ユーザーが支払ったすべてのリソースに基づいて見積もられ、実際の支払い額はチェーン上のデータの影響を受けます。',
  ID.stringSearchDapp: 'Dappを検索するか、URLを入力してください',
  ID.stringSearchResult: '検索結果',
  ID.stringSearchHistory: '検索履歴',
  ID.stringUnableToFind: 'アプリケーションが見つかりません',
  ID.recommend: 'おすすめ',
  ID.stringLockingResource: 'ロック中のリソース',
  ID.stringCanReclaimTitle: 'リサイクル可能',
  ID.stringDateTitle: '時間',
  ID.stringReclaimAmount1: 'リサイクル数量',
  ID.stringReclaimAmount2: 'TRXのステーキング解除数量',
  ID.stringCanReclaimValue1: '回収可能：@value @resource',
  ID.stringCanReclaimValue2: '持ち上げることができます: @value TRX',
  ID.stringInputReclaimHint1: 'リサイクル数量を入力してください',
  ID.stringInputReclaimHint2: 'TRXのステーキング解除数量を入力してください',
  ID.stringInputReclaimTip1: '* 推定ステーキング解除 TRX: @value',
  ID.stringInputReclaimTip2: '* 相手先アドレスからの推定回収: @value @resource',
  ID.stringReclaimEnergy: 'リサイクルエネルギー',
  ID.stringReclaimBandwidth: 'バン_Widthのリサイクル',
  ID.stringResourcePopTip1: 'ノート：ハードウェアウォレット回復はアンロックとして表示されます',
  ID.stringResourcePopTip2: 'ノート：ハードウェアウォレットプロキシはステーキングとして表示されます',
  ID.stringStakeTitle: '誓約',
  ID.stringTotalAmount: '総資産',
  ID.stringTotalStakeAmount: '誓約総額',
  ID.stringUnlocking: 'ロックを解除する',
  ID.stringCanExtract: '抽出可能',
  ID.stringUnderstandingStaking: 'ステーキング 2.0 を理解する',
  ID.stringStakeBandwidthTips: '• 帯域幅はTRXを約束することで取得する必要があります',
  ID.stringStakeEnergyTips: '• エネルギーはTRXをステーキングして取得する必要があります',
  ID.stringUnstakeTitle: 'ロックを解除する',
  ID.stringFavorited: '収集済み (しゅうしゅうずみ)',
  ID.stringEdit: '収集済み (しゅうしゅうずみ)',
  ID.stringWithraw: '引き出し',
  ID.stringGetAmount: '数量を取得する',
  ID.stringStakeGetResource: '利用可能: @value @resource',
  ID.stringStakeHint1: '取得するリソースの量を入力します',
  ID.stringStakeHint2: '誓約額を入力してください',
  ID.stringStakeAmount: '誓約数量',
  ID.stringStakeTip1: '※@trx TRXのステーキングと@voteの議決権の取得を同時に行う予定です',
  ID.stringStakeTip2: '* @value @resource および @vote 議決権を取得する予定',
  ID.stringStakeTip3: '• 自分自身への誓約のみをサポートし、誓約を通じて得られたリソースはいつでも他の人に委任できます。',
  ID.stringStakeTip4: '• ロックを解除してから撤回するには、14 日間待つ必要があります。',
  ID.stringNoSearchResultsFound: '検索結果が見つかりませんでした',
  ID.stringTrxCanUse: '利用可能: @value TRX',
  ID.stringStakeEnergy: 'エネルギーを賭ける',
  ID.stringStakeBandwidth: '保証帯域幅',
  ID.stringUnstake1: 'アセットのロックを解除 1.0',
  ID.stringCanUnstakeTitle: 'ロック解除可能: @value TRX',
  ID.stringUnstakeTip:
      '• TRX を解除するには 14 日間の待機期間が必要で、質権から解除された資産は 14 日後に引き出すことができます。',
  ID.stringClearAll: 'すべて削除',
  ID.stringUnstakeTrx2: 'TRX 2.0のロックを解除する',
  ID.stringUnstakeHint: 'ロック解除数量を入力してください',
  ID.stringUnstakeEnergy: 'エネルギーのロックを解除する',
  ID.stringUnstakeBandwidth: '帯域幅のロックを解除する',
  ID.stringUnstakeEnergy2: 'エネルギー 2.0 のロックを解除する',
  ID.stringUnstakeBandwidth2: '帯域幅 2.0 のロックを解除する',
  ID.stringDayToWithdraw: '@day 曜日を抽出できます',
  ID.stringUnlockingTrx: 'TRXのロックを解除する',
  ID.stringWithdrawTrx: 'TRXを引き出す',
  ID.stringTronNoSupportedStake2:
      'Tron ネットワークのアップグレード Stake 2.0 の影響により、現在のハードウェア ウォレットは Stake Asset 2.0 をサポートしていません',
  ID.stringTronNoSupportedUnstake2:
      'Tron ネットワークのアップグレード Stake 2.0 の影響により、現在のハードウェア ウォレットはアセット 2.0 のロック解除をサポートしていません',
  ID.stringStake1DetialTitle: '誓約内容',
  ID.stringStakeToOwnTitle: '自分自身に誓約する',
  ID.stringStakeToOtherTitle: '他人への誓い',
  ID.stingCannotAccessThisWebsite: 'このウェブサイトにアクセスできません',
  ID.stingCannotAccessThisWebsiteVPN:
      'ウェブサイトへのアクセス中にエラーが発生しました。再読み込みするか、VPNを使用してください',
  ID.stringRedirecting: '第三者のウェブサイトにリダイレクトしています',
  ID.stringPledgedTitle: '抵当に入れられた',
  ID.stringRedemptionTitle: '償還中',
  ID.stringEosResourceTip1: '• メモリ リソースは EOS を使用して購入する必要があります',
  ID.stringEosResourceTip2: '• 帯域幅リソースは EOS を抵当にして取得する必要があります',
  ID.stringAvailableBalance: '利用可能残高',
  ID.stringOnlySupportsTaproot:
      'このDappはTaprootまたは隔離証人のネイティブアドレス形式のみをサポートしています。管理セクションでアドレスを追加してください',
  ID.stringRequestAccess:
      'あなたのウォレットアドレスへのアクセスをリクエストします。このサイトにウォレットアドレスを公開することを確認しますか？',
  ID.stringDeny: '拒否',
  ID.stringRequestAuthorization: '承認をリクエスト',
  ID.stringCurrentNetworkNotSupported: '現在のネットワークはサポートされていません',
  ID.stringContractCall: '契約呼び出し',
  ID.stringAssetSecurityWarning1:
      '資産の安全を確保するために、承認する前に関連するリスクと結果を理解していることを確認してください。',
  ID.stringAssetSecurityWarning2: '私は承認に関する一般的な詐欺手段を理解しています：',
  ID.stringAssetSecurityWarning3: 'OTC取引を理由にQRコードでの資金確認を要求し、承認を促す。',
  ID.stringAssetSecurityWarning4: 'エアドロップされた偽コインを理由に交換を要求し、承認を促す。',
  ID.stringAssetSecurityWarning5: '預金利息を理由に交換を要求し、承認を促す。',
  ID.stringAssetSecurityWarning6: '不明なDappのURLに入り、承認を促される。',
  ID.stringColdWalletAuthorizationWarning:
      '私は理解しています：一度私のコールドウォレットアドレスが他の人に承認されると、相手は私の同意なしに承認されたアドレスから資産を移動できます。',
  ID.stringWarning: '警告',
  ID.stringAuthorizeSmartContract: 'スマートコントラクトを承認',
  ID.stringAuthorizedAddress: '承認されたアドレス',
  ID.stringSignTransaction: '取引に署名',
  ID.stringTransactionType: '取引タイプ',
  ID.stringData: 'データ',
  ID.stringNftAmount: 'NFT数量',
  ID.stringNotCurrentlySupporte: 'このリンクは現在サポートされていません。',
  ID.stringHoldValue: '私が保持している @value',
  ID.stringValuation: '評価',
  ID.stringNftOverview: '概要',
  ID.stringNftActivity: '取引のダイナミクス',
  ID.stringContractAddress: '契約住所',
  ID.stringTokenStandards: 'トークン規格',
  ID.stringNftNetwork: 'ネットワーク',
  ID.stringNftTransfer: '移行',
  ID.stringNftFrom: '@value から',
  ID.stringNftTo: '宛先 @value',
  ID.stringNftSale: '取引をする',
  ID.stringNftMint: '鋳造',
  ID.stringNftBurn: '破壊する',
  ID.stringNftSaleBuy: '買う',
  ID.stringNftSaleSell: '売る',
  ID.stringNftReceiveAddress: '送信アドレス @value',
  ID.stringNftSendAddress: '受信アドレス @value',
  ID.stringNftContractAddress: 'コントラクトアドレス @value',
  ID.stringQrMaxNumberTip: 'QR コード ページが多すぎるため、現在のウォレットはそれらをサポートしていません。',
  ID.stringNftSuccess: '成功',
  ID.stringTargetAddress: '宛先アドレス',
  ID.stringSendNft: 'NFTを送信する',
  ID.stringNftSend: 'NFT発行',
  ID.stringNftReceive: 'NFT受付',
  ID.stringQRScanError: 'QRコード情報は正しくありません。もう一度スキャンしてください',
  ID.stringBindAddressNow: '今すぐバインド',
  ID.stringNotBoundYet: 'まだバインドされていません',
  ID.stringBindAddressInfo: 'アドレスを追加するには、メインチェーンをバインドする必',
  ID.stringTokenAddTitle: 'ERC20を追加',
  ID.stringTokenAddTip1: 'ハードウェアウォレットを使用してQRコードをスキャンし、トークンを追加します',
  ID.stringTokenPro1Tip1: 'Pro 1 では次の手順が追加されました。',
  ID.stringTokenPro1Tip2:
      '1. ハードウェア ウォレットを開いて、「すべての資産」ページに入ります; \n2. 右上隅のアイコンをクリックし、「通貨の追加」をクリックします。\n3. 「新しいアセットの追加」をクリックし、上記の QR コードをスキャンします。',
  ID.stringTokenPro2Tip1: 'Pro 2/Pro 2+ で追加された手順:',
  ID.stringTokenPro2Tip2:
      '1. ハードウェア ウォレットを開いて、「マイ アセット」ページに入ります。\n2. 右上隅のアイコンをクリックして「通貨管理」ページに入ります。\n3. 右上隅のプラス アイコンをクリックして、「トークンの追加」ページに入ります。\n4. 「コードをスキャンしてトークンを追加」ボタンをクリックし、上記の QR コードをスキャンします。',
  ID.stringTokenPro3Tip1: 'Pro 3 では次の手順が追加されました。',
  ID.stringTokenPro3Tip2:
      '1. ハードウェア ウォレットを開いて、「マイ ウォレット」ページに入ります。\n2. 右上隅のプラス アイコンをクリックして、「通貨管理」ページに入ります。\n3. 右上隅のプラス アイコンをクリックして、「トークンの追加」ページに入ります。\n4. 「コードをスキャンしてトークンを追加」ボタンをクリックし、上記の QR コードをスキャンします。',
  ID.stringSyncAmount: '同期バランス',
  ID.stringSyncAmountValue: '@value 同期バランス',
  ID.stringCurrentSyncAmount: 'この同期の量',
  ID.stringSyncAmountTip1: 'ハードウェアウォレットを使用してコードをスキャンしてください',
  ID.stringSyncAmountTip2:
      '各トランザクションを送信する前に、コールド ウォレットに適切なマイナー レートを設定してください。現在の最高のガス価格は @value Gwei です。',
  ID.stringSyncAmountTip3:
      '各トランザクションを送信する前に、コールド ウォレットに適切なマイナー レートを設定してください。現在の最良のマイナー レートは @value BTC/KB です。',
  ID.stringP1info:
      'ハードウェアウォレットを開き、暗号通貨を選択して「受信」ページに移動します;\n 2. 「残高」ページに移動し、「残高を更新」をクリックしてQRコードを表示します;',
  ID.stringP2info:
      '1. ハードウェアウォレットを開く；\n2. 「機能」をクリックし、「アドレスをエクスポート」を選択；\n3. バインドするアドレスを選択し、「エクスポート」ボタンをクリックしてQRコードを表示；\n4. 下の「QRコードでバインド」ボタンをクリックしてコールドエンドQRコードをスキャンします。。',
  ID.stringP2Plusinfo:
      '1. ハードウェアウォレットを開く。\n 2. 「機能」をクリックし、「アカウントを監視」を選択します。\n 3. バインドする通貨を選択し、「エクスポート」ボタンをクリックしてQRコードを表示します。\n 4. 下の「QRコードでバインド」ボタンをクリックし、コールドエンドのQRコードをスキャンします。',
  ID.stringP3info:
      '1. ハードウェアウォレットを開く；\n2. システムメニューを開き、「監視アカウント」を選択する；\n3. 監視する暗号通貨を選択し、「APPに送る」ボタンをクリック、QRコードを使用する；\n4. 下の「スキャンしてバインド」ボタンをクリックし、指示に従ってバインドする。',
  ID.stringNotBoundInfo: 'このハードウェアウォレットは未バインドです。まずバインドしてください。',
  ID.stirngSyncBroadInfo: '結果を同期して送信します',
  ID.stringConfirmTransaction: '取引を確認する',
  ID.stringNoSupportChain: '環境上の理由により、このチェーンはサポートされなくなりました。',
  ID.stringNoMonitorAddress: 'アドレスが見つかりません。まずそのアドレスを監視してください。',
  ID.stringSwitchAddress: 'アドレス形式を切り替える',
  ID.stringNotBoundInfoAddres: 'そのハードウェアウォレットのアドレスはバインドされていません。先にバインドしてください',
  ID.stirngSelectHardwareWallet: 'ハードウェアウォレットを選択',
  ID.stirngSwitchHardwareWallet:
      '現在選択されているハードウェアウォレットは、スキャンされたハードウェアウォレットと一致しません。切り替えてください。',
  ID.stirngBroadcastNoData: 'ブロードキャストデータが欠落しています。再試行してください。',
  ID.stringNoSupportedFeature: '生態的な理由により、この機能はサポートされなくなりました。',
  ID.stringNetworkDiagnostics: 'ネットワーク診断',
  ID.stringNetworkStatus: 'あなたのネットワーク設定を確認してください',
  ID.stringXrpNotSupportedTips:
      '旧型ハードウェアウォレットはXRPのサポートを終了しました。資産の安全を確保するために、早めに新しいウォレットにアップグレードしてください。ご不明な点がございましたら、カスタマーサービスにお問い合わせください。',
  ID.stringSynAddressTitle: '同期アドレスの手順',
  ID.stringSynAddressTip:
      'もし新しいアドレスを追加する必要がある場合は、ハードウェアウォレットで操作する必要があります。その後、アドレスをホットウォレットに同期します。操作手順は以下の通りです：\n',
  ID.stringSynChainError: 'メインチェーンが一致しません',
  ID.stringSynAddressError: 'アドレスの同期エラー',
  ID.stringBuildError: '取引の構築に失敗しました。再度構築してください。',
  ID.stringInputSolTip1: '有効な Compute Unit Limit を入力してください',
  ID.stringInputSolTip2: '有効な Compute Unit Price を入力してください',
  ID.stringInputSolTip3: '少なくとも @value を支払いして家賃を支払ってください',
  ID.stringSolanaTxError1: 'トランザクションの期限切れまたは Blockhash エラー',
  ID.stringBnbRecieveTip: '公式がチャージをサポートしておらず',
  ID.stringAddressSuccess: '追加成功',
  ID.stringSynSuccess: '同期成功',
  ID.stringBackgroundUpgrade: 'バックグラウンドアップグレードが開始されました。しばらくお待ちください！',
  ID.stringBackgroundUpgradeIng: 'アップグレード中...',
  ID.stringContactCustomerServiceToAdd: 'サポートに連絡',
  ID.stringContinueTx: 'まだ実行中',
  ID.stringXrpAddressError: '住所の形式が正しくありません',
  ID.stringXripFeeError: '有効なマイニング料金を入力してください',
  ID.stringSolPriceMinTip: 'Compute Unit Priceが@valueを下回ると取引に失敗する可能性がある',
  ID.stringBabylonError: 'Dappプロジェクトにはリスクがあり、取引はしばらくサポートされていない',
  ID.stringWebUserName: 'ユーザー名',
  ID.stringWebPassword: 'パスワード',
  ID.stringWebLogin: 'ログイン',
  ID.stringTouchChineseTip:
      '中国語入力法にはセキュアなキーボードがありません。助記語を入力する際にはセキュリティリスクが存在する可能性がありますので、中国語の助記語の使用には注意してください 。',
  ID.stringBestFeeTitle: '送金額を入力してください。推定マイナー料金を計算するためです 。',
  ID.stringMinerFee: '推定マイナー料金',
  ID.stringBestFee: '最適マイナー料金',
  ID.stringThemeMode: 'テーマモード',
  ID.stringLightMode: 'ライトモード',
  ID.stringDarkMode: 'ダークモード',
  ID.stringThemeModeText: 'お使いのデバイスのシステム設定に基づいて、アプリのテーマモードを調整します。',
  ID.stringSecurityVerification: 'セキュリティ認証',
  ID.stringOpenSecurityTitle: 'セキュリティ認証を開始する',
  ID.stringVerMethodTitle: '認証方法',
  ID.stringVerPasswordTitle: 'パスワード',
  ID.stringBiometricsTitle: 'バイオメトリック認証',
  ID.stringVerChangePswTitle: 'パスワードの変更',
  ID.stringUnlockAppTitle: 'アプリケーションのロック解除',
  ID.stringUnlockAppSub1Title: 'アプリケーションをロック解除するにはパスワードが必要である',
  ID.stringUnlockAppSub2Title: 'アプリケーションをロック解除するにはバイオメトリック認証が必要である',
  ID.stringAuthTsTitle: '取引の承認',
  ID.stringAuthTsSub2Title: '生体認証またはパスワードを使用して取引を承認したり、アカウントを追加したりします',
  ID.stringCreatePasswordTitle: 'パスワードを作成',
  ID.stringPasswordNotEqualTip: '2つのパスワードが一致しません。再入力してください',
  ID.stringOpenBiometricsTip: 'アプリのロック解除やセキュリティ認証のために、生体認証を有効にしますか？',
  ID.stringInputPassword: '現在のパスワードを入力してください',
  ID.stringForgetPassword: 'パスワードを忘れましたか？',
  ID.stringPasswordErrorNumber: 'パスワードが間違っています。あと @value 回試すことができます',
  ID.stringPasswordError: 'パスワードが間違っています',
  ID.stringIsOpenSecurity:
      'セキュリティを向上させるために、新しいPINコード機能を追加し、指紋認識または顔認識に対応しました。設定が完了すると、アプリのロック解除や取引の承認がより便利になります。\nなお、PINコードを設定しない場合、指紋認識または顔認識の検証機能はデフォルトで無効になります。',
  ID.stringPasswordErrorMax: '間違ったパスワードの数が上限に達しました。@value 後にもう一度お試しください。',
  ID.stringPaswordSecond: '@値 秒',
  ID.stringPaswordMinute: '@値ポイント',
  ID.stringPaswordHour: '@value 時間',
  ID.stringSoloTokenOpenTip: '家賃を支払うためのSOL残高が不足しています',
  ID.stringAppLockInfo:
      '生体認証またはパスワードを使用してアプリをロック解除し、取引を承認したり、アドレスを追加したりする際に確認します',
  ID.stringChangeSuccess: '変化の成功',
  ID.strinTalkAboutItlater: '後で話しましょう',
  ID.stringCosmosTip1: 'ガス価格は@valueより低くてはいけません',
  ID.stringCosmosTip2: 'ガス量は0より大きくなければなりません',
  ID.stringCreatePassCode: '6桁のパスワードを作成する',
  ID.stringCreateNewPasscode: '新しい6桁のパスワードを作成する',
  ID.stringResetPasscode: 'パスワードをリセット',
  ID.stringAddToken: '通貨を追加',
  ID.stringBabyTsTip: '重要なお知らせ：取引所（例：Binance）への出金時、MEMOタグの入力は必須です！',
  ID.stringSelectChain: 'チェーンを選択',
  ID.stringNoMorePrompts: '今後はもう表示しません',
  ID.stringTronNetworkFees: 'TRONネットワーク料金',
  ID.stringContractInteraction: '契約のやり取り',
  ID.stringNotFullySupportETH:
      '現在ご使用のハードウェアウォレットのバージョンが古く、ETH EIP155に完全に対応していないため、取引が失敗する可能性があります。正常にご利用いただくために、新しいデバイスへの交換を推奨します。',
  ID.strinInputMax:
      'トランザクションの金額が大きいため、署名に失敗する可能性があります。送金をスムーズに完了させるため、送金金額を2回以上のトランザクションに分割することをお勧めします。例えば、100を送金する場合、最初に50を送金し、残りの50を送金することで、署名に失敗するリスクを軽減できます。',
  ID.stringCanceTx: '取引をキャンセル',
  ID.stringContinueTx2: '取引を続行',
};
