import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wallet_core/chain/chain.dart';

/// @description :颜色新增需考虑暗黑模式,新增颜色必须这里添加
abstract class AppThemeColors {
  Color get black; //黑色 纯黑 禁止直接使用 与主题黑不是一个颜色
  Color get white; //白色 纯白 禁止直接使用，除非夜间模式也是白
  Color get bgColor; //背景白色
  Color get primary; //主题黑色（例如Button主色，文本）
  Color get colorFF6A16; //主题橙色 c_m
  Color get colorF9F9F9; //说明文案背景色 c_b_m_bg
  Color get colorF3F3F5; //输入框背景色(输入框) i_bg
  Color get color0A1D2023; //条目鼠标经过 e_h_bg
  Color get textPrimary; //文字主题一级颜色 c_t01
  Color get textSecondary; //文字主题二级颜色 c_t02
  Color get textTertiary; //文字主题三级级颜色 c_t03
  Color get color02B58A; //绿 c_c
  Color get colorF44D4D; //红 c_e
  Color get colorF4454545; //Toast颜色c_t_bg
  Color get colorD3D3D3; //通用元素描边线c_s_b01
  Color get colorECECEC; //分割线 c_s_b02
  Color get buttonFocusColor; //按钮选中色focusColor FF121212
  Color get colorFFF2DA; // 个人中心头部渐变色
  Color get colorFD8114; // 带背景颜色的 橙色颜色
  Color get colorD7D7D7;
  Color get color1AFF6A16; //主题10%色
  Color get color6C60FF; //主题10%色
  ///----------------------------链主题色------------------------------
  Color get themeColorBtc;
  Color get themeColorEth;
  Color get themeColorBsc;
  Color get themeColorTrx;
  Color get themeColorDoge;
  Color get themeColorEos;
  Color get themeColorPolygon;
  Color get themeColorBch;
  Color get themeColorArb;
  Color get themeColorOpt;
  Color get themeColorLtc;
  Color get themeColorXrp;
  Color get themeColorBnb;
  Color get themeColorXem;
  Color get themeColorDash;
  Color get themeColorFil;
  Color get themeColorEtc;
  Color get themeColorZec;
  Color get themeColorQtum;
  Color get themeColorBsv;
  Color get themeColorSol;
  Color get themeColorDefault;

  ///----------------------------链主题淡色------------------------------
  Color get disabledColorBtc;
  Color get disabledColorEth;
  Color get disabledColorBsc;
  Color get disabledColorTrx;
  Color get disabledColorDoge;
  Color get disabledColorEos;
  Color get disabledColorPolygon;
  Color get disabledColorBch;
  Color get disabledColorArb;
  Color get disabledColorOpt;
  Color get disabledColorLtc;
  Color get disabledColorXrp;
  Color get disabledColorBnb;
  Color get disabledColorXem;
  Color get disabledColorDash;
  Color get disabledColorFil;
  Color get disabledColorEtc;
  Color get disabledColorZec;
  Color get disabledColorQtum;
  Color get disabledColorBsv;
  Color get disabledColorSol;
  Color get disabledColorDefault;

  // ...可以定义更多颜色属性
}

// 扩展 ThemeData 新增颜色添加后还需要在这里设置引用
extension CustomThemeColors on ThemeData {
  Color get white => getThemeColors(this).white;
  Color get black => getThemeColors(this).black;
  Color get bgColor => getThemeColors(this).bgColor;
  Color get primary => getThemeColors(this).primary;
  Color get colorFF6A16 => getThemeColors(this).colorFF6A16;
  Color get colorF9F9F9 => getThemeColors(this).colorF9F9F9;
  Color get colorF3F3F5 => getThemeColors(this).colorF3F3F5;
  Color get color0A1D2023 => getThemeColors(this).color0A1D2023;
  Color get textPrimary => getThemeColors(this).textPrimary;
  Color get textSecondary => getThemeColors(this).textSecondary;
  Color get textTertiary => getThemeColors(this).textTertiary;
  Color get color02B58A => getThemeColors(this).color02B58A;
  Color get colorF44D4D => getThemeColors(this).colorF44D4D;
  Color get colorF5454545 => getThemeColors(this).colorF4454545;
  Color get colorD3D3D3 => getThemeColors(this).colorD3D3D3;
  Color get colorECECEC => getThemeColors(this).colorECECEC;
  Color get buttonFocusColor => getThemeColors(this).buttonFocusColor;
  Color get colorFFF2DA => getThemeColors(this).colorFFF2DA;
  Color get colorFD8114 => getThemeColors(this).colorFD8114;
  Color get colorD7D7D7 => getThemeColors(this).colorD7D7D7;
  Color get themeColorBtc => getThemeColors(this).themeColorBtc;
  Color get themeColorEth => getThemeColors(this).themeColorEth;
  Color get themeColorBsc => getThemeColors(this).themeColorBsc;
  Color get themeColorTrx => getThemeColors(this).themeColorTrx;
  Color get themeColorDoge => getThemeColors(this).themeColorDoge;
  Color get themeColorEos => getThemeColors(this).themeColorEos;
  Color get themeColorPolygon => getThemeColors(this).themeColorPolygon;
  Color get themeColorBch => getThemeColors(this).themeColorBch;
  Color get themeColorArb => getThemeColors(this).themeColorArb;
  Color get themeColorOpt => getThemeColors(this).themeColorOpt;
  Color get themeColorLtc => getThemeColors(this).themeColorLtc;
  Color get themeColorXrp => getThemeColors(this).themeColorXrp;
  Color get themeColorBnb => getThemeColors(this).themeColorBnb;
  Color get themeColorXem => getThemeColors(this).themeColorXem;
  Color get themeColorDash => getThemeColors(this).themeColorDash;
  Color get themeColorFil => getThemeColors(this).themeColorFil;
  Color get themeColorEtc => getThemeColors(this).themeColorEtc;
  Color get themeColorZec => getThemeColors(this).themeColorZec;
  Color get themeColorBsv => getThemeColors(this).themeColorBsv;
  Color get themeColorSol => getThemeColors(this).themeColorSol;
  Color get themeColorQtum => getThemeColors(this).themeColorQtum;
  Color get themeColorDefault => getThemeColors(this).themeColorDefault;
  Color get disabledColorBtc => getThemeColors(this).disabledColorBtc;
  Color get disabledColorEth => getThemeColors(this).disabledColorEth;
  Color get disabledColorBsc => getThemeColors(this).disabledColorBsc;
  Color get disabledColorTrx => getThemeColors(this).disabledColorTrx;
  Color get disabledColorDoge => getThemeColors(this).disabledColorDoge;
  Color get disabledColorEos => getThemeColors(this).disabledColorEos;
  Color get disabledColorPolygon => getThemeColors(this).disabledColorPolygon;
  Color get disabledColorBch => getThemeColors(this).disabledColorBch;
  Color get disabledColorArb => getThemeColors(this).disabledColorArb;
  Color get disabledColorOpt => getThemeColors(this).disabledColorOpt;
  Color get disabledColorLtc => getThemeColors(this).disabledColorLtc;
  Color get disabledColorXrp => getThemeColors(this).disabledColorXrp;
  Color get disabledColorBnb => getThemeColors(this).disabledColorBnb;
  Color get disabledColorXem => getThemeColors(this).disabledColorXem;
  Color get disabledColorDash => getThemeColors(this).disabledColorDash;
  Color get disabledColorFil => getThemeColors(this).disabledColorFil;
  Color get disabledColorEtc => getThemeColors(this).disabledColorEtc;
  Color get disabledColorZec => getThemeColors(this).disabledColorZec;
  Color get disabledColorQtum => getThemeColors(this).disabledColorQtum;
  Color get disabledColorBsv => getThemeColors(this).disabledColorBsv;
  Color get disabledColorSol => getThemeColors(this).disabledColorSol;
  Color get disabledColorDefault => getThemeColors(this).disabledColorDefault;
  Color get color1AFF6A16 => getThemeColors(this).color1AFF6A16;
  Color get color6C60FF => getThemeColors(this).color6C60FF;
}

AppThemeColors getThemeColors(ThemeData themeData) {
  return themeData.brightness == Brightness.dark
      ? DarkAppThemeColors()
      : LightAppThemeColors();
}

class LightAppThemeColors implements AppThemeColors {
  @override
  Color get black => const Color(0xFF000000);

  @override
  Color get color02B58A => const Color(0xFF02B58A);

  @override
  Color get color0A1D2023 => const Color(0x0A1D2023);

  @override
  Color get colorD3D3D3 => const Color(0xFFD3D3D3);

  @override
  Color get colorECECEC => const Color(0xFFECECEC);

  @override
  Color get colorF3F3F5 => const Color(0xFFF3F3F5);

  @override
  Color get colorF44D4D => const Color(0xFFF44D4D);

  @override
  Color get colorF4454545 => const Color(0xF4454545);

  @override
  Color get colorF9F9F9 => const Color(0xFFF9F9F9);

  @override
  Color get colorFF6A16 => const Color(0xFFFF6A16);

  @override
  Color get primary => const Color(0xFF121212);

  @override
  Color get bgColor => const Color(0xFFFFFFFF);

  @override
  Color get textPrimary => const Color(0xFF121212);

  @override
  Color get textSecondary => const Color(0xFF666666);

  @override
  Color get textTertiary => const Color(0xFF999999);

  @override
  Color get white => const Color(0xFFFFFFFF);

  @override
  Color get buttonFocusColor => const Color(0xF2121212);

  @override
  Color get colorFFF2DA => const Color(0xFFFFF2DA);

  @override
  Color get colorFD8114 => const Color(0xFFFD8114);

  @override
  Color get disabledColorArb => const Color(0x1A747DC1);

  @override
  Color get disabledColorBch => const Color(0x1A53B883);

  @override
  Color get disabledColorBnb => const Color(0x1AEAB300);

  @override
  Color get disabledColorBsc => const Color(0x1A555555);

  @override
  Color get disabledColorBtc => const Color(0x1AFD8114);

  @override
  Color get disabledColorDash => const Color(0x1A257FF2);

  @override
  Color get disabledColorDoge => const Color(0x1AC3A634);

  @override
  Color get disabledColorEos => const Color(0x1A555555);

  @override
  Color get disabledColorEtc => const Color(0x1A368236);

  @override
  Color get disabledColorEth => const Color(0x1A515151);

  @override
  Color get disabledColorFil => const Color(0x1A1992FB);

  @override
  Color get disabledColorLtc => const Color(0x1A747DC1);

  @override
  Color get disabledColorOpt => const Color(0x1AE50E19);

  @override
  Color get disabledColorPolygon => const Color(0x1A8E53F0);

  @override
  Color get disabledColorQtum => const Color(0x1A2EA6DF);

  @override
  Color get disabledColorTrx => const Color(0x1AE50E19);

  @override
  Color get disabledColorXem => const Color(0x1A56AADB);

  @override
  Color get disabledColorXrp => const Color(0x1A378AF7);

  @override
  Color get disabledColorZec => const Color(0x1AF4B728);

  @override
  Color get disabledColorBsv => const Color(0x1AE0AC04);

  @override
  Color get disabledColorSol => const Color(0x1A000000);

  @override
  Color get disabledColorDefault => const Color(0x1A555555);

  @override
  Color get themeColorArb => const Color(0xFF747DC1);

  @override
  Color get themeColorBch => const Color(0xFF53B883);

  @override
  Color get themeColorBnb => const Color(0xFFEAB300);

  @override
  Color get themeColorBsc => const Color(0xFF555555);

  @override
  Color get themeColorBtc => const Color(0xFFFD8114);

  @override
  Color get themeColorDash => const Color(0xFF257FF2);

  @override
  Color get themeColorDoge => const Color(0xFFC3A634);

  @override
  Color get themeColorEos => const Color(0xFF555555);

  @override
  Color get themeColorEtc => const Color(0xFF368236);

  @override
  Color get themeColorEth => const Color(0xFF515151);

  @override
  Color get themeColorFil => const Color(0xFF1992FB);

  @override
  Color get themeColorLtc => const Color(0xFF747DC1);

  @override
  Color get themeColorOpt => const Color(0xFFE50E19);

  @override
  Color get themeColorPolygon => const Color(0xFF8E53F0);

  @override
  Color get themeColorQtum => const Color(0xFF2EA6DF);

  @override
  Color get themeColorTrx => const Color(0xFFE50E19);

  @override
  Color get themeColorXem => const Color(0xFF56AADB);

  @override
  Color get themeColorXrp => const Color(0xFF378AF7);

  @override
  Color get themeColorZec => const Color(0xFFF4B728);

  @override
  Color get themeColorBsv => const Color(0xFFE0AC04);

  @override
  Color get themeColorSol => const Color(0xFF000000);

  @override
  Color get themeColorDefault => const Color(0xFF555555);

  @override
  Color get colorD7D7D7 => const Color(0xFFD7D7D7);

  @override
  Color get color1AFF6A16 => const Color(0x1AFF6A16);

  @override
  Color get color6C60FF => const Color(0xFF6c60ff);
}

///？TODO 暗黑主题后续会加，目前和亮色保持一致#FF6A16
class DarkAppThemeColors implements AppThemeColors {
  @override
  Color get black => const Color(0xFF000000);

  @override
  Color get color02B58A => const Color(0xFF02B58A);

  @override
  Color get color0A1D2023 => const Color(0x0A1D2023);

  @override
  Color get colorD3D3D3 => const Color(0xFFD3D3D3);

  @override
  Color get colorECECEC => const Color(0xFFECECEC);

  @override
  Color get colorF3F3F5 => const Color(0xFFF3F3F5);

  @override
  Color get colorF44D4D => const Color(0xFFF44D4D);

  @override
  Color get colorF4454545 => const Color(0xF4454545);

  @override
  Color get colorF9F9F9 => const Color(0xFFF9F9F9);

  @override
  Color get colorFF6A16 => const Color(0xFFFF6A16);

  @override
  Color get primary => const Color(0xFF121212);

  @override
  Color get bgColor => const Color(0xFFFFFFFF);

  @override
  Color get textPrimary => const Color(0xFF121212);

  @override
  Color get textSecondary => const Color(0xFF666666);

  @override
  Color get textTertiary => const Color(0xFF999999);

  @override
  Color get white => const Color(0xFFFFFFFF);

  @override
  Color get buttonFocusColor => const Color(0xF2121212);

  @override
  Color get colorFFF2DA => const Color(0xFFFFF2DA);

  @override
  Color get colorFD8114 => const Color(0xFFFD8114);

  @override
  Color get disabledColorArb => const Color(0x1A747DC1);

  @override
  Color get disabledColorBch => const Color(0x1A53B883);

  @override
  Color get disabledColorBnb => const Color(0x1AEAB300);

  @override
  Color get disabledColorBsc => const Color(0x1A555555);

  @override
  Color get disabledColorBtc => const Color(0x1AFD8114);

  @override
  Color get disabledColorDash => const Color(0x1A257FF2);

  @override
  Color get disabledColorDoge => const Color(0x1AC3A634);

  @override
  Color get disabledColorEos => const Color(0x1A555555);

  @override
  Color get disabledColorEtc => const Color(0x1A368236);

  @override
  Color get disabledColorEth => const Color(0x1A515151);

  @override
  Color get disabledColorFil => const Color(0x1A1992FB);

  @override
  Color get disabledColorLtc => const Color(0x1A747DC1);

  @override
  Color get disabledColorOpt => const Color(0x1AE50E19);

  @override
  Color get disabledColorPolygon => const Color(0x1A8E53F0);

  @override
  Color get disabledColorQtum => const Color(0x1A2EA6DF);

  @override
  Color get disabledColorTrx => const Color(0x1AE50E19);

  @override
  Color get disabledColorXem => const Color(0x1A56AADB);

  @override
  Color get disabledColorXrp => const Color(0x1A378AF7);

  @override
  Color get disabledColorZec => const Color(0x1AF4B728);

  @override
  Color get disabledColorBsv => const Color(0x1AE0AC04);

  @override
  Color get disabledColorSol => const Color(0x1A000000);

  @override
  Color get disabledColorDefault => const Color(0x1A555555);

  @override
  Color get themeColorArb => const Color(0xFF747DC1);

  @override
  Color get themeColorBch => const Color(0xFF53B883);

  @override
  Color get themeColorBnb => const Color(0xFFEAB300);

  @override
  Color get themeColorBsc => const Color(0xFF555555);

  @override
  Color get themeColorBtc => const Color(0xFFFD8114);

  @override
  Color get themeColorDash => const Color(0xFF257FF2);

  @override
  Color get themeColorDoge => const Color(0xFFC3A634);

  @override
  Color get themeColorEos => const Color(0xFF555555);

  @override
  Color get themeColorEtc => const Color(0xFF368236);

  @override
  Color get themeColorEth => const Color(0xFF515151);

  @override
  Color get themeColorFil => const Color(0xFF1992FB);

  @override
  Color get themeColorLtc => const Color(0xFF747DC1);

  @override
  Color get themeColorOpt => const Color(0xFFE50E19);

  @override
  Color get themeColorPolygon => const Color(0xFF8E53F0);

  @override
  Color get themeColorQtum => const Color(0xFF2EA6DF);

  @override
  Color get themeColorTrx => const Color(0xFFE50E19);

  @override
  Color get themeColorXem => const Color(0xFF56AADB);

  @override
  Color get themeColorXrp => const Color(0xFF378AF7);

  @override
  Color get themeColorZec => const Color(0xFFF4B728);

  @override
  Color get themeColorBsv => const Color(0xFFE0AC04);

  @override
  Color get themeColorSol => const Color(0xFF000000);

  @override
  Color get themeColorDefault => const Color(0xFF555555);

  @override
  Color get colorD7D7D7 => const Color(0xFFD7D7D7);

  @override
  Color get color1AFF6A16 => const Color(0x1AFF6A16);

  @override
  Color get color6C60FF => const Color(0xFF6c60ff);
}

Color getChainThemeColor(String chain, {bool alphaColor = false}) {
  Color color;
  switch (chain) {
    case Chain.btc:
      color = alphaColor ? Get.theme.disabledColorBtc : Get.theme.themeColorBtc;
      break;
    case Chain.eth:
      color = alphaColor ? Get.theme.disabledColorEth : Get.theme.themeColorEth;
      break;
    case Chain.bnb:
      color = alphaColor ? Get.theme.disabledColorBnb : Get.theme.themeColorBnb;
      break;
    case Chain.arb:
      color = alphaColor ? Get.theme.disabledColorArb : Get.theme.themeColorArb;
      break;
    case Chain.opt:
      color = alphaColor ? Get.theme.disabledColorOpt : Get.theme.themeColorOpt;
      break;
    case Chain.bch:
      color = alphaColor ? Get.theme.disabledColorBch : Get.theme.themeColorBch;
      break;
    case Chain.ltc:
      color = alphaColor ? Get.theme.disabledColorLtc : Get.theme.themeColorLtc;
      break;
    case Chain.dash:
      color =
          alphaColor ? Get.theme.disabledColorDash : Get.theme.themeColorDash;
      break;
    case Chain.doge:
      color =
          alphaColor ? Get.theme.disabledColorDoge : Get.theme.themeColorDoge;
      break;
    case Chain.bsc:
      color = alphaColor ? Get.theme.disabledColorBsc : Get.theme.themeColorBsc;
      break;
    case Chain.polygon:
      color = alphaColor
          ? Get.theme.disabledColorPolygon
          : Get.theme.themeColorPolygon;
      break;
    case Chain.qtum:
      color =
          alphaColor ? Get.theme.disabledColorQtum : Get.theme.themeColorQtum;
      break;
    case Chain.zec:
      color = alphaColor ? Get.theme.disabledColorZec : Get.theme.themeColorZec;
      break;
    case Chain.xem:
      color = alphaColor ? Get.theme.disabledColorXem : Get.theme.themeColorXem;
      break;
    case Chain.fil:
      color = alphaColor ? Get.theme.disabledColorFil : Get.theme.themeColorFil;
      break;
    case Chain.etc:
      color = alphaColor ? Get.theme.disabledColorEtc : Get.theme.themeColorEtc;
      break;
    case Chain.eos:
      color = alphaColor ? Get.theme.disabledColorEos : Get.theme.themeColorEos;
      break;
    case Chain.xrp:
      color = alphaColor ? Get.theme.disabledColorXrp : Get.theme.themeColorXrp;
      break;
    case Chain.sol:
      color = alphaColor ? Get.theme.disabledColorSol : Get.theme.themeColorSol;
      break;
    default:
      color = alphaColor
          ? Get.theme.disabledColorDefault
          : Get.theme.themeColorDefault;
      break;
  }

  return color;
}
