class ID {
  static const String appName = 'appName';
  static const String settingLanguageDefault = 'settingLanguageDefault';
  static const String stringConfirm = 'stringConfirm';
  static const String stringCancel = 'stringCancel';
  static const String stringDiscover = 'stringDiscover';
  static const String stringAvatar = 'stringAvatar';
  static const String stringUid = 'stringUid';
  static const String stringPhone = 'stringPhone';
  static const String stringEmail = 'stringEmail';
  static const String stringLoginpassword = 'stringLoginpassword';
  static const String stringAccount = 'stringAccount';
  static const String stringNickname = 'stringNickname';
  static const String stringHelp = 'stringHelp';
  static const String stringcommonproblem = 'stringcommonproblem';
  static const String profileLogin = 'profileLogin';
  static const String profileTool = 'profileTool';
  static const String profileHelp = 'profileHelp';
  static const String profileAddressBook = 'profileAddressBook';
  static const String profileMall = 'profileMall';
  static const String profileLanguage = 'profileLanguage';
  static const String profileQR = 'profileQR';
  static const String profileAbout = 'profileAbout';
  static const String profileCurrency = 'profileCurrency';
  static const String qrStandard = 'qrStandard';
  static const String qrSmall = 'qrSmall';
  static const String qrLarge = 'qrLarge';
  static const String qrSuperLarge = 'qrSuperLarge';
  static const String qrBest = 'qrBest';
  static const String clientNetError = 'clientNetError';
  static const String timeOutError = 'timeOutError';
  static const String serverNetError = 'serverNetError';
  static const String netConnectError = 'netConnectError';
  static const String cancelConnectError = 'cancelConnectError';
  static const String qrTip = 'qrTip';
  static const String emptyData = 'emptyData';
  static const String dataError = 'dataError';
  static const String reload = 'reload';
  static const String exitApplication = 'exitApplication';
  static const String releaseText = 'releaseText';
  static const String refreshingText = 'refreshingText';
  static const String completeText = 'completeText';
  static const String idleText = 'idleText';
  static const String loadingText = 'loadingText';
  static const String pullUpToLoad = 'pullUpToLoad';
  static const String canLoadingText = 'canLoadingText';
  static const String noDataText = 'noDataText';
  static const String aboutKS = 'aboutKS';
  static const String userAgreement = 'userAgreement';
  static const String wallet = 'wallet';
  static const String send = 'send';
  static const String receive = 'receive';
  static const String scan = 'scan';
  static const String activity = 'activity';
  static const String currencySearch = 'currencySearch';
  static const String allWallet = 'allWallet';
  static const String totalAsset = 'totalAsset';
  static const String loginString = 'loginString';
  static const String loginPhone = 'loginPhone';
  static const String loginPhoneHintText = 'loginPhoneHintText';
  static const String loginEmail = 'loginEmail';
  static const String loginEmailHintText = 'loginEmailHintText';
  static const String loginPassword = 'loginPassword';
  static const String loginPasswordHintText = 'loginPasswordHintText';
  static const String loginForgetPassword = 'loginForgetPassword';
  static const String loginRegisterNew = 'loginRegisterNew';
  static const String loginOkPhone = 'loginOkPhone';
  static const String loginOkEmail = 'loginOkEmail';
  static const String loginPswTip = 'loginPswTip';
  static const String loginSuccess = 'loginSuccess';
  static const String emptyWallethint = 'emptyWallethint';
  static const String stringGuideTitle = 'stringGuideTitle';
  static const String stringGuideSubTitle = 'stringGuideSubTitle';
  static const String stringBindNow = 'stringBindNow';
  static const String stringBindBuyU1 = 'stringBindBuyU1';
  static const String stringGuideimmediatelyExperience =
      'stringGuideimmediatelyExperience';
  static const String stringAllWallet = 'stringAllWallet';
  static const String stringDefaultWallet = 'stringDefaultWallet';
  static const String stringBalance = 'stringBalance';
  static const String stringRegister = 'stringRegister';
  static const String stringCode = 'stringCode';
  static const String stringInputCode = 'stringInputCode';
  static const String stringGetCode = 'stringGetCode';
  static const String stringInvitationCode = 'stringInvitationCode';
  static const String stringInputInvitationCode = 'stringInputInvitationCode';
  static const String stringCollection = 'stringCollection';
  static const String stringReceiveAddress = 'stringReceiveAddress';
  static const String stringCopyAddress = 'stringCopyAddress';
  static const String stringSaveImage = 'stringSaveImage';
  static const String stringAgreement1 = 'stringAgreement1';
  static const String stringAgreement2 = 'stringAgreement2';
  static const String stringHasAccount = 'stringHasAccount';
  static const String stringGotoLogin = 'stringGotoLogin';
  static const String stringCopySuccess = 'stringCopySuccess';
  static const String stringAll = 'stringAll';
  static const String stringTransferSend = 'stringTransferSend';
  static const String stringSendAddress = 'stringSendAddress';
  static const String stringReciveAddress = 'stringReciveAddress';
  static const String stringComplte = 'stringComplte';
  static const String stringTxFail = 'stringTxFail';
  static const String stringReadAgreement = 'stringReadAgreement';
  static const String stringOKCode = 'stringOKCode';
  static const String stringPswTip1 = 'stringPswTip1';
  static const String stringPswTip2 = 'stringPswTip2';
  static const String stringPswTip3 = 'stringPswTip3';
  static const String stringPswTip4 = 'stringPswTip4';
  static const String stringPswTip5 = 'stringPswTip5';
  static const String stringConfirmPswHit = 'stringConfirmPswHit';
  static const String stringSetPassword = 'stringSetPassword';
  static const String stringRegisterSuccess = 'stringRegisterSuccess';
  static const String forgetTitle = 'forgetTitle';
  static const String resetPasswordSuccess = 'resetPasswordSuccess';
  static const String sendAddress = 'sendAddress';
  static const String sendCoin = 'sendCoin';
  static const String receiveAddress = 'receiveAddress';
  static const String sendNumber = 'sendNumber';
  static const String sendAvailable = 'sendAvailable';
  static const String sendButton = 'sendButton';
  static const String sendMaxText = 'sendMaxText';
  static const String stringChange = 'stringChange';
  static const String stringChangePassword = 'stringChangePassword';
  static const String oldPassword = 'oldPassword';
  static const String oldHintPassword = 'oldHintPassword';
  static const String newPassword = 'newPassword';
  static const String newHintPassword = 'newHintPassword';
  static const String okNewPassword = 'okNewPassword';
  static const String okNewHintPassword = 'okNewHintPassword';
  static const String passwordChangeSuccess = 'passwordChangeSuccess';
  static const String inputOkToAddress = 'inputOkToAddress';
  static const String balanceNot = 'balanceNot';
  static const String broadcastSending = 'broadcastSending';
  static const String broadcastSuccess = 'broadcastSuccess';
  static const String broadcastError = 'broadcastError';
  static const String broadcastReset = 'broadcastReset';
  static const String stringBack = 'stringBack';
  static const String stringSendOneself = 'stringSendOneself';
  static const String stringAddress = 'stringAddress';
  static const String storageAccessNotEnabled = 'storageAccessNotEnabled';
  static const String cameraAccessNotEnabled = 'cameraAccessNotEnabled';
  static const String contactPerson = 'contactPerson';
  static const String insertaddress = 'insertaddress';
  static const String addressTag = 'addressTag';
  static const String addAddress = 'addAddress';
  static const String usernameEditor = 'usernameEditor';
  static const String accountback = 'accountback';
  static const String bindEmail = 'bindEmail';
  static const String stringonlineservice = 'stringonlineservice';
  static const String stringuserfeedback = 'stringuserfeedback';
  static const String stringhelpqq = 'stringhelpqq';
  static const String stringhelpwechat = 'stringhelpwechat';
  static const String stringhelptel = 'stringhelptel';
  static const String feedbackname = 'feedbackname';
  static const String feedbackcontext = 'feedbackcontext';
  static const String feedbackmode = 'feedbackmode';
  static const String submitfeedback = 'submitfeedback';
  static const String feenotegasprice = 'feenotegasprice';
  static const String makeqr = 'makeqr';
  static const String textcontent = 'textcontent';
  static const String contentText = 'contentText';
  static const String timesync = 'timesync';
  static const String afterRefresh = 'afterRefresh';
  static const String bindHardwareWallet = 'bindHardwareWallet';
  static const String verifysn = 'verifysn';
  static const String p4verifysucess = 'p4verifysucess';
  static const String p4verifyfail = 'p4verifyfail';
  static const String p4verifyRemind2 = 'p4verifyRemind2';
  static const String p4verifyRemind = 'p4verifyRemind';
  static const String verifysnSucceedHint = 'verifysnSucceedHint';
  static const String verifySnTime = 'verifySnTime';
  static const String errwalletFake = 'errwalletFake';
  static const String stringNotices = 'stringNotices';
  static const String textTimesyncInfo = 'textTimesyncInfo';
  static const String stringOfficialEmail = 'stringOfficialEmail';
  static const String stringTextDial = 'stringTextDial';
  static const String textFeedbackDialogMessage = 'textFeedbackDialogMessage';
  static const String textRemind = 'textRemind';
  static const String textUnbounded = 'textUnbounded';
  static const String textBindPhone = 'textBindPhone';
  static const String texSubmit = 'texSubmit';
  static const String textbindSuccess = 'textbindSuccess';
  static const String manageCoin = 'manageCoin';
  static const String supportToken = 'supportToken';
  static const String nosupportToken = 'nosupportToken';
  static const String tokenSearch = 'tokenSearch';
  static const String notifiAllRead = 'notifiAllRead';
  static const String notifiTxTitle = 'notifiTxTitle';
  static const String notifiSysTitle = 'notifiSysTitle';
  static const String textChooseChain = 'textChooseChain';
  static const String textCorrectAddress = 'textCorrectAddress';
  static const String textChain = 'textChain';
  static const String submitToken = 'submitToken';
  static const String tokenSymbol = 'tokenSymbol';
  static const String tokenContracts = 'tokenContracts';
  static const String enterContractsInfo = 'enterContractsInfo';
  static const String addContractRemind1 = 'addContractRemind1';
  static const String stringConfirmPsw = 'stringConfirmPsw';
  static const String addContractRemind2 = 'addContractRemind2';
  static const String importTokenSymbol = 'importTokenSymbol';
  static const String addressBookEditContacts = 'addressBookEditContacts';
  static const String addressBookDelContacts = 'addressBookDelContacts';
  static const String addressBookDelContactsContent =
      'addressBookDelContactsContent';
  static const String addressBookDelContactsConfirm =
      'addressBookDelContactsConfirm';
  static const String addressAlreadyExists = 'addressAlreadyExists';
  static const String unsearchToken = 'unsearchToken';
  static const String tokenLabel = 'tokenLabel';
  static const String authFingerprintTip = 'authFingerprintTip';
  static const String authContnetTextFace = 'authContnetTextFace';
  static const String authContnetTextFingerprint = 'authContnetTextFingerprint';
  static const String authContnetTextSaveFingerprint =
      'authContnetTextSaveFingerprint';
  static const String gotoSettingTitle = 'gotoSettingTitle';
  static const String authFaceUnlock = 'authFaceUnlock';
  static const String authFingerprintUnlock = 'authFingerprintUnlock';
  static const String authFingerprintTap = 'authFingerprintTap';
  static const String authFaceTitle = 'authFaceTitle';
  static const String authFingerprintTitle = 'authFingerprintTitle';
  static const String stringTips = 'stringTips';
  static const String nfcOpen = 'nfcOpen';
  static const String nfcClose = 'nfcClose';
  static const String addHardwareTitle = 'addHardwareTitle';
  static const String bindWalletTitle = 'bindWalletTitle';
  static const String scanBindTitle = 'scanBindTitle';
  static const String pageCount = 'pageCount';
  static const String noinstallQQ = 'noinstallQQ';
  static const String noinstallWechat = 'noinstallWechat';
  static const String scanResult = 'scanResult';
  static const String copy = 'copy';
  static const String zxingError = 'zxingError';
  static const String pleaseScanPage = 'pleaseScanPage';
  static const String scanNotMatchWallet = 'scanNotMatchWallet';
  static const String scanError = 'scanError';
  static const String bindSucessInfo = 'bindSucessInfo';
  static const String bindSucess = 'bindSucess';
  static const String openWallet = 'openWallet';
  static const String chainManager = 'chainManager';
  static const String chainSegWitP2sh = 'chainSegWitP2sh';
  static const String chainSegWit = 'chainSegWit';
  static const String chainLegacy = 'chainLegacy';
  static const String chainSegWitP2shItem = 'chainSegWitP2shItem';
  static const String chainLegacyItem = 'chainLegacyItem';
  static const String chainSegWitItem = 'chainSegWitItem';
  static const String chainSelectWallet = 'chainSelectWallet';
  static const String chainWalletManager = 'chainWalletManager';
  static const String chainAddAddress = 'chainAddAddress';
  static const String chainAddressDetail = 'chainAddressDetail';
  static const String chainEditRemark = 'chainEditRemark';
  static const String chainAddressDetailTip1 = 'chainAddressDetailTip1';
  static const String chainAddressDetailTip2 = 'chainAddressDetailTip2';
  static const String chainAddType = 'chainAddType';
  static const String chainAddRemark = 'chainAddRemark';
  static const String chainComplete = 'chainComplete';
  static const String chainAllChains = 'chainAllChains';
  static const String chainTotalAssets = 'chainTotalAssets';
  static const String allWallets = 'allWallets';
  static const String walletInfo = 'walletInfo';
  static const String walletName = 'walletName';
  static const String bindingTime = 'bindingTime';
  static const String deviceModel = 'deviceModel';
  static const String deviceId = 'deviceId';
  static const String seVersion = 'seVersion';
  static const String walletAppVersion = 'walletAppVersion';
  static const String unbind = 'unbind';
  static const String using = 'using';
  static const String bindFailed = 'bindFailed';
  static const String rebind = 'rebind';
  static const String accountDelete = 'accountDelete';
  static const String accountDeleteTip = 'accountDeleteTip';
  static const String safeSetting = 'safeSetting';
  static const String deleteError = 'deleteError';
  static const String deleteSuccess = 'deleteSuccess';
  static const String loading = 'loading';
  static const String binding = 'binding';
  static const String sameMnemonicWallet = 'sameMnemonicWallet';
  static const String sureDeleteWallet = 'sureDeleteWallet';
  static const String textAddressCount = 'textAddressCount';
  static const String textAccountCount = 'textAccountCount';
  static const String baseSettings = 'baseSettings';
  static const String myTitle = 'myTitle';
  static const String feeSettingTitle = 'feeSettingTitle';
  static const String feeTitle = 'feeTitle';
  static const String gearSelect = 'gearSelect';
  static const String customizeTitle = 'customizeTitle';
  static const String fastTitle = 'fastTitle';
  static const String normalTitle = 'normalTitle';
  static const String slowTitle = 'slowTitle';
  static const String estimateTxTime = 'estimateTxTime';
  static const String minerFeeTitle = 'minerFeeTitle';
  static const String minuteTitle = 'minuteTitle';
  static const String hourTitle = 'hourTitle';
  static const String secondTitle = 'secondTitle';
  static const String p4seedRemind = 'p4seedRemind';
  static const String p4seedIspharase = 'p4seedIspharase';
  static const String p4seedIspharaseYes = 'p4seedIspharaseYes';
  static const String p4seedIspharaseNo = 'p4seedIspharaseNo';
  static const String stringBind = 'stringBind';
  static const String textPro1Binding = 'textPro1Binding';
  static const String scanConnect = 'scanConnect';
  static const String customFeeConfirm = 'customFeeConfirm';
  static const String customFeeSuccess = 'customFeeSuccess';
  static const String feeSlowTips = 'feeSlowTips';
  static const String feeFastTips = 'feeFastTips';
  static const String feeOkTips = 'feeOkTips';
  static const String inputToAddressTip = 'inputToAddressTip';
  static const String inputOkToAddressTip = 'inputOkToAddressTip';
  static const String inputAmountTip = 'inputAmountTip';
  static const String amounNumberError = 'amounNumberError';
  static const String transferToSelfTip = 'transferToSelfTip';
  static const String insufficientBalanceFee = 'insufficientBalanceFee';
  static const String insufficientBalance = 'insufficientBalance';
  static const String insufficientMainFee = 'insufficientMainFee';
  static const String coinTransferTitle = 'coinTransferTitle';
  static const String coinTsTypeTitle = 'coinTsTypeTitle';
  static const String qrTitleTransfer = 'qrTitleTransfer';
  static const String qrAutoPage = 'qrAutoPage';
  static const String qrManualPage = 'qrManualPage';
  static const String scanQrSendTitle = 'scanQrSendTitle';
  static const String qrSettingQrsizeTitle1 = 'qrSettingQrsizeTitle1';
  static const String qrSettingQrsizeTitle2 = 'qrSettingQrsizeTitle2';
  static const String sendAmountSmall = 'sendAmountSmall';
  static const String completionProgress = 'completionProgress';
  static const String ethGasPriceSlowTip = 'ethGasPriceSlowTip';
  static const String ethGasPriceLargeTip = 'ethGasPriceLargeTip';
  static const String ethGasLimitTip = 'ethGasLimitTip';
  static const String filGasEmtyTips = 'filGasEmtyTips';
  static const String filGasSlowTips = 'filGasSlowTips';
  static const String ethGasPriceMinTip = 'ethGasPriceMinTip';
  static const String remarkMemo = 'remarkMemo';
  static const String remark = 'remark';
  static const String remarkNoText = 'remarkNoText';
  static const String remarkHitText = 'remarkHitText';
  static const String remarkTag = 'remarkTag';
  static const String xrpRemarkTips = 'xrpRemarkTips';
  static const String xrpTsTips = 'xrpTsTips';
  static const String xrpNoActivityTips = 'xrpNoActivityTips';
  static const String maxFeeValidTip = 'maxFeeValidTip';
  static const String maxFeeNoLessMaxPriorityFeeTip =
      'maxFeeNoLessMaxPriorityFeeTip';
  static const String maxPriorityFeeTip = 'maxPriorityFeeTip';
  static const String maxPriorityFeeHeightTip = 'maxPriorityFeeHeightTip';
  static const String maxFeeHeightTip = 'maxFeeHeightTip';
  static const String tronActivatedTip = 'tronActivatedTip';
  static const String tronNeedSource = 'tronNeedSource';
  static const String valueBandwidth = 'valueBandwidth';
  static const String valueEnergy = 'valueEnergy';
  static const String activedAccount = 'activedAccount';
  static const String deductionValueBandwidth = 'deductionValueBandwidth';
  static const String deductionValueEnergy = 'deductionValueEnergy';
  static const String tronPopTip = 'tronPopTip';
  static const String tronRemarkTip = 'tronRemarkTip';
  static const String tronResourceDetail = 'tronResourceDetail';
  static const String tronFeeDetail = 'tronFeeDetail';
  static const String tronRiskTip = 'tronRiskTip';
  static const String basicEnergy = 'basicEnergy';
  static const String extraEnergy = 'extraEnergy';
  static const String tronLearnMore = 'tronLearnMore';
  static const String fromAccount = 'fromAccount';
  static const String toAccount = 'toAccount';
  static const String addressLabelEmpty = 'addressLabelEmpty';
  static const String feeRang = 'feeRang';
  static const String bindWallet = 'bindWallet';
  static const String scanQrBind = 'scanQrBind';
  static const String verifySuccessTitle = 'verifySuccessTitle';
  static const String verifyFailTitle = 'verifyFailTitle';
  static const String verifySuccessSubTitle = 'verifySuccessSubTitle';
  static const String verifyFailSubTitle = 'verifyFailSubTitle';
  static const String goBackHome = 'goBackHome';
  static const String stringResource = 'stringResource';
  static const String stringDelegateResource = 'stringDelegateResource';
  static const String stringVote = 'stringVote';
  static const String stringStake2 = 'stringStake2';
  static const String stringUnstake2 = 'stringUnstake2';
  static const String stringStake = 'stringStake';
  static const String stringUnstake = 'stringUnstake';
  static const String stringReclaim = 'stringReclaim';
  static const String stringReclaimAddress = 'stringReclaimAddress';
  static const String stringConfirming = 'stringConfirming';
  static const String stringPacking = 'stringPacking';
  static const String stringSendFail = 'stringSendFail';
  static const String stringTxTime = 'stringTxTime';
  static const String stringTxId = 'stringTxId';
  static const String stringTxBlock = 'stringTxBlock';
  static const String stringTxSource = 'stringTxSource';
  static const String stringTxEnergy = 'stringTxEnergy';
  static const String stringTxBandwidth = 'stringTxBandwidth';
  static const String stringTxWithdraw = 'stringTxWithdraw';
  static const String stringBurnedEnergy = 'stringBurnedEnergy';
  static const String stringBurnedBandwidth = 'stringBurnedBandwidth';
  static const String stringBlockchain = 'stringBlockchain';
  static const String stringSearchEmpty = 'stringSearchEmpty';
  static const String stringSubmitToken = 'stringSubmitToken';
  static const String stringInputOkContract = 'stringInputOkContract';
  static const String stringAddTokenSuccess = 'stringAddTokenSuccess';
  static const String stringBuyWallet = 'stringBuyWallet';
  static const String stringGuideTitle1 = 'stringGuideTitle1';
  static const String stringGuideSubTitle1 = 'stringGuideSubTitle1';
  static const String stringGuideTitle2 = 'stringGuideTitle2';
  static const String stringGuideSubTitle2 = 'stringGuideSubTitle2';
  static const String stringEmptyTitle = 'stringEmptyTitle';
  static const String stringEmptyTitle2 = 'stringEmptyTitle2';
  static const String stringEmptySubTitle2 = 'stringEmptySubTitle2';
  static const String stringAllNetTitle = 'stringAllNetTitle';
  static const String stringWeb3Title = 'stringWeb3Title';
  static const String stringWeb3SubTitle = 'stringWeb3SubTitle';
  static const String stringWeb3Title1 = 'stringWeb3Title1';
  static const String stringWeb3SubTitle1 = 'stringWeb3SubTitle1';
  static const String stringWeb3Title2 = 'stringWeb3Title2';
  static const String stringWeb3SubTitle2 = 'stringWeb3SubTitle2';
  static const String stringWeb3Title3 = 'stringWeb3Title3';
  static const String stringWeb3SubTitle3 = 'stringWeb3SubTitle3';
  static const String stringSupportChains = 'stringSupportChains';
  static const String stringCreateNewWallet = 'stringCreateNewWallet';
  static const String stringScanQrBind = 'stringScanQrBind';
  static const String stringScanQrBindDes = 'stringScanQrBindDes';
  static const String stringNFCBind = 'stringNFCBind';
  static const String stringNFCBindDes = 'stringNFCBindDes';
  static const String stringTouchPsw = 'stringTouchPsw';
  static const String stringTouchPhone = 'stringTouchPhone';
  static const String stringTouchNONfc = 'stringTouchNONfc';
  static const String stringTouchReadError = 'stringTouchReadError';
  static const String stringTouchReading = 'stringTouchReading';
  static const String stringTouchNfcSuccess = 'stringTouchNfcSuccess';
  static const String stringNfcPswMax = 'stringNfcPswMax';
  static const String stringNfcInputOnePsw = 'stringNfcInputOnePsw';
  static const String stringNfcPswErrorNum = 'stringNfcPswErrorNum';
  static const String stringNfcPswError = 'stringNfcPswError';
  static const String stringNfcScanError = 'stringNfcScanError';
  static const String stringCardHaveWallet = 'stringCardHaveWallet';
  static const String stringCardWalletError = 'stringCardWalletError';
  static const String stringCardNumberError = 'stringCardNumberError';
  static const String stringNfcUnknownError = 'stringNfcUnknownError';
  static const String stringTouchNoOfficial = 'stringTouchNoOfficial';
  static const String stringTouchSpotError = 'stringTouchSpotError';
  static const String stringToucReadCard = 'stringToucReadCard';
  static const String stringTouchReadTips = 'stringTouchReadTips';
  static const String stringNewWallet = 'stringNewWallet';
  static const String stringCreateTitle = 'stringCreateTitle';
  static const String stringCreateDesTitle = 'stringCreateDesTitle';
  static const String stringImportTitle = 'stringImportTitle';
  static const String stringImportDesTitle = 'stringImportDesTitle';
  static const String stringCreateWalletTitle = 'stringCreateWalletTitle';
  static const String stringImportWalletTitle = 'stringImportWalletTitle';
  static const String stringAgreementTip = 'stringAgreementTip';
  static const String stringTouchNumber = 'stringTouchNumber';
  static const String stringCreating = 'stringCreating';
  static const String stringImporting = 'stringImporting';
  static const String stringCreateSuccess = 'stringCreateSuccess';
  static const String stringImportSuccess = 'stringImportSuccess';
  static const String stringInputMnemonic = 'stringInputMnemonic';
  static const String stringVerifyFail = 'stringVerifyFail';
  static const String stringImportMnemonic = 'stringImportMnemonic';
  static const String stringBackupMnemonic = 'stringBackupMnemonic';
  static const String stringBackupMnemonicDes = 'stringBackupMnemonicDes';
  static const String stringVerifyMnemonic = 'stringVerifyMnemonic';
  static const String stringOrderInputMnemonic = 'stringOrderInputMnemonic';
  static const String stringTouchPasswordTip = 'stringTouchPasswordTip';
  static const String stringTouchInputPsw = 'stringTouchInputPsw';
  static const String stringTouchInputPswTwo = 'stringTouchInputPswTwo';
  static const String stringTouchImInputCorrectPsw =
      'stringTouchImInputCorrectPsw';
  static const String stringTouchTowPswFail = 'stringTouchTowPswFail';
  static const String stringTouchSetWalletName = 'stringTouchSetWalletName';
  static const String stringTouchInputWalletNameHint =
      'stringTouchInputWalletNameHint';
  static const String stringTouchWalletNameToast = 'stringTouchWalletNameToast';
  static const String stringTouchNextTitle = 'stringTouchNextTitle';
  static const String stringTouchSafeTitle = 'stringTouchSafeTitle';
  static const String stringTouchPopTip1 = 'stringTouchPopTip1';
  static const String stringTouchPopTip2 = 'stringTouchPopTip2';
  static const String stringTouchPopOkTitle = 'stringTouchPopOkTitle';
  static const String stringTouchEnglishTitle = 'stringTouchEnglishTitle';
  static const String stringTouchChineseTitle = 'stringTouchChineseTitle';
  static const String stringTouchNumberTitle = 'stringTouchNumberTitle';
  static const String stringWalletIdTitle = 'stringWalletIdTitle';
  static const String stringResetCardTitle = 'stringResetCardTitle';
  static const String stringChangePswTitle = 'stringChangePswTitle';
  static const String stringChangeWalletNameTitle =
      'stringChangeWalletNameTitle';
  static const String stringCardEmpty = 'stringCardEmpty';
  static const String stringBackupComplete = 'stringBackupComplete';
  static const String stringVerifybackupTitle = 'stringVerifybackupTitle';
  static const String stringWalletNameNoEmpty = 'stringWalletNameNoEmpty';
  static const String stringWalletNameMax12 = 'stringWalletNameMax12';
  static const String stringSetWalletName = 'stringSetWalletName';
  static const String stringNewNameSameOldName = 'stringNewNameSameOldName';
  static const String stringNameChangeSuccess = 'stringNameChangeSuccess';
  static const String stringInputOldPswTip = 'stringInputOldPswTip';
  static const String stringInputNewPswTip = 'stringInputNewPswTip';
  static const String stringInputNewPswTwoTip = 'stringInputNewPswTwoTip';
  static const String stringTouchVerifyPswTitle = 'stringTouchVerifyPswTitle';
  static const String stringOldNewSameTip = 'stringOldNewSameTip';
  static const String stringTouchResetSuccess = 'stringTouchResetSuccess';
  static const String stringTouchSignErrorTip = 'stringTouchSignErrorTip';
  static const String stringTronRawDataHexError = 'stringTronRawDataHexError';
  static const String stringEmptyWalletTitle = 'stringEmptyWalletTitle';
  static const String stringEmptyWalletSubTitle = 'stringEmptyWalletSubTitle';
  static const String stringTabWallet = 'stringTabWallet';
  static const String stringTabDiscover = 'stringTabDiscover';
  static const String stringTabProfile = 'stringTabProfile';
  static const String stringBackupTip = 'stringBackupTip';
  static const String stringBackupButtonTitle = 'stringBackupButtonTitle';
  static const String stringJumpTitle = 'stringJumpTitle';
  static const String stringNfcReading = 'stringNfcReading';
  static const String stringSignLoading = 'stringSignLoading';
  static const String stringBingdingTitle = 'stringBingdingTitle';
  static const String stringCreateDesString = 'stringCreateDesString';
  static const String stringImportDesString = 'stringImportDesString';
  static const String stringCreateLoading = 'stringCreateLoading';
  static const String stringImportLoading = 'stringImportLoading';
  static const String stringNfcCloseAndroidTip = 'stringNfcCloseAndroidTip';
  static const String stringNfcCloseIOSTip = 'stringNfcCloseIOSTip';
  static const String stringTxError01 = 'stringTxError01';
  static const String stringTxError02 = 'stringTxError02';
  static const String stringTxError03 = 'stringTxError03';
  static const String stringTxError04 = 'stringTxError04';
  static const String stringTxError05 = 'stringTxError05';
  static const String stringTxError06 = 'stringTxError06';
  static const String stringTxError07 = 'stringTxError07';
  static const String stringTxError08 = 'stringTxError08';
  static const String stringTxError09 = 'stringTxError09';
  static const String stringTxError10 = 'stringTxError10';
  static const String stringTxError11 = 'stringTxError11';
  static const String stringTxError12 = 'stringTxError12';
  static const String stringTxError13 = 'stringTxError13';
  static const String stringTxError14 = 'stringTxError14';
  static const String stringTxError15 = 'stringTxError15';
  static const String stringTxError16 = 'stringTxError16';
  static const String stringTxError17 = 'stringTxError17';
  static const String stringTxError18 = 'stringTxError18';
  static const String stringTxError19 = 'stringTxError19';
  static const String stringTxError20 = 'stringTxError20';
  static const String stringTxError21 = 'stringTxError21';
  static const String stringTxError22 = 'stringTxError22';
  static const String stringTxError23 = 'stringTxError23';
  static const String stringSetting = 'stringSetting';
  static const String stringP2SHDes = 'stringP2SHDes';
  static const String stringP2TRDes = 'stringP2TRDes';
  static const String stringP2WPKHDes = 'stringP2WPKHDes';
  static const String stringP2PKHDes = 'stringP2PKHDes';
  static const String stringSignMessage = 'stringSignMessage';
  static const String stringMessgaeSign = 'stringMessgaeSign';
  static const String stringSignMethodTitle = 'stringSignMethodTitle';
  static const String stringMessageContentTitle = 'stringMessageContentTitle';
  static const String stringSignTextHint = 'stringSignTextHint';
  static const String stringSignTextTip = 'stringSignTextTip';
  static const String stringUnconfirmedUtxo = 'stringUnconfirmedUtxo';
  static const String stringAddressPublicTitle = 'stringAddressPublicTitle';
  static const String stringUnavailableBalance = 'stringUnavailableBalance';
  static const String stringTotalBalance = 'stringTotalBalance';
  static const String stringNotSupportedTaproot = 'stringNotSupportedTaproot';
  static const String stringDownloadFailed = 'stringDownloadFailed';
  static const String stringUpgradeNow = 'stringUpgradeNow';
  static const String stringTalkLater = 'stringTalkLater';
  static const String stringBackButton = 'stringBackButton';
  static const String stringNewVersion = 'stringNewVersion';
  static const String stringCoinbagISavailable = 'stringCoinbagISavailable';
  static const String stringCheckForUpdates = 'stringCheckForUpdates';
  static const String stringHaveLatestVersion = 'stringHaveLatestVersion';
  static const String stringP2SHDLTCes = 'stringP2SHDLTCes';
  static const String stringP2WPKHLTCDes = 'stringP2WPKHLTCDes';
  static const String stringP2PKHLTCDes = 'stringP2PKHLTCDes';
  static const String stringBroadcast = 'stringBroadcast';
  static const String stringBroadcastTitle = 'stringBroadcastTitle';
  static const String stringNodeTimeOut = 'stringNodeTimeOut';
  static const String stringNotAddedToAddressBook =
      'stringNotAddedToAddressBook';
  static const String stringAddNow = 'stringAddNow';
  static const String stringAddressInTheAddressBook =
      'stringAddressInTheAddressBook';
  static const String stringPaste = 'stringPaste';
  static const String stringSupportsReceivingNetwork =
      'stringSupportsReceivingNetwork';
  static const String stringEosEmptyTitle = 'stringEosEmptyTitle';
  static const String stringEosStake = 'stringEosStake';
  static const String stringEosEmptyDes = 'stringEosEmptyDes';
  static const String stringEosBuyRam = 'stringEosBuyRam';
  static const String stringEosSellRam = 'stringEosSellRam';
  static const String stringEosRefoud = 'stringEosRefoud';
  static const String stringEosNewAccount = 'stringEosNewAccount';
  static const String stringEosBidname = 'stringEosBidname';
  static const String stringEosReceiveTheRefunded =
      'stringEosReceiveTheRefunded';
  static const String stringExportAccountTitle = 'stringExportAccountTitle';
  static const String stringEosPublicKeyTitle = 'stringEosPublicKeyTitle';
  static const String stringEosAccountTitle = 'stringEosAccountTitle';
  static const String stringScanCompleteTitle = 'stringScanCompleteTitle';
  static const String stringNORemarkTitle = 'stringNORemarkTitle';
  static const String stringCheckDetailTitle = 'stringCheckDetailTitle';
  static const String stringShowQRTitle = 'stringShowQRTitle';
  static const String stringCopyAccmountTitle = 'stringCopyAccmountTitle';
  static const String stringEditRemarkTitle = 'stringEditRemarkTitle';
  static const String stringEthNotSupportedTips = 'stringEthNotSupportedTips';
  static const String stringNotSupportedChain = 'stringNotSupportedChain';
  static const String stringZecNotSupportedTips = 'stringZecNotSupportedTips';
  static const String stringCPULease = 'stringCPULease';
  static const String stringEosTxInvalid = 'stringEosTxInvalid';
  static const String stringInsufficientResources =
      'stringInsufficientResources';
  static const String stringEosAccountDetialTitle =
      'stringEosAccountDetialTitle';
  static const String stringCreateDateTitle = 'stringCreateDateTitle';
  static const String stringAccountRemarkTitle = 'stringAccountRemarkTitle';
  static const String stringThresholdTitle = 'stringThresholdTitle';
  static const String stringWeightTitle = 'stringWeightTitle';
  static const String stringVIPbenefits = 'stringVIPbenefits';
  static const String stringVIPTip1 = 'stringVIPTip1';
  static const String stringVIPTip2 = 'stringVIPTip2';
  static const String stringVIPTip3 = 'stringVIPTip3';
  static const String stringNotSupportEIP1559 = 'stringNotSupportEIP1559';
  static const String stringResourceManager = 'stringResourceManager';
  static const String stringNetDetail = 'stringNetDetail';
  static const String stringNetManager = 'stringNetManager';
  static const String stringStakeGet = 'stringStakeGet';
  static const String stringOtherDelegateOwner = 'stringOtherDelegateOwner';
  static const String stringDelegateToOther = 'stringDelegateToOther';
  static const String stringFreeGet = 'stringFreeGet';
  static const String stringDelegated = 'stringDelegated';
  static const String stringCanDelegate = 'stringCanDelegate';
  static const String stringDelegate = 'stringDelegate';
  static const String stringReclaimTitle = 'stringReclaimTitle';
  static const String stringEnergy = 'stringEnergy';
  static const String stringBandwidth = 'stringBandwidth';
  static const String stringGetBandwidth = 'stringGetBandwidth';
  static const String stringEnergyDetail = 'stringEnergyDetail';
  static const String stringEnergyManager = 'stringEnergyManager';
  static const String stringGetEnergy = 'stringGetEnergy';
  static const String stringTronNoSupportedTip = 'stringTronNoSupportedTip';
  static const String stringResourceToAddress = 'stringResourceToAddress';
  static const String stringResourceToAddressTip = 'stringResourceToAddressTip';
  static const String stringDelegateAmount = 'stringDelegateAmount';
  static const String stringDelegateAmountTip = 'stringDelegateAmountTip';
  static const String stringCanDelegateEnergy = 'stringCanDelegateEnergy';
  static const String stringCanDelegateBandwidth = 'stringCanDelegateBandwidth';
  static const String stringUseStakeTrx = 'stringUseStakeTrx';
  static const String stringDelegateTip = 'stringDelegateTip';
  static const String stringOk = 'stringOk';
  static const String stringUseStakeAmount = 'stringUseStakeAmount';
  static const String stringOtherGetResource = 'stringOtherGetResource';
  static const String stringResourceMaxTip = 'stringResourceMaxTip';
  static const String stringInputHintTrx = 'stringInputHintTrx';
  static const String stringResourceOwnAddressTips =
      'stringResourceOwnAddressTips';
  static const String stringDelegateUseTrx = 'stringDelegateUseTrx';
  static const String share = 'share';
  static const String penInBrowser = 'penInBrowser';
  static const String copyLink = 'copyLink';
  static const String favorites = 'favorites';
  static const String switchWallet = 'switchWallet';
  static const String successfullyAddedToFavorites =
      'successfullyAddedToFavorites';
  static const String unfavorited = 'unfavorited';
  static const String recent = 'recent';
  static const String viewAll = 'viewAll';
  static const String recentlyUsed = 'recentlyUsed';
  static const String refresh = 'refresh';
  static const String messages = 'messages';
  static const String paymentDetails = 'paymentDetails';
  static const String stringTronErrorMessage1 = 'stringTronErrorMessage1';
  static const String stringTronErrorMessage2 = 'stringTronErrorMessage2';
  static const String stringTronErrorMessage3 = 'stringTronErrorMessage3';
  static const String stringDelegatePopAmount = 'stringDelegatePopAmount';
  static const String stringDeleagteEnergy = 'stringDeleagteEnergy';
  static const String stringDelegateBandwidth = 'stringDelegateBandwidth';
  static const String stringResouecePopTips = 'stringResouecePopTips';
  static const String stringSearchDapp = 'stringSearchDapp';
  static const String stringSearchResult = 'stringSearchResult';
  static const String stringSearchHistory = 'stringSearchHistory';
  static const String stringUnableToFind = 'stringUnableToFind';
  static const String recommend = 'recommend';
  static const String stringLockingResource = 'stringLockingResource';
  static const String stringCanReclaimTitle = 'stringCanReclaimTitle';
  static const String stringDateTitle = 'stringDateTitle';
  static const String stringReclaimAmount1 = 'stringReclaimAmount1';
  static const String stringReclaimAmount2 = 'stringReclaimAmount2';
  static const String stringCanReclaimValue1 = 'stringCanReclaimValue1';
  static const String stringCanReclaimValue2 = 'stringCanReclaimValue2';
  static const String stringInputReclaimHint1 = 'stringInputReclaimHint1';
  static const String stringInputReclaimHint2 = 'stringInputReclaimHint2';
  static const String stringInputReclaimTip1 = 'stringInputReclaimTip1';
  static const String stringInputReclaimTip2 = 'stringInputReclaimTip2';
  static const String stringReclaimEnergy = 'stringReclaimEnergy';
  static const String stringReclaimBandwidth = 'stringReclaimBandwidth';
  static const String stringResourcePopTip1 = 'stringResourcePopTip1';
  static const String stringResourcePopTip2 = 'stringResourcePopTip2';
  static const String stringStakeTitle = 'stringStakeTitle';
  static const String stringTotalAmount = 'stringTotalAmount';
  static const String stringTotalStakeAmount = 'stringTotalStakeAmount';
  static const String stringUnlocking = 'stringUnlocking';
  static const String stringCanExtract = 'stringCanExtract';
  static const String stringUnderstandingStaking = 'stringUnderstandingStaking';
  static const String stringStakeBandwidthTips = 'stringStakeBandwidthTips';
  static const String stringStakeEnergyTips = 'stringStakeEnergyTips';
  static const String stringUnstakeTitle = 'stringUnstakeTitle';
  static const String stringFavorited = 'stringFavorited';
  static const String stringEdit = 'stringEdit';
  static const String stringWithraw = 'stringWithraw';
  static const String stringGetAmount = 'stringGetAmount';
  static const String stringStakeGetResource = 'stringStakeGetResource';
  static const String stringStakeHint1 = 'stringStakeHint1';
  static const String stringStakeHint2 = 'stringStakeHint2';
  static const String stringStakeAmount = 'stringStakeAmount';
  static const String stringStakeTip1 = 'stringStakeTip1';
  static const String stringStakeTip2 = 'stringStakeTip2';
  static const String stringStakeTip3 = 'stringStakeTip3';
  static const String stringStakeTip4 = 'stringStakeTip4';
  static const String stringNoSearchResultsFound = 'stringNoSearchResultsFound';
  static const String stringTrxCanUse = 'stringTrxCanUse';
  static const String stringStakeEnergy = 'stringStakeEnergy';
  static const String stringStakeBandwidth = 'stringStakeBandwidth';
  static const String stringUnstake1 = 'stringUnstake1';
  static const String stringCanUnstakeTitle = 'stringCanUnstakeTitle';
  static const String stringUnstakeTip = 'stringUnstakeTip';
  static const String stringClearAll = 'stringClearAll';
  static const String stringUnstakeTrx2 = 'stringUnstakeTrx2';
  static const String stringUnstakeHint = 'stringUnstakeHint';
  static const String stringUnstakeEnergy = 'stringUnstakeEnergy';
  static const String stringUnstakeBandwidth = 'stringUnstakeBandwidth';
  static const String stringUnstakeEnergy2 = 'stringUnstakeEnergy2';
  static const String stringUnstakeBandwidth2 = 'stringUnstakeBandwidth2';
  static const String stringDayToWithdraw = 'stringDayToWithdraw';
  static const String stringUnlockingTrx = 'stringUnlockingTrx';
  static const String stringWithdrawTrx = 'stringWithdrawTrx';
  static const String stringTronNoSupportedStake2 =
      'stringTronNoSupportedStake2';
  static const String stringTronNoSupportedUnstake2 =
      'stringTronNoSupportedUnstake2';
  static const String stringStake1DetialTitle = 'stringStake1DetialTitle';
  static const String stringStakeToOwnTitle = 'stringStakeToOwnTitle';
  static const String stringStakeToOtherTitle = 'stringStakeToOtherTitle';
  static const String stingCannotAccessThisWebsite =
      'stingCannotAccessThisWebsite';
  static const String stingCannotAccessThisWebsiteVPN =
      'stingCannotAccessThisWebsiteVPN';
  static const String stringRedirecting = 'stringRedirecting';
  static const String stringPledgedTitle = 'stringPledgedTitle';
  static const String stringRedemptionTitle = 'stringRedemptionTitle';
  static const String stringEosResourceTip1 = 'stringEosResourceTip1';
  static const String stringEosResourceTip2 = 'stringEosResourceTip2';
  static const String stringAvailableBalance = 'stringAvailableBalance';
  static const String stringOnlySupportsTaproot = 'stringOnlySupportsTaproot';
  static const String stringRequestAccess = 'stringRequestAccess';
  static const String stringDeny = 'stringDeny';
  static const String stringRequestAuthorization = 'stringRequestAuthorization';
  static const String stringCurrentNetworkNotSupported =
      'stringCurrentNetworkNotSupported';
  static const String stringContractCall = 'stringContractCall';
  static const String stringAssetSecurityWarning1 =
      'stringAssetSecurityWarning1';
  static const String stringAssetSecurityWarning2 =
      'stringAssetSecurityWarning2';
  static const String stringAssetSecurityWarning3 =
      'stringAssetSecurityWarning3';
  static const String stringAssetSecurityWarning4 =
      'stringAssetSecurityWarning4';
  static const String stringAssetSecurityWarning5 =
      'stringAssetSecurityWarning5';
  static const String stringAssetSecurityWarning6 =
      'stringAssetSecurityWarning6';
  static const String stringColdWalletAuthorizationWarning =
      'stringColdWalletAuthorizationWarning';
  static const String stringWarning = 'stringWarning';
  static const String stringAuthorizeSmartContract =
      'stringAuthorizeSmartContract';
  static const String stringAuthorizedAddress = 'stringAuthorizedAddress';
  static const String stringSignTransaction = 'stringSignTransaction';
  static const String stringTransactionType = 'stringTransactionType';
  static const String stringData = 'stringData';
  static const String stringNftAmount = 'stringNftAmount';
  static const String stringNotCurrentlySupporte = 'stringNotCurrentlySupporte';
  static const String stringHoldValue = 'stringHoldValue';
  static const String stringValuation = 'stringValuation';
  static const String stringNftOverview = 'stringNftOverview';
  static const String stringNftActivity = 'stringNftActivity';
  static const String stringContractAddress = 'stringContractAddress';
  static const String stringTokenStandards = 'stringTokenStandards';
  static const String stringNftNetwork = 'stringNftNetwork';
  static const String stringNftTransfer = 'stringNftTransfer';
  static const String stringNftFrom = 'stringNftFrom';
  static const String stringNftTo = 'stringNftTo';
  static const String stringNftSale = 'stringNftSale';
  static const String stringNftMint = 'stringNftMint';
  static const String stringNftBurn = 'stringNftBurn';
  static const String stringNftSaleBuy = 'stringNftSaleBuy';
  static const String stringNftSaleSell = 'stringNftSaleSell';
  static const String stringNftReceiveAddress = 'stringNftReceiveAddress';
  static const String stringNftSendAddress = 'stringNftSendAddress';
  static const String stringNftContractAddress = 'stringNftContractAddress';
  static const String stringQrMaxNumberTip = 'stringQrMaxNumberTip';
  static const String stringNftSuccess = 'stringNftSuccess';
  static const String stringTargetAddress = 'stringTargetAddress';
  static const String stringSendNft = 'stringSendNft';
  static const String stringNftSend = 'stringNftSend';
  static const String stringNftReceive = 'stringNftReceive';
  static const String stringQRScanError = 'stringQRScanError';
  static const String stringBindAddressNow = 'stringBindAddressNow';
  static const String stringNotBoundYet = 'stringNotBoundYet';
  static const String stringBindAddressInfo = 'stringBindAddressInfo';
  static const String stringTokenAddTitle = 'stringTokenAddTitle';
  static const String stringTokenAddTip1 = 'stringTokenAddTip1';
  static const String stringTokenPro1Tip1 = 'stringTokenPro1Tip1';
  static const String stringTokenPro1Tip2 = 'stringTokenPro1Tip2';
  static const String stringTokenPro2Tip1 = 'stringTokenPro2Tip1';
  static const String stringTokenPro2Tip2 = 'stringTokenPro2Tip2';
  static const String stringTokenPro3Tip1 = 'stringTokenPro3Tip1';
  static const String stringTokenPro3Tip2 = 'stringTokenPro3Tip2';
  static const String stringSyncAmount = 'stringSyncAmount';
  static const String stringSyncAmountValue = 'stringSyncAmountValue';
  static const String stringCurrentSyncAmount = 'stringCurrentSyncAmount';
  static const String stringSyncAmountTip1 = 'stringSyncAmountTip1';
  static const String stringSyncAmountTip2 = 'stringSyncAmountTip2';
  static const String stringSyncAmountTip3 = 'stringSyncAmountTip3';
  static const String stringP1info = 'stringP1info';
  static const String stringP2info = 'stringP2info';
  static const String stringP2Plusinfo = 'stringP2Plusinfo';
  static const String stringP3info = 'stringP3info';
  static const String stringNotBoundInfo = 'stringNotBoundInfo';
  static const String stirngSyncBroadInfo = 'stirngSyncBroadInfo';
  static const String stringConfirmTransaction = 'stringConfirmTransaction';
  static const String stringNoSupportChain = 'stringNoSupportChain';
  static const String stringNoMonitorAddress = 'stringNoMonitorAddress';
  static const String stringSwitchAddress = 'stringSwitchAddress';
  static const String stringNotBoundInfoAddres = 'stringNotBoundInfoAddres';
  static const String stirngSelectHardwareWallet = 'stirngSelectHardwareWallet';
  static const String stirngSwitchHardwareWallet = 'stirngSwitchHardwareWallet';
  static const String stirngBroadcastNoData = 'stirngBroadcastNoData';
  static const String stringNoSupportedFeature = 'stringNoSupportedFeature';
  static const String stringNetworkDiagnostics = 'stringNetworkDiagnostics';
  static const String stringNetworkStatus = 'stringNetworkStatus';
  static const String stringXrpNotSupportedTips = 'stringXrpNotSupportedTips';
  static const String stringSynAddressTitle = 'stringSynAddressTitle';
  static const String stringSynAddressTip = 'stringSynAddressTip';
  static const String stringSynChainError = 'stringSynChainError';
  static const String stringSynAddressError = 'stringSynAddressError';
  static const String stringBuildError = 'stringBuildError';
  static const String stringInputSolTip1 = 'stringInputSolTip1';
  static const String stringInputSolTip2 = 'stringInputSolTip2';
  static const String stringInputSolTip3 = 'stringInputSolTip3';
  static const String stringSolanaTxError1 = 'stringSolanaTxError1';
  static const String stringBnbRecieveTip = 'stringBnbRecieveTip';
  static const String stringAddressSuccess = 'stringAddressSuccess';
  static const String stringSynSuccess = 'stringSynSuccess';
  static const String stringBackgroundUpgrade = 'stringBackgroundUpgrade';
  static const String stringBackgroundUpgradeIng = 'stringBackgroundUpgradeIng';
  static const String stringContactCustomerServiceToAdd =
      'stringContactCustomerServiceToAdd';
  static const String stringContinueTx = 'stringContinueTx';
  static const String stringXrpAddressError = 'stringXrpAddressError';
  static const String stringXripFeeError = 'stringXripFeeError';
  static const String stringSolPriceMinTip = 'stringSolPriceMinTip';
  static const String stringBabylonError = 'stringBabylonError';
  static const String stringWebUserName = 'stringWebUserName';
  static const String stringWebPassword = 'stringWebPassword';
  static const String stringWebLogin = 'stringWebLogin';
  static const String stringTouchChineseTip = 'stringTouchChineseTip';
  static const String stringBestFeeTitle = 'stringBestFeeTitle';
  static const String stringMinerFee = 'stringMinerFee';
  static const String stringBestFee = 'stringBestFee';
  static const String stringThemeMode = 'stringThemeMode';
  static const String stringLightMode = 'stringLightMode';
  static const String stringDarkMode = 'stringDarkMode';
  static const String stringThemeModeText = 'stringThemeModeText';
  static const String stringSecurityVerification = 'stringSecurityVerification';
  static const String stringOpenSecurityTitle = 'stringOpenSecurityTitle';
  static const String stringVerMethodTitle = 'stringVerMethodTitle';
  static const String stringVerPasswordTitle = 'stringVerPasswordTitle';
  static const String stringBiometricsTitle = 'stringBiometricsTitle';
  static const String stringVerChangePswTitle = 'stringVerChangePswTitle';
  static const String stringUnlockAppTitle = 'stringUnlockAppTitle';
  static const String stringUnlockAppSub1Title = 'stringUnlockAppSub1Title';
  static const String stringUnlockAppSub2Title = 'stringUnlockAppSub2Title';
  static const String stringAuthTsTitle = 'stringAuthTsTitle';
  static const String stringAuthTsSub2Title = 'stringAuthTsSub2Title';
  static const String stringCreatePasswordTitle = 'stringCreatePasswordTitle';
  static const String stringPasswordNotEqualTip = 'stringPasswordNotEqualTip';
  static const String stringOpenBiometricsTip = 'stringOpenBiometricsTip';
  static const String stringInputPassword = 'stringInputPassword';
  static const String stringForgetPassword = 'stringForgetPassword';
  static const String stringPasswordErrorNumber = 'stringPasswordErrorNumber';
  static const String stringPasswordError = 'stringPasswordError';
  static const String stringIsOpenSecurity = 'stringIsOpenSecurity';
  static const String stringPasswordErrorMax = 'stringPasswordErrorMax';
  static const String stringPaswordSecond = 'stringPaswordSecond';
  static const String stringPaswordMinute = 'stringPaswordMinute';
  static const String stringPaswordHour = 'stringPaswordHour';
  static const String stringSoloTokenOpenTip = 'stringSoloTokenOpenTip';
  static const String stringAppLockInfo = 'stringAppLockInfo';
  static const String stringChangeSuccess = 'stringChangeSuccess';
  static const String strinTalkAboutItlater = 'strinTalkAboutItlater';
  static const String stringCosmosTip1 = 'stringCosmosTip1';
  static const String stringCosmosTip2 = 'stringCosmosTip2';
  static const String stringCreatePassCode = 'stringCreatePassCode';
  static const String stringCreateNewPasscode = 'stringCreateNewPasscode';
  static const String stringResetPasscode = 'stringResetPasscode';
  static const String stringAddToken = 'stringAddToken';
  static const String stringBabyTsTip = 'stringBabyTsTip';
  static const String stringSelectChain = 'stringSelectChain';
  static const String stringNoMorePrompts = 'stringNoMorePrompts';
  static const String stringTronNetworkFees = 'stringTronNetworkFees';
  static const String stringContractInteraction = 'stringContractInteraction';
  static const String stringNotFullySupportETH = 'stringNotFullySupportETH';
  static const String strinInputMax = 'strinInputMax';
  static const String stringCanceTx = 'stringCanceTx';
  static const String stringContinueTx2 = 'stringContinueTx2';
}
