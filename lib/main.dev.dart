/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2024-08-14 17:27:42
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/main.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

/// 切换为测试环境入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  AppConfig.appConfigType = AppConfigType.dev;
  AppConfig.instance.setApiConfig();
  startApp();
}
