/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 09:18:48
 * @LastEditTime: 2025-04-08 10:15:50
 */

class CommonConstant {
  static const String mobileId = "mobileId";

  static const String appName = "appname";

  static const String currency = "currency";

  static const String hideAssetsStr = "******";

  static const String zeroAsstes = "0.00";

  static const String emptyTotalAsstes = "--.--";

  static const String emptyAsstes = "--";

  //assetPath
  static const assetsPath = "assets/images/";

// PNG
  static const imagePng = ".png";

// jpeg
  static const imageJpeg = ".jpeg";

  /// 90
  static const nfcSw1_90 = '90';

  /// 61
  static const nfcSw1_61 = '61';

  static const brightness = 0.5;

  /// utmSource
  static const String utmSource = "utm_source";

  /// locale
  static const String locale = "locale";

  /// language
  static const String language = "language";

  ///GWEI
  static const String gWEI = "GWEI";

  /// batchId_9001
  static const int batchId_9001 = 9001;

  /// babylonlabs.io
  static const String babylonlabs = 'babylonlabs.io';

  /// babylonchain.io
  static const String babylonchain = 'babylonchain.io';

  /// babylonchain.io
  static const String babylonchainlabs = 'babylonlabs.io';

  /// babylonlabs.io/
  static const String babylonlabs2 = 'babylonlabs.io/';

  /// airdrop-preview.uat.babylonlabs.io
  static const String airdropTest = 'airdrop-preview.uat.babylonlabs.io';
  static const String airdropBabylon = 'airdrop.babylon.foundation';

  /// amino
  static const String amino = 'amino';
}

class StorageKey {
  /// 保存多语言key
  static const String language = 'language';

  /// 保存二维码key
  static const String qr = "QR_Code";

  /// 保存用户信息
  static const String account = "account";

  /// 保存隐藏资产
  static const String hideAssets = "hideAssets";

  /// 首次启动
  static const String fristLaunch = "fristLaunch";

  /// 是否显示了VIP引导
  static const String isShowVIPGuide = "showVIPGuide";

  /// 记录上一个版本
  static const String initCoinBaseV4 = "initCoinBaseV4";

  ///  是否同意用户协议
  static const String isAgree = "isAgreement";

  /// 是否开启人脸或者指纹识别
  static const String isAuth = "local_auth_on";

  /// 安全验证
  static const String sucerity = "sucerity_key";

  /// 是否有生物是被权限
  static const String isBiometrics = "isBiometrics";

  /// 当前生物识别类型
  static const String biometricsType = "biometricsType";

  /// 监听上传监控信息
  static const String monitorinfoModel = "monitorinfoModel";

  /// 监听用户上传地址
  static const String walletAddress = "walletAddress";

  /// rate汇率
  static const String rateModel = "rateModel";

  /// rpcModel
  static const String rpcModel = 'rpcModel';

  /// searchHistoryList
  static const String searchHistoryList = "searchHistoryList";

  /// hideNftAssets
  static const String hideNftAssets = "hideNftAssets";

  /// 授权交易
  static const String authorizeTransaction = "authorizeTransaction";

  ///不再显示提币提示
  static const String noMorePrompts = 'noMorePrompts4';
}

class GetArgumentsKey {
  /// web url
  static const String url = 'url';

  /// web title
  static const String title = 'title';

  /// showTitle
  static const String showTitle = 'showTitle';

  /// isOpenDappBrowser
  static const String isOpenDappBrowser = 'isOpenDappBrowser';

  /// isOpenDappBrowserFromSearch
  static const String isOpenDappBrowserFromSearch =
      'isOpenDappBrowserFromSearch';

  /// homeTotkenModel
  static const String homeTotkenModel = 'homeTotkenModel';

  /// 设置密码 验证码
  static const String code = 'code';

  /// 设置密码 账户
  static const String account = 'account';

  /// 设置密码 邀请码
  static const String inviteCode = 'inviteCode';

  /// 设置密码 类型
  static const String passwordType = 'passwordType';

  /// 拒绝申请权限
  static const String kHaveDenied = 'kHaveDenied';

  ///绑定邮箱手机类型
  static const String bindType = "bindType";

  ///地址薄详情
  static const String addressModel = "addressModel";

  ///地址薄编辑模式
  static const String addressBookAction = "addressBookAction";

  //转账页面跳转添加地址薄模式
  static const String sendToaddressBookAction = "sendToaddressBookAction";

  ///传参walletId
  static const String walletId = "walletId";

  ///传参address
  static const String address = "address";

  ///传参chain
  static const String chain = "chain";

  ///传参contract
  static const String contract = "contract";

  ///balance
  static const String balance = "balance";

  ///扫码结果
  static const String scanResult = "scanResult";

  ///钱包对象
  static const String wallet = "wallet";

  ///扫码错误码
  static const String scanErrorCode = "scanErrorCode";

  ///扫码类型
  static const String scanAction = "scanAction";

  //colde wallet名字
  static const String coldWalletName = "coldWalletName";

  //绑定失败
  static const String bindFailed = "bindFailed";

  //绑定失败原因
  static const String bindFailedText = "bindFailedText";

  ///传参coinModel
  static const String coinModel = "coinModel";

  ///传参TokenModel
  static const String tokenModel = "tokenModel";

  ///coinType
  static const String coinType = "coinType";

  ///cmd
  static const String cmd = "cmd";

  ///eos publice
  static const String eosPublickey = "eosPublickey";

  ///二维码 data
  static const String qrData = "qrData";

  ///转账交易 model
  static const String transferModel = "transferModel";

  ///扫描类型 scanType
  static const String scanType = "scanType";

  /// 构建二维码类型
  static const String qrType = "qrType";

  /// gas_limit
  static const String gasLimit = "gas_limit";

  /// gas_fee_cap
  static const String gasFeeCap = "gas_fee_cap";

  /// gas_fee_premium
  static const String gasFeePremium = "gas_fee_premium";

  /// tsModel
  static const String tsModel = "tsModel";

  /// gearList
  static const String gearList = "gearList";

  /// filGasModel
  static const String filGasModel = "filGasModel";

  /// gearModel
  static const String gearModel = "gearModel";

  /// energy
  static const String energy = "energy";

  /// bandwidth
  static const String bandwidth = "bandwidth";

  /// isFullPage
  static const String isFullPage = "isFullPage";

  /// deviceId
  static const String deviceId = 'deviceId';

  /// batch
  static const String batch = 'batch';

  /// activityModel
  static const String activityModel = 'activityModel';

  /// walletModel
  static const String walletModel = 'walletModel';

  /// controller
  static const String controller = 'controller';

  /// mnemonics_en
  static const String mnemonicsEn = 'mnemonics_en';

  /// mnemonics_zh
  static const String mnemonicsZh = 'mnemonics_zh';

  /// mnemonics_nu
  static const String mnemonicsNu = 'mnemonics_nu';

  /// targetCommand
  static const String targetCommand = 'targetCommand';

  /// command
  static const String command = 'command';

  /// targetTouchModel
  static const String targetTouchModel = 'targetTouchModel';

  /// newTouchWalletMethod
  static const String newTouchWalletMethod = 'newTouchWalletMethod';

  /// password
  static const String password = 'password';

  /// walletName
  static const String walletName = 'walletName';

  /// newPassword
  static const String newPassword = 'newPassword';

  /// resetStatus
  static const String resetStatus = 'resetStatus';

  /// availableBalance
  static const String availableBalance = 'availableBalance';

  /// unavailableBalance
  static const String unavailableBalance = 'unavailableBalance';

  /// totalBalance
  static const String totalBalance = 'totalBalance';

  /// tx_id
  static const String txId = 'tx_id';

  /// txHex
  static const String txHex = 'tx_hex';

  /// fee
  static const String fee = 'fee';

  /// change_amount
  static const String changeAmount = 'change_amount';

  /// best_utxo_list
  static const String bestUtxoList = 'best_utxo_list';

  /// data
  static const String data = "data";

  /// tronResource
  static const String tronResource = "tronResource";

  /// tronResourceType
  static const String tronResourceType = "tronResourceType";

  /// tronAccountInfo
  static const String tronAccountInfo = "tronAccountInfo";

  /// dappModel
  static const String dappModel = "dappModel";

  /// raw_data_hex
  static const String rawDataHex = "raw_data_hex";

  /// raw_data
  static const String rawData = "raw_data";

  /// txID
  static const String txID = "txID";

  static const String mode = 'mode';

  /// dataList
  static const String dataList = 'dataList';

  static const String model = "model";

  static const String headerParameter = 'headerParameter';

  ///  eosResourceInfo
  static const String eosResourceInfo = 'eosResourceInfo';

  ///  isNft
  static const String isNft = 'isNft';

  ///  blockhash
  static const String blockhash = 'blockhash';

  ///  value
  static const String value = 'value';

  ///  openedAccount
  static const String openedAccount = 'openedAccount';

  ///  action
  static const String action = 'action';

  ///  securityStatus
  static const String securityStatus = 'securityStatus';

  ///  biometrics
  static const String biometrics = 'biometrics';
}

class CustomerService {
  //官方电话
  static const String helpPhone = "+852-2456-5848";
}

/// @description: GetBuilder Id key
class GetKey {
  /// 钱包首页Token列表Id
  static const String walletTokenList = 'walletTokenList';

  /// 钱包首页Top面板Id
  static const String walletHomtTopList = 'walletHomtTopList';

  /// 币种详情Top面板Id
  static const String assetsTopId = 'assetsTopId';

  /// 币种详情交易记录
  static const String assetsActivityId = 'assetsActivityId';

  /// 交易详情
  static const String tokenTransactionId = 'tokenTransactionId';

  /// 通知中心
  static const String notificationId = 'notificationId';

  /// 矿工费设置
  static const String feeSettingHeaderId = 'feeSettingHeaderId';

  /// 更新矿工费
  static const String updateFeeId = 'updateFeeId';

  /// send 更新TextField
  static const String updateSendTextField = 'updateSendTextField';

  /// send 更新 button
  static const String sendButtonId = 'sendButtonId';

  /// searchTokens
  static const String searchTokens = 'searchTokens';

  /// backupMnemonicsId
  static const String backupMnemonicsId = 'backupMnemonicsId';

  /// verifyMnemonicsId
  static const String verifyMnemonicsId = 'verifyMnemonicsId';

  /// importWalletId
  static const String importWalletId = 'importWalletId';

  /// blockBaseUrl
  static const String blockBaseUrl = 'blockBaseUrl';

  /// apiBaseUrl
  static const String apiBaseUrl = 'apiBaseUrl';

  /// utxoBalanceId
  static const String utxoBalanceId = 'utxoBalanceId';

  /// stakeEnergyId
  static const String stakeEnergyId = 'stakeEnergyId';

  /// stakeBandwidthId
  static const String stakeBandwidthId = 'stakeBandwidthId';

  /// unstakeEnergyId
  static const String unstakeEnergyId = 'unstakeEnergyId';

  /// unstakeBandwidthId
  static const String unstakeBandwidthId = 'unstakeBandwidthId';

  /// nftHeaderId
  static const String nftHeaderId = 'nftHeaderId';

  /// nftAssetsId
  static const String nftAssetsId = 'nftAssetsId';

  /// nftCollectionsId
  static const String nftCollectionsId = 'nftCollectionsId';

  /// nftActivityId
  static const String nftActivityId = 'nftActivityId';

  /// nftRecordActivityId
  static const String nftRecordActivityId = 'nftRecordActivityId';

  /// syncBalance
  static const String syncBalance = 'syncBalance';

  /// networkId
  static const String networkId = 'networkId';

  /// securityPassword
  static const String securityPassword = 'securityPassword';

  /// securitySetting
  static const String securitySetting = 'securitySetting';

  /// security
  static const String security = 'security';
}

class APIConstant {
  /// 成功code
  static const int responseCode = 200;

  /// 成功code
  static const int responseCodeV2 = 0;

  /// address
  static const String address = "address";

  /// chain
  static const String chain = "chain";

  /// contract
  static const String contract = "contract";

  /// payload
  static const String inputData = "input_data";

  /// type
  static const String type = "type";

  /// account
  static const String account = "account";

  /// extra
  static const String extra = "extra";

  /// token
  static const String token = "token";

  /// publickey
  static const String publickey = "public_key";

  /// block_number
  static const String blockNumber = "block_number";

  /// block_number
  static const String integerAccuracy = "integer_accuracy";

  /// accuracy
  static const String accuracy = "accuracy";

  /// symbol
  static const String symbol = "symbol";

  /// toAddress
  static const String toAddress = "toAddress";

  /// amount
  static const String amount = "amount";

  /// decimal
  static const String decimal = "decimal";

  /// params
  static const String params = "params";

  /// to
  static const String to = "to";

  /// from
  static const String from = "from";

  /// data
  static const String data = "data";

  /// customize
  static const String customize = "customize";

  /// exact
  static const String exact = "exact";

  /// revert
  static const String revert = "revert";

  /// owner_address
  static const String ownerAddress = "owner_address";

  /// extra_data
  static const String extraData = "extra_data";

  /// asset_name
  static const String assetName = "asset_name";

  /// parameter
  static const String parameter = "parameter";

  /// function_selector
  static const String functionSelector = "function_selector";

  /// functionSelectorValue
  static const String functionSelectorValue = "transfer(address,uint256)";

  /// call_value
  static const String callValue = "call_value";

  /// fee_limit
  static const String feeLimit = "fee_limit";

  /// contract_address
  static const String contractAddress = "contract_address";

  /// energy_penalty
  static const String energyPenalty = "energy_penalty";

  /// energy_used
  static const String energyUsed = "energy_used";

  /// to_address
  static const String addressTo = "to_address";

  /// transaction
  static const String transaction = "transaction";

  /// raw_data_hex
  static const String rawDataHex = "raw_data_hex";

  /// rawData
  static const String rawData = "raw_data";

  /// txID
  static const String txID = "txID";

  /// address_is_activated
  static const String addressIsActivated = "address_is_activated";

  /// isRemark
  static const String isRemark = "isRemark";

  /// chain_id
  static const String chainId = "chain_id";

  /// head_block_id
  static const String headBlockId = "head_block_id";

  /// block_time
  static const String blockTime = "block_time";

  /// block_time
  static const String nodeHeight = "node_height";

  /// eosio.token
  static const String eosioToken = "eosio.token";

  /// block_info
  static const String blockInfo = "block_info";

  /// deviceId
  static const String deviceId = "deviceId";

  /// device_id
  static const String deviceIdKey = "device_id";

  /// challenge
  static const String challenge = "challenge";

  /// signature
  static const String signature = "signature";

  /// isExist
  static const String isExist = "isExist";

  /// task_id
  static const String taskId = "task_id";

  /// tx_ids
  static const String txIds = "tx_ids";

  /// size
  static const String size = "size";

  /// search
  static const String search = "search";

  static const String error = "error";

  /// version
  static const String version = "version";

  /// deviceType
  static const String deviceType = "deviceType";

  /// Channel
  static const String channel = "channel";

  /// all_type
  static const String allType = "all_type";

  /// resource
  static const String resource = "resource";

  /// lock
  static const String lock = "lock";

  /// lock_period
  static const String lockPeriod = "lock_period";

  /// ENERGY
  static const String energy = "ENERGY";

  /// BANDWIDTH
  static const String bandWidth = "BANDWIDTH";

  /// balance
  static const String balance = "balance";

  /// receiver_address
  static const String receiverAddress = "receiver_address";

  /// frozen_duration
  static const String frozenDuration = "frozen_duration";

  /// frozen_balance
  static const String frozenBalance = "frozen_balance";

  /// unfreeze_balance
  static const String unfreezeBalance = "unfreeze_balance";

  /// value
  static const String value = "value";

  /// current_page
  static const String currentPage = "current_page";

  /// page_size
  static const String pageSize = "page_size";

  /// limit
  static const String limit = "limit";

  /// token_id
  static const String tokenId = "token_id";

  /// cursor
  static const String cursor = "cursor";

  /// event_type
  static const String eventType = "event_type";

  /// coin_type
  static const String coinType = "coin_type";

  /// sol_user_address
  static const String solUserAddress = "sol_user_address";

  /// tokenType
  static const String tokenType = "tokenType";

  /// ignoreIntercep
  static const String ignoreIntercep = "ignoreIntercep";

  /// from_address
  static const String fromAddress = "from_address";
}

class Flavor {
  /// Android 官网 渠道
  static const String official = "official";

  /// Android GooglePlay 渠道
  static const String googlePlay = "googlePlay";

  /// ios AppStrore
  static const String appStore = "appStore";

  /// ios testFlight
  static const String testFlight = "testFlight";
}

class BuglayKey {
  static const String androidAppId = "a8e439c577";
  static const String iOSAppId = "2fb62dba4a";
}

enum ChainManagerMode {
  wallet,
  nft,
  dapp,
  dappBaby,
}
