/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-31 14:47:52
 * @LastEditTime: 2024-03-28 13:39:10
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';

class SwitchWidget extends StatefulWidget {
  const SwitchWidget(
      {super.key,
      this.onTintColor,
      this.thumbTintColor,
      this.offTintColor,
      this.onChanged,
      this.isOn = false,
      this.width,
      this.height,
      this.padding = 2});

  final bool isOn;
  final Color? onTintColor;
  final Color? offTintColor;
  final Color? thumbTintColor;
  final ValueChanged<bool>? onChanged;
  final double? width;
  final double? height;
  final double padding;

  @override
  State<SwitchWidget> createState() => _SwitchWidgetState();
}

class _SwitchWidgetState extends State<SwitchWidget> {
  bool isOn = false;
  double width = 32;
  double height = 18;
  @override
  void initState() {
    super.initState();
    isOn = widget.isOn;
    width = widget.width ?? Get.setWidth(32);
    height = widget.height ?? Get.setHeight(18);
  }

  double get toggleSize {
    return (width - widget.padding) / 2.0;
  }

  @override
  Widget build(BuildContext context) {
    return FlutterSwitch(
        value: widget.onChanged != null ? widget.isOn : isOn,
        onToggle: (value) {
          if (widget.onChanged != null) {
            widget.onChanged!(value);
          } else {
            setState(() {
              isOn = !value;
            });
          }
        },
        width: width,
        height: height,
        padding: widget.padding,
        toggleSize: toggleSize,
        duration: const Duration(milliseconds: 100),
        activeColor: widget.onTintColor ?? Get.theme.textPrimary,
        inactiveColor: widget.offTintColor ?? Get.theme.colorD7D7D7,
        toggleColor: widget.thumbTintColor ?? Get.theme.bgColor,
        activeToggleColor: widget.thumbTintColor ?? Get.theme.bgColor,
        inactiveToggleColor: widget.thumbTintColor ?? Get.theme.bgColor);
  }
}
