import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/widgets/skeleton/skeleton_widget.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImageWidget extends StatelessWidget {
  const ImageWidget({
    super.key,
    this.imageUrl,
    this.assetUrl,
    this.loadingWidget,
    this.errorWidget,
    this.width,
    this.height,
    this.radius,
    this.cache = true,
    this.cacheRawData = false,
    this.fit = BoxFit.cover,
    this.assetImagefit = BoxFit.contain,
    this.imageformat = ".png",
    this.shape = BoxShape.rectangle,
    this.gaplessPlayback = false,
    this.isAutoSizeImage = false,
    this.assetFullPath,
  });

  /// 自动宽高
  final bool? isAutoSizeImage;

  /// 网络图片地址
  final String? imageUrl;

  /// 本地图片地址
  final String? assetUrl;

  /// 本地图片地址自定义全路径
  final String? assetFullPath;

  /// 网络图片加载时显示
  final Widget? loadingWidget;

  /// 失败时自定义显示
  final Widget? errorWidget;

  /// 自定义宽
  final double? width;

  /// 自定义高
  final double? height;

  /// 自定义圆角角度
  final double? radius;

  ///  网络图片是否缓存
  final bool cache;

  ///  本地图片是否缓存
  final bool cacheRawData;

  final BoxFit? fit;

  final BoxFit? assetImagefit;

  final String? imageformat;

  /// 默认长方形 rectangle 圆形circle
  final BoxShape? shape;

  ///打开[gaplessPlayback]后，您可能会意外中断期望并重复使用旧的小部件。设为true,解决软键盘闪烁问题,但是打开会图片无图时加载错位
  final bool gaplessPlayback;

  @override
  Widget build(BuildContext context) {
    return !GetUtils.isNull(assetUrl) || !GetUtils.isNull(assetFullPath)
        ? ClipRRect(
            borderRadius: BorderRadius.circular(radius ?? 0), // 设置圆角的半径
            child: isAutoSizeImage!
                ? Image.asset(
                    assetFullPath ??
                        "${CommonConstant.assetsPath}${assetUrl!}${imageformat!}",
                    fit: assetImagefit,
                  )
                : Image.asset(
                    assetFullPath ??
                        "${CommonConstant.assetsPath}${assetUrl!}${imageformat!}",
                    fit: assetImagefit,
                    width: width!,
                    height: height!,
                  ),
          )
        : ClipRRect(
            borderRadius: BorderRadius.circular(radius ?? 0), // 设置圆角的半径
            child: ExtendedImage.network(
              imageUrl ?? '',
              width: width!,
              height: height!,
              cache: cache,
              fit: fit,
              printError: !kReleaseMode,
              gaplessPlayback: gaplessPlayback,
              loadStateChanged: (ExtendedImageState state) {
                Widget loader = const SizedBox.shrink();
                switch (state.extendedImageLoadState) {
                  case LoadState.loading:
                    loader = loadingWidget ??
                        SkeletonItems(
                          width: width,
                          height: height,
                          radius: radius,
                        );

                    break;
                  case LoadState.failed:
                    state.imageProvider.evict();
                    loader = errorWidget ??
                        ClipPath.shape(
                            shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(radius ?? 0)),
                            child: Image.asset(
                                "${CommonConstant.assetsPath}icon_dapp_default_logo${imageformat!}",
                                width: width,
                                height: height));

                    break;
                  case LoadState.completed:
                    loader = state.completedWidget;
                    break;
                }
                return loader;
              },
            ));
  }
}
