/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-27 14:39:28
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/database/db_provider.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';

class SymbolWidget extends StatelessWidget {
  const SymbolWidget({
    super.key,
    required this.coinModel,
    this.chainImageSzie = 14,
    double? size = 28, // Set a default size
  })  : width = size,
        height = size;

  final CoinModel? coinModel;

  final double? width;
  final double? height;

  final double chainImageSzie;

  @override
  Widget build(BuildContext context) {
    if (coinModel == null) {
      return const SizedBox.shrink();
    }
    final imageSize = Get.setImageSize(width ?? 28);
    final String? symbolIcon = coinModel!.symbolIcon;

    final String? badgeChainIcon = coinModel!.chainIcon;

    final String? assetUrl =
        coinModel!.isToken! ? "icon_chain_default" : symbolIcon;
    final bool useAssetImage = !coinModel!.isToken! ||
        (coinModel!.isToken! && Get.isEmptyString(symbolIcon));
    final double radius = useAssetImage ? 0 : height! / 2;
    final double offset = -(height! / 7);

    Widget image = ImageWidget(
      imageUrl: useAssetImage ? null : symbolIcon!,
      assetUrl: useAssetImage ? assetUrl : null,
      width: imageSize,
      height: imageSize,
      radius: radius,
      errorWidget: useAssetImage
          ? null
          : ImageWidget(
              assetUrl: "icon_chain_default",
              width: imageSize,
              height: imageSize,
              fit: BoxFit.contain,
            ),
      fit: BoxFit.contain,
    );

    if (coinModel!.isToken! &&
        badgeChainIcon != null &&
        !Get.isEmptyString(badgeChainIcon)) {
      final chainSize = Get.setImageSize(chainImageSzie);

      image = Stack(
        clipBehavior: Clip.none,
        children: [
          image,
          Positioned(
            right: offset,
            bottom: offset,
            child: ImageWidget(
              assetUrl: badgeChainIcon,
              width: chainSize,
              height: chainSize,
            ),
          ),
        ],
      );
    }

    return image;
  }
}
