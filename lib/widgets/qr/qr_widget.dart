/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-10-18 13:47:26
 */

import 'package:coinbag/modules/profile/qr/qr_config.dart';
import 'package:coinbag/modules/profile/qr/qr_controller.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

class QRWidget extends StatelessWidget {
  const QRWidget({
    super.key,
    required this.data,
    this.width,
    this.height,
    this.imageSize,
    this.padding = const EdgeInsets.all(10.0),
    this.assetImage,
  });

  final double? width;
  final double? height;
  final AssetImage? assetImage;
  final double? imageSize;
  final String? data;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? QRConfig.qrSize,
      height: height ?? QRConfig.qrSize,
      child: QrImageView(
        data: data!,
        padding: padding,
        version: QrVersions.auto,
        backgroundColor: Get.theme.white,
        size: QRController.qrModel.qrSize.toDouble(),
        gapless: false,
        embeddedImage:
            assetImage ?? const AssetImage('assets/images/icon_logo.png'),
        embeddedImageStyle: QrEmbeddedImageStyle(
          size: Size(imageSize ?? QRConfig.qrImageSize,
              imageSize ?? QRConfig.qrImageSize),
        ),
      ),
    );
  }
}
