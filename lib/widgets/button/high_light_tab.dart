/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-19 10:35:45
 */
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';

class HighLightInkWell extends StatelessWidget {
  final Widget? child;
  final Color? highlightColor;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;

  const HighLightInkWell({
    super.key,
    required this.child,
    this.highlightColor,
    this.onTap,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // 使用 Material 组件来实现高亮效果
    return Material(
      color: Colors.transparent, // 默认背景色设置为透明
      child: InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        highlightColor: highlightColor ?? Get.theme.colorF3F3F5, // 设置高亮颜色
        splashColor: Colors.transparent, // 可以指定边界半径
        child: child, // 水波纹颜色设置为透明
      ),
    );
  }
}
