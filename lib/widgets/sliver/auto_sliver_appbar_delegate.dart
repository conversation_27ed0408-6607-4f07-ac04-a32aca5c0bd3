/*
 * @description: 自适应高度的吸顶组件
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-10 17:53:30
 * @LastEditTime: 2024-09-12 14:14:57
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';

class AutoSliverPersistentHeaderDelegate
    extends SliverPersistentHeaderDelegate {
  AutoSliverPersistentHeaderDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Get.theme.bgColor,
      padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(AutoSliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
