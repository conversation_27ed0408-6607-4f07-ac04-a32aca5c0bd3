import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class CheckboxWidget extends StatelessWidget {
  final bool isCheck;
  final double? width;
  final ValueChanged<bool>? onChanged; // 添加 onChanged 属性

  const CheckboxWidget({
    super.key,
    this.isCheck = false,
    this.width,
    this.onChanged, // 初始化 onChanged
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onChanged?.call(!isCheck), // 点击时调用 onChanged
      child: AnimatedContainer(
        duration: kThemeAnimationDuration,
        height: width ?? 18,
        width: width ?? 18,
        color: Colors.transparent,
        child: ImageWidget(
          assetUrl: isCheck ? "touch_selected" : "touch_no_selected",
          width: Get.setImageSize(18),
          height: Get.setImageSize(18),
        ),
      ),
    );
  }
}
