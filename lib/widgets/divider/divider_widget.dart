/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2024-09-25 18:02:09
 */
// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';

class DividerWidget extends StatelessWidget {
  const DividerWidget(
      {super.key, this.height, this.padding = EdgeInsets.zero, this.color});

  /// 自定义高
  final double? height;

  final EdgeInsetsGeometry? padding;

  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Divider(
        height: height ?? Get.setHeight(0.5),
        indent: 0.0,
        color: color ?? Get.theme.colorECECEC,
      ),
    );
  }
}

class DividerBoldWidget extends StatelessWidget {
  const DividerBoldWidget(
      {super.key, this.height, this.padding = EdgeInsets.zero, this.color});

  /// 自定义高
  final double? height;

  final EdgeInsetsGeometry? padding;

  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Container(
        height: height ?? Get.setHeight(6),
        color: color ?? Get.theme.colorF9F9F9,
      ),
    );
  }
}
