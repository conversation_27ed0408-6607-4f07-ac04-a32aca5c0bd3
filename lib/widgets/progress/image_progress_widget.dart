/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-09-27 10:37:59
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class ImageProgressWidget extends StatefulWidget {
  const ImageProgressWidget({super.key});

  @override
  ImageProgressWidgetState createState() => ImageProgressWidgetState();
}

class ImageProgressWidgetState extends State<ImageProgressWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2), // 每次旋转的持续时间
      vsync: this,
    )..repeat(); // 无限重复旋转/ Repeat the animation indefinitely
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _controller,
        child: ImageWidget(
            assetUrl: 'pending',
            width: Get.setPaddingSize(60),
            height: Get.setPaddingSize(60)), // 替换为您的本地图片路径
        builder: (context, child) {
          return Transform.rotate(
            angle: _controller.value * 2.0 * 3.14159, // 旋转角度
            child: child,
          );
        },
      ),
    );
  }
}
