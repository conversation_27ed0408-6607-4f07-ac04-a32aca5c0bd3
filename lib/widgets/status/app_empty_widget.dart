/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2024-01-30 10:05:56
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class AppEmptyWidget extends StatelessWidget {
  final bool isCenter;
  const AppEmptyWidget({super.key, this.isCenter = true});

  @override
  Widget build(BuildContext context) {
    if (isCenter) {
      return Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageWidget(
                assetUrl: "ic_no_data",
                width: Get.setImageSize(88),
                height: Get.setImageSize(88),
              ),
              SizedBox(
                height: Get.setHeight(14),
              ),
              Text(ID.emptyData.tr,
                  style: TextStyle(
                    color: Get.theme.textSecondary,
                    fontSize: Get.setFontSize(14),
                    fontFamily: Get.setFontFamily(),
                    fontWeight: FontWeightX.regular,
                  )),
            ],
          ),
        ),
      );
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          children: [
            SizedBox(
              height: Get.setHeight(100),
            ),
            ImageWidget(
              assetUrl: "ic_no_data",
              width: Get.setImageSize(88),
              height: Get.setImageSize(88),
            ),
            SizedBox(
              height: Get.setHeight(14),
            ),
            Text(ID.emptyData.tr,
                style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
          ],
        ),
      ],
    );
  }
}
