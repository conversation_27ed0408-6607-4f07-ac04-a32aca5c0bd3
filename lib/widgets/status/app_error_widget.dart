/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2024-09-19 17:34:55
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/outlined_button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

class AppErrorWidget extends StatelessWidget {
  final VoidCallback? onRefresh;

  const AppErrorWidget({super.key, this.onRefresh});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImageWidget(
              assetUrl: "ic_no_data",
              width: Get.setImageSize(88),
              height: Get.setImageSize(88),
            ),
            SizedBox(
              height: Get.setHeight(14),
            ),
            Text(ID.dataError.tr,
                style: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                )),
            SizedBox(
              height: Get.setHeight(8),
            ),
            OutlinedButtonWidget(
                height: Get.setHeight(32),
                text: ID.reload.tr,
                textStyle: TextStyle(
                  color: Get.theme.textSecondary,
                  fontSize: Get.setFontSize(14),
                  fontFamily: Get.setFontFamily(),
                  fontWeight: FontWeightX.regular,
                ),
                onPressed: onRefresh),
          ],
        ),
      ),
    );
  }
}
