/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2024-08-20 10:56:16
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

AppBar baseAppBar({
  Widget? titleWidget, //自定义Title，仅文字
  String? title,
  bool? centerTitle,
  bool isCusomTitle = false, //是否重写自定义Title，如果Title是文字和图片
  List<Widget>? actionWidget,
  Widget? leading,
  Widget? customTitleWidget, //自定义的TitleWidget 如果Title是文字和图片
  bool? hideLeading = false,
  PreferredSizeWidget? bottom,
  double? titleSpacing,
  Color? backgroundColor,
  VoidCallback? onPressed,
  bool? isLiaghtStatusBarBrightness = false, // StatusBarBr顶部字体白色，如扫码页面
}) {
  return AppBar(
    title: isCusomTitle
        ? const SizedBox.shrink()
        : titleWidget ??
            Text(
              title ?? "",
              style: styleAppbarTitle,
            ),
    centerTitle: centerTitle ?? true,
    elevation: 0,
    titleSpacing: titleSpacing ?? 0,
    backgroundColor: backgroundColor ?? Get.theme.bgColor,
    bottom: bottom,
    systemOverlayStyle: isLiaghtStatusBarBrightness!
        ? SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarBrightness: Brightness.dark,
            systemNavigationBarColor: Get.theme.bgColor, // 根据主题设置底色
            systemNavigationBarIconBrightness: Get.isDarkMode
                ? Brightness.light
                : Brightness.dark, // 根据主题设置图标亮度
            statusBarIconBrightness: Brightness.light)
        : SystemUiOverlayStyle(
            statusBarBrightness: Brightness.light,
            statusBarColor: Colors.transparent,
            systemNavigationBarColor: Get.theme.bgColor, // 根据主题设置底色
            systemNavigationBarIconBrightness: Get.isDarkMode
                ? Brightness.light
                : Brightness.dark, // 根据主题设置图标亮度
            statusBarIconBrightness: Brightness.dark),
    flexibleSpace: LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return !isCusomTitle
            ? const SizedBox.shrink()
            : SafeArea(
                child: customTitleWidget ??
                    Center(
                      child: Text(
                        title ?? "",
                        style: styleAppbarTitle,
                      ),
                    ),
              );
      },
    ),
    leading: hideLeading!
        ? const SizedBox.shrink()
        : leading ??
            IconButton(
              onPressed: () {
                if (onPressed != null) {
                  onPressed();
                } else {
                  Get.back();
                }
              },
              padding: EdgeInsets.only(left: Get.setPaddingSize(6)),
              icon: Semantics(
                label: ID.stringBackButton.tr,
                child: ImageWidget(
                  assetUrl: 'titlebar',
                  cacheRawData: true,
                  shape: BoxShape.circle,
                  width: Get.setImageSize(28),
                  height: Get.setImageSize(28),
                ),
              ),
            ),
    actions: actionWidget ?? <Widget>[],
  );
}
