import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/modules/profile/qr/qr_config.dart';
import 'package:coinbag/res/colors.dart';
import 'package:coinbag/widgets/skeleton/skeleton_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

class ListSkeletonItems extends StatelessWidget {
  const ListSkeletonItems({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: Get.setWidth(16)),
        itemCount: 10,
        itemBuilder: (_, index) {
          return Shimmer(
              child: Container(
            margin: EdgeInsets.only(top: Get.setWidth(10)),
            width: Get.width - 36,
            height: Get.width - 36 + 68,
            decoration: BoxDecoration(
              color: Get.theme.colorF3F3F5,
              borderRadius: BorderRadius.all(
                Radius.circular(Get.setRadius(4)),
              ),
            ),
          ));
        });
  }
}

class GridSkeletonItems extends StatelessWidget {
  const GridSkeletonItems({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    double gridWidth = (Get.width - 50) / 2;

    return GridView.builder(
        padding: EdgeInsets.symmetric(
            horizontal: Get.setWidth(20), vertical: Get.setWidth(10)),
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: gridWidth,
            crossAxisSpacing: 10.0,
            mainAxisSpacing: 10.0,
            childAspectRatio: gridWidth / (gridWidth + 68)),
        itemCount: 10,
        itemBuilder: (context, index) {
          return Shimmer(
            child: Container(
              decoration: BoxDecoration(
                color: Get.theme.colorF3F3F5,
                borderRadius:
                    BorderRadius.all(Radius.circular(Get.setWidth(16))),
              ),
            ),
          );
        });
  }
}

class HomeTokeListSkeletonWidget extends StatelessWidget {
  const HomeTokeListSkeletonWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      physics: const NeverScrollableScrollPhysics(), // 确保ListView总是可以滚动
      itemCount: 15,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(14)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(right: Get.setPaddingSize(10)),
              child: SkeletonItems(
                radius: Get.setRadius(100),
                width: Get.setWidth(28),
                height: Get.setWidth(28),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: [
                    SkeletonItems(
                      radius: Get.setRadius(4),
                      width: Get.width - Get.setWidth(72),
                      height: DefaultTextStyle.of(context).style.fontSize! * .8,
                    ),
                  ],
                ),
                SizedBox(height: Get.setHeight(4)),
                SkeletonItems(
                  radius: 4,
                  width: Get.width / 2 - 28,
                  height: DefaultTextStyle.of(context).style.fontSize! * .8,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class DappListSkeletonWidget extends StatelessWidget {
  const DappListSkeletonWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      physics: const NeverScrollableScrollPhysics(), // 确保ListView总是可以滚动
      itemCount: 15,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(14)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(right: Get.setPaddingSize(12)),
              child: SkeletonItems(
                radius: Get.setRadius(12),
                width: Get.setImageSize(48),
                height: Get.setImageSize(48),
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: [
                    SkeletonItems(
                      radius: Get.setRadius(4),
                      width: Get.width / 2 - 28,
                      height: DefaultTextStyle.of(context).style.fontSize! * .8,
                    ),
                  ],
                ),
                SizedBox(height: Get.setHeight(8)),
                SkeletonItems(
                  radius: 4,
                  width: Get.width - Get.setWidth(100),
                  height: DefaultTextStyle.of(context).style.fontSize! * .8,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class TronReclaimListSkeletonWidget extends StatelessWidget {
  const TronReclaimListSkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 15,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(14)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SkeletonItems(
                  radius: Get.setRadius(6),
                  width: Get.width * 0.6,
                  height: Get.setHeight(12),
                ),
                SizedBox(width: Get.setPaddingSize(12)),
                Expanded(
                  child: SkeletonItems(
                    radius: Get.setRadius(6),
                    height: Get.setHeight(12),
                  ),
                ),
              ],
            ),
            SizedBox(height: Get.setPaddingSize(12)),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SkeletonItems(
                  radius: Get.setRadius(6),
                  width: Get.width * 0.5,
                  height: Get.setHeight(12),
                ),
                SizedBox(width: Get.setPaddingSize(24)),
                Expanded(
                  child: SkeletonItems(
                    radius: Get.setRadius(6),
                    height: Get.setHeight(12),
                  ),
                ),
              ],
            ),
            SizedBox(height: Get.setPaddingSize(12)),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SkeletonItems(
                  radius: Get.setRadius(6),
                  width: Get.width * 0.4,
                  height: Get.setHeight(12),
                ),
                SizedBox(width: Get.setPaddingSize(24)),
                Expanded(
                  child: SkeletonItems(
                    radius: Get.setRadius(6),
                    height: Get.setHeight(12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class NftAssetsSkeletonWidget extends StatelessWidget {
  const NftAssetsSkeletonWidget({super.key});

  double get width => (Get.width - Get.setPaddingSize(16)) / 2;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16),
          vertical: Get.setPaddingSize(16),
        ),
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: width,
          childAspectRatio: 0.8,
          crossAxisSpacing: Get.setPaddingSize(16),
          mainAxisSpacing: Get.setPaddingSize(16),
        ),
        itemCount: 10,
        itemBuilder: (_, index) {
          return Column(
            children: [
              Expanded(
                child: SkeletonItems(
                  width: width,
                  radius: Get.setRadius(12),
                ),
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              SkeletonItems(
                width: width,
                height: 20,
                radius: Get.setRadius(6),
              ),
              SizedBox(height: Get.setPaddingSize(8)),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: SkeletonItems(
                      height: 20,
                      radius: Get.setRadius(6),
                    ),
                  ),
                  SizedBox(width: Get.setPaddingSize(8)),
                  Expanded(
                    flex: 1,
                    child: SkeletonItems(
                      height: 20,
                      radius: Get.setRadius(6),
                    ),
                  )
                ],
              )
            ],
          );
        });
  }
}

class NftCollectionSkeletonWidget extends StatelessWidget {
  const NftCollectionSkeletonWidget({super.key});

  double get width => (Get.width - Get.setPaddingSize(16)) / 2;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        padding: EdgeInsets.symmetric(
          horizontal: Get.setPaddingSize(16),
          vertical: Get.setPaddingSize(16),
        ),
        gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: width,
          childAspectRatio: width /
              (Get.setHeight(60 + 20 + 12 + 40) + Get.setPaddingSize(32 + 50)),
          crossAxisSpacing: Get.setPaddingSize(16),
          mainAxisSpacing: Get.setPaddingSize(16),
        ),
        itemCount: 10,
        itemBuilder: (_, index) {
          return Container(
            padding: EdgeInsets.all(Get.setPaddingSize(16)),
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(color: Get.theme.colorECECEC, width: 1),
            ),
            child: Column(
              children: [
                SkeletonItems(
                  width: Get.setImageSize(60),
                  height: Get.setImageSize(60),
                  radius: Get.setRadius(30),
                ),
                SizedBox(height: Get.setPaddingSize(8)),
                SkeletonItems(
                  width: width,
                  height: Get.setHeight(20),
                  radius: Get.setRadius(6),
                ),
                SizedBox(height: Get.setPaddingSize(4)),
                SkeletonItems(
                  width: width * 0.5,
                  height: Get.setHeight(12),
                  radius: Get.setRadius(6),
                ),
                SizedBox(height: Get.setPaddingSize(8)),
                SkeletonItems(
                  height: Get.setHeight(40),
                  radius: Get.setRadius(6),
                ),
              ],
            ),
          );
        });
  }
}

class NftActivitySkeletonWidget extends StatelessWidget {
  const NftActivitySkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 15,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(14)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 1,
                  child: SkeletonItems(
                    radius: Get.setRadius(4),
                    height: DefaultTextStyle.of(context).style.fontSize! * .8,
                  ),
                ),
                const Expanded(child: SizedBox(width: 20)),
                Expanded(
                  flex: 5,
                  child: SkeletonItems(
                    radius: Get.setRadius(4),
                    height: DefaultTextStyle.of(context).style.fontSize! * .8,
                  ),
                ),
              ],
            ),
            SizedBox(height: Get.setHeight(8)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 2,
                  child: SkeletonItems(
                    radius: Get.setRadius(4),
                    height: DefaultTextStyle.of(context).style.fontSize! * .8,
                  ),
                ),
                const Expanded(child: SizedBox(width: 20)),
                Expanded(
                  flex: 5,
                  child: SkeletonItems(
                    radius: Get.setRadius(4),
                    height: DefaultTextStyle.of(context).style.fontSize! * .8,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}

class NftRecordActivitySkeletonWidget extends StatelessWidget {
  const NftRecordActivitySkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: Get.setPaddingSize(16)),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 15,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.symmetric(vertical: Get.setPaddingSize(14)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(right: Get.setPaddingSize(12)),
              child: SkeletonItems(
                radius: Get.setRadius(12),
                width: Get.setImageSize(48),
                height: Get.setImageSize(48),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 2,
                        child: SkeletonItems(
                          radius: Get.setRadius(4),
                          height:
                              DefaultTextStyle.of(context).style.fontSize! * .8,
                        ),
                      ),
                      const Expanded(child: SizedBox(width: 16)),
                      Expanded(
                        flex: 8,
                        child: SkeletonItems(
                          radius: Get.setRadius(4),
                          height:
                              DefaultTextStyle.of(context).style.fontSize! * .8,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: Get.setHeight(8)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: SkeletonItems(
                          radius: Get.setRadius(4),
                          height:
                              DefaultTextStyle.of(context).style.fontSize! * .8,
                        ),
                      ),
                      const Expanded(child: SizedBox(width: 16)),
                      Expanded(
                        flex: 7,
                        child: SkeletonItems(
                          radius: Get.setRadius(4),
                          height:
                              DefaultTextStyle.of(context).style.fontSize! * .8,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SyncBalanceSkeletonWidget extends StatelessWidget {
  const SyncBalanceSkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SkeletonItems(
      width: QRConfig.qrSize,
      height: QRConfig.qrSize,
      radius: Get.setRadius(12),
    );
  }
}
