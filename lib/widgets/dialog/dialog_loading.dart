/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-18 10:18:52
 * @LastEditTime: 2024-02-27 21:35:33
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

/// @description :公共加载弹窗
class LoadingDialog {
  void init() {
    EasyLoading.instance
      ..indicatorType = EasyLoadingIndicatorType.ring // 使用圆形进度指示器
      ..loadingStyle = EasyLoadingStyle.dark // 暗色主题 暗黑模式这里配置
      // 你可以继续自定义其他的样式属性
      ..indicatorSize = 32.0 // 指示器大小
      ..radius = 10.0 // 圆角大小
      ..progressColor = Colors.white // 进度条颜色
      ..backgroundColor = const Color(0xF4454545) // 背景颜色
      ..indicatorColor = Colors.white // 指示器颜色
      ..textStyle = TextStyle(
        color: Colors.white,
        fontFamily: Get.setFontFamily(),
        fontWeight: FontWeightX.regular,
        fontSize: 12,
      )
      ..maskType = EasyLoadingMaskType.black // 蒙版类型
      ..userInteractions = false // 是否允许用户交互
      ..dismissOnTap = false; // 点击消失
  }
}
