/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-12-09 16:36:16
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/button/button_widget.dart';
import 'package:coinbag/widgets/image/image_widget.dart';
import 'package:flutter/material.dart';

/// @description :公共通用弹窗

/// 中间弹窗信息
class AlertDialog extends StatelessWidget {
  const AlertDialog(
      {super.key,
      this.title,
      this.content,
      this.onContinue,
      this.onCancel,
      this.onConfirm,
      this.onCancelText,
      this.onConfirmText});

  /// 标题
  final String? title;

  /// 内容
  final String? content;

  ///Cancel Text
  final String? onCancelText;

  ///buttonText
  final String? onConfirmText;

  final VoidCallback? onConfirm;

  final VoidCallback? onCancel;

  final VoidCallback? onContinue;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: SizedBox(
      width: Get.width - Get.setWidth(80),
      child: Material(
          borderRadius: BorderRadius.circular(Get.setRadius(16)),
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: Get.setWidth(15), horizontal: Get.setWidth(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(title ?? "",
                        style: TextStyle(
                            color: Get.theme.textPrimary,
                            fontFamily: Get.setFontFamily(),
                            fontSize: Get.setFontSize(18),
                            fontWeight: FontWeightX.medium)),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        if (onCancel != null) {
                          Get.back();
                          onCancel!();
                        } else {
                          Get.dismissDialog();
                        }
                      },
                      child: Container(
                        color: Get.theme.bgColor,
                        child: ImageWidget(
                          assetUrl: "icon_exit",
                          width: Get.setImageSize(24),
                          height: Get.setImageSize(24),
                        ),
                      ),
                    )
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Text(content ?? "",
                      style: TextStyle(
                          color: Get.theme.textPrimary,
                          fontFamily: Get.setFontFamily(),
                          fontSize: Get.setFontSize(16),
                          fontWeight: FontWeightX.regular)),
                ),
                ButtonWidget(
                    buttonSize: ButtonSize.full,
                    text: onConfirmText ?? ID.stringConfirm.tr,
                    onPressed: () {
                      if (onConfirm != null) {
                        onConfirm!();
                      } else {
                        Get.dismissDialog();
                      }
                    }),
                Visibility(
                  visible: onCancelText != null,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        if (onContinue != null) {
                          Get.back();
                          onContinue!();
                        } else {
                          Get.back();
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(onCancelText ?? "",
                                style: TextStyle(
                                    color: Get.theme.colorFF6A16,
                                    fontFamily: Get.setFontFamily(),
                                    fontSize: Get.setFontSize(14),
                                    fontWeight: FontWeightX.regular)),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    ));
  }
}
