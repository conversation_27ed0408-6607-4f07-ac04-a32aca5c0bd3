/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-02-26 16:58:31
 * @LastEditTime: 2024-08-19 17:06:31
 */

import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/res/resource.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class AnimationTextWidget extends StatefulWidget {
  const AnimationTextWidget(
      {super.key,
      this.text = '',
      this.style,
      this.textAlign,
      this.overflow = TextOverflow.clip,
      this.maxLines});

  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final TextOverflow overflow;
  final int? maxLines;

  @override
  State<AnimationTextWidget> createState() => _FeeAnimationTextWidgetState();
}

class _FeeAnimationTextWidgetState extends State<AnimationTextWidget> {
  bool isOpacity = false;

  late TextStyle style;
  late Color color;
  String? oldText;
  bool isAnimation = false;

  Duration get duration {
    if (Get.isEmptyString(oldText)) {
      return const Duration(milliseconds: 0);
    }
    return const Duration(milliseconds: 500);
  }

  @override
  void initState() {
    style = widget.style ??
        TextStyle(fontSize: Get.setFontSize(14), color: Get.theme.textPrimary);
    color = style.color ?? Get.theme.textPrimary;
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> animation() async {
    await Future.delayed(duration);
    isOpacity = false;
    isAnimation = false;
    if (mounted) {
      setState(() {});
    }
    oldText = widget.text;
  }

  @override
  Widget build(BuildContext context) {
    if (oldText != null && oldText != widget.text) {
      isAnimation = true;
      isOpacity = true;
      animation();
    } else {
      oldText = widget.text;
    }

    String? fontFamily = style.fontFamily;
    if (widget.text == CommonConstant.emptyAsstes) {
      // 解决 --显示不全问题
      fontFamily = null;
    }
    return AnimatedDefaultTextStyle(
        style: TextStyle(
            fontSize: style.fontSize,
            fontFamily: fontFamily,
            fontWeight: style.fontWeight,
            color: isOpacity == true
                ? color.withOpacity(0)
                : color.withOpacity(1)),
        duration: duration,
        overflow: widget.overflow,
        maxLines: widget.maxLines,
        textAlign: widget.textAlign,
        child: Text(
          isAnimation == false ? widget.text : (oldText ?? widget.text),
        ));
  }
}
