/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2024-03-12 16:54:18
 */
import 'package:coinbag/base/controllers/base_get_extension.dart';
import 'package:coinbag/res/resource.dart';
import 'package:coinbag/widgets/tab_bar/shape_tab_decoration.dart';
import 'package:flutter/material.dart';

TabBar baseTabBar({
  bool isScrollable = false,
  TabController? controller,
  TextStyle? labelStyle,
  TextStyle? unselectedLabelStyle,
  List<Widget>? tabs,
  EdgeInsetsGeometry? labelPadding,
  ValueChanged<int>? onTab,
  bool? hideDivider = false, //隐藏底部下划线
  bool? hideIndicator = false, //隐藏指示器
}) {
  return TabBar(
      onTap: onTab,
      isScrollable: isScrollable,
      tabAlignment: isScrollable ? TabAlignment.start : TabAlignment.fill,
      controller: controller,
      labelPadding: labelPadding ?? EdgeInsets.zero,
      indicatorSize: TabBarIndicatorSize.label,
      indicatorColor: Get.theme.colorECECEC,
      dividerHeight: Get.setHeight(hideDivider! ? 0 : 0.5),
      dividerColor: Get.theme.colorECECEC,
      overlayColor: WidgetStateProperty.resolveWith((states) {
        return Colors.transparent;
      }),
      labelStyle: labelStyle ??
          TextStyle(
              color: Get.theme.textPrimary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setFontFamily()),
      unselectedLabelStyle: unselectedLabelStyle ??
          TextStyle(
              color: Get.theme.textTertiary,
              fontSize: Get.setFontSize(14),
              fontWeight: FontWeightX.medium,
              fontFamily: Get.setFontFamily()),
      indicator: hideIndicator!
          ? const BoxDecoration()
          : ShapeTabIndicator(
              indicatorBottom: 0,
              indicatorWidth: Get.setWidth(16),
              borderRadius:
                  BorderRadius.all(Radius.circular(Get.setRadius(12))),
              borderSide: BorderSide(
                width: 2,
                color: Get.theme.primary,
              )),
      tabs: tabs!);
}
