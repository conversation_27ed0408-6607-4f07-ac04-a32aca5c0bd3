/*
 * @author: Chen<PERSON>
 * @description: 
 * @LastEditTime: 2024-03-28 13:46:01
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/http/dio/interceptor/http_params_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class DioClient {
  static const int connectTimeOut = 30 * 1000;
  static const int receiveTimeOut = 30 * 1000;
  static final DioClient _instance = DioClient._internal();
  factory DioClient() => _instance;
  late Dio dio;

  ///初始化公共属性
  ///
  /// [baseUrl] 地址前缀
  /// [connectTimeout] 连接超时赶时间
  /// [receiveTimeout] 接收超时赶时间
  /// [interceptors] 基础拦截器
  void init({
    String? baseUrl,
    int connectTimeout = connectTimeOut,
    int receiveTimeout = receiveTimeOut,
    Map<String, String>? headers,
    List<Interceptor>? interceptors,
  }) {
    dio.options = dio.options.copyWith(
      baseUrl: baseUrl,
      connectTimeout: const Duration(milliseconds: connectTimeOut),
      receiveTimeout: const Duration(milliseconds: receiveTimeOut),
      headers: headers ?? const {},
    );
  }

  static void initClient({
    required String baseUrl,
    int connectTimeout = connectTimeOut,
    int receiveTimeout = receiveTimeOut,
    List<Interceptor>? interceptors,
  }) {
    DioClient().init(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      interceptors: interceptors,
    );
  }

  DioClient._internal() {
    BaseOptions options = BaseOptions(
      connectTimeout: const Duration(milliseconds: connectTimeOut),
      receiveTimeout: const Duration(milliseconds: receiveTimeOut),
      headers: {},
    );

    dio = Dio(options);
    // 添加拦截器
    dio.interceptors.add(HttpParamsInterceptor());
    if (kDebugMode && AppConfig.instance.enableRequestLog) {
      dio.interceptors.add(PrettyDioLogger(
        /// 显示请求头
        requestHeader: true,

        /// 显示请求体
        requestBody: true,
        // 显示响应体
        responseBody: true,
        // 不显示响应头
        responseHeader: false,
        // 是否以紧凑形式显示日志
        compact: false,
      ));
    }

    // // 在调试模式下需要抓包调试，所以我们使用代理，并禁用HTTPS证书校验
    // if (App.proxEnable) {
    //   (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //       (client) {
    //     client.findProxy = (uri) {
    //       // return "PROXY $PROXY_IP:$PROXY_PORT";
    //       return "proxy";
    //     };
    //     //代理工具会提供一个抓包的自签名证书，会通不过证书校验，所以我们禁用证书校验
    //     client.badCertificateCallback =
    //         (X509Certificate cert, String host, int port) => true;
    //   };
    // }
  }
}
