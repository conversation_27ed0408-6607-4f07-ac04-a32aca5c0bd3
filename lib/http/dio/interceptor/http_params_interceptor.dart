/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-10 14:47:52
 * @LastEditTime: 2024-12-03 13:09:32
 */

import 'dart:convert';

import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/dio/Exception/app_except.dart';
import 'package:coinbag/modules/locale/locale_controller.dart';
import 'package:coinbag/utils/log_utils.dart';
import 'package:coinbag/utils/package_info_manager.dart';
import 'package:dio/dio.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:get/utils.dart';

///请求公共参数拦截器
class HttpParamsInterceptor extends Interceptor {
  static const language = "language";
  static const appname = "appname";
  static const mobileType = "mobileType";
  static const mobileId = "mobileId";
  static const version = "version";

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    var headers = options.headers;
    headers[language] = LocaleController.getCountryCode;
    headers[mobileType] = GetPlatform.isAndroid ? "Android" : "IOS";
    headers[version] = PackageInfoManager().version;
    headers[appname] = PackageInfoManager().appName;
    if (AppConfig.instance.enableRequestJsonLog) {
      Log.r(
          '${"--------------------onRequest---------------"}\n${options.method}${' Url:'} ${options.uri}\n${'Query Parameters:'}${options.queryParameters}\n${'Body:'}${options.data != null ? const JsonEncoder.withIndent('  ').convert(options.data) : ""}\n\n');
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (AppConfig.instance.enableResponseJsonLog) {
      Log.r(
          '${"-----------------------------Response Start-----------------------------"}\n${response.requestOptions.method}${' Url:'} ${response.requestOptions.uri}\n${'Query Parameters:'}${response.requestOptions.queryParameters}\n${'Body:'}${response.requestOptions.data != null ? const JsonEncoder.withIndent('  ').convert(response.requestOptions.data) : ""}\n${'response data:'}\n$response\n\n${"-----------------------------Response End-------------------------------"}\n\n\n');
    }
    super.onResponse(response, handler);
  }

  @override
  Future<void> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    AppException appException = AppException.create(err);
    if (AppConfig.instance.enableRequestLog) {
      Log.e('DioException===: +${err.toString()}');
    }

    if (AppConfig.instance.enableBuglyHttpLog) {
      try {
        final buglyError = StringBuffer()
          ..writeln('API Error: ${err.response?.statusCode}')
          ..writeln('url: ${err.requestOptions.uri}')
          ..writeln('method: ${err.requestOptions.method}')
          ..writeln('dioType: ${err.type}') // 记录异常类型
          ..writeln('error: ${appException.msg ?? err.message}')
          ..writeln('requestHeaders: ${jsonEncode(err.requestOptions.headers)}')
          ..writeln(
              'requestQuery: ${jsonEncode(err.requestOptions.queryParameters)}')
          ..writeln(
              'requestBody: ${err.requestOptions.data != null ? jsonEncode(err.requestOptions.data) : ''}')
          ..writeln(
              'responseData: ${err.response?.data != null ? jsonEncode(err.response?.data) : ''}');

        FlutterBugly.uploadException(
          message: buglyError.toString(),
          detail: 'httpError',
          type: 'API Error',
        );

        Log.e('FlutterBugly DioException===: +${buglyError.toString()}');
      } catch (_) {}
    }

    Log.logPrint(err.requestOptions.headers);
    Log.logPrint(appException.type);
    Map<String, dynamic> data = err.requestOptions.headers;
    bool? isIntercep = data[APIConstant.ignoreIntercep];
    if (isIntercep == true &&
        (appException.type == DioExceptionType.badResponse ||
            appException.type == DioExceptionType.unknown)) {
      return handler.resolve(
        Response<Map<String, dynamic>>(
          requestOptions: err.requestOptions,
          statusCode: 200,
          data: {},
        ),
      );
    }

    return handler.next(appException);
  }
}
