/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-16 18:47:23
 * @LastEditTime: 2024-11-01 09:23:41
 */
class ErrorBean {
  int? code;
  String? message;

  ErrorBean({
    this.code,
    this.message,
  });

  // 从 JSON 创建 ErrorBean 实例
  factory ErrorBean.fromJson(Map<String, dynamic> json) {
    String? message;

    if (json['message'] is List) {
      // 如果是 List，取第一个元素作为消息
      var messageList = json['message'] as List;
      if (messageList.isNotEmpty) {
        // 检查第一个元素的类型
        var firstElement = messageList.first;
        if (firstElement is String) {
          message = firstElement; // 如果是 String，直接赋值
        } else if (firstElement is int) {
          // 如果是 int，可以根据需要进行处理，例如转换为 String
          message = firstElement.toString(); // 转换为 String
        }
      }
    } else if (json['message'] is String) {
      // 否则直接取值
      message = json['message'] as String?;
    }

    return ErrorBean(
      code: (json['code'] as num?)?.toInt(),
      message: message,
    );
  }

  // 将 ErrorBean 实例转换为 JSON
  Map<String, dynamic> toJson() => {
        'code': code,
        'message': message,
      };
}
