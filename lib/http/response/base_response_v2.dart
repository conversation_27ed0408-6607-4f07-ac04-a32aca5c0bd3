/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-15 12:59:21
 * @LastEditTime: 2024-01-16 18:59:18
 */
import 'package:json_annotation/json_annotation.dart';

/**
 * 标准数据基类V2
 */
part 'base_response_v2.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseResponseV2<T> {
  @JsonKey(name: "code")
  int? code;
  @Json<PERSON>ey(name: "message")
  String? message;
  @JsonKey(name: "data")
  T? data;

  BaseResponseV2({
    this.code,
    this.message,
    this.data,
  });

  factory BaseResponseV2.fromJson(
          Map<String, dynamic> json, T Function(Object? json) fromJsonT) =>
      _$BaseResponseV2FromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$BaseResponseV2ToJson(this, toJsonT);
}
