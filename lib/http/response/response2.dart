import 'package:json_annotation/json_annotation.dart';

class Response2<T> {
  T? body;
  int? code;
  @Json<PERSON>ey(name: 'status')
  String? messgae;

  Response2({this.body, this.code, this.messgae});

  factory Response2.fromBodyCode445StatusFail(Map<String, dynamic> json) {
    return Response2(
      body: json['body'],
      code: json['code'] as int?,
      messgae: json['status'] as String?,
    );
  }

  Map<String, dynamic> toBodyCode445StatusFail() => {
        'body': body,
        'code': code,
        'status': messgae,
      };
}
