/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-09 18:16:49
 * @LastEditTime: 2024-01-12 17:20:15
 */

import 'package:json_annotation/json_annotation.dart';

/**
 * 标准数据基类
 */
part 'base_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  @Json<PERSON>ey(name: "code")
  int? code;
  @Json<PERSON>ey(name: "msg")
  String? message;
  @JsonKey(name: "body")
  T? data;

  BaseResponse({
    this.code,
    this.data,
    this.message,
  });

  factory BaseResponse.fromJson(
          Map<String, dynamic> json, T Function(Object? json) fromJsonT) =>
      _$BaseResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$BaseResponseToJson(this, toJsonT);
}
