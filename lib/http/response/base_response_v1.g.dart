// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_v1.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseResponseV1<T> _$BaseResponseV1FromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseResponseV1<T>(
      id: (json['id'] as num?)?.toInt(),
      jsonrpc: json['jsonrpc'] as String?,
      data: _$nullableGenericFromJson(json['result'], fromJsonT),
    )..error = json['error'] == null
        ? null
        : ErrorBean.fromJson(json['error'] as Map<String, dynamic>);

Map<String, dynamic> _$BaseResponseV1ToJson<T>(
  BaseResponseV1<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'id': instance.id,
      'jsonrpc': instance.jsonrpc,
      'error': instance.error,
      'result': _$nullableGenericToJson(instance.data, toJsonT),
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);
