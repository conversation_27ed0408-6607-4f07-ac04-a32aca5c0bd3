/*
 * @description: Do not edit
 * @Author: chendong
 * @Date: 2024-01-08 09:12:12
 * @LastEditTime: 2024-12-03 11:27:56
 */
import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/dio/dio_provider.dart';
import 'package:coinbag/http/response/base_response_v1.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'block_chain_service.g.dart';

@RestApi()
abstract class BlockChainService {
  factory BlockChainService({Dio? dio, String? baseUrl}) {
    dio ??= DioClient().dio;

    return _BlockChainService(dio, baseUrl: '${BlockChainAPI.baseUrl}/agent');
  }

  //获取首页币种余额
  @POST("")
  Future<BaseResponseV1<dynamic>> getHomeBalance(
    @Header(APIConstant.ignoreIntercep) bool ignoreIntercep,
    @Body() Map<String, dynamic> requestParameters,
  );

  //获取交易记录
  @POST("")
  Future<BaseResponseV1<dynamic>> getTokenActivity(
      @Body() Map<String, dynamic> requestParameters);

  //获取币种详情余额
  @POST("")
  Future<BaseResponseV1<dynamic>> getTokenBalance(
      @Body() Map<String, dynamic> requestParameters);

  //获取btc最佳矿工费
  @POST("")
  Future<BaseResponseV1<dynamic>> getBestBtcFee(
      @Body() Map<String, dynamic> requestParameters);

  //获取eth最佳矿工费
  @POST("")
  Future<BaseResponseV1<dynamic>> getBestEthFee(
      @Body() Map<String, dynamic> requestParameters);

  //矿工费档位
  @POST('')
  Future<BaseResponseV1<dynamic>> getFeeGear(
      @Body() Map<String, dynamic> requestParameters);

  //是否合约授权
  @POST('')
  Future<BaseResponseV1<dynamic>> isApproveTransaction(
      @Body() Map<String, dynamic> requestParameters);

  //矿工费GasLimit
  @POST('')
  Future<BaseResponseV1<dynamic>> getGasLimit(
      @Body() Map<String, dynamic> requestParameters);

  //Eos账户
  @POST('')
  Future<BaseResponseV1<dynamic>> getEosAccout(
      @Body() Map<String, dynamic> requestParameters);

  //FIL Gas
  @POST('')
  Future<BaseResponseV1<dynamic>> getFilLimit(
      @Body() Map<String, dynamic> requestParameters);

  // nonce
  @POST('')
  Future<BaseResponseV1<dynamic>> getNonce(
      @Body() Map<String, dynamic> requestParameters);

  // 余额
  @POST('')
  Future<BaseResponseV1<dynamic>> getBalance(
      @Body() Map<String, dynamic> requestParameters);

  // 广播
  @POST('')
  Future<BaseResponseV1<dynamic>> broadcast(
      @Body() Map<String, dynamic> requestParameters);

  // trx 账户信息
  @POST('')
  Future<BaseResponseV1<dynamic>> trxAccountInfo(
      @Body() Map<String, dynamic> requestParameters);

  // trx20 交易花费能量查询
  @POST('')
  Future<BaseResponseV1<dynamic>> getTrx20TsEnergy(
      @Body() Map<String, dynamic> requestParameters);

  // TRX交易（获取待签名交易数据）
  @POST('')
  Future<BaseResponseV1<dynamic>> getTrxTransactionInfo(
      @Body() Map<String, dynamic> requestParameters);

  // TRX交易 资源消耗
  @POST('')
  Future<BaseResponseV1<dynamic>> getTrxTransactionResource(
      @Body() Map<String, dynamic> requestParameters);

  // EOS区块信息
  @POST('')
  Future<BaseResponseV1<dynamic>> getEOSBlockInfo(
      @Body() Map<String, dynamic> requestParameters);

  // 查询交易详情信息
  @POST('')
  Future<BaseResponseV1<dynamic>> getTransactionDetails(
      @Body() Map<String, dynamic> requestParameters);

  // 查询utxo余额
  @POST('')
  Future<BaseResponseV1<dynamic>> getUtxoBalance(
      @Body() Map<String, dynamic> requestParameters);

// psbt
  @POST('')
  Future<BaseResponseV1<dynamic>> getPsbtTxHexs(
      @Body() Map<String, dynamic> requestParameters);

  /// getArbAndOptNonce
  @POST('')
  Future<BaseResponseV1<dynamic>> getArbAndOptNonce(
      @Body() Map<String, dynamic> requestParameters);

  //获取账户详情
  @POST("")
  Future<BaseResponseV1<dynamic>> getAccountInfo(
      @Body() Map<String, dynamic> requestParameters);

  //获取账户资源详情
  @POST("")
  Future<BaseResponseV1<dynamic>> getAccountResource(
      @Body() Map<String, dynamic> requestParameters);

  // Tron 资源请求
  @POST('')
  Future<BaseResponseV1<dynamic>> tronResource(
      @Body() Map<String, dynamic> requestParameters);

  /// NFT
  @POST('')
  Future<BaseResponseV1<dynamic>> nftRequest(
      @CancelRequest() CancelToken? cancelToken,
      @Body() Map<String, dynamic> requestParameters);

  /// block chain 通用请求
  @POST('')
  Future<BaseResponseV1<dynamic>> blockChainRequest(
      @Body() Map<String, dynamic> requestParameters);

  /// zcashChainBranchId
  @POST('')
  Future<BaseResponseV1<dynamic>> zcashChainBranchId(
      @Body() Map<String, dynamic> requestParameters);
}
