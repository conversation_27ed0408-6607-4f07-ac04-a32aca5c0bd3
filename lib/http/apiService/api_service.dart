/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-11-07 15:54:27
 */

import 'package:coinbag/constant/common_constant.dart';
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/dio/dio_provider.dart';
import 'package:coinbag/http/response/base_response.dart';
import 'package:coinbag/modules/dapp/models/rpc_model.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'api_service.g.dart';

@RestApi()
abstract class ApiService {
  factory ApiService({Dio? dio, String? baseUrl}) {
    dio ??= DioClient().dio;
    return _ApiService(dio, baseUrl: baseUrl ?? API.baseUrl);
  }

  ///----------------------------wallet------------------------------

  ///监听钱包信息并上传
  @POST(API.uploadMonitorMessage)
  Future<BaseResponse<dynamic>> uploadMonitorMessage(@Body() String params);

  ///上传钱包地址
  @POST(API.uploadWalletAddress)
  Future<BaseResponse<dynamic>> uploadWalletAddress(@Body() String params);

  ///删除推送钱包地址
  @POST(API.deletePushAddress)
  Future<BaseResponse<dynamic>> deletePushAddress(@Body() String params);

  ///获取行情前200
  @GET(API.getMarketPriceList)
  Future<BaseResponse<dynamic>> getMarketPriceList(
      @Query("pageNum") int pageNum, @Query("pageSize") int pageSize);

  ///获取美元汇率和USDT单价
  @GET(API.getUSDPrices)
  Future<BaseResponse<dynamic>> getUSDPrices();

  ///----------------------------Dapp------------------------------

  //获取Dapp  rpc List
  @GET(API.getNode)
  Future<BaseResponse<RpcModel>> getDappRpcList();

  //获取Dapp List
  @GET(API.getDappInfo)
  Future<BaseResponse<dynamic>> getDappList(
      {@CancelRequest() CancelToken? cancelToken,
      @Query("version") required int version,
      @Query("pageSize") required String pageSize,
      @Query("filter") required String filter,
      @Query("pageNum") required String pageNum,
      @Query("deviceType") required String deviceType});

  ///----------------------------登录注册------------------------------
  @POST(API.getLogin)
  Future<BaseResponse<dynamic>> login(@Body() Map<String, dynamic> params);

  // 注销
  @GET(API.deleteAccount)
  Future<BaseResponse<dynamic>> deleteAccount(
    @Header(APIConstant.token) String token,
  );

  // 获取注册验证码
  @POST(API.getCode)
  Future<BaseResponse<dynamic>> getReisterCode(
      @Body() Map<String, dynamic> params);
  // 获取注册验证码
  @POST(API.getCodeForget)
  Future<BaseResponse<dynamic>> getForgetCode(
      @Body() Map<String, dynamic> params);
// 注册
  @POST(API.register)
  Future<BaseResponse<dynamic>> register(@Body() Map<String, dynamic> params);
// 设置密码 phone
  @POST(API.passwordPhone)
  Future<BaseResponse<dynamic>> passwordPhone(
      @Body() Map<String, dynamic> params);
  // 设置密码 email
  @POST(API.passwordEamil)
  Future<BaseResponse<dynamic>> passwordEmail(
      @Body() Map<String, dynamic> params);
  // 忘记密码
  @POST(API.forgetPassword)
  Future<BaseResponse<dynamic>> forgetPassword(
      @Body() Map<String, dynamic> params);
  // 修改密码
  @POST(API.changePassword)
  Future<BaseResponse<dynamic>> changePassword(
      @Header(APIConstant.token) String token,
      @Body() Map<String, dynamic> params);
  // 提交反馈
  @POST(API.feedback)
  Future<BaseResponse<dynamic>> feedback(@Body() Map<String, dynamic> params);

  //修改昵称
  @POST(API.editNickName)
  Future<BaseResponse<dynamic>> setNickName(
      @Header(APIConstant.token) String token,
      @Body() Map<String, dynamic> params);

  //连接账号
  @POST(API.bindAccount)
  Future<BaseResponse<dynamic>> bindAccount(
      @Header(APIConstant.token) String token,
      @Body() Map<String, dynamic> params);

  //连接账号发送验证码
  @GET(API.getBindSmsCode)
  Future<BaseResponse<dynamic>> getBindSmsCode(
      @Header(APIConstant.token) String token,
      @Query("account") String account);

//获取连接状态
  @GET(API.getBindStatus)
  Future<BaseResponse<dynamic>> getBindStatus(
    @Header(APIConstant.token) String token,
  );

  // 防伪验证
  @POST(API.verifyDiviceId)
  Future<BaseResponse<dynamic>> verifyDeviceId(
    @Body() Map<String, dynamic> params,
  );

  // Token列表
  @GET(API.getTokenList)
  Future<BaseResponse<dynamic>> getTokenList(
    @Query(APIConstant.size) int size,
  );

  // 搜索Token
  @GET(API.getSearchTokenList)
  Future<BaseResponse<dynamic>> getSearchTokenList(
    @Query(APIConstant.search) String search,
    @Query(APIConstant.type) String type,
  );

  //提交token
  @POST(API.addToken)
  Future<BaseResponse<dynamic>> addToken(
    @Body() Map<String, dynamic> params,
  );

  // 搜索Token
  @GET(API.checkUpgrade)
  Future<BaseResponse<dynamic>> checkUpgrade(
    @Query(APIConstant.version) String verson,
    @Query(APIConstant.deviceType) String platform,
    @Query(APIConstant.channel) String channel,
  );

  /// 获取EOS资源价格
  @GET(API.getEOSResroucePrice)
  Future<BaseResponse<dynamic>> getEosResourcePrice();

  @GET(BlockChainAPI.utxo)
  Future<dynamic> getUtxo(
      {@Query("chain") required String chain,
      @Query("address") required String address});
}
