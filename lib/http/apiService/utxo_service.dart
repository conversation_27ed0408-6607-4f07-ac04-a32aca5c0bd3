/*
 * @author: <PERSON><PERSON>
 * @description: 
 * @LastEditTime: 2024-04-09 14:57:53
 */
import 'package:coinbag/http/api.dart';
import 'package:coinbag/http/dio/dio_provider.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'utxo_service.g.dart';

@RestApi()
abstract class UtxoService {
  factory UtxoService({Dio? dio, String? baseUrl}) {
    dio ??= DioClient().dio;
    return _UtxoService(dio, baseUrl: BlockChainAPI.baseUrl);
  }

  @GET(BlockChainAPI.utxo)
  Future<dynamic> getUtxo(
      {@Query("chain") required String chain,
      @Query("address") required String address});
}
