/*
 * @description: 移动端API文档整理
 * @Author: wangdognshenng
 * @Date: 2024-01-09 18:16:49
 * @LastEditTime: 2025-04-02 15:42:02
 */

class API {
// **************************************************************************
//  apiConfig Url
// **************************************************************************

  ///动态环境api config base url
  static String configBaseUrl = "https://config.hengshengjia.com/config";

  /// 生产环境API Config BaseUrl
  static const String productApiConfigUrl =
      "https://config.hengshengjia.com/config";

  /// 测试环境 api Config  Test BaseUrl
  static const String devApiConfigUrl =
      "************************************************";

  /// 预生产环境 apiConfig
  static const String previewApiConfigUrl =
      "https://config.hengshengjia.com/config";

// **************************************************************************
//  BaseUrl
// **************************************************************************

  ///动态环境Base URL
  static String baseUrl = "https://api.coinbagbit.com";

  ///生产环境Base URL
  static const String productBaseUrl = "https://api.coinbagbit.com";

  ///测试环境Base URL
  static const String devBaseUrl = "http://**************:23130";

  ///预生产环境Base URL
  static const String previewBaseUrl = "https://api.coinbagbit.com";

// **************************************************************************
//  wallet
// **************************************************************************

  ///监听钱包信息上传
  static const String uploadMonitorMessage = "/app/monitoring/add";

  ///上传钱包地址
  static const String uploadWalletAddress = "/app/wallet/addWalletAddress";

  ///删除推送钱包地址
  static const String deletePushAddress = "/app/api/push/delete";

  /// 获取币种行情
  static const String getMarketPriceList = "/app/market/list";

  /// 获取美元汇率和USDT单价
  static const String getUSDPrices = "/app/index/listcoinPrices?coins=USDT";

// **************************************************************************
// Dapp
// **************************************************************************

  ///dapp首页推荐列表
  static const String getDappInfo = '/app/dapp/getDappInfo';

  ///dapp rpc
  static const String getNode = '/app/dapp/getNode';

// **************************************************************************
//  NFT
// **************************************************************************

// **************************************************************************
//  Profile
// **************************************************************************

  ///登录
  static const String getLogin = '/app/user/quickLogin';

  // 忘记密码验证码
  static const String getCodeForget = '/app/common/login/sendMsg';

  // 获取验证码
  static const String getCode = '/app/common/register/sendMsg';

  // 注册
  static const String register = '/app/common/checkIdentCode';

  // 设置密码
  static const String passwordPhone = '/app/user/registerByPhone';

  // 设置密码
  static const String passwordEamil = '/app/user/registerByEmail';

  // 忘记密码
  static const String forgetPassword = '/app/user/resetLoginPwd';

  // 修改密码
  static const String changePassword = '/app/user/changeLoginPwd';

  //提交反馈
  static const String feedback = '/app/api/feedback/add';

  //修改昵称
  static const String editNickName = '/app/user/editNickName';

  //连接账号发送验证码
  static const String getBindSmsCode = '/app/common/getBindSmsCode';

  //连接账号
  static const String bindAccount = '/app/user/bindAccount';

  //获取连接状态
  static const String getBindStatus = '/app/user/getBindStatus';

  //提交token
  static const String addToken = '/app/token/phoneAdd';

  //注销账号
  static const String deleteAccount = '/app/user/removeUser';

  // 验证设备id
  static const String verifyDiviceId = '/app/antiFake/checkDeviceId';

  /// Token 列表
  static const String getTokenList = '/app/token/pubTokenList';

  /// 搜索 Token
  static const String getSearchTokenList = '/app/token/phoneQuery';

  /// 检查升级
  static const String checkUpgrade = '/app/coinbag/version';

  /// eos资源单价
  static const String getEOSResroucePrice = '/app/account/eos_cpu_ram_net';
}

///BlockChain API

///BlockChain API /agent
class BlockChainAPI {
// **************************************************************************
//  BaseUrl
// **************************************************************************

  ///动态环境Base URL
  static String baseUrl = "https://blockchain.coinbagbit.com";

  ///生产环境Base URL
  static const String productBaseUrl = "https://blockchain.coinbagbit.com";

  ///测试环境Base URL
  static const String devBaseUrl = "http://**************:23130";

  ///预生产环境Base URL
  static const String previewBaseUrl = "https://blockchain.coinbagbit.com";

  /// 获取首页币种余额
  static const String getHomeBalance = 'user.get_index_balance';

  /// 交易记录
  static const String getTokenActivity = 'user.get_history';

  /// 获取币种详情余额
  static const String getTokenBalance = 'user.get_balance';

  ///是否授权合约交易
  static const String isApproveTransaction = 'boockchain.is_approve';

  /// 获取EOS账户详情
  static const String getEosAccountInfo = 'user.account.account_info';

  /// 获取Tron账户详情
  static const String getTronAccountInfo = 'user.account.get_account_info';

  /// 获取Torn资源详情
  static const String getAccountResource = 'user.account.get_account_resource';

  //钱包工具获取最佳矿工费
  static const String btcgasprice = 'blockchain.estimatefee';

  //钱包工具获取ETH最佳矿工费
  static const String ethgasprice = "blockchain.eth_estimatefee";

  //档位
  static const String feeGear = 'blockchain.gradient_estimatefee';

  //档位
  static const String feeGearEIP1559 =
      'blockchain.eip1559_gradient_estimatefee';

  //gasLimit
  static const String gasLimit = 'blockchain.eth_estimategas';
  //blockchain.sol_estimategas
  static const String solGasLimit = 'blockchain.sol_estimategas';

  //Eos账户
  static const String getEosAccout = 'user.account.get_key_accounts';

  // fil矿工费
  static const String getFilGas = 'blockchain.estimatefee';

  // balance and Nonce
  static const String getBalanceAndNonce = 'user.get_balance';

  // Utxo
  static const String utxo = '/combine/getAvailableUtxos';

  // 加验证的广播交易
  static const String broadcast = 'transaction.send_transaction_with_verify';

  // EOS广播交易或不验证的广播
  static const String noVerifyBroadcast = 'transaction.send_transaction';

  // trx账户信息
  static const String trxAccountInfo = 'user.account.get_account_info';

  // trx20交易能量查询
  static const String trx20TsEnergy =
      'transaction.get.trigger_constant_contract';

  // TRX交易（获取待签名交易数据）
  static const String trxTransactionInfo =
      'enterprise.block.generate_transaction';

  // TRX 交易消耗资源
  static const String trxTransactionSource =
      'transaction.get.transaction_resources';

  // 查询节点信息（最新区块高度）
  static const String getBlockInfo = 'server.status';

  // 查询交易详情信息
  static const String transactionDetails = 'transaction.get_tx_details';

  // utxo余额
  static const String getUtxoBalance =
      'blockchain.address.get_utxo_balance_detail';

  // getPsbtTxHexs
  static const String getPsbtTxHexs = 'blockchain.tx.get_tx_hexs';

  /// blockchain.get_nonce
  static const String getNonce = 'blockchain.get_nonce';

  /// getTronResourceV2
  static const String getTronResourceV2 =
      'user.account.get_account_resource_v2';

  /// transaction.get.get_candelegatedmaxsize
  static const String availableDelegate =
      'transaction.get.get_candelegatedmaxsize';

  /// enterprise.block.create_delegate_resource
  static const String createDelegateResource =
      'enterprise.block.create_delegate_resource';

  /// enterprise.block.create_undelegate_resource
  static const String reclaimTxResource =
      'enterprise.block.create_undelegate_resource';

  /// enterprise.block.create_unfreeze_transactionV2
  static const String unstakeTransactionV2 =
      'enterprise.block.create_unfreeze_transactionV2';

  /// 提取 enterprise.block.create_withdraw_expire_unfreeze_transaction
  static const String withdrawTransaction =
      'enterprise.block.create_withdraw_expire_unfreeze_transaction';

  /// v1 查询为他人质押列表 enterprise.block.get_delegated_resource_account_list
  static const String stakeV1ToOtherList =
      'enterprise.block.get_delegated_resource_account_list';

  /// unstakeV1Transaction
  static const String unstakeV1Transaction =
      'enterprise.block.generate_transaction';

  /// nft.get_user_all_assets
  static const String nftAssets = 'nft.get_user_all_assets';

  /// nft.get_user_all_assets_with_collection
  static const String nftCollections =
      'nft.get_user_all_assets_with_collection';

  /// nft.get_nft_transactions
  static const String nftActivity = 'nft.get_nft_transactions';

  /// nft.get_user_transactions
  static const String nftRecordActivity = 'nft.get_user_transactions';

  /// blockchain.getLatestBlockhash
  static const String getSolanaBlockHash = 'blockchain.getLatestBlockhash';

  /// blockchain.getMinimumBalanceForRentExemption
  static const String getSolMinBalance =
      'blockchain.getMinimumBalanceForRentExemption';

  /// blockchain.getAccountInfo
  static const String getAccountInfo = 'blockchain.getAccountInfo';

  /// blockchain.zec_consensus_branch_id
  static const String zecBranchId = 'blockchain.zec_consensus_branch_id';

  /// blockchain.getTokenMinimumBalanceForRentExemption
  static const String solTokenMinimum =
      'blockchain.getTokenMinimumBalanceForRentExemption';

  /// blockchain.cosmos.simulate
  static const String cosmosSimulate = 'blockchain.cosmos.simulate';
}

/// Token首页图标URL临时解决方案
class TokenIconUrl {
  // **************************************************************************
// Token首页图标URL
// **************************************************************************

  static const String usdt =
      "http://file.hengshengjia.com/app-front-file/image/2024022210075107181795.png";

  static const String usdc =
      "http://file.hengshengjia.com/app-front-file/image/2023072410400209285009.png";

  static const String ht =
      "http://file.hengshengjia.com/app-front-file/user/imgs/headUrl/01049c26d34e48b492d23bf92f5c20eb_HT.png";

  static const String eth =
      "http://file.hengshengjia.com/app-front-file/image/202402220906490324778.png";
}

class TronUrl {
  /// 交易额外消耗能量详情 us
  static const String tronlinkUS =
      'https://support.tronlink.org/hc/en-us/articles/14496201625113';

  /// 交易额外消耗能量详情 zh
  static const String tronlinkZH =
      'https://support.tronlink.org/hc/zh-cn/articles/14496201625113';

  static const String tronStakeV2 =
      'https://support.tronlink.org/hc/zh-cn/articles/16209476993561';
}

/// 官网相关或者协议相关URL
class AgreementUrl {
  ///官网
  static const String official = 'https://www.coinbagbit.com/';

  /// 关于 URL
  static const String aboutUrl = 'https://www.coinbagbit.com/';

  /// 用户协议 URL
  static const String userArgumentZH = 'https://www.coinbagbit.com/zh/privacy/';
  static const String userArgumentEN = 'https://www.coinbagbit.com/en/privacy/';

  ///官网
  static const String marketUrl = "https://www.coinbagbit.com/";

  ///iOS App Store 链接
  static const String appStoreUrl =
      'https://apps.apple.com/us/app/coinbagbit/id6479732222';

  /// Android Google Play 链接
  static const String playStoreUrl =
      'https://play.google.com/store/apps/details?id=com.coin.bag';

  ///EOS CPU租赁网址
  static const String eosCPULeaseUrl = 'https://eospowerup.io/free';

  ///VIP权益网址
  static const String vipBenefitsUrl =
      'aHR0cHM6Ly93d3cuY29sZGxhci5jb20vdWx0cmEtYXBwL2g1Lw==';
}
