/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-17 11:36:24
 * @LastEditTime: 2024-03-19 19:00:10
 */

import 'package:coinbag/http/params/base_request_model.dart';

class BlockChainParamsManager {
  static Map<String, dynamic> createParams(
      {required String method, required Map<String, dynamic> requestParams}) {
    BaseRequestModel baseRequestModel = BaseRequestModel();
    baseRequestModel.id = 1;
    baseRequestModel.jsonrpc = "2.0";
    baseRequestModel.method = method;
    baseRequestModel.params = requestParams;
    return baseRequestModel.toJson();
  }

  static BaseRequestModel createRequestMode({required String method}) {
    BaseRequestModel baseRequestModel = BaseRequestModel();
    baseRequestModel.id = 1;
    baseRequestModel.jsonrpc = "2.0";
    baseRequestModel.method = method;
    return baseRequestModel;
  }
}
