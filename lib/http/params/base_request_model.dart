/*
 * @description: Do not edit
 * @Author: wangdog<PERSON><PERSON><PERSON>
 * @Date: 2024-01-16 18:09:11
 * @LastEditTime: 2024-01-17 13:05:29
 */
/*
 * @description: Do not edit
 * @Author: wangdognshenng
 * @Date: 2024-01-16 18:09:11
 * @LastEditTime: 2024-01-16 18:11:58
 */
import 'package:json_annotation/json_annotation.dart';

part 'base_request_model.g.dart';

@JsonSerializable()
class BaseRequestModel {
  String? jsonrpc;
  String? method;
  dynamic params;
  int? id;

  BaseRequestModel({
    this.jsonrpc,
    this.method,
    this.params,
    this.id,
  });

  factory BaseRequestModel.fromJson(Map<String, dynamic> json) =>
      _$BaseRequestModelFromJson(json);
  Map<String, dynamic> toJson() => _$BaseRequestModelToJson(this);
}
