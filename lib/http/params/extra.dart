/*
 * @author: Chend
 * @description: 
 * @LastEditTime: 2024-12-11 10:31:30
 */

import 'package:coinbag/database/db_provider.dart';

class Extra {
  Extra();

  Map<String, dynamic> toJson() => {};

  Map<String, dynamic> toBalanceJson() => {
        'all_balance': true,
      };

  Map<String, dynamic> toContractBalanceJson(String contract) => {
        'contract': contract,
      };

  Map<String, dynamic> toSolContractBalanceJson({
    required String contract,
    required String derivedAddresses,
  }) {
    return {
      'contract': contract,
      'tokenDerivedAddresses': derivedAddresses,
    };
  }

  Map<String, dynamic> toTokenActivityJson([String? tokenDerivedAddresses]) => {
        'amount_abs': true,
        'tokenDerivedAddresses': tokenDerivedAddresses ?? '',
      };
  Map<String, dynamic> toBitcoinSeriesUnconfirmed() => {
        'zero_unconfirmed': true,
      };

  Map<String, dynamic> toEosOrTronTokenContractBalanceJson(
          String contract, String symbol) =>
      {
        'contract': contract,
        'symbol': symbol,
      };

  Map<String, dynamic> toTokenBalanceJson(List<TokenModel> tokens) {
    Map<String, dynamic> map = {
      'all_balance': true,
      'tokens': buildHashList(tokens)
    };
    return map;
  }

  Map<String, dynamic> toTokenModelBalanceJson(List<CoinModel> tokens) {
    Map<String, dynamic> map = {
      'all_balance': true,
      'tokens': buildTokenModelHashList(tokens)
    };
    return map;
  }

  List<Map<String, String>> buildTokenModelHashList(List<CoinModel> tokens) {
    List<String> extractHashStrings =
        tokens.map((token) => token.contract!).toList();

    return extractHashStrings.map((hash) => {'hash': hash}).toList();
  }

  List<Map<String, String>> buildHashList(List<TokenModel> tokens) {
    List<String> extractHashStrings =
        tokens.map((token) => token.contract!).toList();

    return extractHashStrings.map((hash) => {'hash': hash}).toList();
  }
}
