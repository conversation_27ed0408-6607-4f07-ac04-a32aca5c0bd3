/*
 * @author: wds
 * @description: 
 * @LastEditTime: 2024-08-14 17:25:25
 */
import 'package:coinbag/app/app_config.dart';
import 'package:coinbag/main.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

/// 测试网络入口
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  AppConfig.appConfigType = AppConfigType.test;
  AppConfig.instance.setApiConfig();
  startApp();
}
